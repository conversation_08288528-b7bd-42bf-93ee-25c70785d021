"""
智能聊天路由
"""

import logging

from fastapi import APIRouter

from core.workflow.graph import workflow_app
from services import ChatService

from ..models.chat import ChatRequest, ChatResponse

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/chat", tags=["智能聊天"])

# 初始化智能聊天服务
chat_service = ChatService()


@router.post("/", response_model=ChatResponse)
async def intelligent_chat(request: ChatRequest):
    """
    智能聊天API - 集成llmcal的智能处理能力
    支持自然语言日历操作、意图识别、多轮对话

    示例请求:
    {
        "message": "明天下午3点安排产品评审会议",
        "user_id": "user123",
        "context": {"timezone": "Asia/Shanghai"}
    }
    """
    try:
        logger.info(f"收到智能聊天请求: {request.message}")

        # 使用传统方式处理（保持兼容性）
        response = await chat_service.process_message(request)
        return response

    except Exception as e:
        logger.error(f"智能聊天处理失败: {str(e)}")
        return ChatResponse(
            message=f"处理请求时发生错误: {str(e)}", intent="error", success=False
        )


@router.post("/agents", response_model=ChatResponse)
async def intelligent_chat_with_agents(request: ChatRequest):
    """
    智能聊天API - 使用多代理工作流
    集成飞书API的完整多代理协作系统

    示例请求:
    {
        "message": "明天下午3点安排产品评审会议",
        "user_id": "user123",
        "context": {"timezone": "Asia/Shanghai"}
    }
    """
    try:
        logger.info(f"收到多代理聊天请求: {request.message}")

        # 使用多代理工作流处理
        response = await chat_service.process_message_with_agents(request)
        return response

    except Exception as e:
        logger.error(f"多代理聊天处理失败: {str(e)}")
        return ChatResponse(
            message=f"处理请求时发生错误: {str(e)}", intent="error", success=False
        )
