# Streamable MCP 核心文件

这个目录包含了Streamable HTTP模式的MCP服务器和客户端的核心实现文件。

## 核心文件说明

### 服务器文件
- `feishu_mcp_server_real.py` - Streamable HTTP模式的MCP服务器（真实飞书API版本）

### 客户端文件
- `feishu_mcp_client_streamable.py` - Streamable HTTP模式的MCP客户端
- `tclientmcp.py` - 简化的MCP客户端测试工具

### 启动脚本
- `start_real_server.py` - 启动真实飞书MCP服务器的脚本

### 配置文件
- `mcp_streamable_config.json` - Streamable MCP客户端配置
- `mcp_server_config.json` - MCP服务器配置

## 使用方法

### 启动真实飞书MCP服务器
```bash
python start_real_server.py
```

### 使用客户端连接
```bash
python feishu_mcp_client_streamable.py
```

## 功能特性

- ✅ 支持真实飞书日历API访问
- ✅ 支持日历列表获取
- ✅ 支持日历事件管理
- ✅ 支持OAuth认证
- ✅ 支持HTTP Streamable模式
- ✅ 完整的错误处理和重试机制

## 注意事项

- 首次使用需要完成OAuth认证
- 确保飞书应用权限配置正确
- 需要有效的用户访问令牌 