[flake8]
max-line-length = 88
extend-ignore = 
    # E203: whitespace before ':'
    E203,
    # W503: line break before binary operator
    W503,
    # E501: line too long (handled by black)
    E501
exclude = 
    .git,
    __pycache__,
    .venv,
    venv,
    .eggs,
    *.egg,
    build,
    dist,
    tests/fixtures/*,
    migrations/*
per-file-ignores =
    # F401: imported but unused
    __init__.py:F401
    # F811: redefinition of unused name
    conftest.py:F811
max-complexity = 10
import-order-style = google
application-import-names = integrations,api,services,core
