name: Test Suite

on:
  push:
    branches: [ main, develop, feature/* ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test-matrix:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9', '3.10', '3.11']
        test-type: ['unit', 'integration']

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio pytest-mock

    - name: Set up test environment
      run: |
        cp tests/test.env.example tests/test.env || echo "No test.env.example found"
        echo "TESTING=1" >> tests/test.env
        echo "FEISHU_ENV=test" >> tests/test.env
        echo "STORAGE_TYPE=memory" >> tests/test.env

    - name: Run ${{ matrix.test-type }} tests
      run: |
        if [ "${{ matrix.test-type }}" = "unit" ]; then
          pytest tests/unit/ -v --cov=. --cov-report=xml
        elif [ "${{ matrix.test-type }}" = "integration" ]; then
          pytest tests/integration/ -v
        fi

    - name: Upload coverage
      if: matrix.test-type == 'unit' && matrix.python-version == '3.10'
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml

  e2e-test:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-asyncio

    - name: Start application
      run: |
        python main.py &
        sleep 10  # 等待应用启动

    - name: Run E2E tests
      run: |
        pytest tests/e2e/ -v --tb=short

    - name: Stop application
      run: |
        pkill -f "python main.py" || true
