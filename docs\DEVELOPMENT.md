# 🛠️ 开发指南

## 📋 开发环境设置

### 环境要求
- Python 3.8+
- Git
- IDE推荐：VSCode、PyCharm
- LLM API密钥

### 本地开发环境搭建

1. **克隆项目**
   ```bash
   git clone https://github.com/ifcheung2012/feishu-coze-plugin.git
   cd feishu-coze-plugin
   ```

2. **创建虚拟环境**
   ```bash
   python -m venv venv
   
   # Windows
   venv\Scripts\activate
   
   # macOS/Linux
   source venv/bin/activate
   ```

3. **安装开发依赖**
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt  # 开发依赖
   ```

4. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，添加你的API密钥
   ```

5. **运行测试**
   ```bash
   pytest tests/
   ```

## 🏗️ 项目架构详解

### 核心模块说明

#### 1. core/ - 核心模块
```
core/
├── agents/          # 多代理系统
│   ├── __init__.py
│   ├── base.py     # 代理基类
│   ├── coordinator.py  # 协调器代理
│   ├── planner.py  # 规划器代理
│   ├── executor.py # 执行器代理
│   └── tools.py    # 工具绑定
├── ai/             # AI客户端
│   ├── __init__.py
│   ├── llm_client.py   # LLM客户端
│   └── providers/      # 不同LLM提供商
└── workflow/       # 工作流引擎
    ├── __init__.py
    ├── engine.py   # 工作流引擎
    └── graph.py    # 工作流图
```

#### 2. models/ - 数据模型
```
models/
├── __init__.py
├── agent_models.py     # 代理相关模型
├── chat.py            # 聊天模型
├── conversation.py    # 会话模型
└── calendar.py        # 日历模型
```

#### 3. services/ - 业务服务
```
services/
├── __init__.py
├── chat_service.py         # 聊天服务
├── confirmation_service.py # 确认服务
├── calendar_service.py     # 日历服务
└── feishu_service.py      # 飞书服务
```

#### 4. utils/ - 工具模块
```
utils/
├── __init__.py
├── time_parser.py     # 时间解析器
├── logger.py          # 日志工具
└── config.py          # 配置管理
```

### 关键设计模式

#### 1. 多代理协作模式
```python
# 代理基类
class BaseAgent:
    async def process(self, state: AgentState) -> AgentCommand:
        raise NotImplementedError

# 具体代理实现
class CoordinatorAgent(BaseAgent):
    async def process(self, state: AgentState) -> AgentCommand:
        # 意图识别逻辑
        intent = await self.classify_intent(state.user_input)
        return AgentCommand(goto="planner", update={"intent": intent})
```

#### 2. 状态管理模式
```python
# 会话状态枚举
class ConversationState(str, Enum):
    NORMAL = "normal"
    WAITING_CONFIRMATION = "waiting_confirmation"
    WAITING_SUPPLEMENT = "waiting_supplement"

# 状态转换
def transition_state(current_state, event):
    transitions = {
        (ConversationState.NORMAL, "need_confirmation"): ConversationState.WAITING_CONFIRMATION,
        (ConversationState.WAITING_CONFIRMATION, "confirmed"): ConversationState.NORMAL,
        # ...
    }
    return transitions.get((current_state, event), current_state)
```

#### 3. 工具绑定模式
```python
# 工具基类
class BaseTool:
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    async def invoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        raise NotImplementedError

# 具体工具实现
class TimeParsingTool(BaseTool):
    async def invoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        text = input_data.get("text", "")
        parser = TimeParser()
        result = parser.parse(text)
        return result.dict()
```

## 🧪 测试指南

### 测试结构
```
tests/
├── unit/               # 单元测试
│   ├── test_agents.py
│   ├── test_time_parser.py
│   └── test_services.py
├── integration/        # 集成测试
│   ├── test_chat_flow.py
│   └── test_api.py
└── e2e/               # 端到端测试
    └── test_scenarios.py
```

### 运行测试
```bash
# 运行所有测试
pytest

# 运行特定测试
pytest tests/unit/test_time_parser.py

# 运行测试并生成覆盖率报告
pytest --cov=core --cov=services --cov=utils

# 运行性能测试
pytest tests/performance/
```

### 编写测试示例
```python
import pytest
from services.chat_service import ChatService
from models.chat import ChatRequest

@pytest.mark.asyncio
async def test_chat_service_intent_recognition():
    """测试意图识别功能"""
    chat_service = ChatService()
    
    request = ChatRequest(
        user_id="test_user",
        message="明天下午3点安排会议"
    )
    
    response = await chat_service.process_message(request)
    
    assert response.intent == "calendar"
    assert response.confidence > 0.9
    assert response.success is True
```

## 🔧 开发工作流

### 1. 功能开发流程
1. **创建功能分支**
   ```bash
   git checkout -b feature/new-feature
   ```

2. **编写代码**
   - 遵循代码规范
   - 添加类型注解
   - 编写文档字符串

3. **编写测试**
   - 单元测试覆盖率 > 80%
   - 集成测试覆盖主要流程

4. **运行测试和检查**
   ```bash
   pytest
   flake8 .
   mypy .
   ```

5. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/new-feature
   ```

### 2. 代码规范

#### Python代码风格
- 遵循 PEP 8
- 使用 Black 格式化代码
- 使用 isort 排序导入
- 使用 flake8 检查代码质量

```bash
# 格式化代码
black .
isort .

# 检查代码质量
flake8 .
mypy .
```

#### 命名规范
- **类名**: PascalCase (如 `ChatService`)
- **函数名**: snake_case (如 `process_message`)
- **常量**: UPPER_SNAKE_CASE (如 `MAX_RETRY_COUNT`)
- **私有方法**: 以下划线开头 (如 `_internal_method`)

#### 文档字符串
```python
async def process_message(self, request: ChatRequest) -> ChatResponse:
    """
    处理聊天消息
    
    Args:
        request: 聊天请求对象
        
    Returns:
        ChatResponse: 聊天响应对象
        
    Raises:
        ValueError: 当请求参数无效时
        
    Example:
        >>> service = ChatService()
        >>> request = ChatRequest(user_id="123", message="hello")
        >>> response = await service.process_message(request)
    """
```

### 3. 调试技巧

#### 日志调试
```python
import logging

logger = logging.getLogger(__name__)

async def process_message(self, request: ChatRequest) -> ChatResponse:
    logger.info(f"处理用户消息: {request.message}")
    
    try:
        # 处理逻辑
        result = await self._handle_request(request)
        logger.info(f"处理成功: {result}")
        return result
    except Exception as e:
        logger.error(f"处理失败: {e}", exc_info=True)
        raise
```

#### 断点调试
```python
# 使用 pdb 调试
import pdb; pdb.set_trace()

# 使用 ipdb 调试 (推荐)
import ipdb; ipdb.set_trace()
```

#### 性能分析
```python
import cProfile
import pstats

# 性能分析
profiler = cProfile.Profile()
profiler.enable()

# 你的代码
await chat_service.process_message(request)

profiler.disable()
stats = pstats.Stats(profiler)
stats.sort_stats('cumulative')
stats.print_stats(10)
```

## 🚀 部署指南

### 开发环境部署
```bash
# 启动开发服务器
python main.py --debug --reload

# 或使用 uvicorn
uvicorn main:app --reload --host 0.0.0.0 --port 8000
```

### 生产环境部署
```bash
# 使用 gunicorn
gunicorn main:app -w 4 -k uvicorn.workers.UvicornWorker

# 使用 Docker
docker build -t intelligent-calendar-assistant .
docker run -p 8000:8000 intelligent-calendar-assistant
```

### 环境变量配置
```bash
# 开发环境
DEBUG=true
LOG_LEVEL=DEBUG

# 生产环境
DEBUG=false
LOG_LEVEL=INFO
WORKERS=4
```

## 🤝 贡献指南

### 提交规范
使用 Conventional Commits 规范：

```
<type>[optional scope]: <description>

[optional body]

[optional footer(s)]
```

类型说明：
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

示例：
```
feat(agents): add new coordinator agent

Add intelligent intent recognition and routing capabilities
to the coordinator agent for better user experience.

Closes #123
```

### Pull Request 流程
1. Fork 项目
2. 创建功能分支
3. 编写代码和测试
4. 确保所有测试通过
5. 提交 Pull Request
6. 代码审查
7. 合并到主分支

---

## 📚 相关资源

- [FastAPI 文档](https://fastapi.tiangolo.com/)
- [Pydantic 文档](https://pydantic-docs.helpmanual.io/)
- [pytest 文档](https://docs.pytest.org/)
- [PDeerFlow 项目](https://github.com/bytedance/deer-flow)
