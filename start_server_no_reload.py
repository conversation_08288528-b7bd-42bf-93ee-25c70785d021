#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
禁用热重载的服务器启动脚本
避免与其他Python进程冲突
"""

import uvicorn
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

if __name__ == "__main__":
    print("🚀 启动服务器 (禁用热重载)")
    print("📡 服务器地址: http://localhost:5000")
    print("📋 API文档: http://localhost:5000/docs")
    print("🔧 MCP协议: http://localhost:5000/mcp/")
    print("💡 按 Ctrl+C 停止服务器")
    print("=" * 50)
    
    # 禁用热重载，避免与其他Python进程冲突
    uvicorn.run(
        "main:app", 
        host="localhost", 
        port=5000, 
        reload=False,  # 关键：禁用热重载
        log_level="info"
    ) 