"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/tools/route";
exports.ids = ["app/api/tools/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftools%2Froute&page=%2Fapi%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftools%2Froute&page=%2Fapi%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/tools/route.ts */ \"(rsc)/./src/app/api/tools/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/tools/route\",\n        pathname: \"/api/tools\",\n        filename: \"route\",\n        bundlePath: \"app/api/tools/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\tools\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/tools/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftools%2Froute&page=%2Fapi%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/tools/route.ts":
/*!************************************!*\
  !*** ./src/app/api/tools/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nasync function GET() {\n    try {\n        // 动态导入核心工具适配器\n        const { ALL_FEISHU_CALENDAR_TOOLS } = await __webpack_require__.e(/*! import() */ \"_rsc_src_lib_tools_tool-adapter-core_ts\").then(__webpack_require__.bind(__webpack_require__, /*! @/lib/tools/tool-adapter-core */ \"(rsc)/./src/lib/tools/tool-adapter-core.ts\"));\n        // 简单的工具分类（基于工具名称）\n        const toolsWithCategories = ALL_FEISHU_CALENDAR_TOOLS.map((tool)=>{\n            let category = \"OTHERS\";\n            if (tool.name.includes(\"calendar.v4.calendar.\")) {\n                category = \"CALENDAR_MANAGEMENT\";\n            } else if (tool.name.includes(\"calendar.v4.calendarEvent.\")) {\n                category = \"EVENT_MANAGEMENT\";\n            }\n            return {\n                ...tool,\n                category\n            };\n        });\n        // 简单的统计信息\n        const stats = {\n            total: ALL_FEISHU_CALENDAR_TOOLS.length,\n            categories: {\n                CALENDAR_MANAGEMENT: toolsWithCategories.filter((t)=>t.category === \"CALENDAR_MANAGEMENT\").length,\n                EVENT_MANAGEMENT: toolsWithCategories.filter((t)=>t.category === \"EVENT_MANAGEMENT\").length,\n                OTHERS: toolsWithCategories.filter((t)=>t.category === \"OTHERS\").length\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            tools: toolsWithCategories,\n            stats,\n            summary: {\n                totalTools: ALL_FEISHU_CALENDAR_TOOLS.length,\n                description: \"核心飞书日历工具集 - 7个精选工具\"\n            }\n        });\n    } catch (error) {\n        console.error(\"获取工具信息失败:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: \"获取工具信息失败\",\n            details: error instanceof Error ? error.message : \"未知错误\",\n            tools: [],\n            categories: [],\n            stats: {\n                totalTools: 0,\n                expectedTotal: 43,\n                isComplete: false,\n                categories: {},\n                commonToolsCount: 0\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/tools/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Ftools%2Froute&page=%2Fapi%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();