import logging
import os

from config import SUPABASE_KEY, SUPABASE_URL

try:
    from supabase import Client, create_client

    SUPABASE_AVAILABLE = True
except ImportError:
    SUPABASE_AVAILABLE = False

    # 创建虚拟的create_client函数
    def create_client(*args, **kwargs):
        raise ImportError("Supabase module not available")

    Client = None

logger = logging.getLogger(__name__)

# 禁用hpack.hpack的DEBUG日志，仅当根日志级别不是DEBUG时
if logger.parent.level > logging.DEBUG:
    logging.getLogger("hpack.hpack").setLevel(logging.INFO)
# logging.getLogger('httpx').setLevel(logging.WARNING)
# logging.getLogger('httpcore').setLevel(logging.WARNING)


def get_supabase_client():
    """获取Supabase客户端实例

    Returns:
        supabase.Client: Supabase客户端实例

    Raises:
        ValueError: 如果SUPABASE_URL或SUPABASE_KEY未设置或supabase模块不可用
    """
    if not SUPABASE_AVAILABLE:
        logger.error("Supabase模块不可用，请安装: pip install supabase")
        raise ValueError("Supabase模块不可用")

    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.error("Supabase环境变量未设置: SUPABASE_URL或SUPABASE_KEY缺失")
        raise ValueError("SUPABASE_URL和SUPABASE_KEY环境变量必须设置")

    try:
        logger.info(f"创建Supabase客户端，URL: {SUPABASE_URL}")
        return create_client(SUPABASE_URL, SUPABASE_KEY)
    except Exception as e:
        logger.error(f"创建Supabase客户端失败: {str(e)}")
        raise
