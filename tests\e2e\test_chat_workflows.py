"""
聊天工作流端到端测试
"""

import asyncio
import json
import time
from datetime import datetime, timedelta

import aiohttp
import pytest

from tests.fixtures.chat_data import MULTI_TURN_SCENARIOS

# 测试配置
BASE_URL = "http://localhost:5000"


class TestChatWorkflows:
    """聊天工作流端到端测试"""

    @pytest.mark.asyncio
    async def test_complete_calendar_creation_workflow(self):
        """测试完整的日历创建工作流"""
        user_id = f"e2e_user_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 执行完整的多轮对话
            scenario = MULTI_TURN_SCENARIOS["calendar_creation_complete"]

            for step in scenario:
                chat_data = {
                    "message": step["user"],
                    "user_id": user_id,
                    "context": {"timezone": "Asia/Shanghai"},
                }

                async with session.post(
                    f"{BASE_URL}/chat/", json=chat_data
                ) as response:
                    assert response.status == 200
                    data = await response.json()

                    # 验证意图识别
                    if step["expected_intent"].value == "calendar":
                        assert data["intent"] == "calendar"
                    elif step["expected_intent"].value == "chat":
                        assert data["intent"] == "chat"

                    # 验证响应内容
                    if "expected_response_contains" in step:
                        response_message = data["message"].lower()
                        assert any(
                            keyword.lower() in response_message
                            for keyword in step["expected_response_contains"]
                        )

                # 添加小延迟模拟真实用户行为
                await asyncio.sleep(0.5)

    @pytest.mark.asyncio
    async def test_conversation_interruption_and_recovery(self):
        """测试对话中断和恢复工作流"""
        user_id = f"e2e_user_interrupt_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            scenario = MULTI_TURN_SCENARIOS["calendar_creation_interrupted"]

            for step in scenario:
                chat_data = {
                    "message": step["user"],
                    "user_id": user_id,
                    "context": {"timezone": "Asia/Shanghai"},
                }

                async with session.post(
                    f"{BASE_URL}/chat/", json=chat_data
                ) as response:
                    assert response.status == 200
                    data = await response.json()

                    # 验证意图识别
                    if step["expected_intent"].value == "calendar":
                        assert data["intent"] == "calendar"
                    elif step["expected_intent"].value == "chat":
                        assert data["intent"] == "chat"

                await asyncio.sleep(0.3)

    @pytest.mark.asyncio
    async def test_multi_intent_handling_workflow(self):
        """测试多意图处理工作流"""
        user_id = f"e2e_user_multi_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            scenario = MULTI_TURN_SCENARIOS["multi_intent_handling"]

            for step in scenario:
                chat_data = {
                    "message": step["user"],
                    "user_id": user_id,
                    "context": {"timezone": "Asia/Shanghai"},
                }

                async with session.post(
                    f"{BASE_URL}/chat/", json=chat_data
                ) as response:
                    assert response.status == 200
                    data = await response.json()

                    # 验证响应
                    if "expected_response_contains" in step:
                        response_message = data["message"].lower()
                        assert any(
                            keyword.lower() in response_message
                            for keyword in step["expected_response_contains"]
                        )

                await asyncio.sleep(0.3)

    @pytest.mark.asyncio
    async def test_concurrent_user_conversations(self):
        """测试并发用户对话"""
        async with aiohttp.ClientSession() as session:
            # 创建多个并发用户对话
            async def user_conversation(user_id):
                messages = [
                    "明天安排会议",
                    "项目讨论",
                    "1小时",
                    "确认",
                ]

                for message in messages:
                    chat_data = {
                        "message": message,
                        "user_id": f"concurrent_user_{user_id}",
                        "context": {"timezone": "Asia/Shanghai"},
                    }

                    async with session.post(
                        f"{BASE_URL}/chat/", json=chat_data
                    ) as response:
                        assert response.status == 200
                        await response.json()

                    await asyncio.sleep(0.2)

            # 启动5个并发对话
            tasks = [user_conversation(i) for i in range(5)]
            await asyncio.gather(*tasks)

    @pytest.mark.asyncio
    async def test_conversation_timeout_handling(self):
        """测试对话超时处理"""
        user_id = f"e2e_user_timeout_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 开始对话
            chat_data1 = {
                "message": "明天安排会议",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data1) as response:
                assert response.status == 200
                data1 = await response.json()
                assert data1["intent"] == "calendar"

            # 等待一段时间（模拟用户长时间无响应）
            await asyncio.sleep(2)

            # 发送新消息
            chat_data2 = {
                "message": "项目讨论",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data2) as response:
                assert response.status == 200
                data2 = await response.json()

                # 根据超时设置，可能需要重新开始对话
                # 这里的具体行为取决于实现

    @pytest.mark.asyncio
    async def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        user_id = f"e2e_user_error_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 开始正常对话
            chat_data1 = {
                "message": "明天安排会议",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data1) as response:
                assert response.status == 200

            # 发送无效输入
            chat_data2 = {
                "message": "32点开会",  # 无效时间
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data2) as response:
                assert response.status == 200
                data2 = await response.json()
                # 应该包含错误提示
                assert "无效" in data2["message"] or "重新" in data2["message"]

            # 发送正确输入
            chat_data3 = {
                "message": "下午3点",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data3) as response:
                assert response.status == 200
                data3 = await response.json()
                # 应该继续正常流程
                assert "会议主题" in data3["message"] or "标题" in data3["message"]

    @pytest.mark.asyncio
    async def test_conversation_personalization(self):
        """测试对话个性化"""
        user_id = f"e2e_user_personal_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 设置用户偏好（通过对话）
            preference_data = {
                "message": "我通常在会议室A开会",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(
                f"{BASE_URL}/chat/", json=preference_data
            ) as response:
                assert response.status == 200

            # 创建会议时应该使用偏好
            meeting_data = {
                "message": "明天下午安排会议",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=meeting_data) as response:
                assert response.status == 200
                data = await response.json()

                # 后续对话中应该体现个性化
                # 具体实现取决于系统设计

    @pytest.mark.asyncio
    async def test_conversation_memory_across_sessions(self):
        """测试跨会话的对话记忆"""
        user_id = f"e2e_user_memory_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 第一次会话
            chat_data1 = {
                "message": "我的默认会议时长是30分钟",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data1) as response:
                assert response.status == 200

            # 模拟会话结束，重新开始
            await asyncio.sleep(1)

            # 第二次会话 - 应该记住用户偏好
            chat_data2 = {
                "message": "明天安排会议",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data2) as response:
                assert response.status == 200
                data = await response.json()

                # 系统应该记住用户的默认设置
                # 具体验证取决于实现

    @pytest.mark.asyncio
    async def test_performance_under_load(self):
        """测试负载下的性能"""
        async with aiohttp.ClientSession() as session:
            start_time = time.time()

            # 发送多个并发请求
            async def send_request(i):
                chat_data = {
                    "message": f"测试消息 {i}",
                    "user_id": f"load_test_user_{i}",
                    "context": {"timezone": "Asia/Shanghai"},
                }

                async with session.post(
                    f"{BASE_URL}/chat/", json=chat_data
                ) as response:
                    assert response.status == 200
                    return await response.json()

            # 并发发送20个请求
            tasks = [send_request(i) for i in range(20)]
            results = await asyncio.gather(*tasks)

            end_time = time.time()
            total_time = end_time - start_time

            # 验证所有请求都成功
            assert len(results) == 20
            assert all("intent" in result for result in results)

            # 验证性能（平均每个请求不超过1秒）
            avg_time_per_request = total_time / 20
            assert avg_time_per_request < 1.0

    @pytest.mark.asyncio
    async def test_conversation_state_persistence(self):
        """测试对话状态持久化"""
        user_id = f"e2e_user_persist_{int(time.time())}"

        async with aiohttp.ClientSession() as session:
            # 开始对话
            chat_data1 = {
                "message": "明天安排会议",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data1) as response:
                assert response.status == 200

            # 提供部分信息
            chat_data2 = {
                "message": "项目讨论",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data2) as response:
                assert response.status == 200

            # 模拟应用重启或网络中断后恢复
            await asyncio.sleep(1)

            # 继续对话
            chat_data3 = {
                "message": "1小时",
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            async with session.post(f"{BASE_URL}/chat/", json=chat_data3) as response:
                assert response.status == 200
                data = await response.json()

                # 应该能够继续之前的对话上下文
                # 具体验证取决于状态持久化的实现
