"""
API性能测试
"""

import asyncio
import statistics
import time
from concurrent.futures import ThreadPoolExecutor

import pytest
from fastapi.testclient import TestClient

from main import app


class TestAPIPerformance:
    """API性能测试"""

    def setup_method(self):
        """测试前准备"""
        self.client = TestClient(app)

    def test_health_check_performance(self):
        """测试健康检查端点性能"""
        response_times = []

        # 执行多次请求测量性能
        for _ in range(50):
            start_time = time.time()
            response = self.client.get("/")
            end_time = time.time()

            assert response.status_code == 200
            response_times.append(end_time - start_time)

        # 计算性能指标
        avg_time = statistics.mean(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
        max_time = max(response_times)

        # 性能断言
        assert avg_time < 0.1  # 平均响应时间小于100ms
        assert p95_time < 0.2  # 95%的请求小于200ms
        assert max_time < 0.5  # 最大响应时间小于500ms

        print(
            f"健康检查性能: 平均={avg_time:.3f}s, P95={p95_time:.3f}s, 最大={max_time:.3f}s"
        )

    def test_chat_api_performance(self):
        """测试聊天API性能"""
        response_times = []
        test_messages = [
            "你好",
            "今天有什么安排？",
            "明天下午安排会议",
            "查看本周日程",
            "取消今天的会议",
        ]

        for message in test_messages:
            for _ in range(10):  # 每个消息测试10次
                chat_data = {
                    "message": message,
                    "user_id": f"perf_test_user_{time.time()}",
                    "context": {"timezone": "Asia/Shanghai"},
                }

                start_time = time.time()
                response = self.client.post("/chat/", json=chat_data)
                end_time = time.time()

                assert response.status_code == 200
                response_times.append(end_time - start_time)

        # 计算性能指标
        avg_time = statistics.mean(response_times)
        p95_time = statistics.quantiles(response_times, n=20)[18]
        max_time = max(response_times)

        # 聊天API性能要求相对宽松
        assert avg_time < 2.0  # 平均响应时间小于2秒
        assert p95_time < 3.0  # 95%的请求小于3秒
        assert max_time < 5.0  # 最大响应时间小于5秒

        print(
            f"聊天API性能: 平均={avg_time:.3f}s, P95={p95_time:.3f}s, 最大={max_time:.3f}s"
        )

    def test_concurrent_requests_performance(self):
        """测试并发请求性能"""

        def make_request():
            chat_data = {
                "message": "性能测试消息",
                "user_id": f"concurrent_user_{time.time()}",
                "context": {"timezone": "Asia/Shanghai"},
            }
            start_time = time.time()
            response = self.client.post("/chat/", json=chat_data)
            end_time = time.time()
            return response.status_code, end_time - start_time

        # 使用线程池执行并发请求
        with ThreadPoolExecutor(max_workers=10) as executor:
            start_time = time.time()
            futures = [executor.submit(make_request) for _ in range(50)]
            results = [future.result() for future in futures]
            end_time = time.time()

        # 验证所有请求都成功
        status_codes = [result[0] for result in results]
        response_times = [result[1] for result in results]

        assert all(status == 200 for status in status_codes)

        # 计算并发性能指标
        total_time = end_time - start_time
        throughput = len(results) / total_time  # 每秒请求数
        avg_response_time = statistics.mean(response_times)

        # 并发性能断言
        assert throughput > 5  # 每秒至少处理5个请求
        assert avg_response_time < 3.0  # 并发情况下平均响应时间小于3秒

        print(
            f"并发性能: 吞吐量={throughput:.2f}req/s, 平均响应时间={avg_response_time:.3f}s"
        )

    def test_memory_usage_under_load(self):
        """测试负载下的内存使用"""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 执行大量请求
        for i in range(100):
            chat_data = {
                "message": f"内存测试消息 {i}",
                "user_id": f"memory_test_user_{i}",
                "context": {"timezone": "Asia/Shanghai"},
            }
            response = self.client.post("/chat/", json=chat_data)
            assert response.status_code == 200

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        # 内存增长应该在合理范围内
        assert memory_increase < 100  # 内存增长不超过100MB

        print(
            f"内存使用: 初始={initial_memory:.2f}MB, 最终={final_memory:.2f}MB, 增长={memory_increase:.2f}MB"
        )

    @pytest.mark.asyncio
    async def test_async_performance(self):
        """测试异步性能"""
        import aiohttp

        async def make_async_request(session, user_id):
            chat_data = {
                "message": "异步测试消息",
                "user_id": f"async_user_{user_id}",
                "context": {"timezone": "Asia/Shanghai"},
            }

            start_time = time.time()
            async with session.post(
                "http://localhost:5000/chat/", json=chat_data
            ) as response:
                await response.json()
                end_time = time.time()
                return response.status, end_time - start_time

        # 异步并发请求
        async with aiohttp.ClientSession() as session:
            start_time = time.time()
            tasks = [make_async_request(session, i) for i in range(30)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()

        # 过滤掉异常结果
        valid_results = [r for r in results if not isinstance(r, Exception)]

        if valid_results:
            total_time = end_time - start_time
            throughput = len(valid_results) / total_time
            response_times = [result[1] for result in valid_results]
            avg_response_time = statistics.mean(response_times)

            print(
                f"异步性能: 吞吐量={throughput:.2f}req/s, 平均响应时间={avg_response_time:.3f}s"
            )

    def test_database_query_performance(self):
        """测试数据库查询性能"""
        # 模拟需要数据库查询的请求
        response_times = []

        for _ in range(20):
            chat_data = {
                "message": "今天有什么安排？",  # 需要查询日历数据
                "user_id": "db_perf_test_user",
                "context": {"timezone": "Asia/Shanghai"},
            }

            start_time = time.time()
            response = self.client.post("/chat/", json=chat_data)
            end_time = time.time()

            assert response.status_code == 200
            response_times.append(end_time - start_time)

        avg_time = statistics.mean(response_times)
        max_time = max(response_times)

        # 数据库查询性能要求
        assert avg_time < 1.5  # 平均查询时间小于1.5秒
        assert max_time < 3.0  # 最大查询时间小于3秒

        print(f"数据库查询性能: 平均={avg_time:.3f}s, 最大={max_time:.3f}s")

    def test_cache_performance(self):
        """测试缓存性能"""
        user_id = "cache_test_user"
        message = "今天有什么安排？"

        # 第一次请求（冷缓存）
        chat_data = {
            "message": message,
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        start_time = time.time()
        response1 = self.client.post("/chat/", json=chat_data)
        cold_cache_time = time.time() - start_time

        assert response1.status_code == 200

        # 第二次相同请求（热缓存）
        start_time = time.time()
        response2 = self.client.post("/chat/", json=chat_data)
        hot_cache_time = time.time() - start_time

        assert response2.status_code == 200

        # 缓存应该提高性能
        if hot_cache_time > 0:  # 避免除零错误
            speedup = cold_cache_time / hot_cache_time
            print(
                f"缓存性能: 冷缓存={cold_cache_time:.3f}s, 热缓存={hot_cache_time:.3f}s, 加速比={speedup:.2f}x"
            )

    def test_error_handling_performance(self):
        """测试错误处理性能"""
        response_times = []

        # 测试各种错误情况的处理时间
        error_cases = [
            {"message": "", "user_id": "test_user"},  # 空消息
            {"message": "测试", "user_id": ""},  # 空用户ID
            {"message": "a" * 10000, "user_id": "test_user"},  # 超长消息
        ]

        for case in error_cases:
            for _ in range(10):
                start_time = time.time()
                response = self.client.post("/chat/", json=case)
                end_time = time.time()

                # 错误情况也应该快速响应
                response_times.append(end_time - start_time)

        avg_error_time = statistics.mean(response_times)
        max_error_time = max(response_times)

        # 错误处理应该很快
        assert avg_error_time < 0.5  # 平均错误处理时间小于500ms
        assert max_error_time < 1.0  # 最大错误处理时间小于1秒

        print(f"错误处理性能: 平均={avg_error_time:.3f}s, 最大={max_error_time:.3f}s")
