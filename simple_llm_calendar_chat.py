#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版LLM+本地MCP交互工具
先实现基本的对话功能，然后逐步添加工具调用
"""

import sys
import os
import asyncio
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from core.ai.llm_client import LLMClient
from models.chat import ToolCall
from core.mcp.simple_supabase_mcp_client import SimpleSupabaseMCPClient


class SimpleLLMCalendarChat:
    """简化版LLM日历聊天工具"""
    
    def __init__(self, use_supabase_token: bool = False, user_id: str = None):
        """初始化

        Args:
            use_supabase_token: 是否使用Supabase中的token
            user_id: 用户ID（当使用Supabase token时）
        """
        self.llm_client = LLMClient()
        self.use_supabase_token = use_supabase_token
        self.user_id = user_id or "default_user"
        self.mcp_base_url = "http://localhost:3000"
        self.conversation_history = []
        self.available_tools = []
        
    async def start(self):
        """启动服务"""
        try:
            print("🚀 正在启动简化版飞书智能日历助手...")
            
            # 获取可用工具
            print("📡 获取MCP工具列表...")
            response = requests.get(f"{self.mcp_base_url}/api/tools")
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.available_tools = data.get("tools", [])
                    print(f"✅ 获取到 {len(self.available_tools)} 个MCP工具")
                else:
                    print("⚠️ 获取工具列表失败，但可以继续使用基本对话功能")
            else:
                print("⚠️ 无法连接到MCP服务，但可以继续使用基本对话功能")
            
            print("🤖 LLM客户端已就绪！")
            print("\n" + "="*60)
            print("🎉 飞书智能日历助手已启动！")
            print("💡 当前支持的功能：")
            print("   • 🗣️  自然语言对话交流")
            print("   • 🤖 智能工具调用（自动识别用户意图）")
            print("   • 📅 实时日历数据查询")
            if self.available_tools:
                print("   • 🔧 手动工具调用（输入 'tools' 查看可用工具）")
            print("\n💬 您可以直接说：")
            print("   • '查看我的日历列表'")
            print("   • '今天有什么日程'")
            print("   • '搜索包含会议的日程'")
            print("   • '创建一个明天的会议'")
            print("\n💡 输入 'help' 查看更多命令，输入 'quit' 退出")
            print("="*60)
            
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
            raise
    
    def _format_tools_for_prompt(self) -> str:
        """格式化MCP工具信息用于提示词"""
        if not self.available_tools:
            return "暂无可用工具"

        tool_descriptions = []
        for tool in self.available_tools:
            name = tool.get("name", "未知工具")
            description = tool.get("description", "无描述")

            # 获取参数信息
            input_schema = tool.get("inputSchema", {})
            properties = input_schema.get("properties", {})
            required = input_schema.get("required", [])

            # 格式化参数
            param_info = []
            for param_name, param_details in properties.items():
                param_type = param_details.get("type", "unknown")
                param_desc = param_details.get("description", "")
                is_required = param_name in required
                required_mark = " (必需)" if is_required else " (可选)"
                param_info.append(f"  - {param_name} ({param_type}){required_mark}: {param_desc}")

            params_str = "\n".join(param_info) if param_info else "  无参数"

            tool_descriptions.append(f"""
• {name}: {description}
  参数:
{params_str}""")

        return "\n".join(tool_descriptions)

    def _extract_calendar_name_from_query(self, query: str, calendar_list: list) -> str:
        """从用户查询中智能提取日历名称"""
        # 简单的包含关系匹配，让LLM来做复杂的语义理解
        for calendar in calendar_list:
            calendar_name = calendar.get("summary", "")
            if calendar_name and calendar_name in query:
                return calendar_name
        return None

    async def _perform_second_round_call(self, user_input: str, calendar_data: list, tool_results: list):
        """执行第二轮工具调用 - 查询具体日程"""
        print(f"🔍 [DEBUG] 开始分析日历数据，准备查询日程")

        # 选择主日历或用户指定的日历
        target_calendar = None

        # 1. 检查用户是否指定了特定日历
        calendar_name = self._extract_calendar_name_from_query(user_input, calendar_data)
        if calendar_name:
            print(f"🔍 [DEBUG] 用户指定日历: {calendar_name}")
            target_calendar = self._find_calendar_by_name(calendar_data, calendar_name)

        # 2. 如果没有指定，使用主日历
        if not target_calendar:
            for cal in calendar_data:
                if cal.get("type") == "primary":
                    target_calendar = cal
                    print(f"🔍 [DEBUG] 使用主日历: {cal.get('summary', '未知')}")
                    break

        # 3. 如果没有主日历，使用第一个日历
        if not target_calendar and calendar_data:
            target_calendar = calendar_data[0]
            print(f"🔍 [DEBUG] 使用第一个日历: {target_calendar.get('summary', '未知')}")

        if not target_calendar:
            print(f"🔍 [DEBUG] ❌ 没有找到可用的日历")
            return

        # 获取正确的calendar_id
        calendar_id = target_calendar.get("calendar_id")
        if not calendar_id:
            print(f"🔍 [DEBUG] ❌ 日历缺少calendar_id")
            return

        print(f"🔍 [DEBUG] 使用calendar_id: {calendar_id}")

        # 解析时间范围
        time_info = self._parse_time_from_query(user_input)
        start_time = time_info["start_time"]
        end_time = time_info["end_time"]
        time_desc = time_info["description"]

        print(f"🔍 [DEBUG] 时间范围: {time_desc} ({start_time} - {end_time})")

        # 调用日程查询工具
        print(f"📅 查询日历 {target_calendar.get('summary', '未知')} 的{time_desc}日程...")

        event_result = await self.call_mcp_tool(
            "calendar.v4.calendarEvent.list",
            {
                "calendar_id": calendar_id,
                "start_time": start_time,
                "end_time": end_time,
                "page_size": 100
            }
        )

        # 添加到结果列表
        tool_results.append({
            "tool_name": "calendar.v4.calendarEvent.list",
            "result": event_result
        })

        print(f"🔍 [DEBUG] 第二轮工具调用完成，成功状态: {event_result.get('success', False)}")

    def _find_calendar_by_name(self, calendar_list: list, target_name: str) -> dict:
        """在日历列表中查找指定名称的日历"""
        if not target_name:
            return None

        # 精确匹配
        for calendar in calendar_list:
            if calendar.get("summary") == target_name:
                return calendar

        # 模糊匹配
        for calendar in calendar_list:
            summary = calendar.get("summary", "")
            if target_name in summary or summary in target_name:
                return calendar

        return None

    def _parse_time_from_query(self, query: str) -> dict:
        """从用户查询中解析时间范围"""
        from datetime import datetime, timedelta

        # 获取当前时间（假设是2025年7月20日）
        current_date = datetime(2025, 7, 20)

        if "今天" in query:
            start_date = current_date
            description = "今天"
        elif "明天" in query or "2025年7月21日" in query:
            start_date = current_date + timedelta(days=1)
            description = "明天"
        elif "后天" in query:
            start_date = current_date + timedelta(days=2)
            description = "后天"
        elif "昨天" in query:
            start_date = current_date - timedelta(days=1)
            description = "昨天"
        elif "前天" in query:
            start_date = current_date - timedelta(days=2)
            description = "前天"
        elif "下周" in query:
            start_date = current_date + timedelta(days=7)
            description = "下周"
        else:
            # 默认查询今天
            start_date = current_date
            description = "今天"

        # 计算时间戳
        start_timestamp = int(start_date.timestamp())
        end_timestamp = int((start_date + timedelta(days=1)).timestamp())

        return {
            "start_time": str(start_timestamp),
            "end_time": str(end_timestamp),
            "description": description
        }

    async def call_mcp_tool(self, tool_name: str, arguments: dict = None) -> dict:
        """调用MCP工具"""
        try:
            if arguments is None:
                arguments = {}

            print(f"🔍 [MCP DEBUG] 准备调用工具: {tool_name}")
            print(f"🔍 [MCP DEBUG] 参数: {arguments}")

            # 准备请求头
            headers = {"Content-Type": "application/json"}

            # 如果使用Supabase token，添加token到请求头
            if self.use_supabase_token:
                print(f"🔍 [MCP DEBUG] 使用Supabase Token模式，用户: {self.user_id}")

                # 直接使用项目现有的token获取函数
                from integrations.storage import get_token, is_token_expired
                import time
                from datetime import datetime

                token_data = get_token(self.user_id)

                if token_data:
                    # 检查token是否过期
                    expire_time = token_data.get('access_token_expire', 0)
                    current_time = int(time.time())

                    print(f"🔍 [MCP DEBUG] Token过期时间: {datetime.fromtimestamp(expire_time)}")
                    print(f"🔍 [MCP DEBUG] 当前时间: {datetime.fromtimestamp(current_time)}")
                    print(f"🔍 [MCP DEBUG] 剩余时间: {expire_time - current_time} 秒")

                    if expire_time <= current_time:
                        print(f"🔍 [MCP DEBUG] ⚠️ Token已过期，尝试刷新...")
                        # 这里可以触发刷新，但为了简单起见，先返回错误
                        return {"error": f"Token已过期，需要刷新"}

                    access_token = token_data['access_token']
                    headers["Authorization"] = f"Bearer {access_token}"
                    print(f"🔍 [MCP DEBUG] 已添加token到请求头，token前缀: {access_token[:20]}...")
                else:
                    print(f"🔍 [MCP DEBUG] ⚠️ 用户 {self.user_id} 没有可用的token")
                    return {"error": f"用户 {self.user_id} 没有可用的token"}

            url = f"{self.mcp_base_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }

            print(f"🔍 [MCP DEBUG] 请求URL: {url}")
            print(f"🔍 [MCP DEBUG] 请求载荷: {payload}")

            response = requests.post(url, json=payload, headers=headers, timeout=30)

            print(f"🔍 [MCP DEBUG] 响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                print(f"🔍 [MCP DEBUG] 响应内容: {result}")
                return result
            else:
                error_text = response.text
                print(f"🔍 [MCP DEBUG] 错误响应: {error_text}")
                return {"error": f"API调用失败: {response.status_code}, 响应: {error_text}"}

        except Exception as e:
            print(f"🔍 [MCP DEBUG] 异常: {str(e)}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    def _prepare_tools_for_llm(self) -> list:
        """准备工具列表供LLM使用"""
        if not self.available_tools:
            return []

        # 核心工具列表 - 与MCP服务的核心工具集保持一致
        core_tools = [
            # 📅 日历管理
            "calendar.v4.calendar.list",
            "calendar.v4.calendar.get",

            # 📝 日程管理 (核心功能)
            "calendar.v4.calendarEvent.list",
            "calendar.v4.calendarEvent.instanceView",  # 重要：查看日程视图(含重复日程展开)
            "calendar.v4.calendarEvent.get",
            "calendar.v4.calendarEvent.create",
            "calendar.v4.calendarEvent.patch",
            "calendar.v4.calendarEvent.delete",
            "calendar.v4.calendarEvent.search"
        ]

        tools = []
        for tool in self.available_tools:
            if tool["name"] in core_tools:
                # 注意：LLM客户端的_prepare_tools方法会再次包装，所以这里只返回function部分
                tools.append({
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool.get("inputSchema", {})
                })

        return tools

    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            print(f"🔍 [DEBUG] 用户输入: '{user_input}'")
            print(f"🔍 [DEBUG] 对话历史长度: {len(self.conversation_history)}")

            # 添加用户消息到对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_input
            })

            # 准备系统提示
            # 获取当前时间信息
            from datetime import datetime, timedelta

            # 假设当前时间为2025年7月20日（根据之前的测试）
            current_time = datetime(2025, 7, 20, 12, 0, 0)  # 2025年7月20日 12:00:00

            # 计算相关日期
            yesterday = current_time - timedelta(days=1)
            tomorrow = current_time + timedelta(days=1)
            day_after_tomorrow = current_time + timedelta(days=2)

            # 格式化时间信息
            current_date_str = current_time.strftime('%Y年%m月%d日')
            current_datetime_str = current_time.strftime('%Y年%m月%d日 %H:%M:%S')
            current_weekday = current_time.strftime('%A')

            # 中文星期映射
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            current_weekday_cn = weekday_map.get(current_weekday, current_weekday)



            system_prompt = {
                "role": "system",
                "content": f"""你是一个飞书日历智能助手。你可以帮助用户管理飞书日历和日程。

当前系统时间信息：
- 今天：{current_date_str} ({current_weekday_cn})
- 当前时间：{current_datetime_str} (UTC+8 亚洲/上海时区)

基于当前时间，你可以准确推断：
- 昨天：{yesterday.strftime('%Y年%m月%d日')}
- 明天：{tomorrow.strftime('%Y年%m月%d日')}
- 后天：{day_after_tomorrow.strftime('%Y年%m月%d日')}

你有权限调用以下MCP工具来帮助用户：

{self._format_tools_for_prompt()}

核心指导原则：

1. **准确理解用户意图**：
   - 仔细分析用户的自然语言表达
   - 区分是要查看日历分类还是具体的时间安排
   - 识别用户指定的时间范围（今天、明天、后天等）
   - 识别用户指定的日历名称
   - 日历和日程是两个概念：一个日历下有多个日程，一个日程只能属于一个日历；

2. **工具调用策略**：
   - **查看日历列表**：用户说"查看我的日历列表"、"有哪些日历"等 → 只调用calendar.v4.calendar.list
   - **查询日程安排**：用户说"明天有什么日程"、"今天的安排"等 → 需要两步：
     1. 先调用calendar.v4.calendar.list获取日历列表，获取到每个日历的日历ID
     2. 再根据每一个日历的日历ID和时间范围，调用calendar.v4.calendarEvent.list查询具体日程列表，N个日历就调用N次
   - **指定日历查询**：用户说"小宝暑假计划中明天有什么" → 同样需要两步，但要选择指定的日历

3. **参数设置**：
   - 获取日历列表：使用page_size=1000确保获取所有日历
   - 查询日程：
     * calendar_id必须使用从日历列表中获取的真实ID（如"<EMAIL>"）
     * 不能使用日历名称（如"小宝暑假计划"）作为calendar_id
     * start_time和end_time使用Unix时间戳格式

4. **多步骤工具调用示例**：
   用户："明天有什么日程"
   第1步：调用calendar.v4.calendar.list获取日历列表
   第2步：从结果中提取calendar_id（如"<EMAIL>"）
   第3步：调用calendar.v4.calendarEvent.list，使用正确的calendar_id和时间戳

⚠️ 重要：calendar_id必须是完整的ID字符串，不能是日历名称！

请根据用户的具体需求智能选择合适的工具组合。"""
            }

            # 准备消息列表（保留最近10轮对话）
            messages = [system_prompt] + self.conversation_history[-10:]

            # 准备工具列表
            tools = self._prepare_tools_for_llm()

            # 调用LLM
            print("🤔 正在思考...")
            print(f"🔍 [DEBUG] 系统提示词长度: {len(system_prompt['content'])} 字符")
            print(f"🔍 [DEBUG] 可用工具数量: {len(tools)}")
            print(f"🔍 [DEBUG] 消息数量: {len(messages)}")

            response = self.llm_client.invoke(messages, tools)

            print(f"🔍 [DEBUG] LLM响应内容: '{response.content}'")
            print(f"🔍 [DEBUG] 工具调用数量: {len(response.tool_calls) if response.tool_calls else 0}")

            # 处理响应 - 智能多步骤工具调用
            if not response.tool_calls:
                # 没有工具调用，直接回复
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response.content
                })
                return response.content

            # 执行第一轮工具调用
            print(f"🔧 LLM决定调用 {len(response.tool_calls)} 个工具")

            tool_results = []
            for tool_call in response.tool_calls:
                print(f"📞 调用工具: {tool_call.name}")
                print(f"📝 参数: {tool_call.args}")

                result = await self.call_mcp_tool(
                    tool_call.name,
                    tool_call.args
                )
                tool_results.append({
                    "tool_name": tool_call.name,
                    "result": result
                })

            # 智能多轮工具调用
            print("🤖 正在分析结果...")
            print(f"🔍 [DEBUG] 工具调用结果数量: {len(tool_results)}")

            # 检查是否需要进行第二轮工具调用
            needs_second_round = False
            calendar_data = None

            for i, tool_result in enumerate(tool_results):
                print(f"🔍 [DEBUG] 工具{i+1}: {tool_result['tool_name']}")
                print(f"🔍 [DEBUG] 成功状态: {tool_result['result'].get('success', False)}")

                # 分析用户意图
                if tool_result['tool_name'] == 'calendar.v4.calendar.list':
                    print(f"🔍 [DEBUG] 用户查询包含时间词汇分析:")
                    time_keywords = ['明天', '今天', '昨天', '后天', '日程', '安排', '事件']
                    found_keywords = [kw for kw in time_keywords if kw in user_input]
                    print(f"🔍 [DEBUG] 发现的时间关键词: {found_keywords}")

                    if found_keywords:
                        print(f"🔍 [DEBUG] ✅ 检测到需要查询具体日程，准备第二轮工具调用")
                        needs_second_round = True

                        # 提取日历数据
                        result = tool_result['result']
                        if result.get("success"):
                            result_data = result.get("result", {})
                            content_text = result_data.get("content", [{}])[0].get("text", "")
                            if content_text:
                                try:
                                    parsed_data = json.loads(content_text)
                                    if parsed_data.get("code") == 0:
                                        calendar_data = parsed_data.get("data", {}).get("calendar_list", [])
                                        print(f"🔍 [DEBUG] 成功提取到 {len(calendar_data)} 个日历")
                                except Exception as e:
                                    print(f"🔍 [DEBUG] 解析日历数据失败: {str(e)}")
                    else:
                        print(f"🔍 [DEBUG] ✅ 用户只想查看日历列表，无需查询日程")

            # 执行第二轮工具调用
            if needs_second_round and calendar_data:
                print("🔄 开始第二轮工具调用...")
                await self._perform_second_round_call(user_input, calendar_data, tool_results)

            # 生成最终回复
            print("🤖 正在整理最终结果...")

            final_content_parts = []
            for tool_result in tool_results:
                tool_name = tool_result["tool_name"]
                result = tool_result["result"]

                if result.get("success") and not result.get("result", {}).get("isError", True):
                    # 工具调用成功
                    result_data = result.get("result", {})
                    content_text = result_data.get("content", [{}])[0].get("text", "")
                    if content_text:
                        try:
                            parsed_data = json.loads(content_text)
                            if parsed_data.get("code") == 0:
                                # 飞书API调用成功
                                if tool_name == "calendar.v4.calendar.list":
                                    calendars = parsed_data.get("data", {}).get("calendar_list", [])
                                    if calendars:
                                        calendar_info = []
                                        for cal in calendars:
                                            cal_name = cal.get("summary", "未命名日历")
                                            cal_type = cal.get("type", "unknown")
                                            cal_desc = cal.get("description", "")
                                            type_emoji = "👑" if cal_type == "primary" else "📅"
                                            calendar_info.append(f"{type_emoji} {cal_name}" + (f" - {cal_desc}" if cal_desc else ""))

                                        final_content_parts.append(f"📋 您的日历列表 (共{len(calendars)}个)：\n\n" + "\n".join(calendar_info))
                                    else:
                                        final_content_parts.append(f"📋 日历列表为空")
                                elif tool_name == "calendar.v4.calendarEvent.list":
                                    events = parsed_data.get("data", {}).get("items", [])
                                    # 从用户查询中获取时间描述
                                    time_info = self._parse_time_from_query(user_input.lower())
                                    time_desc = time_info["description"]
                                    if events:
                                        final_content_parts.append(f"📅 {time_desc}共有 {len(events)} 个日程：\n\n```json\n{json.dumps(parsed_data.get('data', {}), ensure_ascii=False, indent=2)}\n```")
                                    else:
                                        final_content_parts.append(f"📅 {time_desc}没有安排任何日程。")
                                else:
                                    final_content_parts.append(f"✅ {tool_name} 调用成功：\n\n```json\n{json.dumps(parsed_data.get('data', {}), ensure_ascii=False, indent=2)}\n```")
                            else:
                                final_content_parts.append(f"❌ {tool_name} API调用失败：{parsed_data.get('msg', '未知错误')}")
                        except:
                            final_content_parts.append(f"✅ {tool_name} 调用完成，原始结果：\n{content_text[:500]}...")
                else:
                    final_content_parts.append(f"❌ {tool_name} 调用失败")

            final_content = "\n\n".join(final_content_parts) if final_content_parts else "✅ 工具调用完成。"

            # 添加最终回复到对话历史
            self.conversation_history.append({
                "role": "assistant",
                "content": final_content
            })

            return final_content

        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            print(f"❌ {error_msg}")
            return f"抱歉，我遇到了一些技术问题：{error_msg}"
    
    def show_available_tools(self):
        """显示可用工具"""
        if not self.available_tools:
            print("❌ 暂无可用工具")
            return
        
        print(f"\n🔧 可用工具列表（共 {len(self.available_tools)} 个）：")
        print("-" * 60)
        
        # 按类别分组显示
        categories = {}
        for tool in self.available_tools:
            category = tool.get("category", "其他")
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)
        
        for category, tools in categories.items():
            print(f"\n📂 {category}:")
            for tool in tools:
                print(f"   • {tool['name']}: {tool['description']}")
        
        print("\n💡 使用方法: call <工具名> [参数JSON]")
        print("📝 示例: call calendar.v4.calendar.list {}")
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🆘 简化版飞书智能日历助手 - 帮助信息

📋 基本命令：
  help          - 显示此帮助信息
  quit/exit/q   - 退出程序
  clear         - 清屏
  history       - 显示对话历史
  tools         - 显示可用的MCP工具
  call <工具名> [参数] - 手动调用MCP工具

🗓️ 对话示例：
  "飞书日历有哪些功能？"
  "如何创建一个重复的会议？"
  "怎么邀请其他人参加会议？"
  "如何设置会议提醒？"

🔧 工具调用示例：
  call calendar.v4.calendar.list {}
  call calendar.v4.calendarEvent.list {"calendar_id": "xxx"}

💡 使用技巧：
  • 可以用自然语言询问日历相关问题
  • 使用 'tools' 命令查看所有可用工具
  • 使用 'call' 命令手动调用特定工具
        """
        print(help_text)


def get_available_user_id():
    """自动获取Supabase中可用的用户ID"""
    try:
        from integrations.storage import token_storage
        import os

        if token_storage.storage_type == "supabase":
            app_id = os.environ.get('FEISHU_CLIENT_ID', 'default_app_id')
            response = token_storage.supabase.from_(token_storage.table_name).select('tenant_key').eq('app_id', app_id).execute()
            data = response.data if hasattr(response, 'data') else []

            if data and len(data) > 0:
                return data[0].get('tenant_key')

        return None
    except Exception as e:
        print(f"⚠️ 获取用户ID失败: {e}")
        return None

async def main():
    """主函数"""
    import sys

    # 检查命令行参数
    use_supabase_token = "--supabase-token" in sys.argv
    user_id = None

    # 获取用户ID参数
    for i, arg in enumerate(sys.argv):
        if arg == "--user-id" and i + 1 < len(sys.argv):
            user_id = sys.argv[i + 1]
            break

    if use_supabase_token:
        print("🔧 使用Supabase Token模式")

        # 如果没有指定用户ID，自动获取
        if not user_id:
            user_id = get_available_user_id()
            if user_id:
                print(f"👤 自动获取到用户ID: {user_id}")
            else:
                print("❌ 无法获取用户ID，请手动指定")
                return 1
        else:
            print(f"👤 使用指定的用户ID: {user_id}")

    chat = SimpleLLMCalendarChat(use_supabase_token=use_supabase_token, user_id=user_id)
    
    try:
        # 启动服务
        await chat.start()
        
        # 主循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ["quit", "exit", "q"]:
                    print("👋 感谢使用飞书智能日历助手，再见！")
                    break
                elif user_input.lower() == "help":
                    chat.print_help()
                    continue
                elif user_input.lower() == "clear":
                    os.system("cls" if os.name == "nt" else "clear")
                    continue
                elif user_input.lower() == "history":
                    print("\n📜 对话历史：")
                    for i, msg in enumerate(chat.conversation_history[-10:], 1):
                        role = "👤 用户" if msg["role"] == "user" else "🤖 助手"
                        content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                        print(f"{i}. {role}: {content}")
                    continue
                elif user_input.lower() == "tools":
                    chat.show_available_tools()
                    continue
                elif user_input.lower().startswith("call "):
                    # 手动工具调用
                    parts = user_input[5:].strip().split(" ", 1)
                    tool_name = parts[0]
                    
                    if len(parts) > 1:
                        try:
                            arguments = json.loads(parts[1])
                        except json.JSONDecodeError:
                            print("❌ 参数格式错误，请使用JSON格式")
                            continue
                    else:
                        arguments = {}
                    
                    print(f"🔧 调用工具: {tool_name}")
                    result = await chat.call_mcp_tool(tool_name, arguments)
                    print(f"✅ 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    continue
                
                # 处理普通对话
                response = await chat.process_message(user_input)
                print(f"\n🤖 助手: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 出现错误: {str(e)}")
                print("💡 请重试或输入 'help' 查看帮助")
    
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        sys.exit(1)
