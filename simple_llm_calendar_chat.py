#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版LLM+本地MCP交互工具
先实现基本的对话功能，然后逐步添加工具调用
"""

import sys
import os
import asyncio
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from core.ai.llm_client import LLMClient
from models.chat import ToolCall


class SimpleLLMCalendarChat:
    """简化版LLM日历聊天工具"""
    
    def __init__(self):
        """初始化"""
        self.llm_client = LLMClient()
        self.mcp_base_url = "http://localhost:3000"
        self.conversation_history = []
        self.available_tools = []
        
    async def start(self):
        """启动服务"""
        try:
            print("🚀 正在启动简化版飞书智能日历助手...")
            
            # 获取可用工具
            print("📡 获取MCP工具列表...")
            response = requests.get(f"{self.mcp_base_url}/api/tools")
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.available_tools = data.get("tools", [])
                    print(f"✅ 获取到 {len(self.available_tools)} 个MCP工具")
                else:
                    print("⚠️ 获取工具列表失败，但可以继续使用基本对话功能")
            else:
                print("⚠️ 无法连接到MCP服务，但可以继续使用基本对话功能")
            
            print("🤖 LLM客户端已就绪！")
            print("\n" + "="*60)
            print("🎉 飞书智能日历助手已启动！")
            print("💡 当前支持的功能：")
            print("   • 🗣️  自然语言对话交流")
            print("   • 🤖 智能工具调用（自动识别用户意图）")
            print("   • 📅 实时日历数据查询")
            if self.available_tools:
                print("   • 🔧 手动工具调用（输入 'tools' 查看可用工具）")
            print("\n💬 您可以直接说：")
            print("   • '查看我的日历列表'")
            print("   • '今天有什么日程'")
            print("   • '搜索包含会议的日程'")
            print("   • '创建一个明天的会议'")
            print("\n💡 输入 'help' 查看更多命令，输入 'quit' 退出")
            print("="*60)
            
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
            raise
    
    def _extract_calendar_name_from_query(self, query: str) -> str:
        """从用户查询中提取日历名称"""
        # 常见的日历名称提取模式
        patterns = [
            r"日历(.+?)中",
            r"(.+?)日历中",
            r"(.+?)计划中",
            r"(.+?)中.*日程",
            r"查看(.+?)的",
            r"(.+?)有.*日程"
        ]

        import re
        for pattern in patterns:
            match = re.search(pattern, query)
            if match:
                calendar_name = match.group(1).strip()
                # 过滤掉一些无意义的词
                if calendar_name and calendar_name not in ["我", "的", "今天", "明天", "昨天"]:
                    return calendar_name

        return None

    def _find_calendar_by_name(self, calendar_list: list, target_name: str) -> dict:
        """在日历列表中查找指定名称的日历"""
        if not target_name:
            return None

        # 精确匹配
        for calendar in calendar_list:
            if calendar.get("summary") == target_name:
                return calendar

        # 模糊匹配
        for calendar in calendar_list:
            summary = calendar.get("summary", "")
            if target_name in summary or summary in target_name:
                return calendar

        return None

    def _parse_time_from_query(self, query: str) -> dict:
        """从用户查询中解析时间范围"""
        from datetime import datetime, timedelta

        # 获取当前时间（假设是2025年7月20日）
        current_date = datetime(2025, 7, 20)

        if "今天" in query:
            start_date = current_date
            description = "今天"
        elif "明天" in query or "2025年7月21日" in query:
            start_date = current_date + timedelta(days=1)
            description = "明天"
        elif "后天" in query:
            start_date = current_date + timedelta(days=2)
            description = "后天"
        elif "昨天" in query:
            start_date = current_date - timedelta(days=1)
            description = "昨天"
        elif "前天" in query:
            start_date = current_date - timedelta(days=2)
            description = "前天"
        elif "下周" in query:
            start_date = current_date + timedelta(days=7)
            description = "下周"
        else:
            # 默认查询今天
            start_date = current_date
            description = "今天"

        # 计算时间戳
        start_timestamp = int(start_date.timestamp())
        end_timestamp = int((start_date + timedelta(days=1)).timestamp())

        return {
            "start_time": str(start_timestamp),
            "end_time": str(end_timestamp),
            "description": description
        }

    async def call_mcp_tool(self, tool_name: str, arguments: dict = None) -> dict:
        """调用MCP工具"""
        try:
            if arguments is None:
                arguments = {}
                
            url = f"{self.mcp_base_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API调用失败: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"工具调用失败: {str(e)}"}
    
    def _prepare_tools_for_llm(self) -> list:
        """准备工具列表供LLM使用"""
        if not self.available_tools:
            return []

        # 只选择最常用的工具，避免工具列表过长
        common_tools = [
            "calendar.v4.calendar.list",
            "calendar.v4.calendar.primary",
            "calendar.v4.calendarEvent.list",
            "calendar.v4.calendarEvent.create",
            "calendar.v4.calendarEvent.search",
            "calendar.v4.calendarEvent.get",
            "calendar.v4.calendarEvent.delete",
            "calendar.v4.calendar.create",
            "calendar.v4.freebusy.list"
        ]

        tools = []
        for tool in self.available_tools:
            if tool["name"] in common_tools:
                # 注意：LLM客户端的_prepare_tools方法会再次包装，所以这里只返回function部分
                tools.append({
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool.get("inputSchema", {})
                })

        return tools

    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 添加用户消息到对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_input
            })

            # 准备系统提示
            # 获取当前时间信息
            from datetime import datetime, timedelta

            # 假设当前时间为2025年7月20日（根据之前的测试）
            current_time = datetime(2025, 7, 20, 12, 0, 0)  # 2025年7月20日 12:00:00

            # 计算相关日期
            yesterday = current_time - timedelta(days=1)
            tomorrow = current_time + timedelta(days=1)
            day_after_tomorrow = current_time + timedelta(days=2)

            # 格式化时间信息
            current_date_str = current_time.strftime('%Y年%m月%d日')
            current_datetime_str = current_time.strftime('%Y年%m月%d日 %H:%M:%S')
            current_weekday = current_time.strftime('%A')

            # 中文星期映射
            weekday_map = {
                'Monday': '星期一', 'Tuesday': '星期二', 'Wednesday': '星期三',
                'Thursday': '星期四', 'Friday': '星期五', 'Saturday': '星期六', 'Sunday': '星期日'
            }
            current_weekday_cn = weekday_map.get(current_weekday, current_weekday)

            # 计算时间戳（00:00:00开始）
            today_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            yesterday_start = yesterday.replace(hour=0, minute=0, second=0, microsecond=0)
            tomorrow_start = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
            day_after_tomorrow_start = day_after_tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)

            current_timestamp = int(current_time.timestamp())
            today_timestamp = int(today_start.timestamp())
            yesterday_timestamp = int(yesterday_start.timestamp())
            tomorrow_timestamp = int(tomorrow_start.timestamp())
            day_after_tomorrow_timestamp = int(day_after_tomorrow_start.timestamp())

            system_prompt = {
                "role": "system",
                "content": f"""你是一个飞书日历智能助手。你可以帮助用户管理飞书日历和日程。

当前系统时间信息：
- 今天日期：{current_date_str} ({current_weekday_cn})
- 当前时间：{current_datetime_str} (UTC+8 亚洲/上海时区)
- 当前时间戳：{current_timestamp}

基于当前时间，你可以准确计算：
- 昨天：{yesterday.strftime('%Y年%m月%d日')} (时间戳: {yesterday_timestamp})
- 明天：{tomorrow.strftime('%Y年%m月%d日')} (时间戳: {tomorrow_timestamp})
- 后天：{day_after_tomorrow.strftime('%Y年%m月%d日')} (时间戳: {day_after_tomorrow_timestamp})

可用的工具包括：
- calendar.v4.calendar.list: 获取用户的日历列表
- calendar.v4.calendar.primary: 获取主日历信息
- calendar.v4.calendarEvent.list: 获取日历事件列表（需要正确的calendar_id）
- calendar.v4.calendarEvent.create: 创建新的日历事件
- calendar.v4.calendarEvent.search: 搜索日历事件
- calendar.v4.calendarEvent.get: 获取单个事件详情
- calendar.v4.calendarEvent.delete: 删除日程
- calendar.v4.calendar.create: 创建新日历
- calendar.v4.freebusy.list: 查询忙闲信息

重要的调用规则：
1. 当用户询问具体日程时（如"明天有什么日程"、"今天的安排"），你必须：
   - 首先调用 calendar.v4.calendar.list 获取所有日历列表
   - 然后从日历列表中选择主日历（type为"primary"）或用户指定的日历
   - 最后调用 calendar.v4.calendarEvent.list 查询具体日程

2. calendar_id必须使用正确的格式，例如：
   - "<EMAIL>" (主日历)
   - "<EMAIL>" (小宝暑假计划)

3. 时间参数格式：
   - start_time 和 end_time 使用Unix时间戳（秒）
   - 基于当前时间{current_timestamp}来计算相对时间
   - 今天00:00 = {today_timestamp}
   - 明天00:00 = {tomorrow_timestamp}
   - 后天00:00 = {day_after_tomorrow_timestamp}

4. 如果用户指定了具体日历名称，先从日历列表中找到对应的calendar_id

5. 时间推断示例：
   - "今天" = {current_date_str} (时间戳: {today_timestamp})
   - "明天" = {tomorrow.strftime('%Y年%m月%d日')} (时间戳: {tomorrow_timestamp})
   - "昨天" = {yesterday.strftime('%Y年%m月%d日')} (时间戳: {yesterday_timestamp})
   - "后天" = {day_after_tomorrow.strftime('%Y年%m月%d日')} (时间戳: {day_after_tomorrow_timestamp})
   - "下周一" = 根据当前{current_weekday_cn}计算下周一的日期

示例调用流程：
用户："明天有什么日程"
-> 先调用 calendar.v4.calendar.list
-> 找到主日历的calendar_id
-> 再调用 calendar.v4.calendarEvent.list 查询明天({tomorrow.strftime('%Y年%m月%d日')})的事件

请严格按照这个流程执行，确保使用正确的calendar_id和时间戳。"""
            }

            # 准备消息列表（保留最近10轮对话）
            messages = [system_prompt] + self.conversation_history[-10:]

            # 准备工具列表
            tools = self._prepare_tools_for_llm()

            # 调用LLM
            print("🤔 正在思考...")
            response = self.llm_client.invoke(messages, tools)

            # 处理响应 - 智能多步骤工具调用
            if not response.tool_calls:
                # 没有工具调用，直接回复
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response.content
                })
                return response.content

            # 执行第一轮工具调用
            print(f"🔧 LLM决定调用 {len(response.tool_calls)} 个工具")

            tool_results = []
            for tool_call in response.tool_calls:
                print(f"📞 调用工具: {tool_call.name}")
                print(f"📝 参数: {tool_call.args}")

                result = await self.call_mcp_tool(
                    tool_call.name,
                    tool_call.args
                )
                tool_results.append({
                    "tool_name": tool_call.name,
                    "result": result
                })

            # 智能判断是否需要继续调用工具
            print("🤖 正在分析结果...")

            # 检查是否是查询日历列表，如果是且用户询问具体日程，则继续查询事件
            first_tool = response.tool_calls[0].name
            user_query = user_input.lower()

            if (first_tool == "calendar.v4.calendar.list" and
                any(keyword in user_query for keyword in ["日程", "安排", "事件", "会议", "明天", "今天", "昨天"])):

                print("🔍 检测到需要查询具体日程，继续调用事件查询工具...")

                # 从日历列表结果中提取主日历ID
                calendar_result = tool_results[0]["result"]
                if calendar_result.get("success"):
                    result_data = calendar_result.get("result", {})
                    if not result_data.get("isError", True):
                        content_text = result_data.get("content", [{}])[0].get("text", "")
                        try:
                            parsed_data = json.loads(content_text)
                            if parsed_data.get("code") == 0:
                                calendars = parsed_data.get("data", {}).get("calendar_list", [])

                                # 智能选择日历
                                target_calendar = None

                                # 1. 首先尝试从用户查询中提取日历名称
                                calendar_name = self._extract_calendar_name_from_query(user_input)
                                if calendar_name:
                                    print(f"🔍 检测到指定日历: {calendar_name}")
                                    target_calendar = self._find_calendar_by_name(calendars, calendar_name)
                                    if target_calendar:
                                        print(f"✅ 找到匹配的日历: {target_calendar['summary']}")
                                    else:
                                        print(f"⚠️ 未找到名为'{calendar_name}'的日历，将使用主日历")

                                # 2. 如果没有指定日历或找不到，使用主日历
                                if not target_calendar:
                                    for cal in calendars:
                                        if cal.get("type") == "primary":
                                            target_calendar = cal
                                            break

                                # 3. 如果没有主日历，使用第一个日历
                                if not target_calendar and calendars:
                                    target_calendar = calendars[0]

                                if target_calendar:
                                    calendar_id = target_calendar["calendar_id"]

                                    # 智能识别时间范围
                                    time_info = self._parse_time_from_query(user_query)
                                    start_time = time_info["start_time"]
                                    end_time = time_info["end_time"]
                                    time_desc = time_info["description"]

                                    print(f"📅 查询日历 {target_calendar['summary']} 的{time_desc}日程...")

                                    # 调用事件查询工具
                                    event_result = await self.call_mcp_tool(
                                        "calendar.v4.calendarEvent.list",
                                        {
                                            "calendar_id": calendar_id,
                                            "start_time": start_time,
                                            "end_time": end_time,
                                            "page_size": 100
                                        }
                                    )

                                    tool_results.append({
                                        "tool_name": "calendar.v4.calendarEvent.list",
                                        "result": event_result
                                    })
                        except Exception as e:
                            print(f"⚠️ 解析日历列表失败: {str(e)}")

            # 生成最终回复
            print("🤖 正在整理最终结果...")

            final_content_parts = []
            for tool_result in tool_results:
                tool_name = tool_result["tool_name"]
                result = tool_result["result"]

                if result.get("success") and not result.get("result", {}).get("isError", True):
                    # 工具调用成功
                    result_data = result.get("result", {})
                    content_text = result_data.get("content", [{}])[0].get("text", "")
                    if content_text:
                        try:
                            parsed_data = json.loads(content_text)
                            if parsed_data.get("code") == 0:
                                # 飞书API调用成功
                                if tool_name == "calendar.v4.calendar.list":
                                    final_content_parts.append(f"📋 日历列表获取成功")
                                elif tool_name == "calendar.v4.calendarEvent.list":
                                    events = parsed_data.get("data", {}).get("items", [])
                                    # 从用户查询中获取时间描述
                                    time_info = self._parse_time_from_query(user_input.lower())
                                    time_desc = time_info["description"]
                                    if events:
                                        final_content_parts.append(f"📅 {time_desc}共有 {len(events)} 个日程：\n\n```json\n{json.dumps(parsed_data.get('data', {}), ensure_ascii=False, indent=2)}\n```")
                                    else:
                                        final_content_parts.append(f"📅 {time_desc}没有安排任何日程。")
                                else:
                                    final_content_parts.append(f"✅ {tool_name} 调用成功：\n\n```json\n{json.dumps(parsed_data.get('data', {}), ensure_ascii=False, indent=2)}\n```")
                            else:
                                final_content_parts.append(f"❌ {tool_name} API调用失败：{parsed_data.get('msg', '未知错误')}")
                        except:
                            final_content_parts.append(f"✅ {tool_name} 调用完成，原始结果：\n{content_text[:500]}...")
                else:
                    final_content_parts.append(f"❌ {tool_name} 调用失败")

            final_content = "\n\n".join(final_content_parts) if final_content_parts else "✅ 工具调用完成。"

            # 添加最终回复到对话历史
            self.conversation_history.append({
                "role": "assistant",
                "content": final_content
            })

            return final_content

        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            print(f"❌ {error_msg}")
            return f"抱歉，我遇到了一些技术问题：{error_msg}"
    
    def show_available_tools(self):
        """显示可用工具"""
        if not self.available_tools:
            print("❌ 暂无可用工具")
            return
        
        print(f"\n🔧 可用工具列表（共 {len(self.available_tools)} 个）：")
        print("-" * 60)
        
        # 按类别分组显示
        categories = {}
        for tool in self.available_tools:
            category = tool.get("category", "其他")
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)
        
        for category, tools in categories.items():
            print(f"\n📂 {category}:")
            for tool in tools:
                print(f"   • {tool['name']}: {tool['description']}")
        
        print("\n💡 使用方法: call <工具名> [参数JSON]")
        print("📝 示例: call calendar.v4.calendar.list {}")
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🆘 简化版飞书智能日历助手 - 帮助信息

📋 基本命令：
  help          - 显示此帮助信息
  quit/exit/q   - 退出程序
  clear         - 清屏
  history       - 显示对话历史
  tools         - 显示可用的MCP工具
  call <工具名> [参数] - 手动调用MCP工具

🗓️ 对话示例：
  "飞书日历有哪些功能？"
  "如何创建一个重复的会议？"
  "怎么邀请其他人参加会议？"
  "如何设置会议提醒？"

🔧 工具调用示例：
  call calendar.v4.calendar.list {}
  call calendar.v4.calendarEvent.list {"calendar_id": "xxx"}

💡 使用技巧：
  • 可以用自然语言询问日历相关问题
  • 使用 'tools' 命令查看所有可用工具
  • 使用 'call' 命令手动调用特定工具
        """
        print(help_text)


async def main():
    """主函数"""
    chat = SimpleLLMCalendarChat()
    
    try:
        # 启动服务
        await chat.start()
        
        # 主循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ["quit", "exit", "q"]:
                    print("👋 感谢使用飞书智能日历助手，再见！")
                    break
                elif user_input.lower() == "help":
                    chat.print_help()
                    continue
                elif user_input.lower() == "clear":
                    os.system("cls" if os.name == "nt" else "clear")
                    continue
                elif user_input.lower() == "history":
                    print("\n📜 对话历史：")
                    for i, msg in enumerate(chat.conversation_history[-10:], 1):
                        role = "👤 用户" if msg["role"] == "user" else "🤖 助手"
                        content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                        print(f"{i}. {role}: {content}")
                    continue
                elif user_input.lower() == "tools":
                    chat.show_available_tools()
                    continue
                elif user_input.lower().startswith("call "):
                    # 手动工具调用
                    parts = user_input[5:].strip().split(" ", 1)
                    tool_name = parts[0]
                    
                    if len(parts) > 1:
                        try:
                            arguments = json.loads(parts[1])
                        except json.JSONDecodeError:
                            print("❌ 参数格式错误，请使用JSON格式")
                            continue
                    else:
                        arguments = {}
                    
                    print(f"🔧 调用工具: {tool_name}")
                    result = await chat.call_mcp_tool(tool_name, arguments)
                    print(f"✅ 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    continue
                
                # 处理普通对话
                response = await chat.process_message(user_input)
                print(f"\n🤖 助手: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 出现错误: {str(e)}")
                print("💡 请重试或输入 'help' 查看帮助")
    
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        sys.exit(1)
