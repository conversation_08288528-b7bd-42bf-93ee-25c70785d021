#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版LLM+本地MCP交互工具
先实现基本的对话功能，然后逐步添加工具调用
"""

import sys
import os
import asyncio
import json
import requests

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from core.ai.llm_client import LLMClient
from models.chat import ToolCall


class SimpleLLMCalendarChat:
    """简化版LLM日历聊天工具"""
    
    def __init__(self):
        """初始化"""
        self.llm_client = LLMClient()
        self.mcp_base_url = "http://localhost:3000"
        self.conversation_history = []
        self.available_tools = []
        
    async def start(self):
        """启动服务"""
        try:
            print("🚀 正在启动简化版飞书智能日历助手...")
            
            # 获取可用工具
            print("📡 获取MCP工具列表...")
            response = requests.get(f"{self.mcp_base_url}/api/tools")
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.available_tools = data.get("tools", [])
                    print(f"✅ 获取到 {len(self.available_tools)} 个MCP工具")
                else:
                    print("⚠️ 获取工具列表失败，但可以继续使用基本对话功能")
            else:
                print("⚠️ 无法连接到MCP服务，但可以继续使用基本对话功能")
            
            print("🤖 LLM客户端已就绪！")
            print("\n" + "="*60)
            print("🎉 飞书智能日历助手已启动！")
            print("💡 当前支持的功能：")
            print("   • 🗣️  自然语言对话交流")
            print("   • 🤖 智能工具调用（自动识别用户意图）")
            print("   • 📅 实时日历数据查询")
            if self.available_tools:
                print("   • 🔧 手动工具调用（输入 'tools' 查看可用工具）")
            print("\n💬 您可以直接说：")
            print("   • '查看我的日历列表'")
            print("   • '今天有什么日程'")
            print("   • '搜索包含会议的日程'")
            print("   • '创建一个明天的会议'")
            print("\n💡 输入 'help' 查看更多命令，输入 'quit' 退出")
            print("="*60)
            
        except Exception as e:
            print(f"❌ 启动失败: {str(e)}")
            raise
    
    async def call_mcp_tool(self, tool_name: str, arguments: dict = None) -> dict:
        """调用MCP工具"""
        try:
            if arguments is None:
                arguments = {}
                
            url = f"{self.mcp_base_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API调用失败: {response.status_code}"}
                
        except Exception as e:
            return {"error": f"工具调用失败: {str(e)}"}
    
    def _prepare_tools_for_llm(self) -> list:
        """准备工具列表供LLM使用"""
        if not self.available_tools:
            return []

        # 只选择最常用的工具，避免工具列表过长
        common_tools = [
            "calendar.v4.calendar.list",
            "calendar.v4.calendar.primary",
            "calendar.v4.calendarEvent.list",
            "calendar.v4.calendarEvent.create",
            "calendar.v4.calendarEvent.search",
            "calendar.v4.calendarEvent.get",
            "calendar.v4.calendarEvent.delete",
            "calendar.v4.calendar.create",
            "calendar.v4.freebusy.list"
        ]

        tools = []
        for tool in self.available_tools:
            if tool["name"] in common_tools:
                # 注意：LLM客户端的_prepare_tools方法会再次包装，所以这里只返回function部分
                tools.append({
                    "name": tool["name"],
                    "description": tool["description"],
                    "parameters": tool.get("inputSchema", {})
                })

        return tools

    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 添加用户消息到对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_input
            })

            # 准备系统提示
            system_prompt = {
                "role": "system",
                "content": """你是一个飞书日历智能助手。你可以帮助用户管理飞书日历和日程。

可用的工具包括：
- calendar.v4.calendar.list: 获取用户的日历列表
- calendar.v4.calendar.primary: 获取主日历信息
- calendar.v4.calendarEvent.list: 获取日历事件列表
- calendar.v4.calendarEvent.create: 创建新的日历事件
- calendar.v4.calendarEvent.search: 搜索日历事件
- calendar.v4.calendarEvent.get: 获取单个事件详情
- calendar.v4.calendarEvent.delete: 删除日程
- calendar.v4.calendar.create: 创建新日历
- calendar.v4.freebusy.list: 查询忙闲信息

当用户询问关于日历或日程的具体信息时，请使用相应的工具来获取实际数据。
例如：
- 用户问"我的日历列表"或"有哪些日历" -> 使用 calendar.v4.calendar.list
- 用户问"今天的日程"或"我的事件" -> 使用 calendar.v4.calendarEvent.list
- 用户要"创建会议"或"安排日程" -> 使用 calendar.v4.calendarEvent.create
- 用户要"搜索会议"或"查找日程" -> 使用 calendar.v4.calendarEvent.search

请根据用户的自然语言请求，选择合适的工具来完成任务。
如果需要调用工具，请使用function calling。
如果不需要调用工具，请直接回复用户。

回复要友好、简洁、有用。"""
            }

            # 准备消息列表（保留最近10轮对话）
            messages = [system_prompt] + self.conversation_history[-10:]

            # 准备工具列表
            tools = self._prepare_tools_for_llm()

            # 调用LLM
            print("🤔 正在思考...")
            response = self.llm_client.invoke(messages, tools)

            # 处理响应
            if response.tool_calls:
                # 有工具调用
                print(f"🔧 LLM决定调用 {len(response.tool_calls)} 个工具")

                tool_results = []
                for tool_call in response.tool_calls:
                    print(f"📞 调用工具: {tool_call.name}")
                    print(f"📝 参数: {tool_call.args}")

                    result = await self.call_mcp_tool(
                        tool_call.name,
                        tool_call.args
                    )
                    tool_results.append({
                        "tool_call_id": tool_call.id,
                        "result": result
                    })

                # 简化处理：直接基于工具调用结果生成回复
                print("🤖 正在整理结果...")

                # 构建包含工具结果的简单回复
                result_summary = []
                for i, tool_result in enumerate(tool_results):
                    tool_name = response.tool_calls[i].name
                    result = tool_result["result"]

                    if result.get("success") and not result.get("result", {}).get("isError", True):
                        # 工具调用成功
                        result_summary.append(f"✅ {tool_name} 调用成功")
                    else:
                        # 工具调用失败
                        result_summary.append(f"❌ {tool_name} 调用失败")

                # 生成简单的回复，不再调用LLM
                if len(tool_results) == 1 and tool_results[0]["result"].get("success"):
                    # 单个工具调用成功，尝试解析结果
                    result_data = tool_results[0]["result"].get("result", {})
                    if not result_data.get("isError", True):
                        # 解析成功的结果
                        content_text = result_data.get("content", [{}])[0].get("text", "")
                        if content_text:
                            try:
                                parsed_data = json.loads(content_text)
                                if parsed_data.get("code") == 0:
                                    # 飞书API调用成功
                                    final_content = f"✅ 工具调用成功！以下是结果：\n\n```json\n{json.dumps(parsed_data.get('data', {}), ensure_ascii=False, indent=2)}\n```"
                                else:
                                    final_content = f"❌ API调用失败：{parsed_data.get('msg', '未知错误')}"
                            except:
                                final_content = f"✅ 工具调用完成，原始结果：\n{content_text[:500]}..."
                        else:
                            final_content = "✅ 工具调用完成，但没有返回具体数据。"
                    else:
                        final_content = f"❌ 工具调用失败：{result_data.get('content', [{}])[0].get('text', '未知错误')}"
                else:
                    final_content = f"工具调用完成：{', '.join(result_summary)}"

                # 添加简化的对话历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": final_content
                })

                return final_content
            else:
                # 没有工具调用，直接回复
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response.content
                })

                return response.content

        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            print(f"❌ {error_msg}")
            return f"抱歉，我遇到了一些技术问题：{error_msg}"
    
    def show_available_tools(self):
        """显示可用工具"""
        if not self.available_tools:
            print("❌ 暂无可用工具")
            return
        
        print(f"\n🔧 可用工具列表（共 {len(self.available_tools)} 个）：")
        print("-" * 60)
        
        # 按类别分组显示
        categories = {}
        for tool in self.available_tools:
            category = tool.get("category", "其他")
            if category not in categories:
                categories[category] = []
            categories[category].append(tool)
        
        for category, tools in categories.items():
            print(f"\n📂 {category}:")
            for tool in tools:
                print(f"   • {tool['name']}: {tool['description']}")
        
        print("\n💡 使用方法: call <工具名> [参数JSON]")
        print("📝 示例: call calendar.v4.calendar.list {}")
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🆘 简化版飞书智能日历助手 - 帮助信息

📋 基本命令：
  help          - 显示此帮助信息
  quit/exit/q   - 退出程序
  clear         - 清屏
  history       - 显示对话历史
  tools         - 显示可用的MCP工具
  call <工具名> [参数] - 手动调用MCP工具

🗓️ 对话示例：
  "飞书日历有哪些功能？"
  "如何创建一个重复的会议？"
  "怎么邀请其他人参加会议？"
  "如何设置会议提醒？"

🔧 工具调用示例：
  call calendar.v4.calendar.list {}
  call calendar.v4.calendarEvent.list {"calendar_id": "xxx"}

💡 使用技巧：
  • 可以用自然语言询问日历相关问题
  • 使用 'tools' 命令查看所有可用工具
  • 使用 'call' 命令手动调用特定工具
        """
        print(help_text)


async def main():
    """主函数"""
    chat = SimpleLLMCalendarChat()
    
    try:
        # 启动服务
        await chat.start()
        
        # 主循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ["quit", "exit", "q"]:
                    print("👋 感谢使用飞书智能日历助手，再见！")
                    break
                elif user_input.lower() == "help":
                    chat.print_help()
                    continue
                elif user_input.lower() == "clear":
                    os.system("cls" if os.name == "nt" else "clear")
                    continue
                elif user_input.lower() == "history":
                    print("\n📜 对话历史：")
                    for i, msg in enumerate(chat.conversation_history[-10:], 1):
                        role = "👤 用户" if msg["role"] == "user" else "🤖 助手"
                        content = msg["content"][:100] + "..." if len(msg["content"]) > 100 else msg["content"]
                        print(f"{i}. {role}: {content}")
                    continue
                elif user_input.lower() == "tools":
                    chat.show_available_tools()
                    continue
                elif user_input.lower().startswith("call "):
                    # 手动工具调用
                    parts = user_input[5:].strip().split(" ", 1)
                    tool_name = parts[0]
                    
                    if len(parts) > 1:
                        try:
                            arguments = json.loads(parts[1])
                        except json.JSONDecodeError:
                            print("❌ 参数格式错误，请使用JSON格式")
                            continue
                    else:
                        arguments = {}
                    
                    print(f"🔧 调用工具: {tool_name}")
                    result = await chat.call_mcp_tool(tool_name, arguments)
                    print(f"✅ 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
                    continue
                
                # 处理普通对话
                response = await chat.process_message(user_input)
                print(f"\n🤖 助手: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                break
            except Exception as e:
                print(f"\n❌ 出现错误: {str(e)}")
                print("💡 请重试或输入 'help' 查看帮助")
    
    except Exception as e:
        print(f"❌ 程序启动失败: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        sys.exit(1)
