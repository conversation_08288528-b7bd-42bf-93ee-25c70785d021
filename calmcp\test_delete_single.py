#!/usr/bin/env python3
"""
测试删除单个日历
用于调试删除功能
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class SingleDeleteTester:
    """单个删除测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        user_id_vars = ['TEST_USER_ID', 'FEISHU_USER_ID', 'USER_ID']
        for var in user_id_vars:
            user_id = os.environ.get(var)
            if user_id:
                self.user_id = user_id
                print(f"   用户 ID: {user_id}")
                break
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            print(f"📤 调用工具: {tool_name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            print(f"🌐 HTTP 状态码: {response.status_code}")
            print(f"📄 原始响应: {response.text}")
            
            if response.status_code != 200:
                return None
            
            result = response.json()
            print(f"📊 解析后响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def test_delete_calendar(self, calendar_id: str):
        """测试删除指定日历"""
        print(f"🧪 测试删除日历")
        print("="*60)
        print(f"📋 日历ID: {calendar_id}")
        
        # 首先检查工具是否存在
        print(f"\n1️⃣ 检查删除工具是否可用...")
        tools_result = self.call_mcp_tool("list_tools", {})
        
        if tools_result:
            tools = tools_result.get('result', {}).get('tools', [])
            delete_tool_found = any(tool.get('name') == 'calendar.v4.calendar.delete' for tool in tools)
            print(f"   删除工具可用: {'✅' if delete_tool_found else '❌'}")
        
        # 尝试删除
        print(f"\n2️⃣ 尝试删除日历...")
        result = self.call_mcp_tool("calendar.v4.calendar.delete", {
            "calendar_id": calendar_id
        })
        
        if result:
            if result.get('success'):
                print(f"✅ 删除工具调用成功")
                content = result.get('result', {}).get('content', [])
                if content:
                    try:
                        feishu_response = json.loads(content[0].get('text', '{}'))
                        print(f"📊 飞书API响应: {json.dumps(feishu_response, indent=2, ensure_ascii=False)}")
                        
                        if feishu_response.get('code') == 0:
                            print(f"🎉 日历删除成功！")
                        else:
                            print(f"❌ 飞书API删除失败:")
                            print(f"   错误代码: {feishu_response.get('code')}")
                            print(f"   错误信息: {feishu_response.get('msg')}")
                    except json.JSONDecodeError as e:
                        print(f"❌ 响应解析失败: {e}")
                else:
                    print(f"❌ 响应内容为空")
            else:
                print(f"❌ 删除工具调用失败")
                print(f"   错误信息: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 无法调用删除工具")


def main():
    """主函数"""
    # 使用一个测试日历的ID（从之前的输出中选择一个）
    test_calendar_id = input("请输入要测试删除的日历ID: ").strip()
    
    if not test_calendar_id:
        print("❌ 未提供日历ID")
        return
    
    tester = SingleDeleteTester()
    tester.test_delete_calendar(test_calendar_id)


if __name__ == "__main__":
    main()
