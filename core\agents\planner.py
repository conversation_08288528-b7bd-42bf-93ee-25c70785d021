"""
规划器代理
类似PDeerFlow的Planner，负责详细的任务分析和计划制定
"""

import logging
from datetime import datetime
from typing import Any, Dict

from models.agent_models import (
    CalendarAction,
    CalendarEventExtraction,
    CalendarPlan,
    TimeExtraction,
)

from .base import AgentState, AgentType, BaseAgent, Command
from .tools import CalendarTools

logger = logging.getLogger(__name__)


class PlannerAgent(BaseAgent):
    """
    规划器代理

    职责：
    1. 详细分析用户的日历需求
    2. 提取结构化的事件信息
    3. 生成执行计划
    4. 处理用户确认和反馈
    """

    def __init__(self):
        super().__init__(AgentType.PLANNER)
        self.bind_tools(CalendarTools.get_planner_tools())

    async def process(self, state: AgentState) -> Command:
        """
        处理日历规划任务

        Args:
            state: 当前状态

        Returns:
            Command: 下一步操作命令
        """
        user_input = state.user_input or ""
        intent = state.intent or "calendar"

        logger.info(f"Planner processing calendar request: {user_input}")

        try:
            # 如果已有计划且用户提供了反馈，处理反馈
            if state.calendar_plan and state.pending_confirmation:
                return await self._handle_user_feedback(state)

            # 生成新的日历计划
            calendar_plan = await self._generate_calendar_plan(state)

            if calendar_plan.needs_confirmation:
                # 需要用户确认
                return Command(
                    update={
                        "calendar_plan": calendar_plan.dict(),
                        "pending_confirmation": True,
                        "messages": state.messages
                        + [
                            {
                                "role": "assistant",
                                "content": calendar_plan.confirmation_message,
                                "name": "planner",
                            }
                        ],
                    },
                    goto="human_feedback",
                )
            else:
                # 直接执行
                return Command(
                    update={
                        "calendar_plan": calendar_plan.dict(),
                        "pending_confirmation": False,
                    },
                    goto="executor",
                )

        except Exception as e:
            logger.error(f"Planner processing error: {e}")
            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": f"抱歉，分析您的请求时遇到问题：{str(e)}",
                            "name": "planner",
                        }
                    ]
                },
                goto="__end__",
            )

    async def _generate_calendar_plan(self, state: AgentState) -> CalendarPlan:
        """
        生成日历计划

        Args:
            state: 当前状态

        Returns:
            CalendarPlan: 生成的计划
        """
        user_input = state.user_input or ""

        # 构建规划器提示
        system_prompt = self._get_planner_prompt()

        # 准备消息
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"请分析以下日历请求并生成计划：{user_input}"},
        ]

        # 获取绑定了工具的LLM
        llm_with_tools = self.get_llm_with_tools()

        # 使用结构化输出
        llm_with_structured_output = llm_with_tools.with_structured_output(CalendarPlan)

        # 调用LLM生成计划
        calendar_plan = llm_with_structured_output.invoke(messages)

        return calendar_plan

    async def _handle_user_feedback(self, state: AgentState) -> Command:
        """
        处理用户反馈

        Args:
            state: 当前状态

        Returns:
            Command: 下一步操作命令
        """
        user_feedback = state.user_input or ""
        current_plan = state.calendar_plan

        # 检查用户是否确认
        if any(
            word in user_feedback.lower()
            for word in ["确认", "是的", "好的", "可以", "同意"]
        ):
            # 用户确认，执行计划
            return Command(
                update={
                    "pending_confirmation": False,
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "好的，我来为您执行这个操作。",
                            "name": "planner",
                        }
                    ],
                },
                goto="executor",
            )

        elif any(
            word in user_feedback.lower() for word in ["取消", "不要", "算了", "拒绝"]
        ):
            # 用户取消
            return Command(
                update={
                    "pending_confirmation": False,
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "好的，已取消操作。还有什么我可以帮您的吗？",
                            "name": "planner",
                        }
                    ],
                },
                goto="__end__",
            )

        else:
            # 用户提供修改建议，重新生成计划
            modified_input = (
                f"{state.context.get('original_request', '')} {user_feedback}"
            )

            return Command(
                update={
                    "user_input": modified_input,
                    "pending_confirmation": False,
                    "calendar_plan": None,
                    "messages": state.messages
                    + [{"role": "user", "content": user_feedback}],
                },
                goto="planner",  # 重新规划
            )

    def _get_planner_prompt(self) -> str:
        """获取规划器提示词"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        return f"""你是一个专业的日历事件规划器，当前时间是 {current_time}。

你的任务是分析用户的日历请求，提取结构化信息，并生成执行计划。

# 分析框架

1. **操作类型识别**：
   - CREATE: 创建新事件（"安排"、"添加"、"创建"）
   - QUERY: 查询事件（"查看"、"显示"、"列出"）
   - UPDATE: 修改事件（"修改"、"更新"、"改变"）
   - DELETE: 删除事件（"删除"、"取消"、"移除"）

2. **时间信息提取**：
   - 绝对时间：具体日期和时间
   - 相对时间：今天、明天、下周等
   - 时间范围：从...到...
   - 持续时间：多长时间

3. **事件信息提取**：
   - 标题/主题
   - 地点
   - 描述
   - 参与者
   - 重复模式
   - 提醒设置

# 输出要求

请以CalendarPlan格式输出，包含：
- locale: "zh-CN"
- has_enough_context: 是否有足够信息执行操作
- thought: 你的分析思路
- extracted_event: 提取的事件信息
- needs_confirmation: 是否需要用户确认
- confirmation_message: 确认消息（如果需要）

# 注意事项

- 对于模糊的时间表达，尝试推断最合理的含义
- 如果关键信息缺失，标记为需要确认
- 删除操作总是需要确认
- 保持友好、专业的语气"""

    def _extract_entities_with_tools(self, text: str) -> Dict[str, Any]:
        """
        使用工具提取实体（备用方法）

        Args:
            text: 用户输入文本

        Returns:
            提取的实体信息
        """
        # 这里可以调用实际的工具函数
        # 暂时使用简单的关键词匹配

        entities = {
            "action": None,
            "title": None,
            "time_expressions": [],
            "location": None,
        }

        # 简单的动作识别
        if any(word in text for word in ["创建", "安排", "添加", "新建"]):
            entities["action"] = CalendarAction.CREATE
        elif any(word in text for word in ["查询", "查看", "显示", "列出"]):
            entities["action"] = CalendarAction.QUERY
        elif any(word in text for word in ["修改", "更新", "改变"]):
            entities["action"] = CalendarAction.UPDATE
        elif any(word in text for word in ["删除", "取消", "移除"]):
            entities["action"] = CalendarAction.DELETE

        return entities
