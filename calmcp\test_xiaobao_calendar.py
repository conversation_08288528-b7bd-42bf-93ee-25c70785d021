#!/usr/bin/env python3
"""
测试小宝暑假计划日历
使用 Supabase 中的用户 token 查询明天的日程安排
"""

import requests
import json
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class XiaobaoCalendarTester:
    """小宝日历测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        # 使用大宝暑假计划日历（从实际查询结果中获得）
        self.xiaobao_calendar_id = "<EMAIL>"
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        test_token = os.environ.get('TEST_ACCESS_TOKEN')
        test_user_id = os.environ.get('TEST_USER_ID')
        
        if test_token and test_user_id:
            self.user_id = test_user_id
            self.user_token = test_token
            print(f"✅ 已加载测试凭据")
            print(f"   用户 ID: {self.user_id}")
            print(f"   Token 预览: {test_token[:20]}...")
        else:
            print("❌ 未找到测试凭据")
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            # 添加用户信息到参数
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            print(f"📤 调用工具: {tool_name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            print(f"🌐 HTTP 状态码: {response.status_code}")

            if response.status_code != 200:
                print(f"❌ HTTP 错误: {response.text}")
                return None

            result = response.json()
            print(f"📊 响应成功: {result.get('success', 'unknown')}")

            # 如果工具执行失败，显示详细错误信息
            if not result.get('success', True):
                print(f"⚠️  工具执行错误: {result.get('error', '未知错误')}")
                # 尝试解析更详细的错误信息
                if 'result' in result and 'content' in result['result']:
                    content = result['result']['content']
                    if content and len(content) > 0:
                        error_text = content[0].get('text', '')
                        print(f"📝 详细错误: {error_text}")

            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_calendar_list(self):
        """获取日历列表"""
        print("\n" + "="*60)
        print("📅 获取日历列表")
        print("="*60)
        
        result = self.call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 50
        })
        
        if result and result.get('success'):
            content = result.get('result', {}).get('content', [])
            if content:
                feishu_response = json.loads(content[0].get('text', '{}'))
                if feishu_response.get('code') == 0:
                    calendars = feishu_response.get('data', {}).get('calendar_list', [])
                    print(f"✅ 找到 {len(calendars)} 个日历")
                    
                    for i, cal in enumerate(calendars):
                        print(f"  {i+1}. {cal.get('summary', '未命名')}")
                        print(f"     ID: {cal.get('calendar_id')}")
                        print(f"     类型: {cal.get('type', 'N/A')}")
                        if cal.get('calendar_id') == self.xiaobao_calendar_id:
                            print(f"     🎯 这是小宝暑假计划日历！")
                        print()
                    return calendars
                else:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
            else:
                print("❌ 响应内容为空")
        else:
            print(f"❌ 工具调用失败: {result.get('error') if result else '无响应'}")
        
        return []
    
    def get_tomorrow_events(self):
        """获取明天的日程"""
        print("\n" + "="*60)
        print("📅 查询小宝暑假计划 - 明天的日程")
        print("="*60)
        
        # 计算明天的时间范围
        now = datetime.now()
        tomorrow = now + timedelta(days=1)
        tomorrow_start = tomorrow.replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_end = tomorrow.replace(hour=23, minute=59, second=59, microsecond=999999)
        
        start_timestamp = str(int(tomorrow_start.timestamp()))
        end_timestamp = str(int(tomorrow_end.timestamp()))
        
        print(f"🕐 查询时间: {tomorrow_start.strftime('%Y-%m-%d %H:%M:%S')} 到 {tomorrow_end.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📊 时间戳: {start_timestamp} - {end_timestamp}")
        
        result = self.call_mcp_tool("calendar.v4.calendarEvent.list", {
            "calendar_id": self.xiaobao_calendar_id,
            "start_time": start_timestamp,
            "end_time": end_timestamp,
            "page_size": 50
        })
        
        if result and result.get('success'):
            content = result.get('result', {}).get('content', [])
            if content:
                feishu_response = json.loads(content[0].get('text', '{}'))
                if feishu_response.get('code') == 0:
                    events = feishu_response.get('data', {}).get('items', [])
                    print(f"\n✅ 找到 {len(events)} 个日程事件")
                    
                    if events:
                        print("\n📋 明天的日程安排:")
                        for i, event in enumerate(events):
                            print(f"\n  {i+1}. {event.get('summary', '无标题')}")
                            
                            # 显示时间
                            if event.get('start_time') and event.get('end_time'):
                                start_time = datetime.fromtimestamp(int(event['start_time']['timestamp']))
                                end_time = datetime.fromtimestamp(int(event['end_time']['timestamp']))
                                print(f"     ⏰ 时间: {start_time.strftime('%H:%M')} - {end_time.strftime('%H:%M')}")
                            
                            # 显示描述
                            if event.get('description'):
                                print(f"     📝 描述: {event['description']}")
                            
                            # 显示地点
                            if event.get('location') and event['location'].get('name'):
                                print(f"     📍 地点: {event['location']['name']}")
                            
                            # 显示状态
                            if event.get('status'):
                                status_map = {
                                    'confirmed': '已确认',
                                    'tentative': '待定',
                                    'cancelled': '已取消'
                                }
                                print(f"     📊 状态: {status_map.get(event['status'], event['status'])}")
                    else:
                        print("🎉 明天没有安排日程，可以自由安排时间！")
                    
                    return events
                else:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
            else:
                print("❌ 响应内容为空")
        else:
            print(f"❌ 工具调用失败: {result.get('error') if result else '无响应'}")
        
        return []
    
    def run_test(self):
        """运行测试"""
        print("🚀 小宝暑假计划日历测试")
        print("="*60)
        
        if not self.user_token:
            print("❌ 没有有效的用户凭据，无法继续测试")
            return
        
        # 1. 获取日历列表
        calendars = self.get_calendar_list()
        
        # 2. 检查小宝日历是否存在
        xiaobao_found = any(cal.get('calendar_id') == self.xiaobao_calendar_id for cal in calendars)
        if not xiaobao_found:
            print(f"⚠️  未找到小宝暑假计划日历 (ID: {self.xiaobao_calendar_id})")
            print("📋 可用的日历:")
            for cal in calendars:
                print(f"  - {cal.get('summary', '未命名')}: {cal.get('calendar_id')}")
            return
        
        # 3. 查询明天的日程
        events = self.get_tomorrow_events()
        
        print("\n" + "="*60)
        print("🎉 测试完成！")
        print("="*60)
        print(f"📊 测试结果:")
        print(f"  - 日历总数: {len(calendars)}")
        print(f"  - 小宝日历: {'✅ 找到' if xiaobao_found else '❌ 未找到'}")
        print(f"  - 明天日程: {len(events)} 个事件")


def main():
    """主函数"""
    tester = XiaobaoCalendarTester()
    tester.run_test()


if __name__ == "__main__":
    main()
