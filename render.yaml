services:
  - type: web
    name: feishu-coze-plugin
    env: python
    buildCommand: pip install -r requirements.txt
    startCommand: uvicorn main:app --host 0.0.0.0 --port $PORT --workers 2
    envVars:
      # 飞书应用配置
      - key: FEISHU_CLIENT_ID
        sync: false
      - key: FEISHU_CLIENT_SECRET
        sync: false
      - key: REDIRECT_URI
        sync: false
      - key: APP_URL
        sync: false

      # 数据库配置
      - key: SUPABASE_URL
        sync: false
      - key: SUPABASE_KEY
        sync: false

      # AI配置
      - key: SILICONFLOW_API_KEY
        sync: false
      - key: LLM_MODEL
        value: Qwen/Qwen3-14B
      - key: LLM_TEMPERATURE
        value: "0.7"
      - key: LLM_MAX_TOKENS
        value: "2048"

      # 系统配置
      - key: TZ
        value: Asia/Shanghai
      - key: STORAGE_TYPE
        value: supabase
      - key: FEISHU_ENV
        value: production
      - key: LOG_LEVEL
        value: INFO

      # 性能配置
      - key: MAX_RETRY_ATTEMPTS
        value: "3"
      - key: DEFAULT_MEETING_DURATION
        value: "60"
      - key: DEFAULT_REMINDER_TIME
        value: "15"