"""
工作流节点处理器
基于PDeerFlow设计的多代理协作系统
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, Literal

from core.agents import CoordinatorAgent, PlannerAgent
from core.ai import get_llm
from models.agent_models import AgentState, CalendarPlan
from models.workflow import Command
from utils.prompts import (
    CALENDAR_PROCESSOR_PROMPT,
    CHAT_PROCESSOR_PROMPT,
    INTENT_CLASSIFIER_PROMPT,
)
from core.mcp.feishu_mcp_adapter import get_feishu_mcp_adapter

logger = logging.getLogger(__name__)


def text_input_node(state: Dict[str, Any]) -> Command:
    """
    文本输入处理节点

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    user_input = state.get("user_input", "")

    # 简单的文本预处理
    processed_text = user_input.strip()

    logger.info(f"Processing user input: {processed_text}")

    return Command(
        update={"processed_text": processed_text}, goto="coordinator"  # 直接到协调器
    )


async def coordinator_node(state: Dict[str, Any]) -> Command:
    """
    协调器节点 - 类似PDeerFlow的coordinator
    负责初步意图识别和任务分发

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    try:
        # 转换为AgentState
        agent_state = AgentState(
            user_input=state.get("user_input", ""),
            processed_text=state.get("processed_text", ""),
            messages=state.get("messages", []),
            context=state.get("context", {}),
        )

        # 创建协调器代理
        coordinator = CoordinatorAgent()

        # 处理请求
        command = await coordinator.process(agent_state)

        # 转换回字典格式
        update_dict = command.update or {}

        return Command(update=update_dict, goto=command.goto)

    except Exception as e:
        logger.error(f"Coordinator node error: {e}")
        return Command(
            update={
                "intent": "chat",
                "confidence": 0.5,
                "messages": state.get("messages", [])
                + [
                    {
                        "role": "assistant",
                        "content": "抱歉，我遇到了一些问题。请重新描述您的需求。",
                    }
                ],
            },
            goto="__end__",
        )


async def planner_node(state: Dict[str, Any]) -> Command:
    """
    规划器节点 - 类似PDeerFlow的planner
    负责详细的任务分析和计划制定

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    try:
        # 转换为AgentState
        agent_state = AgentState(
            user_input=state.get("user_input", ""),
            processed_text=state.get("processed_text", ""),
            intent=state.get("intent", ""),
            confidence=state.get("confidence", 0.0),
            messages=state.get("messages", []),
            context=state.get("context", {}),
            calendar_plan=state.get("calendar_plan"),
            pending_confirmation=state.get("pending_confirmation", False),
        )

        # 创建规划器代理
        planner = PlannerAgent()

        # 处理请求
        command = await planner.process(agent_state)

        # 转换回字典格式
        update_dict = command.update or {}

        return Command(update=update_dict, goto=command.goto)

    except Exception as e:
        logger.error(f"Planner node error: {e}")
        return Command(
            update={
                "messages": state.get("messages", [])
                + [
                    {
                        "role": "assistant",
                        "content": f"抱歉，分析您的请求时遇到问题：{str(e)}",
                    }
                ]
            },
            goto="__end__",
        )


async def human_feedback_node(state: Dict[str, Any]) -> Command:
    """
    人机协作节点 - 类似PDeerFlow的human_feedback
    处理用户确认和反馈

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    # 检查是否有待确认的计划
    if not state.get("pending_confirmation"):
        return Command(goto="__end__")

    # 等待用户输入（这里需要实际的中断机制）
    # 在实际实现中，这里会暂停工作流等待用户响应

    # 暂时直接返回到规划器处理反馈
    return Command(goto="planner")


async def executor_node(state: Dict[str, Any]) -> Command:
    """
    执行器节点
    执行具体的日历操作

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    try:
        calendar_plan = state.get("calendar_plan")
        if not calendar_plan:
            return Command(
                update={
                    "messages": state.get("messages", [])
                    + [{"role": "assistant", "content": "没有找到执行计划。"}]
                },
                goto="__end__",
            )

        # 这里应该调用实际的日历API
        # 暂时返回成功消息

        extracted_event = calendar_plan.get("extracted_event", {})
        action = extracted_event.get("action", "处理")
        title = extracted_event.get("title", "事件")

        success_message = f"已成功{action}日历事件：{title}"

        return Command(
            update={
                "messages": state.get("messages", [])
                + [{"role": "assistant", "content": success_message}]
            },
            goto="__end__",
        )

    except Exception as e:
        logger.error(f"Executor node error: {e}")
        return Command(
            update={
                "messages": state.get("messages", [])
                + [{"role": "assistant", "content": f"执行操作时遇到问题：{str(e)}"}]
            },
            goto="__end__",
        )


def intent_classifier_node(state: Dict[str, Any]) -> Command:
    """
    意图识别节点

    Args:
        state: 当前状态

    Returns:
        Command对象，指示下一步操作
    """
    processed_text = state.get("processed_text", "")

    # 检查是否有上下文状态
    context_state = state.get("context_state", None)
    last_intent = state.get("intent", None)

    # 如果有上下文状态，优先处理上下文相关的回复
    if context_state == "awaiting_calendar_confirmation" and last_intent == "calendar":
        # 检查是否是确认类的回复
        confirmation_keywords = [
            "确认",
            "好的",
            "是的",
            "没问题",
            "无需",
            "不用",
            "直接",
            "创建",
            "安排",
        ]
        rejection_keywords = ["取消", "不要", "重新", "修改", "不对"]

        text_lower = processed_text.lower()

        if any(word in text_lower for word in confirmation_keywords):
            logger.info("Detected calendar confirmation, continuing calendar flow")
            return Command(
                update={
                    "intent": "calendar_confirm",
                    "confidence": 0.9,
                    "context_state": None,  # 清除等待状态
                },
                goto="calendar_confirm_processor",
            )
        elif any(word in text_lower for word in rejection_keywords):
            logger.info("Detected calendar rejection, back to calendar processor")
            return Command(
                update={
                    "intent": "calendar",
                    "confidence": 0.9,
                    "context_state": None,  # 清除等待状态
                },
                goto="calendar_processor",
            )

    # 正常的意图识别流程

    # 获取当前系统时间并格式化
    now = datetime.now()
    current_date = now.strftime("%Y年%m月%d日")
    current_time = now.strftime("%H:%M")

    # 动态更新提示词，插入当前日期和时间
    prompt = INTENT_CLASSIFIER_PROMPT.format(
        current_date=current_date, current_time=current_time
    )

    # 准备消息
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": processed_text},
    ]

    try:
        # 调用LLM进行意图识别
        response = get_llm().invoke(messages)

        logger.debug(f"Intent classification response: {response.content}")

        # 解析JSON响应
        content = response.content.strip()
        if content.startswith("```json"):
            content = content[7:]
        if content.endswith("```"):
            content = content[:-3]
        content = content.strip()

        try:
            logger.debug(f"Attempting to parse JSON content: {repr(content)}")
            result = json.loads(content)
            intent = result.get("intent", "chat")
            confidence = result.get("confidence", 0.8)
            logger.debug(
                f"Successfully parsed JSON: intent={intent}, confidence={confidence}"
            )
        except json.JSONDecodeError as e:
            # 如果JSON解析失败，根据关键词判断
            logger.warning(f"JSON parsing failed: {e}, content: {repr(content)}")
            content = response.content.lower()
            if any(
                word in content
                for word in ["日程", "会议", "安排", "提醒", "约会", "日历", "查询"]
            ):
                intent = "calendar"
                confidence = 0.7
            elif any(word in content for word in ["日记", "记录", "笔记"]):
                intent = "journal"
                confidence = 0.7
            elif any(word in content for word in ["写", "创作", "诗", "故事"]):
                intent = "media"
                confidence = 0.7
            else:
                intent = "chat"
                confidence = 0.8

        # 根据意图类型路由到对应的处理器
        if intent == "calendar":
            next_node = "calendar_processor"
        elif intent == "journal":
            next_node = "journal_processor"
        elif intent == "media":
            next_node = "media_processor"
        else:
            next_node = "chat_processor"

        logger.info(
            f"Detected intent: {intent} (confidence: {confidence}), goto: {next_node}"
        )

        return Command(
            update={
                "intent": intent,
                "confidence": confidence,
            },
            goto=next_node,
        )

    except Exception as e:
        logger.error(f"Error in intent classification: {e}")
        # 降级到聊天处理
        return Command(
            update={"intent": "chat", "confidence": 0.5}, goto="chat_processor"
        )


async def calendar_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    日历处理器节点 - 支持MCP集成

    Args:
        state: 当前状态

    Returns:
        包含处理结果的字典
    """
    processed_text = state.get("processed_text", "")

    # 获取当前系统时间并格式化
    now = datetime.now()
    current_date = now.strftime("%Y年%m月%d日")
    current_time = now.strftime("%H:%M")

    # 增强的日历处理提示词，包含MCP工具调用指导
    enhanced_prompt = f"""你是一个专业的日历助手，能够处理各种日历相关的操作请求。

当前时间：{current_date} {current_time}

你的能力包括：
1. 创建日程事件 - 解析时间、地点、参与者等信息
2. 查询日程安排 - 按日期、时间范围查询
3. 修改日程事件 - 更新时间、地点、描述等
4. 删除日程事件 - 取消或删除指定事件
5. 搜索日历事件 - 按关键词搜索

处理原则：
- 准确解析自然语言中的时间表达
- 主动询问缺失的必要信息
- 提供清晰的操作确认
- 友好地处理异常情况

如果用户的请求包含足够的信息来执行操作，请提取以下信息：
- action: create/query/update/delete/search
- title: 事件标题
- start_time: 开始时间（ISO格式）
- end_time: 结束时间（ISO格式）
- location: 地点
- description: 描述

请以JSON格式返回操作信息，如果信息不完整，则返回需要补充的信息提示。

用户输入：{processed_text}"""

    # 准备消息
    messages = [
        {"role": "system", "content": enhanced_prompt},
        {"role": "user", "content": processed_text},
    ]

    try:
        # 调用LLM进行处理
        response = get_llm().invoke(messages)
        message = response.content

        logger.info("Calendar processing completed")

        # 尝试解析LLM响应中的操作信息
        operation_info = await _extract_calendar_operation(message, processed_text)

        if operation_info and operation_info.get("action"):
            # 有明确的操作信息，尝试执行MCP调用
            mcp_result = await _execute_calendar_mcp_operation(operation_info)

            if mcp_result.get("success"):
                return {
                    "message": f"✅ {mcp_result.get('message', '操作成功')}\n\n{message}",
                    "intent": "calendar",
                    "data": {
                        "processed": True,
                        "mcp_result": mcp_result,
                        "operation": operation_info
                    },
                }
            else:
                return {
                    "message": f"❌ {mcp_result.get('message', '操作失败')}\n\n{message}",
                    "intent": "calendar",
                    "data": {
                        "processed": True,
                        "mcp_error": mcp_result.get('error'),
                        "operation": operation_info
                    },
                }
        else:
            # 检查是否需要用户确认或补充信息
            message_lower = message.lower()
            if any(
                word in message_lower for word in ["确认", "请问", "是否", "需要", "补充"]
            ):
                # 需要用户确认，设置等待状态
                return {
                    "message": message,
                    "intent": "calendar",
                    "data": {"processed": True},
                    "context_state": "awaiting_calendar_confirmation",
                }
            else:
                # 直接完成处理
                return {
                    "message": message,
                    "intent": "calendar",
                    "data": {"processed": True},
                }

    except Exception as e:
        logger.error(f"Error in calendar processor: {e}")
        return {
            "message": f"抱歉，处理日历请求时出现错误：{str(e)}",
            "intent": "calendar",
            "data": {"processed": False, "error": str(e)},
        }


async def _extract_calendar_operation(llm_response: str, user_input: str) -> Dict[str, Any]:
    """从LLM响应中提取日历操作信息"""
    try:
        # 尝试解析JSON格式的响应
        import re
        json_match = re.search(r'\{[^}]*\}', llm_response)
        if json_match:
            import json
            operation_info = json.loads(json_match.group())
            return operation_info

        # 如果没有JSON，使用关键词分析
        user_lower = user_input.lower()

        # 判断操作类型
        if any(word in user_lower for word in ["创建", "安排", "预定", "约", "会议"]):
            action = "create"
        elif any(word in user_lower for word in ["查询", "查看", "显示", "今天", "明天", "安排"]):
            action = "query"
        elif any(word in user_lower for word in ["搜索", "找", "查找"]):
            action = "search"
        elif any(word in user_lower for word in ["修改", "更改", "改", "调整"]):
            action = "update"
        elif any(word in user_lower for word in ["删除", "取消", "不要"]):
            action = "delete"
        else:
            return {}

        # 简单的时间提取（这里可以使用更复杂的时间解析库）
        operation_info = {
            "action": action,
            "title": "",
            "start_time": "",
            "end_time": "",
            "location": "",
            "description": user_input
        }

        return operation_info

    except Exception as e:
        logger.error(f"提取日历操作信息失败: {e}")
        return {}


async def _execute_calendar_mcp_operation(operation_info: Dict[str, Any]) -> Dict[str, Any]:
    """执行日历MCP操作"""
    try:
        adapter = get_feishu_mcp_adapter()
        action = operation_info.get("action")

        if action == "create":
            # 创建事件
            event_data = {
                "title": operation_info.get("title", "新事件"),
                "description": operation_info.get("description", ""),
                "start_time": operation_info.get("start_time", ""),
                "end_time": operation_info.get("end_time", ""),
                "location": operation_info.get("location", "")
            }

            # 如果时间信息不完整，返回需要补充信息的提示
            if not event_data["start_time"]:
                return {
                    "success": False,
                    "message": "请提供事件的开始时间",
                    "need_more_info": True
                }

            result = await adapter.create_calendar_event(event_data)
            return result

        elif action == "query":
            # 查询日历
            result = await adapter.list_calendars()
            return result

        elif action == "search":
            # 搜索事件
            query = operation_info.get("title", operation_info.get("description", ""))
            if not query:
                return {
                    "success": False,
                    "message": "请提供搜索关键词",
                    "need_more_info": True
                }

            result = await adapter.search_calendar_events(query)
            return result

        else:
            return {
                "success": False,
                "message": f"暂不支持的操作类型: {action}",
                "need_more_info": False
            }

    except Exception as e:
        logger.error(f"执行MCP操作失败: {e}")
        return {
            "success": False,
            "error": str(e),
            "message": f"执行操作时出现错误: {str(e)}"
        }
        return {
            "message": "抱歉，处理日历请求时发生错误，请稍后重试。",
            "intent": "calendar",
            "error": str(e),
        }


def calendar_confirm_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    日历确认处理器节点
    处理用户对日历操作的确认

    Args:
        state: 当前状态

    Returns:
        包含处理结果的字典
    """
    processed_text = state.get("processed_text", "")

    # 获取当前系统时间并格式化
    now = datetime.now()
    current_date = now.strftime("%Y年%m月%d日")
    current_time = now.strftime("%H:%M")

    # 构建确认处理的提示词
    confirm_prompt = f"""你是一个专业的日历助手，用户刚才确认了日历操作。

当前时间：{current_date} {current_time}

用户的确认回复：{processed_text}

请根据用户的确认，执行相应的日历操作并给出友好的回复。

处理原则：
- 如果用户确认创建事件，说明事件已成功创建
- 如果用户确认查询，提供查询结果
- 如果用户确认修改，说明修改已完成
- 给出简洁友好的确认信息

请直接回复用户，不需要再次询问确认。"""

    # 准备消息
    messages = [
        {"role": "system", "content": confirm_prompt},
        {"role": "user", "content": processed_text},
    ]

    try:
        # 调用LLM进行处理
        response = get_llm().invoke(messages)
        message = response.content

        logger.info("Calendar confirmation processing completed")

        return {
            "message": message,
            "intent": "calendar",
            "data": {"confirmed": True, "processed": True},
        }

    except Exception as e:
        logger.error(f"Error in calendar confirmation processing: {e}")
        return {
            "message": f"处理确认时发生错误: {str(e)}",
            "intent": "calendar",
            "data": {"error": str(e)},
        }


def chat_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    聊天处理器节点

    Args:
        state: 当前状态

    Returns:
        包含回复消息的字典
    """
    processed_text = state.get("processed_text", "")

    # 获取当前系统时间并格式化
    now = datetime.now()
    current_date = now.strftime("%Y年%m月%d日")
    current_time = now.strftime("%H:%M")

    # 动态更新提示词
    prompt = CHAT_PROCESSOR_PROMPT.format(
        current_date=current_date, current_time=current_time
    )

    # 准备消息
    messages = [
        {"role": "system", "content": prompt},
        {"role": "user", "content": processed_text},
    ]

    try:
        # 调用LLM进行聊天
        response = get_llm().invoke(messages)
        message = response.content

        logger.info("Chat processing completed")

        return {"message": message, "intent": "chat"}

    except Exception as e:
        logger.error(f"Error in chat processor: {e}")
        return {
            "message": "抱歉，我现在无法回复您的消息，请稍后重试。",
            "intent": "chat",
            "error": str(e),
        }


def journal_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    日记处理器节点
    """
    return {"message": "日记功能正在开发中，敬请期待。", "intent": "journal"}


def media_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    媒体处理器节点
    """
    return {"message": "媒体创作功能正在开发中，敬请期待。", "intent": "media"}


def result_presenter_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """
    结果展示节点
    """
    return state
