"""
调试确认流程
"""

import asyncio
import logging

from models.chat import ChatRequest
from services.chat_service import ChatService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_confirmation_debug():
    """调试确认流程"""
    chat_service = ChatService()
    user_id = "debug_user_001"

    print("=== 第一步：创建日历事件 ===")
    request1 = ChatRequest(user_id=user_id, message="明天下午3点安排一个产品评审会议")

    response1 = await chat_service.process_message(request1)
    print(f"响应1: {response1.message}")
    print(f"状态1: {response1.context}")

    print("\n=== 第二步：用户确认 ===")
    request2 = ChatRequest(user_id=user_id, message="确认")

    response2 = await chat_service.process_message(request2)
    print(f"响应2: {response2.message}")
    print(f"状态2: {response2.context}")


if __name__ == "__main__":
    asyncio.run(test_confirmation_debug())
