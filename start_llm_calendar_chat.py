#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动LLM+MCP飞书日历聊天界面
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from scripts.llm_mcp_cli import llm_mcp_interactive_cli


def main():
    """主函数"""
    print("🚀 启动飞书智能日历助手...")
    print("📋 这是一个基于硅基流动LLM和飞书MCP的智能日历管理工具")
    print("💡 您可以用自然语言来管理您的飞书日历和日程")
    print("-" * 60)
    
    try:
        # 运行异步聊天界面
        asyncio.run(llm_mcp_interactive_cli())
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断，正在退出...")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
