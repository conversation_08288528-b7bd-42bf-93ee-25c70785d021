#!/usr/bin/env python3
"""
测试 CalMCP 服务的 Python 脚本
调用 calendar_list 工具获取飞书日历清单
"""

import requests
import json
import sys
from typing import Dict, Any, Optional

class CalMCPClient:
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
    
    def health_check(self) -> bool:
        """检查服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def get_tools(self) -> Optional[Dict[str, Any]]:
        """获取可用工具列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/mcp/tools")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return None
    
    def call_tool(self, name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            payload = {
                "name": name,
                "arguments": arguments
            }

            print(f"📤 调用工具: {name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")

            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )

            print(f"🌐 HTTP 状态码: {response.status_code}")

            # 详细错误处理
            if response.status_code == 401:
                print("❌ 认证失败 (401): 用户访问令牌可能已过期")
                print("💡 解决方案: 访问 http://localhost:3000/auth 重新获取令牌")
                return None
            elif response.status_code == 404:
                print("❌ 工具未找到 (404): 指定的工具不存在")
                return None
            elif response.status_code >= 400:
                print(f"❌ HTTP 错误 ({response.status_code}): {response.text}")
                return None

            response.raise_for_status()
            result = response.json()

            # 显示响应的基本信息
            print(f"📊 响应成功: {result.get('success', 'unknown')}")
            if not result.get('success', True):
                print(f"⚠️  工具执行错误: {result.get('error', '未知错误')}")

            return result

        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求失败: {e}")
            return None
        except json.JSONDecodeError as e:
            print(f"❌ 响应解析失败: {e}")
            return None
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_calendar_list(self, page_size: int = 50, page_token: Optional[str] = None) -> None:
        """获取日历列表"""
        print("📅 获取飞书日历列表...")
        
        arguments = {"page_size": page_size}
        if page_token:
            arguments["page_token"] = page_token
        
        result = self.call_tool("calendar_list", arguments)
        
        if not result:
            return
        
        print(f"\n📊 响应状态: {'成功' if result.get('success') else '失败'}")

        # 检查是否有错误
        if result.get('success') == False:
            error_msg = result.get('error', '未知错误')
            print(f"❌ 工具执行失败: {error_msg}")

            # 如果是认证错误，提供解决建议
            if '401' in str(error_msg) or 'Unauthorized' in str(error_msg):
                print("💡 建议: 用户访问令牌可能已过期，请重新获取:")
                print("   1. 访问 http://localhost:3000/auth")
                print("   2. 完成飞书授权流程")
                print("   3. 重新运行测试")
            return

        if result.get('success'):
            # 解析飞书 API 响应
            content = result.get('result', {}).get('content', [])
            if content and len(content) > 0:
                try:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    
                    print(f"🔍 飞书 API 响应:")
                    print(f"   代码: {feishu_response.get('code')}")
                    print(f"   消息: {feishu_response.get('msg')}")
                    
                    if feishu_response.get('code') == 0:
                        data = feishu_response.get('data', {})
                        
                        print(f"\n📈 分页信息:")
                        print(f"   有更多数据: {data.get('has_more', False)}")
                        print(f"   分页令牌: {data.get('page_token', 'None')}")
                        print(f"   同步令牌: {data.get('sync_token', 'None')}")
                        
                        calendar_list = data.get('calendar_list', [])
                        print(f"\n📅 找到 {len(calendar_list)} 个日历:")
                        
                        if calendar_list:
                            for i, calendar in enumerate(calendar_list, 1):
                                print(f"\n  {i}. {calendar.get('summary', '未命名日历')}")
                                print(f"     📋 ID: {calendar.get('calendar_id', 'N/A')}")
                                print(f"     👤 角色: {calendar.get('role', 'N/A')}")
                                print(f"     🏷️  类型: {calendar.get('type', 'N/A')}")
                                print(f"     🎨 颜色: {calendar.get('color', 'N/A')}")
                                print(f"     🔒 权限: {calendar.get('permissions', 'N/A')}")
                                
                                if calendar.get('description'):
                                    print(f"     📝 描述: {calendar.get('description')}")
                        else:
                            print("   ⚠️  日历列表为空")
                            
                        # 如果有更多数据，询问是否继续获取
                        if data.get('has_more') and data.get('page_token'):
                            print(f"\n🔄 检测到更多数据，分页令牌: {data.get('page_token')}")
                            try:
                                user_input = input("是否获取下一页数据? (y/n): ").strip().lower()
                                if user_input == 'y':
                                    print("\n" + "="*50)
                                    self.get_calendar_list(page_size, data.get('page_token'))
                            except KeyboardInterrupt:
                                print("\n\n👋 用户取消操作")
                                
                    else:
                        print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ 解析响应失败: {e}")
                    print(f"原始响应: {content[0].get('text', '')}")
            else:
                print("❌ 响应内容为空")
        else:
            error_msg = result.get('error', '未知错误')
            print(f"❌ 工具执行失败: {error_msg}")

def main():
    """主函数"""
    print("🚀 CalMCP 日历测试脚本")
    print("=" * 40)
    
    # 创建客户端
    client = CalMCPClient()
    
    # 1. 健康检查
    print("🏥 检查服务状态...")
    if not client.health_check():
        print("❌ CalMCP 服务未运行，请先启动服务:")
        print("   cd calmcp && npm run dev")
        sys.exit(1)
    
    print("✅ CalMCP 服务运行正常")
    
    # 2. 获取工具列表
    print("\n🔧 获取可用工具...")
    tools_info = client.get_tools()
    if tools_info:
        tools = tools_info.get('data', {}).get('tools', [])
        print(f"✅ 找到 {len(tools)} 个可用工具")
        
        # 检查是否有 calendar_list 工具
        calendar_tool = next((t for t in tools if t.get('name') == 'calendar_list'), None)
        if not calendar_tool:
            print("❌ 未找到 calendar_list 工具")
            sys.exit(1)
        
        print("✅ calendar_list 工具可用")
    else:
        print("❌ 无法获取工具列表")
        sys.exit(1)
    
    # 3. 获取日历列表
    print("\n" + "="*40)
    try:
        client.get_calendar_list()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断操作")
    except Exception as e:
        print(f"\n❌ 执行过程中出错: {e}")
    
    print("\n🏁 测试完成!")

if __name__ == "__main__":
    main()
