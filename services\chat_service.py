"""
聊天服务
处理智能聊天相关的业务逻辑
支持多轮对话和人机协作
"""

import json
import logging
from typing import Any, Dict, List

from core.ai import get_llm
from core.workflow.calendar_workflow import CalendarWorkflowEngine
from core.mcp.working_mcp_client import get_working_mcp_client
from models.chat import ChatRequest, ChatResponse
from models.conversation import ConversationState, conversation_manager
from services.confirmation_service import ConfirmationService
from services.compound_operation_service import get_compound_operation_service
from utils.time_parser import TimeParser

logger = logging.getLogger(__name__)


class ChatService:
    """聊天服务类 - 支持多轮对话和人机协作"""

    def __init__(self):
        """初始化聊天服务"""
        self.llm = get_llm()
        self.time_parser = TimeParser()
        self.confirmation_service = ConfirmationService()
        # 使用新的会话管理器
        self.conversation_manager = conversation_manager
        # 初始化多代理工作流引擎
        self.workflow_engine = CalendarWorkflowEngine()

    async def process_message(self, request: ChatRequest) -> ChatResponse:
        """
        处理聊天消息 - 支持多轮对话和人机协作

        Args:
            request: 聊天请求

        Returns:
            聊天响应
        """
        try:
            user_id = request.user_id or "default_user"
            user_input = request.message

            logger.info(f"处理用户消息: {user_input}")

            # 获取或创建会话上下文（使用固定的session_id）
            session_id = f"session_{user_id}"
            context = self.conversation_manager.get_or_create_conversation(
                user_id, session_id
            )

            # 添加用户消息到历史
            context.add_message("user", user_input)

            # 检查是否在等待用户反馈状态
            logger.info(f"当前会话状态: {context.state}, 用户ID: {context.user_id}")
            should_skip = self.conversation_manager.should_skip_intent_classification(
                context
            )
            logger.info(f"是否跳过意图识别: {should_skip}")

            if should_skip:
                logger.info(f"跳过意图识别，当前状态: {context.state}")
                # 直接处理用户反馈
                response_data = await self._handle_user_feedback(user_input, context)
            else:
                # 正常的意图识别和处理流程
                logger.info("执行正常意图识别流程")
                response_data = await self._handle_normal_flow(user_input, context)

            # 添加助手响应到历史
            context.add_message("assistant", response_data.get("message", ""))

            # 更新会话状态
            if response_data.get("should_clear_state"):
                context.clear_pending_state()
            elif response_data.get("updated_operation"):
                context.pending_operation = response_data["updated_operation"]
                if response_data.get("requires_confirmation"):
                    context.set_waiting_confirmation(
                        response_data["updated_operation"], response_data["message"]
                    )

            # 更新会话管理器
            self.conversation_manager.update_conversation(context)

            return ChatResponse(
                message=response_data.get("message", ""),
                intent=context.current_intent or "chat",
                confidence=context.intent_confidence or 0.0,
                success=response_data.get("success", True),
                data=response_data.get("data"),
                context={
                    "state": context.state.value,
                    "conversation_summary": context.get_conversation_summary(),
                },
            )

        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
            return ChatResponse(
                message=f"抱歉，处理您的请求时遇到问题：{str(e)}",
                intent="error",
                success=False,
            )

    async def process_message_with_agents(self, request: ChatRequest) -> ChatResponse:
        """
        使用多代理工作流处理聊天消息

        Args:
            request: 聊天请求

        Returns:
            聊天响应
        """
        try:
            user_id = request.user_id or "default_user"
            user_input = request.message

            logger.info(f"使用多代理工作流处理用户消息: {user_input}")

            # 获取或创建会话上下文
            session_id = f"session_{user_id}"
            context = self.conversation_manager.get_or_create_conversation(
                user_id, session_id
            )

            # 添加用户消息到历史
            context.add_message("user", user_input)

            # 运行多代理工作流
            workflow_result = await self.workflow_engine.run_workflow(
                user_input=user_input, user_id=user_id, context=context
            )

            # 添加助手响应到历史
            context.add_message("assistant", workflow_result.get("message", ""))

            # 更新会话管理器
            self.conversation_manager.update_conversation(context)

            return ChatResponse(
                message=workflow_result.get("message", ""),
                intent=workflow_result.get("intent", "calendar"),
                confidence=workflow_result.get("confidence", 0.8),
                success=workflow_result.get("success", True),
                data=workflow_result.get("data"),
                context={
                    "state": context.state.value,
                    "conversation_summary": context.get_conversation_summary(),
                    "workflow_status": (
                        self.workflow_engine.get_workflow_status(
                            workflow_result.get("state")
                        )
                        if workflow_result.get("state")
                        else None
                    ),
                },
            )

        except Exception as e:
            logger.error(f"多代理工作流处理消息时发生错误: {str(e)}")
            return ChatResponse(
                message=f"抱歉，处理您的请求时遇到问题：{str(e)}",
                intent="error",
                success=False,
            )

    async def _handle_user_feedback(self, user_input: str, context) -> Dict[str, Any]:
        """处理用户反馈（在等待确认/补充状态下）"""
        logger.info(f"处理用户反馈，当前状态: {context.state}")

        # 使用确认服务处理反馈
        result = await self.confirmation_service.process_user_feedback(
            user_input, context
        )

        return result

    async def _handle_normal_flow(self, user_input: str, context) -> Dict[str, Any]:
        """处理正常流程（意图识别 + 处理）"""
        # 第一步：意图识别
        intent_result = await self._classify_intent(user_input)
        intent = intent_result.get("intent", "chat")
        confidence = intent_result.get("confidence", 0.0)

        logger.info(f"识别意图: {intent} (置信度: {confidence})")

        # 更新上下文
        context.current_intent = intent
        context.intent_confidence = confidence
        context.original_request = user_input

        # 第二步：根据意图处理
        if intent == "calendar":
            response_data = await self._handle_calendar_intent(
                user_input, intent_result, context
            )
        elif intent == "chat":
            response_data = await self._handle_chat_intent(user_input, context)
        else:
            response_data = await self._handle_other_intent(user_input, intent, context)

        return response_data

    async def _classify_intent(self, user_input: str) -> Dict[str, Any]:
        """意图分类"""
        system_prompt = """你是一个智能意图识别助手。请分析用户输入，识别以下意图类型之一：
1. calendar - 日历相关操作（创建、查询、修改、删除日程事件）
2. chat - 日常聊天对话
3. journal - 日记记录相关
4. media - 媒体创作相关

请以JSON格式返回结果：
{
    "intent": "意图类型",
    "confidence": 0.0-1.0的置信度,
    "action": "具体操作类型（如果是calendar）",
    "reasoning": "判断理由"
}"""

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input},
            ]

            response = self.llm.invoke(messages)
            content = response.content.strip()

            # 解析JSON响应
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            result = json.loads(content)
            return result

        except Exception as e:
            logger.error(f"意图分类失败: {e}")
            # 简单的关键词分类作为降级
            if any(
                word in user_input
                for word in [
                    "安排",
                    "会议",
                    "日程",
                    "提醒",
                    "约会",
                    "活动",
                    "明天",
                    "下周",
                ]
            ):
                return {"intent": "calendar", "confidence": 0.7, "action": "unknown"}
            else:
                return {"intent": "chat", "confidence": 0.8}

    async def _handle_calendar_intent(
        self, user_input: str, intent_result: Dict[str, Any], context
    ) -> Dict[str, Any]:
        """处理日历意图 - 集成MCP支持"""
        try:
            # 检查是否为复合操作
            compound_service = get_compound_operation_service()
            if compound_service.is_compound_operation(user_input):
                return await self._handle_compound_operation(user_input, context)

            # 提取日历实体
            entities = await self._extract_calendar_entities(user_input)

            # 解析时间信息
            time_info = self._parse_time_expressions(user_input)

            # 合并实体和时间信息
            operation = {**entities, **time_info}

            # 更新上下文
            context.extracted_entities = entities

            action = operation.get("action", "unknown")
            logger.info(f"识别的日历操作: {action}, 操作详情: {operation}")

            # 检查是否需要确认或补充信息
            missing_fields = self._check_missing_fields(operation)

            if missing_fields:
                # 需要补充信息
                if action in ["create_calendar", "query_calendars", "update_calendar", "delete_calendar"]:
                    message = f"我理解您想要{self._get_action_text(action)}日历。\n"
                else:
                    message = f"我理解您想要{self._get_action_text(action)}日历事件。\n"
                message += f"还需要以下信息：{', '.join(missing_fields)}\n"
                message += "请提供缺失的信息。"

                context.set_waiting_supplement(missing_fields, message)
                context.pending_operation = operation

                return {
                    "success": True,
                    "message": message,
                    "should_clear_state": False,
                    "data": {"action": action, "missing_fields": missing_fields},
                }

            elif action in ["create_calendar", "update_calendar", "delete_calendar", "create_event", "update_event", "delete_event", "create", "update", "delete"]:
                # 需要用户确认的操作
                confirmation_message = self._generate_confirmation_message(operation)

                context.set_waiting_confirmation(operation, confirmation_message)

                return {
                    "success": True,
                    "message": confirmation_message,
                    "should_clear_state": False,
                    "requires_confirmation": True,
                    "updated_operation": operation,
                    "data": {"action": action, "operation": operation},
                }

            elif action == "query_calendars" or action == "query":
                # 查询操作，直接执行MCP调用
                logger.info("执行日历查询操作，调用MCP服务")
                mcp_result = await self._execute_mcp_calendar_operation(operation)

                if mcp_result.get("success"):
                    message = f"✅ {mcp_result.get('message', '查询成功')}\n\n"

                    # 格式化日历数据
                    calendars = mcp_result.get("calendars", [])
                    if calendars:
                        message += "📅 您的日历列表：\n"
                        for i, calendar in enumerate(calendars, 1):  # 显示所有日历
                            summary = calendar.get('summary', '未命名日历')
                            description = calendar.get('description', '')
                            calendar_type = calendar.get('type', '')

                            message += f"{i}. {summary}"
                            if calendar_type == 'primary':
                                message += " (主日历)"
                            message += "\n"

                            if description:
                                message += f"   📄 {description}\n"

                        message += f"\n共 {len(calendars)} 个日历"
                    else:
                        message += "暂无日历数据"
                else:
                    message = f"❌ {mcp_result.get('message', '查询失败')}"

                return {
                    "success": mcp_result.get("success", False),
                    "message": message,
                    "should_clear_state": True,
                    "data": {"action": action, "operation": operation, "mcp_result": mcp_result},
                }

            elif action == "search_events" or action == "search":
                # 搜索操作，直接执行MCP调用
                logger.info("执行日历搜索操作，调用MCP服务")
                mcp_result = await self._execute_mcp_calendar_operation(operation)

                if mcp_result.get("success"):
                    message = f"✅ {mcp_result.get('message', '搜索成功')}\n\n"

                    # 格式化搜索结果
                    events = mcp_result.get("events", [])
                    if events:
                        message += "🔍 搜索结果：\n"
                        for i, event in enumerate(events[:5], 1):  # 最多显示5个
                            message += f"{i}. {event.get('summary', '未命名事件')}\n"
                            if event.get('start_time'):
                                message += f"   时间: {event['start_time']}\n"
                    else:
                        message += "未找到匹配的事件"
                else:
                    message = f"❌ {mcp_result.get('message', '搜索失败')}"

                return {
                    "success": mcp_result.get("success", False),
                    "message": message,
                    "should_clear_state": True,
                    "data": {"action": action, "operation": operation, "mcp_result": mcp_result},
                }

            else:
                message = (
                    f"我理解这是一个日历相关的请求，但需要更多信息来确定具体操作。"
                )
                return {
                    "success": True,
                    "message": message,
                    "should_clear_state": True,
                    "data": {"action": action},
                }

        except Exception as e:
            logger.error(f"处理日历意图失败: {e}")
            return {
                "success": False,
                "message": "抱歉，处理日历请求时遇到问题。请重新描述您的需求。",
                "should_clear_state": True,
            }

    async def _handle_chat_intent(self, user_input: str, context) -> Dict[str, Any]:
        """处理聊天意图"""
        try:
            # 简单的聊天响应
            chat_prompt = f"""你是一个友好的智能助手。用户说："{user_input}"
请给出一个简短、友好的回应。如果用户询问你的功能，请介绍你可以帮助管理日历、安排会议等。"""

            messages = [{"role": "system", "content": chat_prompt}]

            response = self.llm.invoke(messages)

            return {
                "success": True,
                "message": response.content,
                "should_clear_state": True,
                "data": None,
            }

        except Exception as e:
            logger.error(f"处理聊天意图失败: {e}")
            return {
                "success": True,
                "message": "你好！我是您的智能助手，可以帮您管理日历、安排会议等。有什么我可以帮您的吗？",
                "should_clear_state": True,
                "data": None,
            }

    async def _handle_other_intent(
        self, user_input: str, intent: str, context
    ) -> Dict[str, Any]:
        """处理其他意图"""
        return {
            "success": True,
            "message": f"我理解这是一个{intent}相关的请求。目前这个功能还在开发中，请稍后再试。",
            "should_clear_state": True,
            "data": {"intent": intent},
        }

    def _get_action_text(self, action: str) -> str:
        """获取操作文本"""
        action_map = {
            # 日历操作
            "create_calendar": "创建",
            "query_calendars": "查询",
            "update_calendar": "修改",
            "delete_calendar": "删除",
            # 日历事件操作
            "create_event": "创建",
            "query_events": "查询",
            "search_events": "搜索",
            "update_event": "修改",
            "delete_event": "删除",
            # 兼容旧格式
            "create": "创建",
            "update": "修改",
            "delete": "删除",
            "query": "查询",
            "search": "搜索",
        }
        return action_map.get(action, "处理")

    def _check_missing_fields(self, operation: Dict[str, Any]) -> List[str]:
        """检查缺失字段"""
        missing = []

        action = operation.get("action", "")

        # 日历操作
        if action == "create_calendar":
            if not operation.get("title"):
                missing.append("日历名称")
        elif action == "update_calendar":
            if not operation.get("title"):
                missing.append("要修改的日历名称")
        elif action == "delete_calendar":
            if not operation.get("title"):
                missing.append("要删除的日历名称")

        # 日历事件操作
        elif action == "create_event" or action == "create":
            if not operation.get("title"):
                missing.append("事件标题")
            if not operation.get("start_time") and not operation.get("relative_time"):
                missing.append("开始时间")
        elif action == "update_event" or action == "update":
            if not operation.get("title") and not operation.get("start_time"):
                missing.append("要修改的事件标识（标题或时间）")
        elif action == "delete_event" or action == "delete":
            if not operation.get("title") and not operation.get("start_time"):
                missing.append("要删除的事件标识（标题或时间）")

        return missing

    def _generate_confirmation_message(self, operation: Dict[str, Any]) -> str:
        """生成确认消息"""
        action_text = self._get_action_text(operation.get("action", ""))
        action = operation.get("action", "")

        # 根据操作类型确定对象类型
        if action in ["create_calendar", "query_calendars", "update_calendar", "delete_calendar"]:
            object_type = "日历"
        else:
            object_type = "日历事件"

        message = f"请确认{action_text}以下{object_type}：\n"

        if operation.get("title"):
            if object_type == "日历":
                message += f"📅 日历名称：{operation['title']}\n"
            else:
                message += f"📝 事件标题：{operation['title']}\n"

        # 只有事件操作才显示时间信息
        if object_type == "日历事件":
            if operation.get("start_time"):
                message += f"📅 开始时间：{operation['start_time']}\n"
            elif operation.get("relative_time"):
                message += f"📅 时间：{operation['relative_time']}\n"

            if operation.get("time_of_day"):
                message += f"🕐 具体时间：{operation['time_of_day']}\n"

            if operation.get("location"):
                message += f"📍 地点：{operation['location']}\n"

            if operation.get("attendees"):
                message += f"👥 参与者：{', '.join(operation['attendees'])}\n"

            if operation.get("target_calendar"):
                message += f"📂 目标日历：{operation['target_calendar']}\n"

        # 日历操作可能有描述
        if operation.get("description"):
            message += f"📄 描述：{operation['description']}\n"

        message += "\n请回复 '确认' 继续，'取消' 放弃，或提供修改建议。"

        return message

    async def _extract_calendar_entities(self, user_input: str) -> Dict[str, Any]:
        """提取日历实体"""
        system_prompt = """你是一个日历系统操作信息提取助手。请准确识别用户想要操作的对象和操作类型。

## 核心概念区分：
- **日历**：日历容器，用于组织和分类事件（如"工作日历"、"个人日历"）
- **日历事件**：具体的活动安排，存储在某个日历中（如"会议"、"约会"）

## 操作类型分类：

### 日历操作（Calendar Operations）：
1. **create_calendar**：创建新日历
   - 关键词："创建日历"、"新建日历"、"建立日历"
   - 示例："创建日历 我的暑假计划"、"新建工作日历"
   - 必需信息：日历名称

2. **query_calendars**：查询日历列表
   - 关键词："查看日历列表"、"显示所有日历"、"我有哪些日历"
   - 示例："查看我的日历列表"、"显示所有日历"

3. **update_calendar**：修改日历信息
   - 关键词："修改日历"、"更改日历名称"
   - 示例："修改工作日历的名称"、"更改日历描述"

4. **delete_calendar**：删除日历
   - 关键词："删除日历"、"移除日历"
   - 示例："删除测试日历"、"移除旧的工作日历"

### 日历事件操作（Calendar Event Operations）：
1. **create_event**：创建日历事件
   - 关键词："安排"、"预定"、"约会"、"会议"、"活动"、"添加事件"
   - 示例："明天下午2点安排会议"、"在工作日历中添加项目评审"
   - 必需信息：事件标题、开始时间
   - 可选信息：目标日历、结束时间、地点、参与者

2. **query_events**：查询日历事件
   - 关键词："查看事件"、"显示今天的安排"、"我今天有什么"
   - 示例："查看今天的事件"、"显示本周安排"

3. **search_events**：搜索日历事件
   - 关键词："搜索"、"查找"、"找事件"
   - 示例："搜索项目会议"、"查找本周的重要事件"

4. **update_event**：修改日历事件
   - 关键词："修改事件"、"更改会议时间"、"调整安排"
   - 示例："修改明天的会议时间"、"更改约会地点"

5. **delete_event**：删除日历事件
   - 关键词："取消"、"删除事件"、"移除安排"
   - 示例："取消周五的约会"、"删除明天的会议"

## 识别要点：
- 明确区分操作对象：是日历容器还是日历事件
- 创建事件时，如果用户指定了日历，提取目标日历信息
- 时间信息只对事件操作有意义，日历操作不需要时间
- **复合操作处理**：如果用户提到多个操作（如"创建日历然后创建事件"），优先识别第一个操作，并在描述中记录后续操作

请以JSON格式返回：
{
    "action": "操作类型(create_calendar/query_calendars/update_calendar/delete_calendar/create_event/query_events/search_events/update_event/delete_event)",
    "title": "日历名称或事件标题",
    "target_calendar": "目标日历名称（仅事件操作需要，如果用户指定）",
    "start_time": "开始时间描述（仅事件操作需要）",
    "end_time": "结束时间描述（可选）",
    "location": "地点（可选）",
    "attendees": ["参与者列表（可选）"],
    "description": "描述信息（可选）",
    "has_follow_up": "是否有后续操作（true/false）",
    "follow_up_description": "后续操作描述（如果有）",
    "confidence": 0.0-1.0的提取置信度
}"""

        try:
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input},
            ]

            response = self.llm.invoke(messages)
            content = response.content.strip()

            # 解析JSON响应
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            result = json.loads(content)
            return result

        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            # 简单的关键词提取作为降级
            entities = {"action": "unknown", "title": "事件", "confidence": 0.5}

            if any(word in user_input for word in ["创建", "安排", "添加", "新建"]):
                entities["action"] = "create"
            elif any(word in user_input for word in ["查询", "查看", "显示", "列出"]):
                entities["action"] = "query"
            elif any(word in user_input for word in ["修改", "更新", "改变"]):
                entities["action"] = "update"
            elif any(word in user_input for word in ["删除", "取消", "移除"]):
                entities["action"] = "delete"

            return entities

    def _parse_time_expressions(self, user_input: str) -> Dict[str, Any]:
        """解析时间表达式"""
        try:
            result = {}

            # 使用增强的时间解析器
            relative_time = self.time_parser.parse_relative_time(user_input)
            if relative_time:
                result["relative_time"] = relative_time.isoformat()

            time_of_day = self.time_parser.parse_time_of_day(user_input)
            if time_of_day:
                result["time_of_day"] = f"{time_of_day[0]:02d}:{time_of_day[1]:02d}"

            duration = self.time_parser.parse_duration(user_input)
            if duration:
                result["duration_minutes"] = duration

            recurring = self.time_parser.parse_recurring_pattern(user_input)
            if recurring:
                result["recurring_pattern"] = recurring

            return result

        except Exception as e:
            logger.error(f"时间解析失败: {e}")
            return {}
            result = self.workflow_engine.run(initial_state, start_node="text_input")

            # 保存会话状态
            self.user_sessions[user_id] = {
                "context_state": result.get("context_state"),
                "intent": result.get("intent"),
                "last_message": request.message,
            }

            # 构建响应
            response = ChatResponse(
                message=result.get("message", "抱歉，我无法理解您的请求"),
                intent=result.get("intent", "unknown"),
                data=result.get("data", {}),
                success=result.get("error") is None,
            )

            return response

        except Exception as e:
            logger.error(f"处理聊天消息时发生错误: {str(e)}")
            return ChatResponse(
                message=f"处理请求时发生错误: {str(e)}", intent="error", success=False
            )

    async def _handle_compound_operation(self, user_input: str, context) -> Dict[str, Any]:
        """处理复合操作"""
        try:
            compound_service = get_compound_operation_service()

            # 分析复合操作
            compound_analysis = await compound_service.analyze_compound_operation(user_input)
            extracted_info = await compound_service.extract_calendar_and_event_info(user_input)

            logger.info(f"复合操作分析: {compound_analysis}")
            logger.info(f"提取信息: {extracted_info}")

            if extracted_info.get("has_both"):
                # 有日历和事件信息，先创建日历
                calendar_operation = {
                    "action": "create_calendar",
                    "title": extracted_info["calendar_name"],
                    "description": f"为事件 '{extracted_info['event_title']}' 创建的日历"
                }

                # 设置复合操作状态
                context.set_compound_operation({
                    "current_step": 1,
                    "total_steps": 2,
                    "step_1": calendar_operation,
                    "step_2": {
                        "action": "create_event",
                        "title": extracted_info["event_title"],
                        "target_calendar": extracted_info["calendar_name"],
                        "start_time": extracted_info["time_info"],
                        "description": "复合操作中的事件"
                    }
                })

                # 生成确认消息
                message = f"我理解您想要执行以下操作：\n\n"
                message += f"步骤1: 创建日历 '{extracted_info['calendar_name']}'\n"
                message += f"步骤2: 在该日历中创建事件 '{extracted_info['event_title']}'"
                if extracted_info["time_info"]:
                    message += f" ({extracted_info['time_info']})"
                message += "\n\n请回复 '确认' 继续执行，'取消' 放弃操作。"

                context.set_waiting_confirmation(calendar_operation, message)

                return {
                    "success": True,
                    "message": message,
                    "should_clear_state": False,
                    "data": {"action": "compound_operation", "steps": 2}
                }

            else:
                # 回退到普通处理
                return {
                    "success": False,
                    "message": "无法解析复合操作，请分别执行各个步骤。",
                    "should_clear_state": True
                }

        except Exception as e:
            logger.error(f"复合操作处理失败: {e}")
            return {
                "success": False,
                "message": f"复合操作处理失败: {str(e)}",
                "should_clear_state": True
            }

    async def _execute_mcp_calendar_operation(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """执行MCP日历操作"""
        try:
            adapter = get_working_mcp_client()
            action = operation.get("action")

            logger.info(f"执行MCP操作: {action}")

            if action == "create_calendar":
                # 创建日历
                calendar_data = {
                    "summary": operation.get("title", "新日历"),
                    "description": operation.get("description", "")
                }

                # 调用创建日历的MCP工具
                result = await adapter.create_calendar(calendar_data)
                return result

            elif action == "create_event" or action == "create":
                # 创建事件
                event_data = {
                    "title": operation.get("title", operation.get("summary", "新事件")),
                    "description": operation.get("description", ""),
                    "start_time": operation.get("start_time", ""),
                    "end_time": operation.get("end_time", ""),
                    "location": operation.get("location", "")
                }

                # 检查必要字段
                if not event_data["start_time"]:
                    return {
                        "success": False,
                        "message": "缺少开始时间信息",
                        "need_more_info": True
                    }

                result = await adapter.create_calendar_event(event_data)
                return result

            elif action == "query_calendars" or action == "query":
                # 查询日历列表
                result = await adapter.list_calendars()
                return result

            elif action == "update_calendar":
                # 更新日历
                calendar_data = {
                    "calendar_name": operation.get("title", ""),
                    "new_summary": operation.get("new_title", operation.get("title", "")),
                    "description": operation.get("description", "")
                }
                result = await adapter.update_calendar(calendar_data)
                return result

            elif action == "delete_calendar":
                # 删除日历
                calendar_name = operation.get("title", "")
                result = await adapter.delete_calendar(calendar_name)
                return result

            elif action == "query_events":
                # 查询日历事件
                calendar_name = operation.get("target_calendar", "")
                result = await adapter.list_calendar_events(calendar_name)
                return result

            elif action == "search_events" or action == "search":
                # 搜索事件
                query = operation.get("title", operation.get("description", operation.get("query", "")))
                if not query:
                    return {
                        "success": False,
                        "message": "缺少搜索关键词",
                        "need_more_info": True
                    }

                start_time = operation.get("start_time")
                end_time = operation.get("end_time")
                result = await adapter.search_calendar_events(query, start_time, end_time)
                return result

            else:
                return {
                    "success": False,
                    "message": f"暂不支持的操作类型: {action}",
                    "need_more_info": False
                }

        except Exception as e:
            logger.error(f"执行MCP操作失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"执行操作时出现错误: {str(e)}"
            }
