#!/usr/bin/env python3
"""
获取真实用户token
通过OAuth授权流程获取当前用户的访问令牌
"""

import sys
import os
import webbrowser
import urllib.parse
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")

# 导入项目模块
try:
    from integrations.feishu import get_feishu_client
    from integrations.storage import save_token, get_all_tokens
    print("✅ 项目模块导入成功")
except ImportError as e:
    print(f"❌ 项目模块导入失败: {e}")
    sys.exit(1)


class UserTokenGetter:
    """获取用户token的工具"""
    
    def __init__(self):
        self.app_id = os.environ.get('FEISHU_CLIENT_ID')
        self.app_secret = os.environ.get('FEISHU_CLIENT_SECRET')
        self.redirect_uri = "http://localhost:5000/auth/callback"  # 使用项目的回调地址
        
        if not self.app_id or not self.app_secret:
            print("❌ 未找到飞书应用凭据")
            print("💡 请确保设置了以下环境变量:")
            print("   - FEISHU_CLIENT_ID")
            print("   - FEISHU_CLIENT_SECRET")
            return
        
        print(f"✅ 飞书应用配置:")
        print(f"   应用ID: {self.app_id}")
        print(f"   回调地址: {self.redirect_uri}")
        
        # 创建飞书客户端
        self.feishu_client = get_feishu_client()
    
    def start_oauth_flow(self):
        """启动OAuth授权流程"""
        print("\n🚀 启动OAuth授权流程")
        print("="*60)
        
        # 构建授权URL
        auth_url = f"https://open.feishu.cn/open-apis/authen/v1/index"
        params = {
            "app_id": self.app_id,
            "redirect_uri": self.redirect_uri,
            "response_type": "code",
            "scope": "calendar:all",  # 请求日历权限
            "state": f"auth_{int(__import__('time').time())}"
        }
        
        full_auth_url = f"{auth_url}?{urllib.parse.urlencode(params)}"
        
        print(f"📋 授权URL: {full_auth_url}")
        print("\n📝 操作步骤:")
        print("1. 点击上面的URL或复制到浏览器打开")
        print("2. 使用你的飞书账号登录并授权")
        print("3. 授权后会跳转到回调页面")
        print("4. 从回调URL中复制 'code' 参数的值")
        print("5. 将code值粘贴到下面的输入框中")
        
        # 尝试自动打开浏览器
        try:
            webbrowser.open(full_auth_url)
            print("\n✅ 已自动打开浏览器")
        except:
            print("\n⚠️  无法自动打开浏览器，请手动复制URL")
        
        print("\n" + "="*60)
        
        # 等待用户输入授权码
        while True:
            try:
                code = input("请输入授权码 (code): ").strip()
                if code:
                    return code
                else:
                    print("❌ 授权码不能为空，请重新输入")
            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                return None
    
    async def exchange_code_for_token(self, code: str):
        """用授权码换取访问令牌"""
        print(f"\n🔄 用授权码换取访问令牌...")
        print(f"   授权码: {code[:10]}...")
        
        try:
            # 调用飞书API换取token
            result = await self.feishu_client.exchange_code(code)
            
            print(f"📊 API响应: {result.get('code')} - {result.get('msg')}")
            
            if result.get("code") != 0:
                print(f"❌ 换取token失败: {result.get('msg')}")
                return None
            
            data = result.get("data")
            if not data:
                print(f"❌ 响应数据为空")
                return None
            
            access_token = data.get("access_token")
            refresh_token = data.get("refresh_token")
            expires_in = data.get("expires_in")
            
            print(f"✅ 成功获取访问令牌:")
            print(f"   访问令牌: {access_token[:20]}...")
            print(f"   刷新令牌: {refresh_token[:20]}...")
            print(f"   有效期: {expires_in} 秒")
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "expires_in": expires_in
            }
            
        except Exception as e:
            print(f"❌ 换取token时出现异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def get_user_info(self, access_token: str):
        """获取用户信息"""
        print(f"\n👤 获取用户信息...")
        
        try:
            user_info = await self.feishu_client.get_user_info(access_token)
            
            print(f"📊 API响应: {user_info.get('code')} - {user_info.get('msg')}")
            
            if user_info.get("code") != 0:
                print(f"❌ 获取用户信息失败: {user_info.get('msg')}")
                return None
            
            data = user_info.get("data")
            if not data:
                print(f"❌ 用户信息为空")
                return None
            
            user_id = data.get("open_id")
            name = data.get("name", "未知用户")
            
            print(f"✅ 用户信息:")
            print(f"   用户ID: {user_id}")
            print(f"   用户名: {name}")
            
            return {
                "user_id": user_id,
                "name": name,
                "data": data
            }
            
        except Exception as e:
            print(f"❌ 获取用户信息时出现异常: {e}")
            return None
    
    def save_user_token(self, user_id: str, token_data: Dict[str, Any]):
        """保存用户token到存储"""
        print(f"\n💾 保存用户token...")
        
        try:
            import time
            expire_ts = int(time.time()) + token_data["expires_in"]
            
            save_data = {
                "access_token": token_data["access_token"],
                "refresh_token": token_data["refresh_token"],
                "access_token_expire": expire_ts,
            }
            
            save_token(user_id, save_data)
            
            print(f"✅ Token已保存到存储")
            print(f"   用户ID: {user_id}")
            print(f"   过期时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(expire_ts))}")
            
            return True
            
        except Exception as e:
            print(f"❌ 保存token时出现异常: {e}")
            return False
    
    def update_env_file(self, user_id: str, access_token: str):
        """更新环境变量文件"""
        print(f"\n📝 更新环境变量文件...")
        
        try:
            env_file = os.path.join(project_root, '.env.local')
            
            # 读取现有内容
            lines = []
            if os.path.exists(env_file):
                with open(env_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
            
            # 更新或添加新的token
            updated = False
            for i, line in enumerate(lines):
                if line.startswith('TEST_ACCESS_TOKEN='):
                    lines[i] = f"TEST_ACCESS_TOKEN={access_token}\n"
                    updated = True
                elif line.startswith('TEST_USER_ID='):
                    lines[i] = f"TEST_USER_ID={user_id}\n"
            
            # 如果没有找到，添加新行
            if not updated:
                lines.append(f"\n# 真实用户token\n")
                lines.append(f"TEST_ACCESS_TOKEN={access_token}\n")
                lines.append(f"TEST_USER_ID={user_id}\n")
            
            # 写回文件
            with open(env_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            print(f"✅ 环境变量文件已更新: {env_file}")
            print(f"   TEST_ACCESS_TOKEN: {access_token[:20]}...")
            print(f"   TEST_USER_ID: {user_id}")
            
            return True
            
        except Exception as e:
            print(f"❌ 更新环境变量文件时出现异常: {e}")
            return False
    
    async def run(self):
        """运行完整流程"""
        print("🎯 获取真实用户token")
        print("="*60)
        
        # 1. 启动OAuth流程
        code = self.start_oauth_flow()
        if not code:
            return
        
        # 2. 换取token
        token_data = await self.exchange_code_for_token(code)
        if not token_data:
            return
        
        # 3. 获取用户信息
        user_info = await self.get_user_info(token_data["access_token"])
        if not user_info:
            return
        
        # 4. 保存到存储
        if self.save_user_token(user_info["user_id"], token_data):
            # 5. 更新环境变量文件
            self.update_env_file(user_info["user_id"], token_data["access_token"])
        
        print(f"\n🎉 完成！")
        print("="*60)
        print(f"✅ 已获取并保存你的真实用户token")
        print(f"✅ 现在可以重新运行日历测试脚本")
        print(f"💡 建议运行: python test_direct_feishu_api.py")


async def main():
    """主函数"""
    getter = UserTokenGetter()
    await getter.run()


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
