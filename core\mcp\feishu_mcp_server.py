#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书MCP服务器
基于现有飞书日历API实现的MCP服务器，使用streamable模式
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Sequence

from mcp.server import Server, NotificationOptions
from mcp.server.models import InitializationOptions
from mcp.server.stdio import stdio_server
from mcp.types import (
    CallToolRequest,
    CallToolResult,
    ListToolsRequest,
    TextContent,
    Tool,
)
from pydantic import BaseModel, Field

# 导入现有的飞书日历服务
from integrations.feishu import (
    get_calendars,
    get_calendar_events,
    create_event,
    update_event,
    delete_event,
    get_today_events,
    FeishuCalendarLark,
)
from models.calendar import Calendar, CalendarEvent, TimeInfo, Location, Attendee
from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("feishu_mcp_server")


class FeishuMCPServer:
    """飞书MCP服务器"""
    
    def __init__(self):
        self.server = Server("feishu-calendar")
        self.calendar_client = FeishuCalendarLark(FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET)
        self._setup_tools()
    
    def _setup_tools(self):
        """设置MCP工具"""
        
        # 注册工具处理器
        @self.server.list_tools()
        async def handle_list_tools() -> List[Tool]:
            """列出所有可用的工具"""
            return [
                Tool(
                    name="list_calendars",
                    description="获取用户的日历列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID，如果不提供则使用默认用户"
                            }
                        }
                    }
                ),
                Tool(
                    name="get_calendar_events",
                    description="获取指定日历的事件列表",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "calendar_id": {
                                "type": "string",
                                "description": "日历ID，默认为primary",
                                "default": "primary"
                            },
                            "start_time": {
                                "type": "string",
                                "description": "开始时间 (ISO格式)"
                            },
                            "end_time": {
                                "type": "string",
                                "description": "结束时间 (ISO格式)"
                            }
                        },
                        "required": ["user_id"]
                    }
                ),
                Tool(
                    name="create_calendar_event",
                    description="创建新的日历事件",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "calendar_id": {
                                "type": "string",
                                "description": "日历ID，默认为primary",
                                "default": "primary"
                            },
                            "title": {
                                "type": "string",
                                "description": "事件标题"
                            },
                            "description": {
                                "type": "string",
                                "description": "事件描述"
                            },
                            "start_time": {
                                "type": "string",
                                "description": "开始时间 (ISO格式)"
                            },
                            "end_time": {
                                "type": "string",
                                "description": "结束时间 (ISO格式)"
                            },
                            "location": {
                                "type": "string",
                                "description": "事件地点"
                            },
                            "attendees": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "参与者邮箱列表"
                            }
                        },
                        "required": ["user_id", "title", "start_time", "end_time"]
                    }
                ),
                Tool(
                    name="update_calendar_event",
                    description="更新现有的日历事件",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "event_id": {
                                "type": "string",
                                "description": "事件ID"
                            },
                            "calendar_id": {
                                "type": "string",
                                "description": "日历ID"
                            },
                            "title": {
                                "type": "string",
                                "description": "事件标题"
                            },
                            "description": {
                                "type": "string",
                                "description": "事件描述"
                            },
                            "start_time": {
                                "type": "string",
                                "description": "开始时间 (ISO格式)"
                            },
                            "end_time": {
                                "type": "string",
                                "description": "结束时间 (ISO格式)"
                            },
                            "location": {
                                "type": "string",
                                "description": "事件地点"
                            }
                        },
                        "required": ["user_id", "event_id"]
                    }
                ),
                Tool(
                    name="delete_calendar_event",
                    description="删除日历事件",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            },
                            "event_id": {
                                "type": "string",
                                "description": "事件ID"
                            },
                            "calendar_id": {
                                "type": "string",
                                "description": "日历ID"
                            }
                        },
                        "required": ["user_id", "event_id"]
                    }
                ),
                Tool(
                    name="get_today_events",
                    description="获取今天的所有事件",
                    inputSchema={
                        "type": "object",
                        "properties": {
                            "user_id": {
                                "type": "string",
                                "description": "用户ID"
                            }
                        },
                        "required": ["user_id"]
                    }
                )
            ]
        
        @self.server.call_tool()
        async def handle_call_tool(name: str, arguments: Dict[str, Any]) -> List[TextContent]:
            """处理工具调用"""
            try:
                logger.info(f"调用工具: {name}, 参数: {arguments}")
                
                if name == "list_calendars":
                    return await self._handle_list_calendars(arguments)
                elif name == "get_calendar_events":
                    return await self._handle_get_calendar_events(arguments)
                elif name == "create_calendar_event":
                    return await self._handle_create_calendar_event(arguments)
                elif name == "update_calendar_event":
                    return await self._handle_update_calendar_event(arguments)
                elif name == "delete_calendar_event":
                    return await self._handle_delete_calendar_event(arguments)
                elif name == "get_today_events":
                    return await self._handle_get_today_events(arguments)
                else:
                    return [TextContent(
                        type="text",
                        text=f"未知工具: {name}"
                    )]
                    
            except Exception as e:
                logger.error(f"工具调用失败: {str(e)}")
                return [TextContent(
                    type="text",
                    text=f"工具调用失败: {str(e)}"
                )]
    
    async def _handle_list_calendars(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理获取日历列表"""
        user_id = arguments.get("user_id", "default_user")

        try:
            # 使用FeishuCalendarLark的get_calendars方法
            result = self.calendar_client.get_calendars(user_id)

            if result.get("code") == 0:
                calendars = []
                for cal_data in result.get("data", {}).get("calendar_list", []):
                    calendars.append({
                        "id": cal_data.get("calendar_id"),
                        "name": cal_data.get("summary", ""),
                        "description": cal_data.get("description", ""),
                        "color": cal_data.get("color", "#1976d2"),
                        "permissions": cal_data.get("permissions", "private")
                    })

                response = {
                    "success": True,
                    "calendars": calendars
                }
            else:
                response = {
                    "success": False,
                    "error": result.get("msg", "获取日历列表失败")
                }

            return [TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"获取日历列表失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]
    
    async def _handle_get_calendar_events(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理获取日历事件"""
        user_id = arguments["user_id"]
        calendar_id = arguments.get("calendar_id", "primary")
        start_time = arguments.get("start_time")
        end_time = arguments.get("end_time")

        try:
            # 使用FeishuCalendarLark的get_calendar_events方法
            result = self.calendar_client.get_calendar_events(
                user_id=user_id,
                calendar_id=calendar_id,
                start_time=start_time,
                end_time=end_time
            )

            if result.get("code") == 0:
                events = []
                for event_data in result.get("data", {}).get("items", []):
                    events.append({
                        "id": event_data.get("event_id"),
                        "title": event_data.get("summary", ""),
                        "description": event_data.get("description", ""),
                        "start_time": event_data.get("start_time", {}).get("date_time") or event_data.get("start_time", {}).get("timestamp"),
                        "end_time": event_data.get("end_time", {}).get("date_time") or event_data.get("end_time", {}).get("timestamp"),
                        "location": event_data.get("location", {}).get("name", ""),
                        "attendees": [att.get("user_id", "") for att in event_data.get("attendees", [])]
                    })

                response = {
                    "success": True,
                    "events": events
                }
            else:
                response = {
                    "success": False,
                    "error": result.get("msg", "获取日历事件失败")
                }

            return [TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"获取日历事件失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]

    async def _handle_create_calendar_event(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理创建日历事件"""
        user_id = arguments["user_id"]
        calendar_id = arguments.get("calendar_id", "primary")

        try:
            # 准备事件数据
            start_time = {"date_time": arguments["start_time"]}
            end_time = {"date_time": arguments["end_time"]}

            location = None
            if arguments.get("location"):
                location = {"name": arguments["location"]}

            attendees = []
            for attendee_email in arguments.get("attendees", []):
                # 注意：飞书API需要user_id而不是email
                attendees.append({"user_id": attendee_email})

            # 使用FeishuCalendarLark的create_event方法
            result = self.calendar_client.create_event(
                user_id=user_id,
                calendar_id=calendar_id,
                summary=arguments["title"],
                start_time=start_time,
                end_time=end_time,
                description=arguments.get("description", ""),
                location=location,
                attendees=attendees if attendees else None
            )

            if result.get("code") == 0:
                response = {
                    "success": True,
                    "message": "事件创建成功",
                    "event_id": result.get("data", {}).get("event", {}).get("event_id")
                }
            else:
                response = {
                    "success": False,
                    "error": result.get("msg", "创建事件失败")
                }

            return [TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"创建日历事件失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]

    async def _handle_update_calendar_event(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理更新日历事件"""
        user_id = arguments["user_id"]
        event_id = arguments["event_id"]
        calendar_id = arguments.get("calendar_id", "primary")

        try:
            # 准备更新数据
            update_data = {}
            if "title" in arguments:
                update_data["summary"] = arguments["title"]
            if "description" in arguments:
                update_data["description"] = arguments["description"]
            if "start_time" in arguments:
                update_data["start_time"] = {"date_time": arguments["start_time"]}
            if "end_time" in arguments:
                update_data["end_time"] = {"date_time": arguments["end_time"]}
            if "location" in arguments:
                update_data["location"] = {"name": arguments["location"]}

            # 使用FeishuCalendarLark的update_event方法
            result = self.calendar_client.update_event(
                user_id=user_id,
                calendar_id=calendar_id,
                event_id=event_id,
                **update_data
            )

            if result.get("code") == 0:
                response = {
                    "success": True,
                    "message": "事件更新成功"
                }
            else:
                response = {
                    "success": False,
                    "error": result.get("msg", "更新事件失败")
                }

            return [TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"更新日历事件失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]

    async def _handle_delete_calendar_event(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理删除日历事件"""
        user_id = arguments["user_id"]
        event_id = arguments["event_id"]
        calendar_id = arguments.get("calendar_id", "primary")

        try:
            # 使用FeishuCalendarLark的delete_event方法
            result = self.calendar_client.delete_event(
                user_id=user_id,
                calendar_id=calendar_id,
                event_id=event_id
            )

            if result.get("code") == 0:
                response = {
                    "success": True,
                    "message": "事件删除成功"
                }
            else:
                response = {
                    "success": False,
                    "error": result.get("msg", "删除事件失败")
                }

            return [TextContent(
                type="text",
                text=json.dumps(response, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"删除日历事件失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]

    async def _handle_get_today_events(self, arguments: Dict[str, Any]) -> List[TextContent]:
        """处理获取今天的事件"""
        user_id = arguments["user_id"]

        try:
            # 使用现有的get_today_events函数
            result = await get_today_events(user_id)

            return [TextContent(
                type="text",
                text=json.dumps(result, ensure_ascii=False, indent=2)
            )]

        except Exception as e:
            logger.error(f"获取今天事件失败: {str(e)}")
            return [TextContent(
                type="text",
                text=json.dumps({
                    "success": False,
                    "error": str(e)
                }, ensure_ascii=False)
            )]

    async def run(self):
        """运行MCP服务器"""
        logger.info("启动飞书MCP服务器...")

        try:
            # 使用stdio传输
            async with stdio_server() as (read_stream, write_stream):
                notification_options = NotificationOptions()
                initialization_options = InitializationOptions(
                    server_name="feishu-calendar",
                    server_version="1.0.0",
                    capabilities=self.server.get_capabilities(
                        notification_options=notification_options,
                        experimental_capabilities={},
                    ),
                )

                await self.server.run(
                    read_stream,
                    write_stream,
                    initialization_options,
                    raise_exceptions=True
                )
        except Exception as e:
            logger.error(f"MCP服务器运行失败: {str(e)}")
            raise


async def main():
    """主函数"""
    server = FeishuMCPServer()
    await server.run()


if __name__ == "__main__":
    asyncio.run(main())
