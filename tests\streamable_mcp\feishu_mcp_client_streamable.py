#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书MCP客户端 - Streamable HTTP模式
用于连接HTTP模式的MCP服务器
"""

import json
import logging
import asyncio
import aiohttp
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
import sys
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("feishu_mcp_client_streamable")


class StreamableMCPClient:
    """Streamable HTTP模式的MCP客户端"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url.rstrip('/')
        self.session_id = None
        self.session = None
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        # 配置连接器以支持连接复用和超时
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "Feishu-MCP-Client/1.0.0"}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def _make_request(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """发送HTTP请求"""
        if not self.session:
            raise RuntimeError("客户端未初始化，请使用 async with 语句")
        
        url = f"{self.base_url}{endpoint}"
        headers = {"Content-Type": "application/json"}
        
        try:
            # 增加超时设置和重试机制
            timeout = aiohttp.ClientTimeout(total=30, connect=10)
            async with self.session.post(url, json=data, headers=headers, timeout=timeout) as response:
                if response.status != 200:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                
                result = await response.json()
                return result
        except asyncio.TimeoutError:
            logger.error(f"请求超时: {url}")
            raise Exception(f"请求超时: {url}")
        except aiohttp.ClientError as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"请求失败: {str(e)}")
            raise
    
    async def initialize(self) -> Dict[str, Any]:
        """初始化MCP连接"""
        logger.info("🔗 初始化MCP连接...")
        
        request_data = {
            "jsonrpc": "2.0",
            "id": "init_1",
            "method": "initialize",
            "params": {
                "protocolVersion": "2024-11-05",
                "capabilities": {
                    "tools": {}
                },
                "clientInfo": {
                    "name": "feishu-mcp-client-streamable",
                    "version": "1.0.0"
                }
            }
        }
        
        result = await self._make_request("/mcp/initialize", request_data)
        logger.info("✅ MCP连接初始化成功")
        return result
    
    async def list_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        logger.info("📋 获取可用工具...")
        
        request_data = {
            "jsonrpc": "2.0",
            "id": "tools_1",
            "method": "tools/list"
        }
        
        result = await self._make_request("/mcp/tools/list", request_data)
        
        if result.get("result", {}).get("tools"):
            tools = result["result"]["tools"]
            logger.info(f"✅ 发现 {len(tools)} 个工具:")
            for i, tool in enumerate(tools, 1):
                logger.info(f"  {i}. {tool['name']}: {tool['description'].split('Args:')[0].strip()}")
        
        return result
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        logger.info(f"🛠️ 调用工具: {name}")
        
        request_data = {
            "jsonrpc": "2.0",
            "id": f"call_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            "method": "tools/call",
            "params": {
                "name": name,
                "arguments": arguments
            }
        }
        
        result = await self._make_request("/mcp/tools/call", request_data)
        return result
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        if not self.session:
            raise RuntimeError("客户端未初始化")
        
        try:
            async with self.session.get(f"{self.base_url}/health") as response:
                if response.status == 200:
                    return await response.json()
                else:
                    raise Exception(f"健康检查失败: HTTP {response.status}")
        except Exception as e:
            logger.error(f"健康检查失败: {str(e)}")
            raise


# 测试代码已移除，保持代码整洁 