# CalMCP 集成指南

## 概述

CalMCP 是一个独立的 MCP 服务，可以被主项目的 LLM 调用。本文档说明如何将 CalMCP 集成到现有的飞书日历项目中。

## 架构图

```
主项目 (feishu-coze-plugin)
├── LLM 服务 (Python)
│   ├── 意图识别
│   ├── 实体提取
│   └── MCP 客户端 ──────┐
│                        │
│                        ▼
CalMCP 服务 (Node.js)    │
├── Next.js Web 界面     │
├── MCP 服务器 ◄─────────┘
├── 飞书 API 客户端
└── 流式处理器
```

## 集成步骤

### 1. 启动 CalMCP 服务

```bash
cd calmcp
npm install
npm run dev  # 开发模式
# 或
npm run build && npm start  # 生产模式
```

服务将在以下端口启动：
- Next.js 应用: `http://localhost:3001`
- MCP 服务器: `http://localhost:3002` (如果使用独立模式)

### 2. 在主项目中配置 MCP 客户端

在主项目的 `config.py` 中添加：

```python
# MCP 配置
MCP_SERVER_URL = "http://localhost:3001"
MCP_TIMEOUT = 30
MCP_STREAM_MODE = True  # 启用流式模式
```

### 3. 创建 MCP 客户端

在主项目中创建 `core/mcp/calmcp_client.py`：

```python
import httpx
import json
import asyncio
from typing import Dict, Any, AsyncGenerator

class CalMCPClient:
    def __init__(self, base_url: str = "http://localhost:3001"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)
    
    async def call_tool(self, name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """标准工具调用"""
        response = await self.client.post(
            f"{self.base_url}/api/mcp/tools/call",
            json={"name": name, "arguments": arguments}
        )
        response.raise_for_status()
        return response.json()
    
    async def stream_tool(self, name: str, arguments: Dict[str, Any]) -> AsyncGenerator[Dict[str, Any], None]:
        """流式工具调用"""
        async with self.client.stream(
            "POST",
            f"{self.base_url}/api/mcp/tools/stream",
            json={"name": name, "arguments": arguments}
        ) as response:
            response.raise_for_status()
            
            async for line in response.aiter_lines():
                if line.startswith("data: "):
                    try:
                        data = json.loads(line[6:])
                        yield data
                    except json.JSONDecodeError:
                        continue
    
    async def get_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        response = await self.client.get(f"{self.base_url}/api/mcp/tools")
        response.raise_for_status()
        return response.json()
    
    async def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        response = await self.client.get(f"{self.base_url}/api/health")
        response.raise_for_status()
        return response.json()
    
    async def close(self):
        """关闭客户端"""
        await self.client.aclose()
```

### 4. 在工作流中使用

在主项目的工作流节点中集成 CalMCP：

```python
# core/workflow/nodes.py

from core.mcp.calmcp_client import CalMCPClient

async def calendar_processor_node(state: Dict[str, Any]) -> Dict[str, Any]:
    """日历处理器节点"""
    intent = state.get("intent")
    entities = state.get("extracted_entities", {})
    
    # 初始化 CalMCP 客户端
    mcp_client = CalMCPClient()
    
    try:
        # 根据意图选择工具
        if intent == "create_event":
            result = await mcp_client.call_tool("calendar_event_create", {
                "calendar_id": entities.get("calendar_id"),
                "summary": entities.get("title"),
                "start_time": entities.get("start_time"),
                "end_time": entities.get("end_time"),
                "description": entities.get("description"),
                "location": entities.get("location")
            })
        
        elif intent == "search_events":
            # 使用流式模式处理大量数据
            events = []
            async for chunk in mcp_client.stream_tool("calendar_event_search", {
                "calendar_id": entities.get("calendar_id"),
                "query": entities.get("query"),
                "start_time": entities.get("start_time"),
                "end_time": entities.get("end_time")
            }):
                if chunk.get("type") == "data":
                    events.append(chunk.get("data"))
            
            result = {"success": True, "events": events}
        
        elif intent == "list_calendars":
            result = await mcp_client.call_tool("calendar_list", {
                "page_size": 20
            })
        
        else:
            result = {"success": False, "error": f"Unknown intent: {intent}"}
        
        return {
            "mcp_result": result,
            "success": result.get("success", False)
        }
    
    finally:
        await mcp_client.close()
```

### 5. 配置环境变量

在主项目的 `.env.local` 中添加：

```env
# CalMCP 配置
CALMCP_URL=http://localhost:3001
CALMCP_TIMEOUT=30
CALMCP_STREAM_MODE=true
```

在 CalMCP 的 `.env.local` 中配置：

```env
# 飞书应用配置（与主项目相同）
FEISHU_APP_ID=cli_a76a68f612bf900c
FEISHU_APP_SECRET=your_app_secret_here

# MCP 服务配置
MCP_SERVER_PORT=3002
NEXT_PUBLIC_API_URL=http://localhost:3001
```

## 部署配置

### Docker Compose 集成

创建 `docker-compose.integrated.yml`：

```yaml
version: '3.8'

services:
  # 主项目服务
  feishu-main:
    build: ../  # 主项目目录
    ports:
      - "5000:5000"
    environment:
      - CALMCP_URL=http://calmcp:3001
    depends_on:
      - calmcp
    networks:
      - feishu-network

  # CalMCP 服务
  calmcp:
    build: .
    ports:
      - "3001:3001"
      - "3002:3002"
    environment:
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
    networks:
      - feishu-network

networks:
  feishu-network:
    driver: bridge
```

### Kubernetes 部署

```yaml
# k8s/calmcp-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: calmcp
spec:
  replicas: 2
  selector:
    matchLabels:
      app: calmcp
  template:
    metadata:
      labels:
        app: calmcp
    spec:
      containers:
      - name: calmcp
        image: calmcp:latest
        ports:
        - containerPort: 3001
        - containerPort: 3002
        env:
        - name: FEISHU_APP_ID
          valueFrom:
            secretKeyRef:
              name: feishu-secrets
              key: app-id
        - name: FEISHU_APP_SECRET
          valueFrom:
            secretKeyRef:
              name: feishu-secrets
              key: app-secret

---
apiVersion: v1
kind: Service
metadata:
  name: calmcp-service
spec:
  selector:
    app: calmcp
  ports:
  - name: web
    port: 3001
    targetPort: 3001
  - name: mcp
    port: 3002
    targetPort: 3002
```

## 监控和日志

### 健康检查

在主项目中添加 CalMCP 健康检查：

```python
# services/health_service.py

async def check_calmcp_health():
    """检查 CalMCP 服务健康状态"""
    try:
        client = CalMCPClient()
        health = await client.health_check()
        await client.close()
        return health.get("status") == "ok"
    except Exception:
        return False
```

### 日志聚合

配置日志聚合，将 CalMCP 日志与主项目日志统一收集：

```yaml
# docker-compose.logging.yml
version: '3.8'

services:
  elasticsearch:
    image: elasticsearch:7.14.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  logstash:
    image: logstash:7.14.0
    volumes:
      - ./logstash.conf:/usr/share/logstash/pipeline/logstash.conf
    depends_on:
      - elasticsearch

  kibana:
    image: kibana:7.14.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

## 性能优化

### 连接池

在主项目中使用连接池：

```python
import asyncio
from contextlib import asynccontextmanager

class CalMCPPool:
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.pool = asyncio.Queue(maxsize=max_connections)
        self._initialize_pool()
    
    def _initialize_pool(self):
        for _ in range(self.max_connections):
            client = CalMCPClient()
            self.pool.put_nowait(client)
    
    @asynccontextmanager
    async def get_client(self):
        client = await self.pool.get()
        try:
            yield client
        finally:
            await self.pool.put(client)

# 全局连接池
mcp_pool = CalMCPPool()

# 使用示例
async def call_mcp_tool(name: str, args: dict):
    async with mcp_pool.get_client() as client:
        return await client.call_tool(name, args)
```

### 缓存

添加 Redis 缓存：

```python
import redis.asyncio as redis
import json

class CachedCalMCPClient(CalMCPClient):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.redis = redis.Redis(host='localhost', port=6379, db=0)
    
    async def call_tool_cached(self, name: str, arguments: Dict[str, Any], ttl: int = 300):
        # 生成缓存键
        cache_key = f"mcp:{name}:{hash(json.dumps(arguments, sort_keys=True))}"
        
        # 尝试从缓存获取
        cached = await self.redis.get(cache_key)
        if cached:
            return json.loads(cached)
        
        # 调用工具
        result = await self.call_tool(name, arguments)
        
        # 缓存结果
        await self.redis.setex(cache_key, ttl, json.dumps(result))
        
        return result
```

## 故障排除

### 常见问题

1. **连接超时**
   - 检查 CalMCP 服务是否正常运行
   - 验证网络连接和防火墙设置
   - 增加超时时间配置

2. **认证失败**
   - 确认飞书应用配置正确
   - 检查环境变量是否正确设置
   - 验证应用权限

3. **流式响应中断**
   - 检查网络稳定性
   - 调整缓冲区大小
   - 实现重试机制

### 调试工具

使用 CalMCP 提供的测试脚本：

```bash
cd calmcp
npm run test
```

或者使用 curl 测试：

```bash
# 健康检查
curl http://localhost:3001/api/health

# 工具列表
curl http://localhost:3001/api/mcp/tools

# 工具调用
curl -X POST http://localhost:3001/api/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{"name": "calendar_list", "arguments": {"page_size": 5}}'
```

## 最佳实践

1. **错误处理**: 实现完整的错误处理和重试机制
2. **监控**: 设置适当的监控和告警
3. **日志**: 记录详细的调用日志用于调试
4. **缓存**: 对频繁调用的数据进行缓存
5. **限流**: 实现适当的限流机制
6. **安全**: 使用 HTTPS 和适当的认证机制

通过以上集成步骤，主项目可以充分利用 CalMCP 的流式处理能力和完整的飞书日历功能。
