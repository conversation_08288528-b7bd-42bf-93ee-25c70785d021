"""
事件相关路由
"""

import json
import logging
from datetime import datetime

from fastapi import APIRouter, Body, HTTPException, Request

from config import TIME<PERSON>ON<PERSON>
from integrations.feishu import (
    batch_create_events,
    batch_delete_events,
    batch_update_events,
    create_daily_task,
    create_event,
    create_task_for_date,
    delete_event,
    get_calendar_events,
    get_event_detail,
    get_today_events,
    get_week_events,
    search_calendar_events,
    update_event,
)

from ..dependencies import ensure_valid_token
from ..models.event import (
    BatchEventCreate,
    BatchEventDelete,
    BatchEventUpdate,
    DailyTaskCreate,
    DateTaskCreate,
    EventCreate,
    EventCreateWithInput,
    EventUpdate,
    FlatEventCreate,
    FlatEventUpdate,
    SearchQuery,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/events", tags=["事件管理"])


# 日历事件相关路由
@router.get("/calendars/{calendar_id}")
async def calendar_events(
    user_id: str,
    calendar_id: str,
    start_time: str = None,
    end_time: str = None,
    page_token: str = None,
    page_size: int = 100,
    query: str = None,
):
    """获取特定日历的事件列表"""
    access_token = await ensure_valid_token(user_id)
    result = await get_calendar_events(
        access_token,
        calendar_id=calendar_id,
        start_time=start_time,
        end_time=end_time,
        page_token=page_token,
        page_size=page_size,
        query=query,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"获取日历事件失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.post("/calendars/{calendar_id}")
async def create_calendar_event(
    user_id: str, calendar_id: str, event_data: dict = Body(...)
):
    """创建日历事件"""
    try:
        # 如果传入的是带有input字段的数据，则使用input字段的内容
        if (
            "input" in event_data
            and event_data["input"]
            and isinstance(event_data["input"], dict)
        ):
            event_data = event_data["input"]

        # 提取事件数据
        summary = event_data.get("summary")
        description = event_data.get("description")
        start_time = event_data.get("start_time")
        end_time = event_data.get("end_time")
        location = event_data.get("location")
        reminders = event_data.get("reminders")
        attendees = event_data.get("attendees")
        is_all_day = event_data.get("is_all_day", False)
        recurrence = event_data.get("recurrence")

        # 检查并转换reminders格式
        if (
            reminders
            and isinstance(reminders, list)
            and reminders
            and isinstance(reminders[0], int)
        ):
            logger.info(f"检测到整数格式的reminders: {reminders}，进行转换")
            reminders = [{"minutes": minutes} for minutes in reminders]
            logger.info(f"转换后的提醒格式: {reminders}")

        # 验证必要字段
        if not summary:
            raise HTTPException(status_code=400, detail="事件标题(summary)不能为空")
        if not start_time:
            raise HTTPException(status_code=400, detail="开始时间(start_time)不能为空")
        if not end_time:
            raise HTTPException(status_code=400, detail="结束时间(end_time)不能为空")

        # 获取有效的访问令牌
        access_token = await ensure_valid_token(user_id)

        # 创建事件
        result = await create_event(
            access_token,
            calendar_id=calendar_id,
            summary=summary,
            start_time=start_time,
            end_time=end_time,
            description=description,
            location=location,
            reminders=reminders,
            attendees=attendees,
            is_all_day=is_all_day,
            recurrence=recurrence,
        )

        if "code" in result and result["code"] != 0:
            raise HTTPException(
                status_code=400, detail=f"创建事件失败: {result.get('msg', '未知错误')}"
            )

        return result
    except HTTPException:
        raise
    except Exception as e:
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"创建日历事件失败: {str(e)}")


@router.get("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def get_event_details(user_id: str, calendar_id: str, event_id: str):
    """获取事件详情"""
    access_token = await ensure_valid_token(user_id)
    result = await get_event_detail(access_token, calendar_id, event_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"获取事件详情失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.delete("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def delete_calendar_event(user_id: str, calendar_id: str, event_id: str):
    """删除日历事件"""
    access_token = await ensure_valid_token(user_id)
    result = await delete_event(access_token, calendar_id, event_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"删除事件失败: {result.get('msg', '未知错误')}"
        )

    return result


# 事件查询相关路由
@router.get("/auth/callback/events")
async def events(user_id: str, query: str = None):
    """获取当天的日历事件（保留原有接口，兼容性考虑）"""
    access_token = await ensure_valid_token(user_id)
    events = await get_today_events(access_token, query=query)
    return events


@router.get("/auth/callback/events/today")
async def today_events(user_id: str, calendar_id: str = "primary", query: str = None):
    """获取今天的日历事件"""
    logger.info(
        f"请求今天的日历事件 - 用户:{user_id}, 日历:{calendar_id}, 查询:{query}"
    )

    access_token = await ensure_valid_token(user_id)
    logger.info(f"获取到访问令牌，开始请求今天的事件")

    result = await get_today_events(access_token, calendar_id, query)

    # 打印返回的数据结构以进行调试
    logger.info(f"今日事件API返回结果: {json.dumps(result, ensure_ascii=False)}")

    if "code" in result and result["code"] != 0:
        logger.info(f"获取今日事件失败: {result.get('msg', '未知错误')}")
        raise HTTPException(
            status_code=400, detail=f"获取今日事件失败: {result.get('msg', '未知错误')}"
        )

    # 检查是否有过滤查询，如果有则记录结果
    if query:
        items_count = len(result.get("data", {}).get("items", []))
        logger.info(f"使用查询'{query}'过滤后的事件数量: {items_count}")

        # 打印每个匹配项的标题
        if items_count > 0:
            logger.info("匹配的事件标题:")
            for item in result["data"]["items"]:
                if "summary" in item:
                    logger.info(f" - {item['summary']}")

    return result


@router.get("/auth/callback/events/week")
async def week_events(user_id: str, calendar_id: str = "primary", query: str = None):
    """获取本周的日历事件"""
    access_token = await ensure_valid_token(user_id)
    result = await get_week_events(access_token, calendar_id, query)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"获取本周事件失败: {result.get('msg', '未知错误')}"
        )

    return result


# 搜索相关路由
@router.post("/auth/callback/events/search")
async def search_events_post(user_id: str, search_data: SearchQuery):
    """搜索日历事件（POST方法）"""
    access_token = await ensure_valid_token(user_id)
    result = await search_calendar_events(
        access_token,
        search_data.query,
        search_data.calendar_id,
        search_data.page_token,
        search_data.page_size,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"搜索日历事件失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.get("/auth/callback/events/search")
async def search_events_get(
    user_id: str,
    query: str,
    calendar_id: str = "primary",
    page_token: str = None,
    page_size: int = 50,
):
    """搜索日历事件（GET方法）"""
    access_token = await ensure_valid_token(user_id)
    result = await search_calendar_events(
        access_token, query, calendar_id, page_token, page_size
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"搜索日历事件失败: {result.get('msg', '未知错误')}"
        )

    return result


# 任务创建相关路由
@router.post("/auth/callback/events/daily-task")
async def create_new_daily_task(
    user_id: str, task: DailyTaskCreate, calendar_id: str = "primary"
):
    """创建当天任务"""
    access_token = await ensure_valid_token(user_id)
    result = await create_daily_task(
        access_token,
        summary=task.summary,
        start_time=task.start_time,
        duration_minutes=task.duration_minutes,
        description=task.description,
        calendar_id=calendar_id,
        location=task.location,
        reminders=task.reminders,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"创建当天任务失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.post("/auth/callback/events/date-task")
async def create_new_date_task(
    user_id: str, task: DateTaskCreate, calendar_id: str = "primary"
):
    """为指定日期创建任务"""
    access_token = await ensure_valid_token(user_id)
    result = await create_task_for_date(
        access_token,
        summary=task.summary,
        task_date=task.task_date,
        start_time=task.start_time,
        duration_minutes=task.duration_minutes,
        description=task.description,
        calendar_id=calendar_id,
        location=task.location,
        reminders=task.reminders,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400,
            detail=f"创建指定日期任务失败: {result.get('msg', '未知错误')}",
        )

    return result


# 批量操作相关路由
@router.post("/auth/callback/events/batch-create")
async def batch_create_calendar_events(user_id: str, batch: BatchEventCreate):
    """批量创建日历事件"""
    access_token = await ensure_valid_token(user_id)

    # 检查并转换每个事件中可能的整数列表格式的reminders
    for event in batch.events:
        if (
            "reminders" in event
            and isinstance(event["reminders"], list)
            and event["reminders"]
            and isinstance(event["reminders"][0], int)
        ):
            logger.info(f"检测到整数格式的reminders: {event['reminders']}，进行转换")
            event["reminders"] = [
                {"minutes": minutes} for minutes in event["reminders"]
            ]
            logger.info(f"转换后的提醒格式: {event['reminders']}")

    result = await batch_create_events(access_token, batch.events)
    return result


@router.post("/auth/callback/events/batch-update")
async def batch_update_calendar_events(user_id: str, batch: BatchEventUpdate):
    """批量更新日历事件"""
    access_token = await ensure_valid_token(user_id)

    # 检查并转换每个事件中可能的整数列表格式的reminders
    for event in batch.events:
        if (
            "reminders" in event
            and isinstance(event["reminders"], list)
            and event["reminders"]
            and isinstance(event["reminders"][0], int)
        ):
            logger.info(f"检测到整数格式的reminders: {event['reminders']}，进行转换")
            event["reminders"] = [
                {"minutes": minutes} for minutes in event["reminders"]
            ]
            logger.info(f"转换后的提醒格式: {event['reminders']}")

    result = await batch_update_events(access_token, batch.events)
    return result


@router.post("/auth/callback/events/batch-delete")
async def batch_delete_calendar_events(user_id: str, batch: BatchEventDelete):
    """批量删除日历事件"""
    access_token = await ensure_valid_token(user_id)
    result = await batch_delete_events(access_token, batch.events)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"批量删除事件失败: {result.get('msg', '未知错误')}"
        )

    return result


# 扁平化事件操作路由
@router.post("/auth/callback/calendars/{calendar_id}/events/flat")
async def create_flat_calendar_event(
    user_id: str, calendar_id: str, event: FlatEventCreate
):
    """使用扁平化格式创建日历事件"""
    access_token = await ensure_valid_token(user_id)

    logger.info(f"创建扁平化事件请求 - 用户ID: {user_id}, 日历ID: {calendar_id}")
    logger.info(
        f"请求数据: {json.dumps(event.model_dump(), ensure_ascii=False, indent=2)}"
    )

    # 处理地点
    location = None
    if event.location and len(event.location) > 0:
        location = {"name": event.location[0]}

    # 处理提醒
    reminders = None
    if event.reminders:
        # 将整数列表转换为字典列表格式
        reminders = [{"minutes": minutes} for minutes in event.reminders]
        logger.info(f"转换后的提醒格式: {reminders}")

    # 使用原有API创建事件
    try:
        result = await create_event(
            access_token=access_token,
            calendar_id=calendar_id,
            summary=event.summary,
            start_time=event.start_time,  # 直接传递ISO格式字符串
            end_time=event.end_time,  # 直接传递ISO格式字符串
            description=event.description,
            location=location,
            reminders=reminders,
            is_all_day=event.is_all_day,
            recurrence=event.recurrence,
        )

        logger.info(f"飞书API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if "code" in result and result["code"] != 0:
            logger.info(
                f"创建事件失败，错误代码: {result['code']}, 错误信息: {result.get('msg', '未知错误')}"
            )
            raise HTTPException(
                status_code=400,
                detail=f"创建日历事件失败: {result.get('msg', '未知错误')}",
            )

        return result
    except Exception as e:
        logger.info(f"创建事件时发生异常: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"创建日历事件失败: {str(e)}")


@router.patch("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def update_calendar_event(
    user_id: str, calendar_id: str, event_id: str, request: Request
):
    """更新日历事件"""
    access_token = await ensure_valid_token(user_id)

    # 获取请求体内容
    content_type = request.headers.get("content-type", "").lower()

    try:
        # 根据Content-Type解析请求体
        if "application/json" in content_type:
            event_data = await request.json()
        else:
            # 处理text/plain或其他格式
            body_text = await request.body()
            try:
                event_data = json.loads(body_text)
            except json.JSONDecodeError as e:
                raise HTTPException(status_code=400, detail=f"无效的JSON格式: {str(e)}")

        logger.info(
            f"收到事件更新请求 - 用户ID: {user_id}, 日历ID: {calendar_id}, 事件ID: {event_id}"
        )
        logger.info(f"请求数据: {json.dumps(event_data, ensure_ascii=False, indent=2)}")

        # 构建更新参数
        update_params = {}

        # 处理基本字段
        if "summary" in event_data:
            update_params["summary"] = event_data["summary"]
        if "description" in event_data:
            update_params["description"] = event_data["description"]
        if "is_all_day" in event_data:
            update_params["is_all_day"] = event_data["is_all_day"]
        if "status" in event_data:
            update_params["status"] = event_data["status"]
        if "recurrence" in event_data:
            update_params["recurrence"] = event_data["recurrence"]

        # 处理嵌套字段
        if "start_time" in event_data:
            update_params["start_time"] = event_data["start_time"]
        if "end_time" in event_data:
            update_params["end_time"] = event_data["end_time"]
        if "location" in event_data:
            update_params["location"] = event_data["location"]
        if "reminders" in event_data:
            # 检查reminders是否为整数列表，如果是则转换为字典列表格式
            reminders = event_data["reminders"]
            if (
                isinstance(reminders, list)
                and reminders
                and isinstance(reminders[0], int)
            ):
                logger.info(f"检测到整数格式的reminders: {reminders}，进行转换")
                update_params["reminders"] = [
                    {"minutes": minutes} for minutes in reminders
                ]
                logger.info(f"转换后的提醒格式: {update_params['reminders']}")
            else:
                update_params["reminders"] = reminders

        # 处理扁平化字段
        if "start_date_time" in event_data:
            try:
                dt = datetime.fromisoformat(
                    event_data["start_date_time"].replace("Z", "+00:00")
                )
                timestamp = str(int(dt.timestamp()))
                update_params["start_time"] = {
                    "timestamp": timestamp,
                    "timezone": TIMEZONE,
                }
            except Exception as e:
                logger.info(f"解析开始时间出错: {e}")
                raise HTTPException(
                    status_code=400, detail=f"无效的开始时间格式: {str(e)}"
                )

        if "end_date_time" in event_data:
            try:
                dt = datetime.fromisoformat(
                    event_data["end_date_time"].replace("Z", "+00:00")
                )
                timestamp = str(int(dt.timestamp()))
                update_params["end_time"] = {
                    "timestamp": timestamp,
                    "timezone": TIMEZONE,
                }
            except Exception as e:
                logger.info(f"解析结束时间出错: {e}")
                raise HTTPException(
                    status_code=400, detail=f"无效的结束时间格式: {str(e)}"
                )

        # 调用API更新事件
        result = await update_event(
            access_token, calendar_id=calendar_id, event_id=event_id, **update_params
        )

        if "code" in result and result["code"] != 0:
            raise HTTPException(
                status_code=400, detail=f"更新事件失败: {result.get('msg', '未知错误')}"
            )

        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.info(f"更新事件时发生异常: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"更新日历事件失败: {str(e)}")


@router.patch("/auth/callback/calendars/{calendar_id}/events/{event_id}/flat")
async def update_flat_calendar_event(
    user_id: str, calendar_id: str, event_id: str, event: FlatEventUpdate
):
    """使用扁平化格式更新日历事件"""
    access_token = await ensure_valid_token(user_id)

    logger.info(
        f"更新扁平化事件请求 - 用户ID: {user_id}, 日历ID: {calendar_id}, 事件ID: {event_id}"
    )
    logger.info(
        f"请求数据: {json.dumps(event.model_dump(), ensure_ascii=False, indent=2)}"
    )

    # 构建更新参数
    update_params = {}

    # 基本字段直接复制
    if event.summary is not None:
        update_params["summary"] = event.summary
    if event.description is not None:
        update_params["description"] = event.description
    if event.is_all_day is not None:
        update_params["is_all_day"] = event.is_all_day
    if event.recurrence is not None:
        update_params["recurrence"] = event.recurrence
    if event.status is not None:
        update_params["status"] = event.status

    # 处理开始时间
    if event.start_date_time is not None:
        update_params["start_time"] = event.start_date_time

    # 处理结束时间
    if event.end_date_time is not None:
        update_params["end_time"] = event.end_date_time

    # 处理地点
    if event.location is not None and len(event.location) > 0:
        update_params["location"] = {"name": event.location[0]}

    # 处理提醒
    if event.reminders is not None:
        # 将整数列表转换为字典列表格式
        update_params["reminders"] = [
            {"minutes": minutes} for minutes in event.reminders
        ]
        logger.info(f"转换后的提醒格式: {update_params['reminders']}")

    # 调用API更新事件
    try:
        result = await update_event(
            access_token, calendar_id=calendar_id, event_id=event_id, **update_params
        )

        logger.info(f"飞书API响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

        if "code" in result and result["code"] != 0:
            logger.info(
                f"更新事件失败，错误代码: {result['code']}, 错误信息: {result.get('msg', '未知错误')}"
            )
            raise HTTPException(
                status_code=400,
                detail=f"更新日历事件失败: {result.get('msg', '未知错误')}",
            )

        return result
    except Exception as e:
        logger.info(f"更新事件时发生异常: {str(e)}")
        import traceback

        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"更新日历事件失败: {str(e)}")
