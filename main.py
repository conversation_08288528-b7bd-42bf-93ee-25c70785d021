"""
Feishu Coze Plugin - 主启动文件
重构后的版本，使用模块化路由结构
"""

import logging
import os
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.responses import JSONResponse

# 导入API路由
from api.routers import auth, calendar, chat, event, health, mcp

# 导入配置
from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET, STORAGE_TYPE, TIMEZONE

# 导入服务和存储
from integrations.feishu import FeishuCalendarLark
from integrations.storage import TokenStorage
from services import ChatService
from services.token_refresh_service import start_token_refresh_service

# 设置日志
logger = logging.getLogger(__name__)

# 禁用hpack.hpack的DEBUG日志
logging.getLogger("hpack.hpack").setLevel(logging.INFO)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    print("LIFESPAN: 开始执行")  # 使用print确保输出
    # 启动时执行
    root_logger = logging.getLogger()
    log_level = root_logger.level
    logger.setLevel(log_level)
    print("LIFESPAN: 日志设置完成")

    # 记录当前日志级别
    level_name = logging.getLevelName(log_level)
    logger.info(f"应用启动，当前日志级别: {level_name}")

    # 禁用hpack.hpack的DEBUG日志，除非明确要求DEBUG级别
    if log_level > logging.DEBUG:
        logging.getLogger("hpack.hpack").setLevel(logging.INFO)

    # 设置时区为配置中指定的时区
    os.environ["TZ"] = TIMEZONE
    # 设置Python时区
    try:
        import time

        time.tzset()
        logger.info(f"时区已设置为: {os.environ.get('TZ', '未设置')}")
    except AttributeError:
        # Windows系统不支持tzset
        logger.warning("无法设置系统时区，可能是Windows系统")

    # 如果使用Supabase存储，尝试初始化数据库表
    print(f"LIFESPAN: 检查存储类型: {STORAGE_TYPE}")
    logger.info(f"当前存储类型: {STORAGE_TYPE}")
    if STORAGE_TYPE == "supabase":
        logger.info("跳过Supabase数据库初始化（暂时禁用）")
        # try:
        #     logger.info("检查并初始化Supabase数据库表...")
        #     from scripts.init_db import create_tables
        #     if create_tables():
        #         logger.info("Supabase数据库表初始化成功")
        #     else:
        #         logger.warning("无法初始化Supabase数据库表，将使用降级的存储方式")
        # except Exception as e:
        #     logger.error(f"初始化Supabase数据库表时出错: {str(e)}")
        #     import traceback
        #     logger.error(traceback.format_exc())
    else:
        logger.info("跳过Supabase数据库初始化")

    # 启动token自动刷新服务和健康检查服务
    logger.info("=== 开始启动token刷新服务 ===")
    try:
        logger.info("正在启动token刷新服务...")
        start_token_refresh_service()
        logger.info("Token刷新服务启动成功")
    except Exception as e:
        logger.error(f"启动token刷新服务失败: {e}")
        import traceback

        logger.error(traceback.format_exc())
    logger.info("=== token刷新服务启动流程结束 ===")

    logger.info("应用启动完成")
    
    # 初始化MCP工具
    logger.info("=== 开始初始化MCP工具 ===")
    try:
        mcp.init_mcp_tools(feishu_calendar)
        logger.info("MCP工具初始化成功")
    except Exception as e:
        logger.error(f"MCP工具初始化失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
    logger.info("=== MCP工具初始化完成 ===")

    yield  # 应用运行期间

    # 关闭时执行（如果需要的话）
    logger.info("应用正在关闭...")


# 创建FastAPI应用实例
app = FastAPI(
    title="Feishu Coze Plugin",
    description="飞书智能日历插件 - 集成llmcal的智能处理能力",
    version="2.0.0",
    lifespan=lifespan,
    redirect_slashes=False,  # 关键：禁用自动斜杠重定向
)

# 初始化全局服务实例
feishu_calendar = FeishuCalendarLark(
    app_id=FEISHU_CLIENT_ID, app_secret=FEISHU_CLIENT_SECRET
)
chat_service = ChatService()
token_storage = TokenStorage()

# 注册路由
app.include_router(health.router, prefix="", tags=["健康检查"])
app.include_router(auth.router, prefix="", tags=["认证"])
app.include_router(chat.router, prefix="/api", tags=["聊天"])
app.include_router(calendar.router, prefix="/api", tags=["日历"])
app.include_router(event.router, prefix="/api", tags=["事件"])

# 注册MCP路由
app.include_router(mcp.router, prefix="/mcp", tags=["MCP协议"])


# 添加全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    logger.error(f"全局异常处理器捕获异常: {str(exc)}")
    import traceback

    traceback.print_exc()
    return JSONResponse(
        status_code=500, content={"error": "内部服务器错误", "detail": str(exc)}
    )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="localhost", port=5000, reload=False, log_level="debug")
