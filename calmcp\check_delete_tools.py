#!/usr/bin/env python3
"""
检查删除相关的 MCP 工具
"""

import requests
import json

def check_delete_tools():
    """检查删除相关工具"""
    try:
        response = requests.post('http://localhost:3000/api/mcp/tools/call', 
            json={'name': 'list_tools', 'arguments': {}})
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                tools = result.get('result', {}).get('tools', [])
                
                print(f"📊 总工具数: {len(tools)}")
                
                # 查找删除相关工具
                delete_tools = [tool for tool in tools if 'delete' in tool.get('name', '').lower()]
                print(f"🗑️  删除相关工具: {len(delete_tools)} 个")
                
                for tool in delete_tools:
                    name = tool.get('name', '未知')
                    description = tool.get('description', '无描述')
                    print(f"  - {name}")
                    print(f"    描述: {description}")
                
                # 查找日历相关工具
                calendar_tools = [tool for tool in tools if 'calendar' in tool.get('name', '').lower()]
                print(f"\n📅 日历相关工具: {len(calendar_tools)} 个")
                
                for tool in calendar_tools:
                    name = tool.get('name', '未知')
                    if 'delete' in name.lower():
                        print(f"  🗑️  {name}")
                    elif 'create' in name.lower():
                        print(f"  ➕ {name}")
                    elif 'list' in name.lower():
                        print(f"  📋 {name}")
                    else:
                        print(f"  📅 {name}")
                
            else:
                print('❌ 工具列表获取失败')
                print(f"错误: {result}")
        else:
            print(f'❌ HTTP错误: {response.status_code}')
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查过程中出现异常: {e}")

if __name__ == "__main__":
    check_delete_tools()
