# LangGraph + LLM + MCP + Supabase + Vercel + Render 架构方案

## 一、方案概述

本方案实现一个智能秘书/日历/任务/多场景系统，采用现代云原生架构：
- **MCP Server**：基于Next.js（API Route）实现，部署在Vercel，负责MCP协议、日历/任务等工具API，持久化数据存储在Supabase，并通过Supabase获取用户token后调用飞书官方API。
- **LangGraph+LLM+MCP Client**：部署在Render，负责多意图识别、LLM推理、MCP工具编排和业务逻辑。
- **Supabase**：统一存储用户token、日历、任务、用户会话、认证、JWT等所有持久化数据。
- **飞书官方API**：MCP Server通过token调用飞书开放平台API，完成日历、事件等操作。
- **前端/用户入口**：可为Web、CLI、API等多种形态，用户登录认证、会话管理也通过Supabase。

## 二、整体技术架构与数据流

```
┌──────────────┐      HTTP      ┌────────────────────┐
│   用户/前端   │ <───────────> │  Render: LangGraph  │
│  (Web/CLI)   │               │  + LLM + MCPClient  │
└──────────────┘               └─────────┬──────────┘
                                         │HTTP
                                         ▼
                              ┌────────────────────┐
                              │ Vercel: Next.js MCP│
                              │   API Server       │
                              └─────────┬──────────┘
                                         │HTTP
                                         ▼
                              ┌────────────────────┐
                              │    Supabase DB     │
                              └─────────┬──────────┘
                                         │REST API
                                         ▼
                              ┌────────────────────┐
                              │  飞书官方API       │
                              └────────────────────┘
```

### 数据流与会话/认证说明
- 用户在前端/移动端登录，认证信息（如JWT、会话）存储在Supabase。
- LangGraph+LLM+MCP Client通过Supabase校验用户身份、获取token。
- MCP Server每次工具调用时，从Supabase获取token，调用飞书官方API。
- Supabase既是MCP Server的token/日历/任务后端，也是用户会话、认证、JWT等的统一后端。

### 详细流程图
```mermaid
graph TD
    A[用户/前端/移动端] -- 登录/认证 --> S1[Supabase: 用户会话/JWT]
    A -- 业务请求 --> R[Render: LangGraph+LLM+MCP Client]
    R -- 校验/获取token --> S1
    R -- MCP工具调用 --> V[Vercel: Next.js MCP Server]
    V -- token/日历/任务存取 --> S2[Supabase: 统一后端]
    V -- token鉴权后 --> F[飞书官方API]
    F -- 日历/事件数据 --> V
    S2 -- 认证/会话/业务数据 --> R
    V -- 业务响应 --> R
    R -- 结果聚合/总结 --> A
    style S1 fill:#e3f2fd
    style S2 fill:#e3f2fd
    style F fill:#fff3e0
    style V fill:#f3e5f5
    style R fill:#e8f5e8
    style A fill:#e1f5fe
```

## 三、MCP Server（Next.js on Vercel）设计

### 1. 技术选型
- Next.js 14+（API Route/Edge Function）
- Node.js 18+
- axios（HTTP请求飞书API/Supabase）
- 支持JSON-RPC 2.0协议

### 2. 主要API接口
- `POST /api/mcp`：MCP主入口，支持initialize、tools/list、tools/call
- `GET /api/mcp/tools`：获取工具列表
- `GET /api/health`：健康检查

### 3. 工具定义（参考FEISHU_MCP_TOOLS.md）
- list_calendars
- get_calendar_events
- create_calendar_event
- get_today_events
- ...（可扩展）

### 4. 数据持久化与飞书API调用
- 通过Supabase REST API/PostgREST访问token、日历、任务、用户会话、认证、JWT等表
- MCP Server每次调用飞书API前，先从Supabase查token
- 用户token通过`user_id`索引
- 飞书API调用如：`/open-apis/calendar/v4/calendars`、`/open-apis/calendar/v4/calendars/{calendar_id}/events`等

### 5. 典型API实现（伪代码）
```js
// pages/api/mcp.js
export default async function handler(req, res) {
  const { jsonrpc, id, method, params } = req.body;
  switch (method) {
    case 'initialize':
      // ...
    case 'tools/list':
      // ...
    case 'tools/call':
      // 解析params.name, params.arguments
      // 通过Supabase查token，调飞书API
      // 返回MCP格式
  }
}
```

## 四、LangGraph+LLM+MCP Client（Render）设计

### 1. 技术选型
- Python 3.10+
- langgraph, langchain, langchain_openai
- httpx（MCP Client）
- pydantic
- asyncio

### 2. 主要功能
- 多意图识别（LLM Function Calling）
- LangGraph多子图并发/编排
- MCP工具自动发现与调用
- 业务聚合与总结
- 用户认证/会话管理（通过Supabase）

### 3. MCP Client实现
```python
import httpx
class McpClient:
    def __init__(self, base_url):
        self.base = base_url.rstrip('/')
    async def list_tools(self):
        async with httpx.AsyncClient() as c:
            return (await c.get(f"{self.base}/api/mcp/tools")).json()
    async def call(self, tool, arguments):
        async with httpx.AsyncClient() as c:
            return (await c.post(f"{self.base}/api/mcp", json={
                "jsonrpc": "2.0", "id": "cli", "method": "tools/call",
                "params": {"name": tool, "arguments": arguments}
            })).json()
```

### 4. LangGraph子图/主图
- 每个业务域（calendar、task、chat等）为一个子图
- 主图负责意图分发、聚合、总结
- LLM自动选择MCP工具并生成参数
- 用户认证/会话校验通过Supabase

## 五、Supabase持久化设计

- 用户token表：user_id, access_token, refresh_token, expires_at
- 日历表、任务表、用户会话表、认证表、JWT表等
- 通过Supabase REST API/PostgREST访问
- Vercel/Render均可安全访问Supabase
- 用户认证/会话/业务数据统一存储和校验

## 六、部署方案

### 1. MCP Server（Vercel）
- 目录结构：`/api/mcp.js`、`/api/mcp/tools.js`、`/api/health.js`
- 环境变量：FEISHU_CLIENT_ID、FEISHU_CLIENT_SECRET、SUPABASE_URL、SUPABASE_KEY
- 部署命令：`vercel --prod`

### 2. LangGraph+LLM+MCP Client（Render）
- Python服务，长期运行
- 环境变量：OPENAI_API_KEY、MCP_SERVER_URL、SUPABASE_URL、SUPABASE_KEY
- 部署命令：`render.yaml`配置自动部署

### 3. Supabase
- 云PostgreSQL，免费额度充足
- 控制台建表、管理token/日历/任务/用户会话/认证/JWT

## 七、注意事项与最佳实践

- MCP Server所有接口必须为HTTP短连接，响应时间<10s
- 不要在Vercel上实现WebSocket/SSE等长连接
- Supabase表结构需合理设计，token/认证/会话安全存储
- Render服务可实现多轮对话、异步任务、WebSocket等
- LLM Function Calling需严格校验参数，防止注入
- 日志、监控、健康检查建议接入Prometheus/Grafana

## 八、参考文档
- [STREAMABLE_MCP_ARCHITECTURE.md](./STREAMABLE_MCP_ARCHITECTURE.md)
- [FEISHU_MCP_TOOLS.md](./FEISHU_MCP_TOOLS.md)
- [DEPLOYMENT.md](./DEPLOYMENT.md)
- [TECH_ARCHITECTURE.md](./TECH_ARCHITECTURE.md)
- [kimi-mcp-llm-lang架构实现.md](../kimi-mcp-llm-lang架构实现.md)

---

如需详细代码模板或Next.js MCP Server样例，请随时告知！ 