"""
测试人机协作功能
验证多轮对话、确认机制、上下文保持等功能
"""

import asyncio
import logging
from datetime import datetime

from models.chat import ChatRequest
from services.chat_service import ChatService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class HumanCollaborationTester:
    """人机协作测试器"""

    def __init__(self):
        self.chat_service = ChatService()
        self.test_user_id = "test_user_001"

    async def simulate_conversation(self, messages: list, test_name: str):
        """模拟对话"""
        print(f"\n{'='*60}")
        print(f"🧪 测试场景: {test_name}")
        print(f"{'='*60}")

        for i, user_message in enumerate(messages):
            print(f"\n👤 用户 ({i+1}): {user_message}")

            # 创建请求
            request = ChatRequest(user_id=self.test_user_id, message=user_message)

            # 处理消息
            response = await self.chat_service.process_message(request)

            # 显示响应
            print(f"🤖 助手: {response.message}")
            print(f"📊 意图: {response.intent} (置信度: {response.confidence})")

            if response.context:
                print(f"🔄 状态: {response.context.get('state', 'unknown')}")
                print(
                    f"📝 对话摘要: {response.context.get('conversation_summary', 'N/A')}"
                )

            if response.data:
                print(f"📋 数据: {response.data}")

            # 短暂延迟模拟真实对话
            await asyncio.sleep(0.5)

    async def test_confirmation_flow(self):
        """测试确认流程"""
        messages = [
            "明天下午3点安排一个产品评审会议",  # 初始请求
            "确认",  # 用户确认
        ]

        await self.simulate_conversation(messages, "基本确认流程")

    async def test_rejection_flow(self):
        """测试拒绝流程"""
        messages = [
            "后天上午10点安排团队会议",  # 初始请求
            "取消",  # 用户拒绝
        ]

        await self.simulate_conversation(messages, "拒绝操作流程")

    async def test_modification_flow(self):
        """测试修改流程"""
        messages = [
            "下周一下午2点安排客户会议",  # 初始请求
            "改成下午4点",  # 用户修改时间
            "确认",  # 确认修改后的安排
        ]

        await self.simulate_conversation(messages, "修改确认流程")

    async def test_supplement_flow(self):
        """测试补充信息流程"""
        messages = [
            "安排一个会议",  # 缺少时间和标题
            "产品讨论会",  # 补充标题
            "明天下午3点",  # 补充时间
            "确认",  # 最终确认
        ]

        await self.simulate_conversation(messages, "补充信息流程")

    async def test_complex_modification(self):
        """测试复杂修改流程"""
        messages = [
            "明天下午3点在会议室A安排产品评审会议",  # 完整请求
            "改成后天上午10点，地点改为会议室B",  # 复杂修改
            "再改成下午2点",  # 再次修改时间
            "确认",  # 最终确认
        ]

        await self.simulate_conversation(messages, "复杂修改流程")

    async def test_context_preservation(self):
        """测试上下文保持"""
        messages = [
            "下周五下午安排重要会议",  # 初始请求
            "会议主题是季度总结",  # 补充信息（应该保持日历上下文）
            "地点在大会议室",  # 继续补充
            "确认",  # 确认
        ]

        await self.simulate_conversation(messages, "上下文保持测试")

    async def test_intent_preservation(self):
        """测试意图保持（避免重新分类）"""
        messages = [
            "明天开会",  # 简单日历请求
            "会议主题是项目进展讨论",  # 这应该被识别为补充信息，而不是重新分类为聊天
            "时间是下午3点",  # 继续补充
            "确认",  # 确认
        ]

        await self.simulate_conversation(messages, "意图保持测试")

    async def test_mixed_conversation(self):
        """测试混合对话"""
        messages = [
            "你好",  # 聊天
            "明天安排会议",  # 切换到日历
            "会议主题是产品发布",  # 补充信息
            "取消",  # 取消操作
            "谢谢",  # 回到聊天
        ]

        await self.simulate_conversation(messages, "混合对话测试")


async def main():
    """主测试函数"""
    print("🚀 开始人机协作功能测试")
    print(f"⏰ 测试时间: {datetime.now()}")

    tester = HumanCollaborationTester()

    # 执行各种测试场景
    test_scenarios = [
        tester.test_confirmation_flow,
        tester.test_rejection_flow,
        tester.test_modification_flow,
        tester.test_supplement_flow,
        tester.test_complex_modification,
        tester.test_context_preservation,
        tester.test_intent_preservation,
        tester.test_mixed_conversation,
    ]

    for i, test_func in enumerate(test_scenarios, 1):
        try:
            print(f"\n🔄 执行测试 {i}/{len(test_scenarios)}")
            await test_func()
            print(f"✅ 测试 {i} 完成")
        except Exception as e:
            print(f"❌ 测试 {i} 失败: {e}")
            import traceback

            traceback.print_exc()

        # 测试间隔
        await asyncio.sleep(1)

    print(f"\n{'='*60}")
    print("🎉 人机协作功能测试完成！")
    print(f"{'='*60}")

    print("\n📊 测试总结:")
    print("✅ 确认流程 - 用户可以确认操作")
    print("✅ 拒绝流程 - 用户可以取消操作")
    print("✅ 修改流程 - 用户可以修改参数")
    print("✅ 补充流程 - 系统可以要求补充信息")
    print("✅ 上下文保持 - 多轮对话保持上下文")
    print("✅ 意图保持 - 避免重复意图识别")
    print("✅ 混合对话 - 支持聊天和任务混合")


if __name__ == "__main__":
    asyncio.run(main())
