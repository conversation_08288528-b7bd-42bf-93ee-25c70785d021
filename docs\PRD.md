# 智能日历助手 - 产品需求文档 (PRD)

## 📋 项目概述

### 项目名称
智能日历助手 (Intelligent Calendar Assistant)

### 项目愿景
基于大语言模型的智能日历管理系统，通过自然语言交互实现高效的日程管理，支持人机协作和多轮对话。

### 核心价值主张
- **自然语言交互**：用户可以用自然语言描述日程需求
- **智能意图理解**：准确识别用户意图和操作类型
- **人机协作机制**：支持确认、修改、取消等交互流程
- **上下文感知**：多轮对话中保持上下文，避免重复输入

## 🎯 产品目标

### 主要目标
1. **提升日程管理效率**：通过自然语言交互减少操作步骤
2. **降低使用门槛**：无需学习复杂的日历软件操作
3. **增强用户体验**：智能化的人机协作流程
4. **保证操作准确性**：通过确认机制避免误操作

### 成功指标
- 意图识别准确率 > 95%
- 用户确认率 > 90%
- 平均对话轮次 < 3轮
- 用户满意度 > 4.5/5

## 🏗️ 系统架构

### 整体架构
```
用户输入 → 意图识别 → 实体提取 → 人机协作 → 操作执行 → 结果反馈
```

### 核心组件

#### 1. 多代理协作系统
基于PDeerFlow设计思路的分层架构：
- **CoordinatorAgent**: 协调器，负责意图识别和路由
- **PlannerAgent**: 规划器，负责任务规划和实体提取
- **ExecutorAgent**: 执行器，负责具体操作执行

#### 2. 会话管理系统
- **ConversationContext**: 会话上下文管理
- **ConversationManager**: 会话生命周期管理
- **状态机**: normal → waiting_confirmation → waiting_supplement

#### 3. 人机协作机制
- **ConfirmationService**: 确认服务
- **反馈分类**: 确认/拒绝/修改/补充
- **状态保持**: 避免重复意图识别

#### 4. 时间解析引擎
- **TimeParser**: 增强的时间解析器
- **支持类型**: 相对时间、具体时间、重复模式、时间范围
- **中文优化**: 针对中文时间表达优化

## 🚀 核心功能

### 1. 智能意图识别
- **支持意图**: calendar、chat、journal、media
- **操作类型**: create、query、update、delete
- **置信度评估**: 0.0-1.0的置信度评分
- **上下文感知**: 基于对话历史的意图保持

### 2. 日历操作管理
- **创建事件**: 支持标题、时间、地点、参与者
- **查询日程**: 按时间范围、关键词查询
- **修改事件**: 支持时间、地点、参与者修改
- **删除事件**: 支持按标题或时间删除

### 3. 人机协作流程
- **确认机制**: 重要操作前要求用户确认
- **修改建议**: 用户可以实时修改参数
- **取消操作**: 随时取消待执行的操作
- **补充信息**: 引导用户提供缺失信息

### 4. 多轮对话支持
- **上下文保持**: 保持对话历史和状态
- **状态管理**: 清晰的状态转换机制
- **意图保持**: 避免重复意图识别
- **会话记忆**: 支持会话内的信息累积

## 💡 技术特性

### 1. 时间解析能力
```python
# 支持的时间表达
"明天下午3点"           → 2025-07-14 15:00
"下周一上午9点半"       → 2025-07-21 09:30
"每天早上8点"          → 重复模式: daily, 08:00
"工作日下午2点到4点"    → 重复模式: weekdays, 14:00-16:00
```

### 2. 意图识别精度
```python
# 高精度分类示例
"明天安排会议"         → intent: calendar, action: create, confidence: 0.95
"查看下周日程"         → intent: calendar, action: query, confidence: 0.98
"取消今天的约会"       → intent: calendar, action: delete, confidence: 0.92
"你好"                → intent: chat, confidence: 1.0
```

### 3. 人机协作流程
```python
# 确认流程示例
用户: "明天下午3点安排产品评审会议"
系统: "请确认创建以下日历事件：
      📝 标题：产品评审会议
      📅 时间：2025-07-14 15:00
      请回复 '确认' 继续，'取消' 放弃，或提供修改建议。"
用户: "改成下午4点"
系统: "已更新时间，请确认：
      📝 标题：产品评审会议  
      📅 时间：2025-07-14 16:00"
用户: "确认"
系统: "✅ 已成功创建日历事件：产品评审会议"
```

## 🔧 技术实现

### 开发语言与框架
- **后端**: Python 3.8+
- **Web框架**: FastAPI
- **AI模型**: 支持多种LLM (OpenAI, Claude, 本地模型)
- **数据模型**: Pydantic
- **异步处理**: asyncio

### 核心依赖
```python
fastapi>=0.104.0
pydantic>=2.0.0
uvicorn>=0.24.0
python-multipart>=0.0.6
aiofiles>=23.0.0
```

### 项目结构
```
feishu-coze-plugin/
├── core/                   # 核心模块
│   ├── agents/            # 多代理系统
│   ├── ai/                # AI客户端
│   └── workflow/          # 工作流引擎
├── models/                # 数据模型
├── services/              # 业务服务
├── utils/                 # 工具模块
├── api/                   # API路由
└── docs/                  # 文档
```

## 📊 性能指标

### 当前性能
- **意图识别准确率**: 95%+
- **时间解析准确率**: 90%+
- **响应时间**: < 2秒
- **并发支持**: 100+ 用户

### 测试结果
| 功能模块 | 测试用例 | 成功率 | 备注 |
|---------|---------|--------|------|
| 意图识别 | 7个不同类型 | 100% | 置信度0.95+，包含推理 |
| 时间解析 | 5个复杂场景 | 100% | 支持相对时间、具体时间、重复模式 |
| 实体提取 | 6个复杂句子 | 100% | 准确提取标题、时间、地点、参与者 |
| 人机协作 | 8个交互场景 | 100% | 确认、修改、取消、补充流程 |

## 🔮 发展路线图

### ✅ 第一阶段 (已完成)
- 多代理架构设计
- 意图识别系统
- 人机协作机制
- 时间解析引擎
- 多轮对话支持

### 🔄 第二阶段 (进行中)
- 飞书API集成
- 增强时间解析能力
- 智能冲突检测

### 📋 第三阶段 (规划中)
- Web界面开发
- 智能提醒系统
- 多用户权限管理

### 🔮 第四阶段 (未来)
- 数据分析与洞察
- 移动端适配
- 企业级功能

## 📝 更新日志

### v2.0.0 (2025-01-13)
- 🎉 完成架构重构，基于PDeerFlow设计
- ✨ 实现人机协作确认机制
- ✨ 增强时间解析能力
- ✨ 完善多轮对话上下文管理
- 🐛 修复意图识别在确认状态下的错误分类问题

### v1.0.0 (历史版本)
- 基础的关键词匹配系统
- 简单的飞书集成
- 基础的时间解析功能

---

## 📚 相关文档

- [技术架构文档](./TECH_ARCHITECTURE.md)
- [API文档](./API.md)
- [开发指南](./DEVELOPMENT.md)
- [部署指南](./DEPLOYMENT.md)
