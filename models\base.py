"""
基础数据模型
"""

from typing import Any, Dict, Optional

from pydantic import BaseModel as PydanticBaseModel


class BaseModel(PydanticBaseModel):
    """基础模型类"""

    class Config:
        """Pydantic配置"""

        # 允许使用字段别名
        validate_by_name = True
        # 验证赋值
        validate_assignment = True
        # 使用枚举值
        use_enum_values = True


class Response(BaseModel):
    """通用响应模型"""

    success: bool = True
    message: str = ""
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None

    @classmethod
    def success_response(cls, message: str = "操作成功", data: Dict[str, Any] = None):
        """创建成功响应"""
        return cls(success=True, message=message, data=data or {})

    @classmethod
    def error_response(cls, message: str = "操作失败", error: str = None):
        """创建错误响应"""
        return cls(success=False, message=message, error=error)
