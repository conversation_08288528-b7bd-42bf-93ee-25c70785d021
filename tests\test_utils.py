import os
import time
import json
from lark_oapi.core import Config, LogLevel
from lark_oapi.service.authen.v1 import AuthenticateService, AuthenticateUserAccessTokenRequest
from sseclient import SSEClient
from threading import Thread
from queue import Queue

# 配置参数（从环境变量读取，不要硬编码）
APP_ID = os.environ.get("FEISHU_APP_ID", "cli_a76a68f612bf900c")
APP_SECRET = os.environ.get("FEISHU_APP_SECRET", "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC")
REDIRECT_URI = os.environ.get("FEISHU_REDIRECT_URI", "https://feishu-coze-plugin.onrender.com/auth/callback")
MCP_SERVER_URL = os.environ.get("MCP_SERVER_URL", "http://localhost:3000/sse")
SCOPES = ["calendar:all"]  # 根据实际需求调整

# 初始化飞书客户端配置
config = Config.builder() \
    .app_id(APP_ID) \
    .app_secret(APP_SECRET) \
    .log_level(LogLevel.INFO) \
    .build()

# 消息队列（用于线程间通信）
message_queue = Queue()

def get_user_authorization():
    """获取用户授权URL并处理回调"""
    auth_url = f"https://open.feishu.cn/open-apis/authen/v1/index?app_id={APP_ID}" \
               f"&redirect_uri={REDIRECT_URI}" \
               f"&response_type=code" \
               f"&scope=calendar:all" \
               f"&state=random_state"
    print(f"请访问以下URL进行授权: {auth_url}")
    
    code = input("请输入回调URL中的code参数值: ")
    
    service = AuthenticateService(config)
    request = AuthenticateUserAccessTokenRequest.builder() \
        .code(code) \
        .grant_type("authorization_code") \
        .build()
    
    response = service.user_access_token(request)
    if response.code != 0:
        raise Exception(f"获取User Token失败: {response.code} {response.msg}")
    
    user_token = response.data.access_token
    refresh_token = response.data.refresh_token
    print(f"获取User Access Token成功")
    
    return user_token, refresh_token

def refresh_user_token(refresh_token):
    """刷新User Access Token"""
    service = AuthenticateService(config)
    request = AuthenticateUserAccessTokenRequest.builder() \
        .refresh_token(refresh_token) \
        .grant_type("refresh_token") \
        .build()
    
    response = service.user_access_token(request)
    if response.code != 0:
        raise Exception(f"刷新User Token失败: {response.code} {response.msg}")
    
    new_token = response.data.access_token
    new_refresh_token = response.data.refresh_token
    print(f"刷新User Access Token成功")
    
    return new_token, new_refresh_token

def sse_consumer(user_token):
    """SSE消息消费者（持续监听服务器推送）"""
    headers = {
        "Authorization": f"Bearer {user_token}",
        "Content-Type": "application/json"
    }
    
    try:
        print(f"正在连接到SSE服务: {MCP_SERVER_URL}")
        client = SSEClient(MCP_SERVER_URL, headers=headers)
        
        for event in client.events():
            try:
                data = json.loads(event.data)
                print(f"收到服务器消息: {data}")
                message_queue.put(data)
                
                if "session_id" in data:
                    session_id = data["session_id"]
                    print(f"已获取Session ID: {session_id}")
                    
            except json.JSONDecodeError:
                print(f"收到非JSON格式消息: {event.data}")
            except Exception as e:
                print(f"处理消息时出错: {e}")
    
    except Exception as e:
        print(f"SSE连接错误: {e}")

def message_processor():
    """消息处理器（从队列中取出消息并处理）"""
    while True:
        message = message_queue.get()
        try:
            print(f"处理消息: {message}")
            
            if "calendar_event" in message.get("type", ""):
                process_calendar_event(message)
                
        except Exception as e:
            print(f"处理消息业务逻辑时出错: {e}")
        finally:
            message_queue.task_done()

def process_calendar_event(event):
    """处理日历事件（示例）"""
    # 注意：此处需要使用用户Token创建新的Config
    user_config = config.clone()
    user_config.auth_token = event.get("user_token", "")  # 假设消息中包含user_token
    
    # 示例：调用日历API
    from lark_oapi.service.calendar.v4 import CalendarService, ListCalendarsRequest
    service = CalendarService(user_config)
    request = ListCalendarsRequest.builder().build()
    
    response = service.list_calendars(request)
    if response.code == 0:
        print(f"获取日历列表成功，共{len(response.data.items)}个日历")
    else:
        print(f"获取日历列表失败: {response.code} {response.msg}")

if __name__ == "__main__":
    try:
        user_token, refresh_token = get_user_authorization()
        
        processor_thread = Thread(target=message_processor)
        processor_thread.daemon = True
        processor_thread.start()
        
        consumer_thread = Thread(target=sse_consumer, args=(user_token,))
        consumer_thread.daemon = True
        consumer_thread.start()
        
        print("程序已启动，按Ctrl+C退出")
        while True:
            time.sleep(60)
            
    except KeyboardInterrupt:
        print("用户中断，程序退出")
    except Exception as e:
        print(f"程序运行出错: {e}")