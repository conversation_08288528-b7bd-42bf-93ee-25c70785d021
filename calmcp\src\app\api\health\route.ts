/**
 * Next.js API Route for Health Check
 * GET /api/health - 健康检查
 */

import { NextRequest, NextResponse } from 'next/server';
import { ALL_TOOLS } from '@/lib/mcp-tools';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const health = {
      status: 'ok',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      service: 'calmcp',
      tools: {
        count: ALL_TOOLS.length,
        available: ALL_TOOLS.map(tool => tool.name)
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        hasFeishuConfig: !!(process.env.FEISHU_APP_ID && process.env.FEISHU_APP_SECRET)
      }
    };
    
    logger.info('Health check requested', { 
      userAgent: request.headers.get('user-agent'),
      ip: request.ip
    });
    
    return NextResponse.json(health);
    
  } catch (error) {
    logger.error('Health check failed', error as Error);
    
    return NextResponse.json({
      status: 'error',
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
