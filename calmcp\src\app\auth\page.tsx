'use client';

import { useState } from 'react';

export default function AuthPage() {
  const [isLoading, setIsLoading] = useState(false);

  const handleAuth = () => {
    setIsLoading(true);
    
    // 构建飞书 OAuth 授权 URL
    const authUrl = new URL('https://open.feishu.cn/open-apis/authen/v1/authorize');
    authUrl.searchParams.set('app_id', 'cli_a76a68f612bf900c');
    authUrl.searchParams.set('redirect_uri', 'http://localhost:3000/callback');
    authUrl.searchParams.set('state', `auth_${Date.now()}`);
    authUrl.searchParams.set('response_type', 'code');

    // 跳转到飞书授权页面
    window.location.href = authUrl.toString();
  };

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            🔑 CalMCP 授权
          </h1>
          <p className="text-gray-600">
            获取飞书用户访问令牌以访问个人日历
          </p>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <div className="space-y-6">
            <div>
              <h2 className="text-lg font-medium text-gray-900 mb-4">
                为什么需要授权？
              </h2>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  访问你的个人日历
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  读取和管理日历事件
                </li>
                <li className="flex items-start">
                  <span className="text-green-500 mr-2">✓</span>
                  通过 MCP 协议提供日历服务
                </li>
              </ul>
            </div>

            <div className="bg-blue-50 p-4 rounded-md">
              <div className="flex">
                <div className="flex-shrink-0">
                  <span className="text-blue-400">ℹ️</span>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-blue-800">
                    授权流程
                  </h3>
                  <div className="mt-2 text-sm text-blue-700">
                    <ol className="list-decimal list-inside space-y-1">
                      <li>点击下方按钮跳转到飞书授权页面</li>
                      <li>使用你的飞书账号登录并授权</li>
                      <li>授权成功后会自动跳转回来</li>
                      <li>复制获取到的用户访问令牌</li>
                      <li>更新 .env.local 配置文件</li>
                    </ol>
                  </div>
                </div>
              </div>
            </div>

            <div>
              <button
                onClick={handleAuth}
                disabled={isLoading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    跳转中...
                  </>
                ) : (
                  '🚀 开始飞书授权'
                )}
              </button>
            </div>

            <div className="text-xs text-gray-500 text-center">
              <p>
                授权后，CalMCP 将能够访问你的飞书日历数据。
                <br />
                我们不会存储你的个人信息。
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <span className="text-yellow-400">⚠️</span>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">
                注意事项
              </h3>
              <div className="mt-2 text-sm text-yellow-700">
                <ul className="list-disc list-inside space-y-1">
                  <li>确保飞书应用已配置正确的回调地址</li>
                  <li>用户访问令牌有效期较短，需要定期更新</li>
                  <li>如果授权失败，请检查应用权限配置</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
