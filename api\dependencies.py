"""
API共享依赖项
"""

import logging
from datetime import datetime, timezone

from fastapi import HTTPException

from integrations.feishu import get_feishu_client
from integrations.storage import get_token, is_token_expired, save_token

logger = logging.getLogger(__name__)


async def ensure_valid_token(user_id: str) -> str:
    """确保用户token有效，如果过期则刷新

    Args:
        user_id: 用户ID

    Returns:
        str: 有效的访问令牌

    Raises:
        HTTPException: 当token无效或刷新失败时
    """
    token = get_token(user_id)
    if not token:
        raise HTTPException(status_code=401, detail="请先授权")

    if is_token_expired(token["access_token_expire"]):
        # 获取统一的飞书客户端
        feishu_client = get_feishu_client()
        refreshed = await feishu_client.refresh_token(token["refresh_token"])
        if "code" in refreshed and refreshed["code"] != 0:
            raise HTTPException(
                status_code=401, detail="Token已过期，刷新失败，请重新授权"
            )

        new_token = refreshed["data"]
        save_token(
            user_id,
            {
                "access_token": new_token["access_token"],
                "refresh_token": new_token["refresh_token"],
                "access_token_expire": int(datetime.now(timezone.utc).timestamp())
                + new_token["expires_in"],
            },
        )

        return new_token["access_token"]
    return token["access_token"]
