# 文本日历助手设计方案

## 项目概述

本项目旨在开发一个智能文本助手，能够通过对话界面接收用户的文本输入，识别用户意图，并执行日历和日程相关的操作。系统能够区分聊天、记日记、安排日程和媒体创作等不同意图，并专注处理日历日程相关操作。当信息不完整时，系统会与用户进行交互，收集必要信息后完成操作。

## 系统架构流程图

```mermaid
flowchart TB
    START(开始) --> text_input[文本输入处理]
    text_input --> intent_classifier
    
    subgraph 意图识别阶段
        intent_classifier[意图分类器]
    end
    
    subgraph 处理阶段
        chat_handler[聊天处理]
        journal_handler[日记处理]
        calendar_handler[日历日程处理]
        media_handler[媒体创作处理]
    end
    
    subgraph 日历处理子流程
        calendar_parser[日历信息解析]
        info_validator[信息完整性验证]
        user_confirmation[用户确认]
    end
    
    subgraph 执行阶段
        calendar_executor[日历操作执行]
    end
    
    intent_classifier -->|"意图=聊天"| chat_handler
    intent_classifier -->|"意图=记日记"| journal_handler
    intent_classifier -->|"意图=日历日程"| calendar_parser
    intent_classifier -->|"意图=媒体创作"| media_handler
    
    chat_handler --> END
    journal_handler --> END
    media_handler --> END
    
    calendar_parser --> info_validator
    info_validator -->|"信息完整"| user_confirmation
    info_validator -->|"信息不完整"| info_collector[信息收集器]
    
    info_collector --> info_validator
    
    user_confirmation -->|"确认"| calendar_executor
    user_confirmation -->|"拒绝"| calendar_parser
    
    calendar_executor --> result_presenter[结果展示]
    result_presenter --> END(结束)
```

## 核心组件详解

### 1. 文本输入处理模块

#### 功能描述
- 接收并处理用户的文本输入
- 预处理文本，提高后续处理的准确性
- 处理多语言输入

#### 技术选择
- **文本预处理**: NLTK或spaCy进行基础NLP处理
- **多语言支持**: 使用langdetect进行语言检测

#### 实现要点
- 文本规范化处理
- 拼写纠正和自动补全
- 多语言支持（中文和英文）

#### 代码结构
```python
# text_processor.py
class TextProcessor:
    def __init__(self, enable_spell_check=True, enable_lang_detection=True):
        self.enable_spell_check = enable_spell_check
        self.enable_lang_detection = enable_lang_detection
        self.spell_checker = SpellChecker() if enable_spell_check else None
        
    def process_text(self, text):
        """处理输入文本"""
        # 文本规范化
        text = self._normalize_text(text)
        
        # 拼写检查（如果启用）
        if self.enable_spell_check:
            text = self._spell_check(text)
            
        # 语言检测（如果启用）
        language = None
        if self.enable_lang_detection:
            language = self._detect_language(text)
            
        return {
            "processed_text": text,
            "language": language
        }
        
    def _normalize_text(self, text):
        """规范化文本（去除多余空格、标准化标点等）"""
        # 文本规范化逻辑
        return text
        
    def _spell_check(self, text):
        """拼写检查和纠正"""
        # 拼写检查逻辑
        return text
        
    def _detect_language(self, text):
        """检测文本语言"""
        # 语言检测逻辑
        return "zh-CN"  # 默认返回中文
```

### 2. 意图分类器

#### 功能描述
- 分析用户输入文本
- 识别用户意图类别
- 提取意图相关参数

#### 意图类别
- **聊天**：一般对话，问候等
- **记日记**：记录个人事件、感想等
- **安排日程**：创建、修改、删除、查询日历事件
- **媒体创作**：生成内容、创意等

#### 实现方式
- 使用LLM进行意图分类
- 绑定工具调用，类似DeerFlow的coordinator_node
- 返回意图类型和相关参数

#### 代码实现
```python
# intent_classifier.py
def intent_classifier_node(state):
    """分析用户输入，识别意图类型"""
    user_input = state.get("processed_text", "")
    
    # 使用LLM进行意图分类
    response = (
        get_llm()
        .bind_tools([
            handle_chat, 
            handle_journal, 
            handle_calendar, 
            handle_media
        ])
        .invoke({"messages": [{"role": "user", "content": user_input}]})
    )
    
    # 检查工具调用
    if len(response.tool_calls) > 0:
        tool_name = response.tool_calls[0].get("name", "")
        tool_args = response.tool_calls[0].get("args", {})
        
        # 根据工具名确定意图
        if tool_name == "handle_calendar":
            goto = "calendar_parser"
            intent = "calendar"
        elif tool_name == "handle_journal":
            goto = "journal_handler"
            intent = "journal"
        elif tool_name == "handle_media":
            goto = "media_handler"
            intent = "media"
        else:
            goto = "chat_handler"
            intent = "chat"
    else:
        # 默认为聊天
        goto = "chat_handler"
        intent = "chat"
    
    return Command(
        update={
            "intent": intent,
            "intent_params": tool_args,
        },
        goto=goto,
    )
```

#### 意图分类提示模板
```markdown
# 意图分类指南

你是一个专门用于分类用户意图的助手。你需要将用户输入分为以下几类：

1. **聊天意图**：一般对话、问候、闲聊等
   - 例如："你好"、"今天天气怎么样"、"讲个笑话"

2. **记日记意图**：记录个人事件、感想、经历等
   - 例如："记录今天我去了公园"、"写下我今天的感受"

3. **日历日程意图**：创建、修改、删除、查询日历事件
   - 例如："明天下午3点安排会议"、"取消周五的约会"、"查看下周的行程"

4. **媒体创作意图**：生成内容、创意等
   - 例如："帮我写一首诗"、"创建一个故事"

## 执行规则
- 如果用户输入属于日历日程意图，调用handle_calendar工具，并提取相关参数
- 如果用户输入属于记日记意图，调用handle_journal工具
- 如果用户输入属于媒体创作意图，调用handle_media工具
- 如果用户输入属于聊天意图或无法确定，调用handle_chat工具

请直接使用工具，不要解释你的决定过程。
```

### 3. 日历信息解析器

#### 功能描述
- 从用户输入中提取日历相关信息
- 结构化日历事件数据
- 标准化日期时间格式

#### 提取内容
- 事件类型（会议、提醒、任务等）
- 日期时间（开始时间、结束时间）
- 地点
- 参与者
- 重复规则（每天、每周、每月等）
- 提醒设置

#### 实现方式
- 使用结构化输出的LLM提取信息
- 日期时间标准化处理
- 模糊表达解析（"明天"、"下周一"等）

#### 数据模型
```python
# models.py
from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime, time

class CalendarEvent(BaseModel):
    """日历事件数据模型"""
    title: str
    start_date: datetime
    end_date: Optional[datetime] = None
    all_day: bool = False
    location: Optional[str] = None
    description: Optional[str] = None
    participants: Optional[List[str]] = None
    recurrence: Optional[str] = None
    reminders: Optional[List[int]] = None  # 提前多少分钟提醒
    event_type: str = "default"  # meeting, task, reminder, etc.
```

#### 代码实现
```python
# calendar_parser.py
def calendar_parser_node(state):
    """从用户输入中提取日历事件信息"""
    user_input = state.get("processed_text", "")
    intent_params = state.get("intent_params", {})
    
    # 使用LLM提取结构化日历信息
    calendar_info = (
        get_llm()
        .with_structured_output(CalendarEvent)
        .invoke({"messages": [
            {"role": "system", "content": "提取日历事件信息，将模糊的时间表达转换为具体日期时间"},
            {"role": "user", "content": user_input}
        ]})
    )
    
    # 处理日期时间（如果需要）
    calendar_info = normalize_datetime(calendar_info)
    
    return Command(
        update={
            "calendar_info": calendar_info,
            "operation": intent_params.get("operation", "create"),
        },
        goto="info_validator",
    )
```

### 4. 信息完整性验证器

#### 功能描述
- 检查提取的日历信息是否完整
- 验证信息的有效性
- 确定是否需要额外信息

#### 验证项目
- 必要字段（事件名称、日期）是否存在
- 日期时间格式是否有效
- 时间冲突检测
- 信息是否足够明确

#### 实现方式
- 规则验证 + LLM辅助判断
- 字段完整性检查
- 业务逻辑验证

#### 代码实现
```python
# info_validator.py
def info_validator_node(state):
    """验证日历信息的完整性"""
    calendar_info = state.get("calendar_info", {})
    operation = state.get("operation", "create")
    
    # 检查必要字段
    missing_fields = []
    
    # 创建和修改操作需要检查的字段
    if operation in ["create", "update"]:
        if not calendar_info.get("title"):
            missing_fields.append("事件标题")
        if not calendar_info.get("start_date"):
            missing_fields.append("开始日期")
    
    # 删除和查询操作需要的字段
    elif operation in ["delete", "query"]:
        if not (calendar_info.get("title") or calendar_info.get("start_date")):
            missing_fields.append("识别条件(标题或日期)")
    
    # 检查时间冲突（仅创建和更新操作）
    conflicts = []
    if operation in ["create", "update"] and calendar_info.get("start_date"):
        conflicts = check_calendar_conflicts(calendar_info)
    
    if missing_fields:
        return Command(
            update={
                "missing_fields": missing_fields,
                "conflicts": conflicts,
            },
            goto="info_collector",
        )
    elif conflicts:
        return Command(
            update={
                "conflicts": conflicts,
            },
            goto="conflict_resolver",  # 可选的冲突解决节点
        )
    else:
        return Command(
            goto="user_confirmation",
        )
```

### 5. 信息收集器

#### 功能描述
- 与用户交互，收集缺失信息
- 提供智能建议
- 处理用户回答

#### 交互方式
- 针对性提问（"您想在什么时间安排这个会议？"）
- 提供建议选项
- 多轮对话收集

#### 实现方式
- 基于缺失字段生成提问
- 处理用户回答并更新信息
- 上下文保持

#### 代码实现
```python
# info_collector.py
def info_collector_node(state):
    """收集缺失的日历信息"""
    missing_fields = state.get("missing_fields", [])
    calendar_info = state.get("calendar_info", {})
    
    if not missing_fields:
        return Command(goto="info_validator")
    
    # 获取第一个缺失字段
    current_field = missing_fields[0]
    
    # 生成提问
    questions = {
        "事件标题": "这个事件的标题是什么？",
        "开始日期": "您想在什么时间安排这个事件？",
        "结束日期": "这个事件预计什么时间结束？（若为全天事件请回答"全天"）",
        "地点": "这个事件在哪里举行？",
        "识别条件": "请提供更多信息以识别您要操作的事件（如标题或日期）"
    }
    
    question = questions.get(current_field, f"请提供{current_field}信息")
    
    # 生成建议（如果适用）
    suggestions = generate_suggestions(current_field, calendar_info)
    if suggestions:
        question += f"\n建议: {', '.join(suggestions)}"
    
    # 中断工作流，等待用户回答
    user_response = interrupt(question)
    
    # 使用LLM解析用户回答
    updated_info = process_user_response(user_response, current_field, calendar_info)
    
    # 更新状态，移除已处理的缺失字段
    remaining_fields = missing_fields[1:] if len(missing_fields) > 1 else []
    
    return Command(
        update={
            "calendar_info": updated_info,
            "missing_fields": remaining_fields,
        },
        goto="info_validator" if not remaining_fields else "info_collector",
    )
```

### 6. 用户确认

#### 功能描述
- 向用户展示解析结果
- 获取用户确认
- 处理用户反馈

#### 展示内容
- 格式化的日历事件信息
- 可能的冲突提醒
- 操作类型说明

#### 实现方式
- 生成确认消息
- 处理用户的确认或拒绝
- 提供修改选项

#### 代码实现
```python
# user_confirmation.py
def user_confirmation_node(state):
    """获取用户对日历事件的确认"""
    calendar_info = state.get("calendar_info", {})
    operation = state.get("operation", "create")
    
    # 格式化日历信息
    operation_text = {
        "create": "创建",
        "update": "更新",
        "delete": "删除",
        "query": "查询"
    }.get(operation, "处理")
    
    # 格式化日期时间
    start_date = calendar_info.get("start_date", "")
    if start_date:
        if isinstance(start_date, datetime):
            start_date = start_date.strftime("%Y年%m月%d日 %H:%M")
    
    confirmation_msg = f"""
    请确认以下{operation_text}日历事件:
    - 标题: {calendar_info.get('title', '未指定')}
    - 日期: {start_date or '未指定'}
    """
    
    # 添加额外信息（如果有）
    if calendar_info.get("location"):
        confirmation_msg += f"- 地点: {calendar_info.get('location')}\n"
    if calendar_info.get("description"):
        confirmation_msg += f"- 描述: {calendar_info.get('description')}\n"
    
    confirmation_msg += f"\n是否{operation_text}此事件？(确认/拒绝)"
    
    # 中断工作流，等待用户确认
    user_response = interrupt(confirmation_msg)
    
    if "确认" in user_response.lower() or "是" in user_response.lower():
        return Command(goto="calendar_executor")
    else:
        # 如果用户提供了修改建议，更新日历信息
        updated_info = extract_modifications(user_response, calendar_info)
        
        return Command(
            update={"calendar_info": updated_info},
            goto="calendar_parser"  # 返回解析器重新开始
        )
```

### 7. 日历操作执行器

#### 功能描述
- 执行日历操作
- 与日历系统集成
- 处理执行结果

#### 操作类型
- 创建新事件
- 修改现有事件
- 删除事件
- 查询日程

#### 实现方式
- 与日历API集成（Google Calendar, Apple Calendar等）
- 本地日历数据库操作
- 错误处理和重试机制

#### 代码实现
```python
# calendar_executor.py
def calendar_executor_node(state):
    """执行日历操作"""
    calendar_info = state.get("calendar_info", {})
    operation = state.get("operation", "create")
    
    # 选择日历服务
    calendar_service = get_calendar_service(state.get("user_preferences", {}).get("calendar_provider", "default"))
    
    result = {}
    try:
        if operation == "create":
            result = calendar_service.create_event(calendar_info)
        elif operation == "update":
            result = calendar_service.update_event(calendar_info)
        elif operation == "delete":
            result = calendar_service.delete_event(calendar_info)
        elif operation == "query":
            result = calendar_service.query_events(calendar_info)
        
        success = True
        message = f"成功{operation_text(operation)}日历事件"
    except Exception as e:
        success = False
        message = f"操作失败: {str(e)}"
        logger.error(f"Calendar operation error: {e}")
    
    return Command(
        update={
            "operation_result": {
                "success": success,
                "message": message,
                "data": result
            },
        },
        goto="result_presenter",
    )
```

### 8. 结果展示器

#### 功能描述
- 向用户展示操作结果
- 提供后续操作建议
- 处理错误情况

#### 展示内容
- 操作成功/失败信息
- 创建/修改的事件详情
- 查询结果列表

#### 实现方式
- 格式化结果消息
- 提供交互式选项
- 错误处理和重试建议

#### 代码实现
```python
# result_presenter.py
def result_presenter_node(state):
    """向用户展示操作结果"""
    result = state.get("operation_result", {})
    operation = state.get("operation", "create")
    
    if result.get("success"):
        if operation == "query":
            # 格式化查询结果
            events = result.get("data", [])
            if events:
                message = "找到以下事件:\n\n"
                for i, event in enumerate(events[:5]):  # 限制显示数量
                    message += f"{i+1}. {event.get('title')} - {format_date(event.get('start_date'))}\n"
                
                if len(events) > 5:
                    message += f"\n...共找到 {len(events)} 个事件"
            else:
                message = "未找到符合条件的事件"
        else:
            # 创建/更新/删除结果
            message = result.get("message", "操作成功")
            
            # 添加事件详情（如果有）
            if "data" in result and operation in ["create", "update"]:
                event = result["data"]
                message += f"\n\n事件详情:\n- 标题: {event.get('title')}\n- 时间: {format_date(event.get('start_date'))}"
    else:
        # 操作失败
        message = result.get("message", "操作失败")
        message += "\n\n您可以尝试重新操作或提供更多信息。"
    
    # 向用户展示结果
    return {"message": message}
```

## 技术栈选择

### 1. 框架和库

#### 后端框架
- **LangGraph/LangChain**: 用于构建工作流和代理系统
- **FastAPI**: 后端API服务，提供高性能异步支持
- **SQLAlchemy**: 数据库ORM
- **Pydantic**: 数据验证和序列化

#### 前端框架
- **React/Next.js**: 用户界面开发
- **TailwindCSS**: UI样式
- **React Query**: 状态管理
- **Socket.io**: 实时通信

#### 文本处理
- **NLTK/spaCy**: 自然语言处理
- **langdetect**: 语言检测
- **pyspellchecker**: 拼写检查

### 2. 数据存储

#### 日历数据
- **PostgreSQL/MySQL**: 主数据库
- **Redis**: 缓存和会话存储
- **SQLite**: 轻量级本地存储选项

#### 用户数据
- **Firebase/Auth0**: 用户认证
- **MongoDB**: 用户偏好和设置存储

### 3. 外部集成

#### 日历服务
- **Google Calendar API**
- **Microsoft Graph API (Outlook)**
- **Apple Calendar API**
- **CalDAV标准**: 通用日历协议支持

#### LLM服务
- **Claude API**: 意图分类和信息提取
- **OpenAI API**: 备选LLM服务
- **Ollama**: 本地LLM部署选项

## 项目结构

```
text-calendar-assistant/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                # FastAPI入口点
│   │   ├── core/
│   │   │   ├── config.py          # 配置管理
│   │   │   └── security.py        # 安全相关
│   │   ├── api/
│   │   │   ├── endpoints/
│   │   │   │   ├── chat.py        # 聊天API
│   │   │   │   └── calendar.py    # 日历操作API
│   │   │   └── router.py          # API路由
│   │   ├── models/
│   │   │   ├── calendar.py        # 日历事件模型
│   │   │   └── user.py            # 用户模型
│   │   ├── services/
│   │   │   ├── text_processor.py  # 文本处理服务
│   │   │   └── calendar_service.py# 日历服务
│   │   └── workflow/
│   │       ├── builder.py         # 工作流构建
│   │       ├── nodes/
│   │       │   ├── intent_classifier.py
│   │       │   ├── calendar_parser.py
│   │       │   ├── info_validator.py
│   │       │   └── ...            # 其他节点
│   │       └── types.py           # 工作流类型定义
│   ├── tests/
│   │   ├── test_text_processor.py
│   │   └── test_calendar.py
│   └── requirements.txt
├── frontend/
│   ├── public/
│   ├── src/
│   │   ├── components/
│   │   │   ├── ChatInput.tsx
│   │   │   ├── Calendar.tsx
│   │   │   └── ChatInterface.tsx
│   │   ├── services/
│   │   │   ├── api.ts
│   │   │   └── chat.ts
│   │   ├── pages/
│   │   │   ├── index.tsx
│   │   │   └── calendar.tsx
│   │   └── App.tsx
│   ├── package.json
│   └── tsconfig.json
├── docker-compose.yml
└── README.md
```

## 实现路线图

### 第一阶段：基础功能（1-2周）
1. 搭建项目基础架构
2. 实现文本处理基本功能
3. 构建意图分类器
4. 开发基本日历操作（创建事件）
5. 构建简单的用户界面

### 第二阶段：增强功能（2-3周）
1. 完善信息提取和验证
2. 添加用户交互补全信息
3. 实现更多日历操作（修改、删除、查询）
4. 集成至少一种外部日历服务
5. 改进文本处理准确性

### 第三阶段：高级功能（3-4周）
1. 智能推荐（基于用户习惯）
2. 多日历同步
3. 自然语言复杂查询
4. 用户偏好设置
5. 性能优化和测试

### 第四阶段：部署和扩展（2周）
1. 容器化部署
2. 用户反馈收集和迭代
3. 多语言支持
4. 移动端适配
5. 文档和教程

## 评估指标

### 功能指标
- 意图分类准确率 > 90%
- 日历信息提取准确率 > 85%
- 操作成功率 > 99%

### 性能指标
- 文本处理响应时间 < 200ms
- 意图分类响应时间 < 500ms
- 日历操作执行时间 < 2秒
- 系统总体响应时间 < 3秒

### 用户体验指标
- 首次使用成功率 > 80%
- 任务完成时间减少 > 50%（相比手动操作）
- 用户满意度评分 > 4.5/5

## 总结

本设计方案提供了一个完整的文本日历助手系统架构，借鉴了DeerFlow的多代理协作模式，构建了从文本输入到日历操作执行的端到端流程。系统通过模块化设计，实现了高度可扩展性和可维护性，能够准确识别用户意图，提取关键信息，并在信息不完整时与用户交互补全。

通过分阶段实施，可以快速构建出基础功能，并逐步增强系统能力，最终实现一个智能、高效、用户友好的文本日历助手。 