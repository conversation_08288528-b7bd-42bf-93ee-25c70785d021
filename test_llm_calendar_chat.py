#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试LLM+MCP飞书日历聊天功能
"""

import sys
import os
import asyncio

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

from scripts.llm_mcp_cli import LLMMCPChatInterface


async def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试LLM+MCP飞书日历聊天功能...")
    
    chat_interface = LLMMCPChatInterface()
    
    try:
        # 测试启动
        print("\n1️⃣ 测试服务启动...")
        await chat_interface.start()
        print("✅ 服务启动成功")
        
        # 测试工具准备
        print("\n2️⃣ 测试工具准备...")
        tools = chat_interface._prepare_mcp_tools_for_llm()
        print(f"✅ 准备了 {len(tools)} 个工具")
        for tool in tools:
            print(f"   • {tool['function']['name']}: {tool['function']['description']}")
        
        # 测试简单对话（不涉及工具调用）
        print("\n3️⃣ 测试简单对话...")
        response = await chat_interface.process_message("你好，请介绍一下你的功能")
        print(f"✅ 对话响应: {response[:100]}...")
        
        # 测试工具相关对话
        print("\n4️⃣ 测试工具相关对话...")
        response = await chat_interface.process_message("查看我的日历列表")
        print(f"✅ 工具调用响应: {response[:100]}...")
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        # 清理资源
        await chat_interface.stop()
        print("🧹 资源清理完成")
    
    return True


async def test_mcp_connection():
    """测试MCP连接"""
    print("🧪 测试MCP连接...")
    
    from core.mcp.working_mcp_client import WorkingMCPClient
    
    mcp_client = WorkingMCPClient()
    
    try:
        # 测试MCP服务启动
        print("📡 启动MCP服务...")
        await mcp_client.start_mcp_service()
        print("✅ MCP服务启动成功")
        
        # 测试工具列表
        print(f"🔧 加载了 {len(mcp_client.tools_cache)} 个工具:")
        for tool in mcp_client.tools_cache:
            print(f"   • {tool['name']}")
        
        # 测试基本功能
        print("📅 测试获取日历列表...")
        result = await mcp_client.list_calendars()
        print(f"✅ 日历列表获取结果: {result.get('success', False)}")
        
        return True
        
    except Exception as e:
        print(f"❌ MCP连接测试失败: {str(e)}")
        return False
    
    finally:
        await mcp_client.close()


async def test_llm_client():
    """测试LLM客户端"""
    print("🧪 测试LLM客户端...")
    
    from core.ai.llm_client import LLMClient
    
    try:
        llm_client = LLMClient()
        
        # 测试简单调用
        messages = [
            {"role": "user", "content": "你好，请简单介绍一下自己"}
        ]
        
        print("🤖 调用LLM...")
        response = llm_client.invoke(messages)
        print(f"✅ LLM响应: {response.content[:100]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ LLM客户端测试失败: {str(e)}")
        return False


async def main():
    """主测试函数"""
    print("🚀 开始LLM+MCP飞书日历聊天功能测试")
    print("=" * 60)
    
    # 测试LLM客户端
    print("\n📋 测试1: LLM客户端")
    llm_success = await test_llm_client()
    
    # 测试MCP连接
    print("\n📋 测试2: MCP连接")
    mcp_success = await test_mcp_connection()
    
    # 测试完整功能
    if llm_success and mcp_success:
        print("\n📋 测试3: 完整功能")
        full_success = await test_basic_functionality()
    else:
        print("\n⚠️ 跳过完整功能测试（前置测试失败）")
        full_success = False
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   LLM客户端: {'✅ 通过' if llm_success else '❌ 失败'}")
    print(f"   MCP连接: {'✅ 通过' if mcp_success else '❌ 失败'}")
    print(f"   完整功能: {'✅ 通过' if full_success else '❌ 失败'}")
    
    if llm_success and mcp_success and full_success:
        print("\n🎉 所有测试通过！您可以开始使用飞书智能日历助手了。")
        print("💡 运行命令: python start_llm_calendar_chat.py")
        return 0
    else:
        print("\n⚠️ 部分测试失败，请检查配置和网络连接。")
        return 1


if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n👋 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行出错: {str(e)}")
        sys.exit(1)
