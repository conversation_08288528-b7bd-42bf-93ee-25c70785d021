version: '3.8'

services:
  calmcp:
    build: .
    ports:
      - "3000:3000"  # Next.js app
      - "3002:3002"  # MCP server
    environment:
      - NODE_ENV=production
      - FEISHU_APP_ID=${FEISHU_APP_ID}
      - FEISHU_APP_SECRET=${FEISHU_APP_SECRET}
      - MCP_SERVER_PORT=3002
      - NEXT_PUBLIC_API_URL=http://localhost:3000
      - LOG_LEVEL=info
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Redis for caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data

volumes:
  redis_data:
