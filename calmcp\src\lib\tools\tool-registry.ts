/**
 * 飞书日历工具注册表
 * 提供所有可用工具的统计和分类信息
 */

import { ALL_FEISHU_CALENDAR_TOOLS, type MCPTool } from './tool-adapter';

/**
 * 工具分类
 */
export const TOOL_CATEGORIES = {
  // 日历管理 (9个工具)
  CALENDAR_MANAGEMENT: [
    'calendar.v4.calendar.create',
    'calendar.v4.calendar.delete', 
    'calendar.v4.calendar.get',
    'calendar.v4.calendar.list',
    'calendar.v4.calendar.patch',
    'calendar.v4.calendar.primary',
    'calendar.v4.calendar.search',
    'calendar.v4.calendar.subscribe',
    'calendar.v4.calendar.unsubscribe'
  ],

  // 日历访问控制 (5个工具)
  CALENDAR_ACL: [
    'calendar.v4.calendarAcl.create',
    'calendar.v4.calendarAcl.delete',
    'calendar.v4.calendarAcl.list',
    'calendar.v4.calendarAcl.subscription',
    'calendar.v4.calendarAcl.unsubscription'
  ],

  // 日历事件管理 (11个工具)
  CALENDAR_EVENT: [
    'calendar.v4.calendarEvent.create',
    'calendar.v4.calendarEvent.delete',
    'calendar.v4.calendarEvent.get',
    'calendar.v4.calendarEvent.instanceView',
    'calendar.v4.calendarEvent.instances',
    'calendar.v4.calendarEvent.list',
    'calendar.v4.calendarEvent.patch',
    'calendar.v4.calendarEvent.reply',
    'calendar.v4.calendarEvent.search',
    'calendar.v4.calendarEvent.subscription',
    'calendar.v4.calendarEvent.unsubscription'
  ],

  // 日程参与者管理 (5个工具)
  EVENT_ATTENDEE: [
    'calendar.v4.calendarEventAttendee.batchDelete',
    'calendar.v4.calendarEventAttendee.chatMembersBatchCreate',
    'calendar.v4.calendarEventAttendee.create',
    'calendar.v4.calendarEventAttendee.list',
    'calendar.v4.calendarEventAttendeeChatMember.list'
  ],

  // 会议聊天 (4个工具)
  MEETING_CHAT: [
    'calendar.v4.calendarEventMeetingChat.create',
    'calendar.v4.calendarEventMeetingChat.delete',
    'calendar.v4.calendarEventMeetingChat.patch',
    'calendar.v4.calendarEventMeetingMinute.create'
  ],

  // Exchange集成 (3个工具)
  EXCHANGE_BINDING: [
    'calendar.v4.exchangeBinding.create',
    'calendar.v4.exchangeBinding.delete',
    'calendar.v4.exchangeBinding.get'
  ],

  // 其他工具 (8个工具)
  OTHERS: [
    'calendar.v4.calendar.subscription',
    'calendar.v4.calendar.unsubscription',
    'calendar.v4.freebusy.list',
    'calendar.v4.setting.generateCaldavConf',
    'calendar.v4.timeoffEvent.create',
    'calendar.v4.timeoffEvent.delete'
  ]
};

/**
 * 获取工具分类信息
 */
export function getToolCategories() {
  const categories = Object.entries(TOOL_CATEGORIES).map(([category, tools]) => ({
    category,
    count: tools.length,
    tools
  }));

  const totalTools = categories.reduce((sum, cat) => sum + cat.count, 0);

  return {
    categories,
    totalTools,
    summary: {
      '日历管理': TOOL_CATEGORIES.CALENDAR_MANAGEMENT.length,
      '访问控制': TOOL_CATEGORIES.CALENDAR_ACL.length,
      '事件管理': TOOL_CATEGORIES.CALENDAR_EVENT.length,
      '参与者管理': TOOL_CATEGORIES.EVENT_ATTENDEE.length,
      '会议聊天': TOOL_CATEGORIES.MEETING_CHAT.length,
      'Exchange集成': TOOL_CATEGORIES.EXCHANGE_BINDING.length,
      '其他工具': TOOL_CATEGORIES.OTHERS.length
    }
  };
}

/**
 * 根据分类获取工具
 */
export function getToolsByCategory(category: keyof typeof TOOL_CATEGORIES): MCPTool[] {
  const toolNames = TOOL_CATEGORIES[category];
  return ALL_FEISHU_CALENDAR_TOOLS.filter(tool => toolNames.includes(tool.name));
}

/**
 * 搜索工具
 */
export function searchTools(keyword: string): MCPTool[] {
  const lowerKeyword = keyword.toLowerCase();
  return ALL_FEISHU_CALENDAR_TOOLS.filter(tool => 
    tool.name.toLowerCase().includes(lowerKeyword) ||
    tool.description.toLowerCase().includes(lowerKeyword)
  );
}

/**
 * 获取常用工具（推荐使用）
 */
export function getCommonTools(): MCPTool[] {
  const commonToolNames = [
    'calendar.v4.calendar.list',
    'calendar.v4.calendar.get',
    'calendar.v4.calendarEvent.create',
    'calendar.v4.calendarEvent.list',
    'calendar.v4.calendarEvent.get',
    'calendar.v4.calendarEvent.patch',
    'calendar.v4.calendarEvent.delete',
    'calendar.v4.calendarEvent.search',
    'calendar.v4.calendarEventAttendee.create',
    'calendar.v4.calendarEventAttendee.list'
  ];
  
  return ALL_FEISHU_CALENDAR_TOOLS.filter(tool => commonToolNames.includes(tool.name));
}

/**
 * 工具使用统计
 */
export function getToolStats() {
  const categories = getToolCategories();
  
  return {
    totalTools: ALL_FEISHU_CALENDAR_TOOLS.length,
    expectedTotal: 43, // 官方文档中的工具总数
    isComplete: ALL_FEISHU_CALENDAR_TOOLS.length >= 43,
    categories: categories.summary,
    commonToolsCount: getCommonTools().length
  };
}

/**
 * 验证工具完整性
 */
export function validateToolCompleteness() {
  const stats = getToolStats();
  const allCategoryTools = Object.values(TOOL_CATEGORIES).flat();
  const uniqueTools = new Set(allCategoryTools);
  
  return {
    isComplete: stats.isComplete,
    totalFound: stats.totalTools,
    expectedTotal: stats.expectedTotal,
    categorizedTools: uniqueTools.size,
    uncategorizedTools: stats.totalTools - uniqueTools.size,
    duplicateInCategories: allCategoryTools.length - uniqueTools.size
  };
}
