#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Supabase Token启动LLM日历聊天
"""

import sys
import os
import asyncio
import subprocess
import time

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

def start_token_bridge_service():
    """启动Token桥接服务"""
    print("🚀 启动Token桥接服务...")
    try:
        # 启动Token桥接服务
        process = subprocess.Popen([
            sys.executable, "-m", "core.mcp.token_bridge_service"
        ], cwd=os.path.dirname(__file__))
        
        # 等待服务启动
        time.sleep(3)
        
        print("✅ Token桥接服务启动成功")
        return process
    except Exception as e:
        print(f"❌ Token桥接服务启动失败: {str(e)}")
        return None

def main():
    """主函数"""
    print("🎯 启动使用Supabase Token的飞书日历助手")
    print("-" * 60)
    
    # 启动Token桥接服务
    bridge_process = start_token_bridge_service()
    
    try:
        # 启动主程序
        print("🚀 启动主程序...")
        
        # 获取用户ID（可以从环境变量或命令行参数获取）
        user_id = os.getenv("DEFAULT_USER_ID", "default_user")
        
        # 构建启动命令
        cmd = [
            sys.executable, "simple_llm_calendar_chat.py",
            "--supabase-token",
            "--user-id", user_id
        ]
        
        print(f"📋 启动命令: {' '.join(cmd)}")
        
        # 启动主程序
        result = subprocess.run(cmd, cwd=os.path.dirname(__file__))
        
        return result.returncode
        
    except KeyboardInterrupt:
        print("\n👋 程序被用户中断")
        return 1
    except Exception as e:
        print(f"\n❌ 程序运行出错: {str(e)}")
        return 1
    finally:
        # 清理Token桥接服务
        if bridge_process:
            print("🧹 正在停止Token桥接服务...")
            bridge_process.terminate()
            try:
                bridge_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                bridge_process.kill()
            print("✅ Token桥接服务已停止")

if __name__ == "__main__":
    sys.exit(main())
