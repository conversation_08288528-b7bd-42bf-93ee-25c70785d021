# 🛠️ 本地开发指南

## 🚀 快速开始

### 1. 运行本地开发测试

```bash
python run_local_tests.py
```

这个脚本会检查：
- ✅ **基本依赖** - pytest, black, isort
- ✅ **语法检查** - Python语法正确性
- ✅ **代码格式** - Black和isort格式化
- ⚠️ **基本测试** - pytest测试（可能需要配置）

### 2. 代码格式化

如果格式化检查失败，运行：

```bash
# Windows PowerShell
black .
isort .

# 或者在Linux/Mac
black . && isort .
```

### 3. 运行应用

```bash
python main.py
```

访问 http://localhost:5000

## 📋 开发流程

### 日常开发步骤

1. **编写代码**
   ```bash
   # 编辑你的Python文件
   ```

2. **运行本地测试**
   ```bash
   python run_local_tests.py
   ```

3. **修复格式化问题**（如果有）
   ```bash
   black .
   isort .
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: 你的更改描述"
   git push origin main
   ```

5. **GitHub自动运行CI/CD**
   - 自动测试
   - 自动部署（如果测试通过）

## 🧪 测试相关

### 当前测试状态

- ✅ **语法检查** - 所有Python文件语法正确
- ✅ **代码格式** - Black和isort格式化通过
- ⚠️ **单元测试** - 需要完善测试配置

### 如果测试失败

1. **查看具体错误信息**
2. **修复导入问题**
3. **完善测试配置**
4. **或者暂时跳过测试，专注于功能开发**

## 🔧 工具说明

### run_local_tests.py

这是一个简化的本地开发测试脚本，专门为本地环境设计：

- 🟢 **宽松模式** - 不会因为小问题而失败
- 🟢 **快速检查** - 只检查最重要的内容
- 🟢 **友好提示** - 提供修复建议

### run_tests.py

这是完整的CI/CD测试脚本，模拟GitHub Actions：

- 🔴 **严格模式** - 所有检查都必须通过
- 🔴 **完整检查** - 包括安全检查、类型检查等
- 🔴 **生产准备** - 确保代码可以部署

## 📁 项目结构

```
feishu-coze-plugin/
├── api/                    # API路由
├── integrations/          # 第三方集成
├── services/             # 业务服务
├── tests/                # 测试文件
├── main.py              # 应用入口
├── run_local_tests.py   # 本地开发测试 ⭐
├── run_tests.py         # 完整CI/CD测试
└── requirements.txt     # 依赖列表
```

## 💡 开发建议

### 1. 专注于功能开发

- 使用 `python run_local_tests.py` 进行快速检查
- 不要被测试配置问题阻塞功能开发
- 完整的测试会在GitHub上自动运行

### 2. 保持代码整洁

- 定期运行 `black .` 和 `isort .`
- 提交前运行本地测试
- 遵循Python编码规范

### 3. 渐进式改进

- 先让功能工作
- 再完善测试
- 最后优化性能

## 🎯 下一步

1. **继续功能开发** - 专注于核心功能
2. **定期提交代码** - 让GitHub CI/CD验证
3. **逐步完善测试** - 在功能稳定后完善
4. **准备生产部署** - 通过完整的CI/CD流程

---

**🎉 现在你可以高效地进行本地开发了！**

记住：
- `python run_local_tests.py` - 日常开发检查
- `black . && isort .` - 代码格式化
- `python main.py` - 运行应用
