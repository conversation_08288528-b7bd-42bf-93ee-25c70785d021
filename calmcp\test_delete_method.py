#!/usr/bin/env python3
"""
测试删除日历方法
"""

import requests
import json
import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


def test_delete_method():
    """测试删除方法是否可用"""
    print("🧪 测试删除日历方法")
    print("="*60)
    
    # 首先获取一个测试日历的ID
    print("1️⃣ 获取日历列表，找到测试日历...")
    
    try:
        response = requests.post(
            "http://localhost:3000/api/mcp/tools/call",
            json={
                "name": "calendar.v4.calendar.list",
                "arguments": {"page_size": 10}
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                content = result.get('result', {}).get('content', [])
                if content:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    if feishu_response.get('code') == 0:
                        calendars = feishu_response.get('data', {}).get('calendar_list', [])
                        
                        # 找到一个测试日历
                        test_calendar = None
                        for cal in calendars:
                            name = cal.get('summary', '')
                            if '测试' in name or 'test' in name.lower():
                                test_calendar = cal
                                break
                        
                        if test_calendar:
                            calendar_id = test_calendar.get('calendar_id')
                            calendar_name = test_calendar.get('summary', '未命名')
                            
                            print(f"✅ 找到测试日历: {calendar_name}")
                            print(f"   ID: {calendar_id}")
                            
                            # 测试删除方法
                            print(f"\n2️⃣ 测试删除方法...")
                            delete_response = requests.post(
                                "http://localhost:3000/api/mcp/tools/call",
                                json={
                                    "name": "calendar.v4.calendar.delete",
                                    "arguments": {"calendar_id": calendar_id}
                                },
                                timeout=10
                            )
                            
                            print(f"🌐 删除请求状态码: {delete_response.status_code}")
                            print(f"📄 删除响应: {delete_response.text}")
                            
                            if delete_response.status_code == 200:
                                delete_result = delete_response.json()
                                if delete_result.get('success'):
                                    print("✅ 删除方法调用成功！")
                                    
                                    # 解析删除结果
                                    delete_content = delete_result.get('result', {}).get('content', [])
                                    if delete_content:
                                        delete_feishu_response = json.loads(delete_content[0].get('text', '{}'))
                                        delete_code = delete_feishu_response.get('code', -1)
                                        delete_msg = delete_feishu_response.get('msg', '无消息')
                                        
                                        print(f"   飞书删除代码: {delete_code}")
                                        print(f"   飞书删除消息: {delete_msg}")
                                        
                                        if delete_code == 0:
                                            print("🎉 日历删除成功！")
                                        else:
                                            print(f"❌ 飞书API删除失败: {delete_msg}")
                                else:
                                    print(f"❌ 删除方法调用失败: {delete_result.get('error', '未知错误')}")
                            else:
                                print(f"❌ 删除请求失败")
                        else:
                            print("⚠️  没有找到测试日历，创建一个测试日历...")
                            # 这里可以添加创建测试日历的逻辑
                            print("💡 请手动创建一个名称包含'测试'的日历后再运行此脚本")
                    else:
                        print(f"❌ 获取日历列表失败: {feishu_response.get('msg')}")
                else:
                    print("❌ 日历列表响应内容为空")
            else:
                print(f"❌ 日历列表调用失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 日历列表请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")


def test_delete_method_availability():
    """测试删除方法是否在MCP服务器中可用"""
    print("\n🔍 测试删除方法可用性")
    print("="*60)
    
    try:
        # 使用一个无效的日历ID测试方法是否存在
        response = requests.post(
            "http://localhost:3000/api/mcp/tools/call",
            json={
                "name": "calendar.v4.calendar.delete",
                "arguments": {"calendar_id": "invalid_id_for_testing"}
            },
            timeout=10
        )
        
        print(f"🌐 测试请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"📄 测试响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                content = result.get('result', {}).get('content', [])
                if content:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    code = feishu_response.get('code', -1)
                    msg = feishu_response.get('msg', '无消息')
                    
                    if msg == "Unknown method: calendar.v4.calendar.delete":
                        print("❌ 删除方法尚未在MCP服务器中实现")
                        print("💡 需要重启MCP服务器以加载新的删除方法")
                    else:
                        print("✅ 删除方法已在MCP服务器中可用")
                        print(f"   响应代码: {code}")
                        print(f"   响应消息: {msg}")
            else:
                error = result.get('error', '未知错误')
                print(f"❌ 方法调用失败: {error}")
        else:
            print(f"❌ 测试请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")


if __name__ == "__main__":
    test_delete_method_availability()
    test_delete_method()
