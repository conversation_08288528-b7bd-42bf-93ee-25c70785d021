#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API端点测试脚本
测试飞书集成的各个API端点
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta

import aiohttp

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:5000"

# 测试用户ID（从之前的授权获取）
TEST_USER_ID = "ou_57792fdcc9ab9970117ac3dbdf8a5b25"
# 真实的主日历ID
REAL_CALENDAR_ID = "<EMAIL>"


async def test_health_check():
    """测试健康检查"""
    logger.info("🏥 测试健康检查")

    async with aiohttp.ClientSession() as session:
        async with session.get(f"{BASE_URL}/") as response:
            data = await response.json()
            logger.info(f"✅ 健康检查: {data}")


async def test_calendar_list():
    """测试日历列表获取"""
    logger.info("📅 测试日历列表获取")

    async with aiohttp.ClientSession() as session:
        url = f"{BASE_URL}/auth/callback/calendars/"
        params = {"user_id": TEST_USER_ID}

        async with session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"✅ 日历列表获取成功")

                # 修正：飞书API返回的是calendar_list不是items
                calendars = data.get("data", {}).get("calendar_list", [])
                logger.info(f"   共找到 {len(calendars)} 个日历")

                for i, calendar in enumerate(calendars[:3]):
                    name = calendar.get("summary", "N/A")
                    cal_id = calendar.get("calendar_id", "N/A")
                    logger.info(f"   📅 {i+1}. {name} (ID: {cal_id})")

                return calendars
            else:
                error_text = await response.text()
                logger.error(f"❌ 日历列表获取失败: {response.status} - {error_text}")
                return []


async def test_today_events():
    """测试今日事件获取"""
    logger.info("📆 测试今日事件获取")

    async with aiohttp.ClientSession() as session:
        # 修正API路径
        url = f"{BASE_URL}/auth/callback/events/today"
        params = {
            "user_id": TEST_USER_ID,
            "calendar_id": REAL_CALENDAR_ID,  # 使用真实的日历ID
        }

        async with session.get(url, params=params) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"✅ 今日事件获取成功")

                events = data.get("data", {}).get("items", [])
                logger.info(f"   共找到 {len(events)} 个今日事件")

                for i, event in enumerate(events[:3]):
                    # 获取事件标题，如果没有则显示状态信息
                    title = event.get("summary")
                    if not title:
                        status = event.get("status", "unknown")
                        event_id = event.get("event_id", "unknown")[:8]
                        title = f"[{status}] 事件 {event_id}..."

                    start_time = event.get("start_time", {}).get("iso_format", "N/A")
                    logger.info(f"   📝 {i+1}. {title} ({start_time})")

                return events
            else:
                error_text = await response.text()
                logger.error(f"❌ 今日事件获取失败: {response.status} - {error_text}")
                return []


async def test_event_creation():
    """测试事件创建"""
    logger.info("➕ 测试事件创建")

    # 准备事件数据
    now = datetime.now()
    start_time = now + timedelta(hours=2)
    end_time = start_time + timedelta(hours=1)

    # 修正事件数据格式，使用查询参数传递user_id
    event_data = {
        "summary": f"🧪 API测试事件 - {now.strftime('%Y%m%d_%H%M%S')}",
        "description": "这是通过API创建的测试事件",
        "start_time": start_time.strftime("%Y-%m-%dT%H:%M:%S+08:00"),
        "end_time": end_time.strftime("%Y-%m-%dT%H:%M:%S+08:00"),
        "location": {"name": "API测试地点"},  # 修正location格式
        "reminders": [{"minutes": 10}],
    }

    async with aiohttp.ClientSession() as session:
        # 使用真实的日历ID
        url = f"{BASE_URL}/auth/callback/calendars/{REAL_CALENDAR_ID}/events"
        params = {"user_id": TEST_USER_ID}  # user_id作为查询参数

        async with session.post(url, json=event_data, params=params) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"✅ 事件创建成功")

                event_id = data.get("data", {}).get("event", {}).get("event_id")
                logger.info(f"   事件ID: {event_id}")
                logger.info(f"   标题: {event_data['summary']}")

                return event_id
            else:
                error_text = await response.text()
                logger.error(f"❌ 事件创建失败: {response.status} - {error_text}")
                return None


async def test_chat_api():
    """测试智能聊天API"""
    logger.info("💬 测试智能聊天API")

    chat_data = {
        "message": "明天下午3点安排一个产品评审会议",
        "user_id": TEST_USER_ID,
        "context": {"timezone": "Asia/Shanghai"},
    }

    async with aiohttp.ClientSession() as session:
        url = f"{BASE_URL}/chat/"

        async with session.post(url, json=chat_data) as response:
            if response.status == 200:
                data = await response.json()
                logger.info(f"✅ 智能聊天API测试成功")
                logger.info(f"   意图: {data.get('intent', 'N/A')}")
                logger.info(f"   响应: {data.get('message', 'N/A')}")
                logger.info(f"   成功: {data.get('success', False)}")

                return data
            else:
                error_text = await response.text()
                logger.error(
                    f"❌ 智能聊天API测试失败: {response.status} - {error_text}"
                )
                return None


async def test_event_deletion(event_id):
    """测试事件删除"""
    if not event_id:
        logger.warning("⚠️ 没有事件ID，跳过删除测试")
        return

    logger.info(f"🗑️ 测试事件删除: {event_id}")

    async with aiohttp.ClientSession() as session:
        # 使用真实的日历ID
        url = f"{BASE_URL}/auth/callback/calendars/{REAL_CALENDAR_ID}/events/{event_id}"
        params = {"user_id": TEST_USER_ID}

        async with session.delete(url, params=params) as response:
            if response.status == 200:
                logger.info(f"✅ 事件删除成功")
            else:
                error_text = await response.text()
                logger.error(f"❌ 事件删除失败: {response.status} - {error_text}")


async def run_all_tests():
    """运行所有测试"""
    logger.info("🚀 开始API端点集成测试")
    logger.info("=" * 50)

    try:
        # 1. 健康检查
        await test_health_check()

        # 2. 日历列表
        calendars = await test_calendar_list()

        # 3. 今日事件
        events = await test_today_events()

        # 4. 事件创建
        event_id = await test_event_creation()

        # 5. 智能聊天
        chat_result = await test_chat_api()

        # 6. 事件删除（清理）
        await test_event_deletion(event_id)

        logger.info("=" * 50)
        logger.info("✅ 所有API端点测试完成！")

        # 总结
        logger.info("📊 测试总结:")
        logger.info(f"   📅 日历数量: {len(calendars)}")
        logger.info(f"   📆 今日事件: {len(events)}")
        logger.info(f"   ➕ 事件创建: {'成功' if event_id else '失败'}")
        logger.info(f"   💬 智能聊天: {'成功' if chat_result else '失败'}")

    except Exception as e:
        logger.error(f"❌ 测试过程中发生错误: {str(e)}")
        raise


async def main():
    """主函数"""
    print("🚀 飞书API端点集成测试")
    print("=" * 50)
    print("此测试将验证以下API端点:")
    print("1. 健康检查 (GET /)")
    print("2. 日历列表 (GET /auth/callback/calendars/)")
    print("3. 今日事件 (GET /auth/callback/calendars/primary/events/today)")
    print("4. 事件创建 (POST /auth/callback/calendars/primary/events)")
    print("5. 智能聊天 (POST /chat/)")
    print("6. 事件删除 (DELETE /auth/callback/calendars/primary/events/{id})")
    print("=" * 50)
    print(f"测试用户ID: {TEST_USER_ID}")
    print(f"服务地址: {BASE_URL}")
    print("=" * 50)

    confirm = input("\n确认开始测试? (y/N): ").strip().lower()
    if confirm != "y":
        print("测试已取消")
        return

    await run_all_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        exit(1)
