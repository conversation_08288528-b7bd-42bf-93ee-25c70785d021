"""
事件相关的API模型
"""

from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class EventCreate(BaseModel):
    """创建事件的请求模型"""

    summary: str
    start_time: Dict[str, Any]
    end_time: Dict[str, Any]
    description: Optional[str] = None
    location: Optional[Dict[str, str]] = None
    reminders: Optional[List[Dict[str, int]]] = None
    attendees: Optional[List[Dict[str, str]]] = None
    is_all_day: Optional[bool] = False
    recurrence: Optional[str] = None


class EventCreateWithInput(BaseModel):
    """带有input字段的事件创建模型"""

    input: Optional[EventCreate] = None
    summary: Optional[str] = None
    start_time: Optional[Dict[str, Any]] = None
    end_time: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    location: Optional[Dict[str, str]] = None
    reminders: Optional[List[Dict[str, int]]] = None
    attendees: Optional[List[Dict[str, str]]] = None
    is_all_day: Optional[bool] = False
    recurrence: Optional[str] = None


class EventUpdate(BaseModel):
    """更新事件的请求模型"""

    summary: Optional[str] = None
    start_time: Optional[Dict[str, Any]] = None
    end_time: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    location: Optional[Dict[str, str]] = None
    reminders: Optional[List[Dict[str, int]]] = None
    is_all_day: Optional[bool] = None
    recurrence: Optional[str] = None
    status: Optional[str] = (
        None  # 日程状态，可选值为'confirmed'(默认)、'tentative'、'cancelled'
    )


class FlatEventCreate(BaseModel):
    """扁平化的事件创建模型，用于接收用户简化的输入格式"""

    summary: str
    description: Optional[str] = None
    start_time: str  # 直接接收ISO格式的时间字符串，如"2025-06-25T14:00:00+08:00"
    end_time: str  # 直接接收ISO格式的时间字符串，如"2025-06-25T15:00:00+08:00"
    location: Optional[List[str]] = None  # 接收地点名称列表，如["会议室A"]
    reminders: Optional[List[int]] = None  # 接收提醒时间列表，如[10, 30]
    is_all_day: Optional[bool] = False
    recurrence: Optional[str] = (
        None  # 重复规则，如"FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,WE,FR"
    )


class FlatEventUpdate(BaseModel):
    """扁平化的事件更新模型，用于接收用户简化的输入格式"""

    summary: Optional[str] = None
    description: Optional[str] = None
    start_date_time: Optional[str] = (
        None  # 直接接收ISO格式的时间字符串，如"2025-06-25T14:00:00+08:00"
    )
    end_date_time: Optional[str] = (
        None  # 直接接收ISO格式的时间字符串，如"2025-06-25T15:00:00+08:00"
    )
    location: Optional[List[str]] = None  # 接收地点名称列表，如["会议室A"]
    reminders: Optional[List[int]] = None  # 接收提醒时间列表，如[10, 30]
    is_all_day: Optional[bool] = None
    recurrence: Optional[str] = (
        None  # 重复规则，如"FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,WE,FR"
    )
    status: Optional[str] = (
        None  # 日程状态，可选值为'confirmed'(默认)、'tentative'、'cancelled'
    )


class DailyTaskCreate(BaseModel):
    """创建当天任务的请求模型"""

    summary: str
    start_time: str  # 可以是时间字符串如"10:00"或datetime对象
    duration_minutes: Optional[int] = 60
    description: Optional[str] = ""
    location: Optional[str] = None
    reminders: Optional[List[Dict[str, int]]] = None


class DateTaskCreate(BaseModel):
    """创建指定日期任务的请求模型"""

    summary: str
    task_date: str  # 日期字符串，格式为"2023-01-01"
    start_time: str  # 时间字符串，格式为"10:00"
    duration_minutes: Optional[int] = 60
    description: Optional[str] = ""
    location: Optional[str] = None
    reminders: Optional[List[Dict[str, int]]] = None


class BatchEventCreate(BaseModel):
    """批量创建事件的请求模型"""

    events: List[Dict[str, Any]]


class BatchEventUpdate(BaseModel):
    """批量更新事件的请求模型"""

    events: List[Dict[str, Any]]


class BatchEventDelete(BaseModel):
    """批量删除事件的请求模型"""

    events: List[Dict[str, str]]


class CancelEventInstance(BaseModel):
    """取消重复日程的单个实例的请求模型"""

    instance_start_time: str  # ISO格式时间字符串，如"2023-07-05T09:00:00+08:00"


class SearchQuery(BaseModel):
    """搜索日历事件的请求模型"""

    query: str
    calendar_id: Optional[str] = "primary"
    page_token: Optional[str] = None
    page_size: Optional[int] = 50
