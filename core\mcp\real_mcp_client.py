#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实的MCP客户端
参考PDeerFlow实现，使用stdio模式连接到飞书MCP服务
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class RealMCPClient:
    """真实的MCP客户端 - 参考PDeerFlow实现"""

    def __init__(self):
        self.is_connected = False
        self.process = None
        self.request_id = 0
        self.tools_cache = {}
        
    async def start_mcp_service(self):
        """启动MCP服务 - 参考PDeerFlow实现"""
        try:
            logger.info("启动飞书MCP服务...")

            # 构建启动命令 - 使用您提供的命令
            cmd = [
                "npx", "-y", "@larksuiteoapi/lark-mcp", "mcp",
                "-a", "cli_a76a68f612bf900c",
                "-s", "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC",
                "--oauth",
                "--token-mode", "user_access_token",
                "-t", "calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.list,calendar.v4.calendarEvent.create,calendar.v4.calendarEvent.search"
            ]

            logger.info(f"启动飞书MCP服务: {' '.join(cmd)}")

            # 启动进程
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )

            # 等待服务启动
            await asyncio.sleep(3)

            if self.process.returncode is not None:
                stderr = await self.process.stderr.read()
                raise Exception(f"MCP服务启动失败: {stderr.decode()}")

            logger.info("飞书MCP服务启动成功")

            # 初始化连接
            await self._initialize_connection()
            self.is_connected = True

        except Exception as e:
            logger.error(f"启动MCP服务失败: {str(e)}")
            raise

    async def _initialize_connection(self):
        """初始化MCP连接 - 参考PDeerFlow实现"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "feishu-calendar-assistant",
                        "version": "1.0.0"
                    }
                }
            }

            response = await self._send_request(init_request)
            logger.info("MCP连接初始化成功")

            # 获取工具列表
            await self._load_tools()

        except Exception as e:
            logger.error(f"MCP连接初始化失败: {str(e)}")
            raise

    async def _send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送MCP请求 - 参考PDeerFlow实现"""
        if not self.process or self.process.returncode is not None:
            raise Exception("MCP服务未运行")

        try:
            # 发送请求
            request_data = json.dumps(request) + "\n"
            self.process.stdin.write(request_data.encode())
            await self.process.stdin.drain()

            # 读取响应
            response_line = await self.process.stdout.readline()
            if not response_line:
                raise Exception("未收到MCP响应")

            response = json.loads(response_line.decode().strip())

            if "error" in response:
                raise Exception(f"MCP错误: {response['error']}")

            return response

        except Exception as e:
            logger.error(f"MCP请求失败: {str(e)}")
            raise

    async def _load_tools(self):
        """加载可用工具 - 参考PDeerFlow实现"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }

            response = await self._send_request(request)
            tools = response.get("result", {}).get("tools", [])

            for tool in tools:
                self.tools_cache[tool["name"]] = tool

            logger.info(f"加载了 {len(self.tools_cache)} 个工具: {list(self.tools_cache.keys())}")

        except Exception as e:
            logger.error(f"加载工具失败: {str(e)}")
            raise
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用MCP工具 - 参考PDeerFlow实现"""
        if tool_name not in self.tools_cache:
            raise Exception(f"工具不存在: {tool_name}")

        try:
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            response = await self._send_request(request)
            result = response.get("result", {})

            logger.info(f"工具调用成功: {tool_name}")
            return result

        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}: {str(e)}")
            raise
    
    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件"""
        try:
            # 转换为MCP工具参数格式
            arguments = {
                "summary": event_data.get("title", event_data.get("summary", "")),
                "description": event_data.get("description", ""),
                "start_time": {
                    "timestamp": event_data.get("start_time", "")
                },
                "end_time": {
                    "timestamp": event_data.get("end_time", "")
                },
                "location": event_data.get("location", ""),
                "attendee_ability": "can_see_others",
                "free_busy_status": "busy",
                "visibility": "default"
            }
            
            # 调用MCP工具
            result = await self._call_mcp_tool("calendar.v4.calendarEvent.create", arguments)
            
            return {
                "success": True,
                "event_id": result.get("data", {}).get("event", {}).get("event_id"),
                "message": "事件创建成功",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"事件创建失败: {str(e)}"
            }
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            result = await self._call_mcp_tool("calendar.v4.calendar.list", {})
            
            calendars = result.get("data", {}).get("calendar_list", [])
            
            return {
                "success": True,
                "calendars": calendars,
                "message": f"获取到 {len(calendars)} 个日历",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历列表失败: {str(e)}"
            }
    
    async def search_calendar_events(self, query: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """搜索日历事件"""
        try:
            arguments = {
                "query": query
            }
            
            if start_time:
                arguments["start_time"] = {"timestamp": start_time}
            if end_time:
                arguments["end_time"] = {"timestamp": end_time}
            
            result = await self._call_mcp_tool("calendar.v4.calendarEvent.search", arguments)
            
            events = result.get("data", {}).get("items", [])
            
            return {
                "success": True,
                "events": events,
                "message": f"搜索到 {len(events)} 个相关事件",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": f"搜索事件失败: {str(e)}"
            }
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.aclose()
        self.is_connected = False
        logger.info("MCP客户端连接已关闭")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [
            "calendar.v4.calendar.list",
            "calendar.v4.calendar.create",
            "calendar.v4.calendar.search",
            "calendar.v4.calendarEvent.create",
            "calendar.v4.calendarEvent.search"
        ]


# 全局客户端实例
_real_mcp_client: Optional[RealMCPClient] = None

def get_real_mcp_client() -> RealMCPClient:
    """获取真实MCP客户端实例"""
    global _real_mcp_client
    if _real_mcp_client is None:
        _real_mcp_client = RealMCPClient()
    return _real_mcp_client


# 测试代码已移除，保持代码整洁
