"""
端到端测试：日历API
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta

import aiohttp
import httpx
import pytest

# 确保可以导入项目模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

# API基础URL
BASE_URL = os.getenv("TEST_BASE_URL", "http://localhost:5000")
TEST_USER_ID = "test_user_e2e"

# Python 3.10+ 支持anext
if sys.version_info >= (3, 10):
    from builtins import anext
else:
    # Python 3.9及以下的兼容实现
    async def anext(agen):
        return await agen.__anext__()


class TestCalendarAPI:
    @pytest.mark.asyncio
    async def test_get_calendar_events(self, test_event):
        """测试获取日历事件列表API"""
        # 首先等待异步生成器产生值
        event_data = await anext(test_event)
        user_id = event_data["user_id"]
        calendar_id = event_data["calendar_id"]

        # 调用API获取事件列表
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{BASE_URL}/auth/callback/calendars/{calendar_id}/events",
                params={"user_id": user_id},
            )

        # 验证响应
        assert response.status_code == 200, f"API请求失败: {response.text}"

        data = response.json()
        assert isinstance(data, list), "API返回的数据不是列表"

        # 检查是否能找到测试事件
        event_found = False
        for event in data:
            if event["event_id"] == event_data["event_id"]:
                event_found = True
                break

        assert event_found, "在返回的事件列表中未找到测试事件"

    @pytest.mark.asyncio
    async def test_update_calendar_event(self, test_event):
        """测试更新日历事件API"""
        # 首先等待异步生成器产生值
        event_data = await anext(test_event)
        user_id = event_data["user_id"]
        calendar_id = event_data["calendar_id"]
        event_id = event_data["event_id"]

        # 准备更新数据
        update_data = {
            "summary": "API更新的测试事件",
            "description": "通过API更新的测试事件描述",
            "status": "confirmed",
        }

        # 调用API更新事件
        async with httpx.AsyncClient() as client:
            response = await client.patch(
                f"{BASE_URL}/auth/callback/calendars/{calendar_id}/events/{event_id}",
                params={"user_id": user_id},
                json=update_data,
            )

        # 验证响应
        assert response.status_code == 200, f"API请求失败: {response.text}"

        # 获取更新后的事件详情
        async with httpx.AsyncClient() as client:
            response = await client.get(
                f"{BASE_URL}/auth/callback/calendars/{calendar_id}/events/{event_id}",
                params={"user_id": user_id},
            )

        # 验证更新是否成功
        assert response.status_code == 200, f"获取事件详情失败: {response.text}"

        data = response.json()
        assert data["summary"] == "API更新的测试事件", "事件标题未更新"
        assert data["description"] == "通过API更新的测试事件描述", "事件描述未更新"
        assert data["status"] == "confirmed", "事件状态未更新"
