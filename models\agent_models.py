"""
代理系统相关的数据模型
基于PDeerFlow设计的结构化输出模型
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional, Union

from pydantic import BaseModel, Field, validator


class IntentType(str, Enum):
    """意图类型枚举"""

    CALENDAR = "calendar"
    CHAT = "chat"
    JOURNAL = "journal"
    MEDIA = "media"


class AgentState(BaseModel):
    """代理状态模型"""

    user_input: Optional[str] = None
    processed_text: Optional[str] = None
    intent: Optional[str] = None
    confidence: Optional[float] = None
    messages: List[Dict[str, Any]] = []
    context: Dict[str, Any] = {}

    # 日历相关状态
    calendar_plan: Optional[Dict[str, Any]] = None
    extracted_entities: Optional[Dict[str, Any]] = None
    pending_confirmation: bool = False
    last_operation: Optional[str] = None

    class Config:
        arbitrary_types_allowed = True


class CalendarAction(str, Enum):
    """日历操作类型"""

    CREATE = "create"
    QUERY = "query"
    UPDATE = "update"
    DELETE = "delete"


class RecurrenceType(str, Enum):
    """重复类型"""

    NONE = "none"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    WEEKDAYS = "weekdays"
    WEEKENDS = "weekends"


class TimeExtraction(BaseModel):
    """时间提取结果模型"""

    original_text: str = Field(..., description="原始时间文本")
    parsed_datetime: Optional[datetime] = Field(None, description="解析后的日期时间")
    time_of_day: Optional[tuple] = Field(None, description="一天中的时间 (小时, 分钟)")
    duration_minutes: Optional[int] = Field(None, description="持续时间（分钟）")
    is_relative: bool = Field(False, description="是否为相对时间")
    confidence: float = Field(0.0, description="解析置信度")

    @validator("parsed_datetime", pre=True)
    def parse_datetime_string(cls, v):
        if isinstance(v, str):
            try:
                return datetime.fromisoformat(v)
            except:
                return None
        return v


class CalendarEventExtraction(BaseModel):
    """日历事件提取模型"""

    action: CalendarAction = Field(..., description="操作类型")
    title: Optional[str] = Field(None, description="事件标题")
    description: Optional[str] = Field(None, description="事件描述")
    location: Optional[str] = Field(None, description="地点")

    # 时间相关
    start_time: Optional[TimeExtraction] = Field(None, description="开始时间")
    end_time: Optional[TimeExtraction] = Field(None, description="结束时间")
    duration: Optional[TimeExtraction] = Field(None, description="持续时间")

    # 重复和提醒
    recurrence: RecurrenceType = Field(RecurrenceType.NONE, description="重复类型")
    reminder_minutes: Optional[int] = Field(None, description="提前提醒分钟数")

    # 参与者
    attendees: List[str] = Field(default_factory=list, description="参与者列表")

    # 元数据
    confidence: float = Field(0.0, description="整体提取置信度")
    missing_fields: List[str] = Field(
        default_factory=list, description="缺失的必要字段"
    )

    @validator("missing_fields", always=True)
    def check_missing_fields(cls, v, values):
        """检查缺失的必要字段"""
        missing = []

        action = values.get("action")
        if action == CalendarAction.CREATE:
            if not values.get("title"):
                missing.append("title")
            if not values.get("start_time"):
                missing.append("start_time")
        elif action == CalendarAction.QUERY:
            # 查询操作可能不需要所有字段
            pass
        elif action in [CalendarAction.UPDATE, CalendarAction.DELETE]:
            if not values.get("title") and not values.get("start_time"):
                missing.append("identifier")  # 需要标题或时间来识别事件

        return missing


class CalendarPlan(BaseModel):
    """日历计划模型，类似PDeerFlow的Plan"""

    locale: str = Field("zh-CN", description="语言区域")
    has_enough_context: bool = Field(False, description="是否有足够的上下文信息")
    thought: str = Field(..., description="分析思路")

    # 提取的信息
    extracted_event: CalendarEventExtraction = Field(
        ..., description="提取的日历事件信息"
    )

    # 执行计划
    steps: List[Dict[str, Any]] = Field(default_factory=list, description="执行步骤")

    # 确认信息
    needs_confirmation: bool = Field(True, description="是否需要用户确认")
    confirmation_message: Optional[str] = Field(None, description="确认消息")

    @validator("needs_confirmation", always=True)
    def determine_confirmation_need(cls, v, values):
        """判断是否需要确认"""
        extracted_event = values.get("extracted_event")
        if not extracted_event:
            return True

        # 如果有缺失字段或置信度低，需要确认
        if extracted_event.missing_fields or extracted_event.confidence < 0.8:
            return True

        # 删除操作总是需要确认
        if extracted_event.action == CalendarAction.DELETE:
            return True

        return v

    @validator("confirmation_message", always=True)
    def generate_confirmation_message(cls, v, values):
        """生成确认消息"""
        if not values.get("needs_confirmation"):
            return None

        extracted_event = values.get("extracted_event")
        if not extracted_event:
            return "请提供更多信息以完成操作。"

        action_text = {
            CalendarAction.CREATE: "创建",
            CalendarAction.UPDATE: "修改",
            CalendarAction.DELETE: "删除",
            CalendarAction.QUERY: "查询",
        }.get(extracted_event.action, "处理")

        message = f"请确认{action_text}以下日历事件：\n"

        if extracted_event.title:
            message += f"• 标题：{extracted_event.title}\n"

        if extracted_event.start_time and extracted_event.start_time.parsed_datetime:
            message += f"• 开始时间：{extracted_event.start_time.parsed_datetime.strftime('%Y-%m-%d %H:%M')}\n"

        if extracted_event.location:
            message += f"• 地点：{extracted_event.location}\n"

        if extracted_event.missing_fields:
            message += f"\n缺失信息：{', '.join(extracted_event.missing_fields)}\n"

        message += "\n请回复 '确认' 继续，或提供修改建议。"

        return message


class AgentResponse(BaseModel):
    """代理响应模型"""

    agent_type: str = Field(..., description="代理类型")
    success: bool = Field(True, description="是否成功")
    message: Optional[str] = Field(None, description="响应消息")
    data: Optional[Dict[str, Any]] = Field(None, description="响应数据")
    next_action: Optional[str] = Field(None, description="下一步操作")

    # 工具调用相关
    tool_calls: List[Dict[str, Any]] = Field(
        default_factory=list, description="工具调用记录"
    )

    # 错误信息
    error: Optional[str] = Field(None, description="错误信息")
    error_code: Optional[str] = Field(None, description="错误代码")
