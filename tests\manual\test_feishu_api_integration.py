#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书正式环境API集成测试脚本
测试所有核心API功能，确保与飞书开放平台的集成正常工作
"""

import asyncio
import json
import logging
import sys
from datetime import datetime, timedelta

# 设置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 导入项目模块
from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET, REDIRECT_URI
from integrations.feishu import (
    create_event,
    delete_event,
    exchange_code,
    get_calendar_events,
    get_calendars,
    get_today_events,
    get_user_info,
)
from integrations.storage import save_token


class FeishuAPITester:
    """飞书API集成测试器"""

    def __init__(self):
        self.access_token = None
        self.test_user_id = None
        self.test_calendar_id = "primary"
        self.created_event_ids = []

    async def run_tests(self):
        """运行所有测试"""
        logger.info("🚀 开始飞书API集成测试")
        logger.info(f"📱 应用ID: {FEISHU_CLIENT_ID}")
        logger.info(f"🔗 回调地址: {REDIRECT_URI}")

        try:
            # 步骤1: 获取授权
            await self.get_authorization()

            # 步骤2: 测试用户信息
            await self.test_user_info()

            # 步骤3: 测试日历功能
            await self.test_calendar_functions()

            # 步骤4: 测试事件管理
            await self.test_event_management()

            # 步骤5: 清理测试数据
            await self.cleanup_test_data()

            logger.info("✅ 所有测试完成！飞书API集成正常工作")

        except Exception as e:
            logger.error(f"❌ 测试失败: {str(e)}")
            raise

    async def get_authorization(self):
        """获取授权"""
        logger.info("🔐 步骤1: 获取授权")

        # 生成授权URL
        auth_url = f"https://open.feishu.cn/open-apis/authen/v1/index?app_id={FEISHU_CLIENT_ID}&redirect_uri={REDIRECT_URI}"

        print(f"\n📱 请在浏览器中访问以下URL进行授权:")
        print(f"🔗 {auth_url}")
        print("\n授权完成后，请从回调URL中复制授权码 (code参数)")

        # 等待用户输入授权码
        code = input("\n请输入授权码: ").strip()
        if not code:
            raise ValueError("授权码不能为空")

        # 交换访问令牌
        logger.info("🔄 正在交换访问令牌...")
        result = await exchange_code(code)

        if result.get("code") != 0:
            raise Exception(f"获取访问令牌失败: {result}")

        token_data = result["data"]
        self.access_token = token_data["access_token"]

        logger.info("✅ 授权成功，已获取访问令牌")

    async def test_user_info(self):
        """测试用户信息获取"""
        logger.info("👤 步骤2: 测试用户信息获取")

        result = await get_user_info(self.access_token)

        if result.get("code") != 0:
            raise Exception(f"获取用户信息失败: {result}")

        user_data = result["data"]
        self.test_user_id = user_data["open_id"]

        logger.info(f"✅ 用户信息获取成功")
        logger.info(f"   用户ID: {self.test_user_id}")
        logger.info(f"   用户名: {user_data.get('name', 'N/A')}")
        logger.info(f"   头像: {user_data.get('avatar_url', 'N/A')}")

        # 保存令牌（确保refresh_token不为None）
        token_data = result["data"]
        refresh_token = token_data.get("refresh_token") or "dummy_refresh_token"

        save_token(
            self.test_user_id,
            {
                "access_token": self.access_token,
                "refresh_token": refresh_token,
                "access_token_expire": int(datetime.now().timestamp())
                + token_data.get("expires_in", 7200),
            },
        )

    async def test_calendar_functions(self):
        """测试日历功能"""
        logger.info("📅 步骤3: 测试日历功能")

        # 获取日历列表
        logger.info("📋 获取日历列表...")
        result = await get_calendars(self.access_token)

        if result.get("code") != 0:
            raise Exception(f"获取日历列表失败: {result}")

        # 检查响应数据结构
        data = result.get("data", {})
        calendars = data.get("items", [])

        logger.info(f"✅ 日历列表获取成功，共 {len(calendars)} 个日历")

        if not calendars:
            logger.warning("⚠️ 未找到任何日历，响应数据结构:")
            logger.warning(
                f"   完整响应: {json.dumps(result, ensure_ascii=False, indent=2)}"
            )
            return

        for i, calendar in enumerate(calendars[:3]):
            logger.info(
                f"   📅 {i+1}. {calendar.get('summary', 'N/A')} (ID: {calendar.get('calendar_id', 'N/A')})"
            )

        # 获取今日事件
        logger.info("📆 获取今日事件...")
        result = await get_today_events(self.access_token, self.test_calendar_id)

        if result.get("code") != 0:
            raise Exception(f"获取今日事件失败: {result}")

        # 检查响应数据结构
        data = result.get("data", {})
        events = data.get("items", [])

        logger.info(f"✅ 今日事件获取成功，共 {len(events)} 个事件")

        for i, event in enumerate(events[:3]):
            summary = event.get("summary", "N/A")
            start_time = event.get("start_time", {}).get("iso_format", "N/A")
            logger.info(f"   📝 {i+1}. {summary} ({start_time})")

    async def test_event_management(self):
        """测试事件管理"""
        logger.info("📝 步骤4: 测试事件管理")

        # 创建测试事件
        logger.info("➕ 创建测试事件...")
        now = datetime.now()
        start_time = now + timedelta(hours=2)
        end_time = start_time + timedelta(hours=1)

        event_data = {
            "summary": f"🧪 API集成测试事件 - {now.strftime('%Y%m%d_%H%M%S')}",
            "description": "这是一个自动化测试创建的事件，测试完成后会自动删除",
            "start_time": {"timestamp": str(int(start_time.timestamp()))},
            "end_time": {"timestamp": str(int(end_time.timestamp()))},
            "location": {"name": "API测试地点"},
            "reminders": [{"minutes": 10}],
        }

        result = await create_event(
            self.access_token, self.test_calendar_id, **event_data
        )

        if result.get("code") != 0:
            raise Exception(f"创建事件失败: {result}")

        event_id = result["data"]["event"]["event_id"]
        self.created_event_ids.append(event_id)

        logger.info(f"✅ 事件创建成功")
        logger.info(f"   事件ID: {event_id}")
        logger.info(f"   标题: {event_data['summary']}")
        logger.info(f"   开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")

        # 查询事件
        logger.info("🔍 查询创建的事件...")
        search_start = now.strftime("%Y-%m-%dT00:00:00+08:00")
        search_end = (now + timedelta(days=1)).strftime("%Y-%m-%dT23:59:59+08:00")

        result = await get_calendar_events(
            self.access_token,
            self.test_calendar_id,
            start_time=search_start,
            end_time=search_end,
            query="API集成测试",
        )

        if result.get("code") != 0:
            raise Exception(f"查询事件失败: {result}")

        found_events = result["data"]["items"]
        logger.info(f"✅ 事件查询成功，找到 {len(found_events)} 个匹配事件")

        # 验证创建的事件是否在查询结果中
        found_test_event = any(
            event.get("event_id") in self.created_event_ids for event in found_events
        )

        if found_test_event:
            logger.info("   ✅ 成功找到刚创建的测试事件")
        else:
            logger.warning("   ⚠️ 未在查询结果中找到测试事件")

    async def cleanup_test_data(self):
        """清理测试数据"""
        logger.info("🗑️ 步骤5: 清理测试数据")

        for event_id in self.created_event_ids:
            try:
                logger.info(f"🗑️ 删除测试事件: {event_id}")
                result = await delete_event(
                    self.access_token, self.test_calendar_id, event_id
                )

                if result.get("code") == 0:
                    logger.info(f"   ✅ 事件删除成功")
                else:
                    logger.warning(f"   ⚠️ 事件删除失败: {result}")

            except Exception as e:
                logger.error(f"   ❌ 删除事件时出错: {str(e)}")

        logger.info("✅ 测试数据清理完成")


async def main():
    """主函数"""
    print("🚀 飞书API正式环境集成测试")
    print("=" * 50)
    print("此测试将验证以下功能:")
    print("1. OAuth认证流程")
    print("2. 用户信息获取")
    print("3. 日历列表获取")
    print("4. 今日事件查询")
    print("5. 事件创建和查询")
    print("6. 事件删除")
    print("=" * 50)

    confirm = input("\n是否开始测试? (y/N): ").strip().lower()
    if confirm != "y":
        print("测试已取消")
        return

    tester = FeishuAPITester()
    await tester.run_tests()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        sys.exit(1)
