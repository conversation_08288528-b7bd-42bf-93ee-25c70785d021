# 飞书插件与MCP协议整合指南

## 🎯 整合概述

成功将MCP Streamable服务整合到主服务中，现在一个端口提供所有功能：

- **原有API服务**: 聊天、日历、事件、认证
- **MCP协议支持**: 日历工具集成
- **统一管理**: 共享飞书客户端、认证、存储

## 📊 整合前后对比

### 整合前
```
端口5000: main.py (原有API)
端口8000: MCP服务器 (独立服务)
```

### 整合后
```
端口5000: main.py (原有API + MCP协议)
```

## 🏗️ 整合架构

```
main.py (端口5000)
├── /api/chat          # 原有聊天API
├── /api/calendar      # 原有日历API  
├── /api/event         # 原有事件API
├── /api/auth          # 原有认证API
├── /health            # 原有健康检查
└── /mcp/*             # 新增MCP协议路由
    ├── /mcp/initialize
    ├── /mcp/tools/list
    ├── /mcp/tools/call
    └── /mcp/health
```

## 🔧 技术实现

### 1. 新增文件
- `api/routers/mcp.py` - MCP路由模块
- `start_integrated_server.py` - 整合启动脚本

### 2. 修改文件
- `main.py` - 集成MCP路由和初始化
- `tests/streamable_mcp/feishu_mcp_client_streamable.py` - 更新端口
- `tests/streamable_mcp/test_calendar_view.py` - 更新端口
- 各种文档文件 - 更新端口信息

### 3. 共享组件
- `FeishuCalendarLark` 飞书客户端
- `TokenStorage` 存储服务
- 配置和日志系统
- 中间件和错误处理

## 🚀 使用方法

### 启动整合服务器
```bash
# 方法1: 使用整合启动脚本
python start_integrated_server.py

# 方法2: 直接启动main.py
python main.py
```

### 访问服务
- **原有API**: http://localhost:5000/api/*
- **MCP协议**: http://localhost:5000/mcp/*
- **API文档**: http://localhost:5000/docs
- **健康检查**: http://localhost:5000/health

### 测试MCP功能
```bash
cd tests/streamable_mcp
python test_calendar_view.py
```

## 📋 可用MCP工具

| 工具名称 | 功能描述 | 参数 |
|---------|----------|------|
| `list_calendars` | 获取用户日历列表 | `user_id` |
| `get_calendar_events` | 获取日历事件 | `user_id`, `calendar_id`, `start_time`, `end_time` |
| `create_calendar_event` | 创建日历事件 | `user_id`, `title`, `start_time`, `end_time`, `calendar_id`, `description`, `location`, `attendees` |
| `get_today_events` | 获取今天的事件 | `user_id` |

## ✅ 整合优势

1. **统一服务**: 一个端口提供所有功能
2. **资源共享**: 共享飞书客户端、认证、存储
3. **简化部署**: 只需要启动一个服务
4. **统一管理**: 统一的日志、配置、错误处理
5. **避免冲突**: 避免端口冲突和资源竞争
6. **向后兼容**: 原有API完全不变

## 🔍 验证整合

### 1. 检查原有API
```bash
curl http://localhost:5000/health
curl http://localhost:5000/api/calendar/calendars
```

### 2. 检查MCP协议
```bash
curl -X POST http://localhost:5000/mcp/initialize \
  -H "Content-Type: application/json" \
  -d '{"jsonrpc":"2.0","id":"1","method":"initialize","params":{}}'
```

### 3. 检查API文档
访问 http://localhost:5000/docs 查看完整的API文档

## ⚠️ 注意事项

1. **端口变更**: MCP客户端现在连接到5000端口
2. **认证要求**: MCP功能需要OAuth认证
3. **向后兼容**: 原有API接口完全不变
4. **错误处理**: 统一的错误处理机制

## 🧹 清理建议

整合完成后，可以考虑清理以下文件：
- `tests/streamable_mcp/feishu_mcp_server_real.py` (已整合到主服务)
- `tests/streamable_mcp/start_real_server.py` (使用新的启动脚本)

保留以下文件：
- `tests/streamable_mcp/feishu_mcp_client_streamable.py` (客户端库)
- `tests/streamable_mcp/test_calendar_view.py` (测试脚本)
- 各种文档文件

## 📞 故障排除

### 1. 服务器启动失败
- 检查端口5000是否被占用
- 确认所有依赖已安装
- 检查配置文件是否正确

### 2. MCP功能异常
- 确认MCP工具已正确初始化
- 检查飞书客户端配置
- 验证OAuth认证状态

### 3. 原有API异常
- 确认路由注册正确
- 检查共享组件状态
- 验证数据库连接

## 🎉 整合完成

现在你的飞书插件服务已经成功整合了MCP协议支持，可以在一个端口上提供完整的API和MCP功能！ 