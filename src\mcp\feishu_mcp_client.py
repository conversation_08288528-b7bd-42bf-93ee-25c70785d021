#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书 MCP 客户端
基于飞书官方 MCP 服务的客户端实现
"""

import asyncio
import json
import logging
import subprocess
import os
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class FeishuMCPClient:
    """飞书 MCP 客户端"""
    
    def __init__(self, app_id: str, app_secret: str, use_oauth: bool = True):
        self.app_id = app_id
        self.app_secret = app_secret
        self.use_oauth = use_oauth
        self.process = None
        self.tools_cache = {}
        
    async def start(self):
        """启动 MCP 服务"""
        try:
            # 构建启动命令
            cmd = [
                "npx", "-y", "@larksuiteoapi/lark-mcp", "mcp",
                "-a", self.app_id,
                "-s", self.app_secret,
                "-t", "preset.calendar.default,preset.im.default",
                "-l", "zh"
            ]
            
            if self.use_oauth:
                cmd.append("--oauth")
            
            logger.info(f"启动飞书 MCP 服务: {' '.join(cmd)}")
            
            # 启动进程
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 等待服务启动
            await asyncio.sleep(2)
            
            if self.process.returncode is not None:
                stderr = await self.process.stderr.read()
                raise Exception(f"MCP 服务启动失败: {stderr.decode()}")
            
            logger.info("飞书 MCP 服务启动成功")
            
            # 初始化连接
            await self._initialize_connection()
            
        except Exception as e:
            logger.error(f"启动 MCP 服务失败: {str(e)}")
            raise
    
    async def _initialize_connection(self):
        """初始化 MCP 连接"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "llmcal",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await self._send_request(init_request)
            logger.info("MCP 连接初始化成功")
            
            # 获取工具列表
            await self._load_tools()
            
        except Exception as e:
            logger.error(f"MCP 连接初始化失败: {str(e)}")
            raise
    
    async def _send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送 MCP 请求"""
        if not self.process or self.process.returncode is not None:
            raise Exception("MCP 服务未运行")
        
        try:
            # 发送请求
            request_data = json.dumps(request) + "\n"
            self.process.stdin.write(request_data.encode())
            await self.process.stdin.drain()
            
            # 读取响应
            response_line = await self.process.stdout.readline()
            if not response_line:
                raise Exception("未收到 MCP 响应")
            
            response = json.loads(response_line.decode().strip())
            
            if "error" in response:
                raise Exception(f"MCP 错误: {response['error']}")
            
            return response
            
        except Exception as e:
            logger.error(f"MCP 请求失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """加载可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self._send_request(request)
            tools = response.get("result", {}).get("tools", [])
            
            for tool in tools:
                self.tools_cache[tool["name"]] = tool
            
            logger.info(f"加载了 {len(tools)} 个工具")
            
        except Exception as e:
            logger.error(f"加载工具失败: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用工具"""
        if tool_name not in self.tools_cache:
            raise Exception(f"工具不存在: {tool_name}")
        
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_request(request)
            result = response.get("result", {})
            
            logger.info(f"工具调用成功: {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}: {str(e)}")
            raise
    
    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件"""
        try:
            arguments = {
                "summary": event_data.get("title", ""),
                "description": event_data.get("description", ""),
                "start_time": {
                    "timestamp": event_data.get("start_date", "")
                },
                "end_time": {
                    "timestamp": event_data.get("end_date", "")
                },
                "location": event_data.get("location", ""),
                "attendee_ability": "can_see_others",
                "free_busy_status": "busy",
                "visibility": "default"
            }
            
            result = await self.call_tool("calendar_v4_calendar_event_create", arguments)
            
            return {
                "success": True,
                "event_id": result.get("data", {}).get("event", {}).get("event_id"),
                "message": "事件创建成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "事件创建失败"
            }
    
    async def get_calendar_events(self, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """获取日历事件"""
        try:
            # 首先获取主日历
            primary_result = await self.call_tool("calendar_v4_calendar_primary", {})
            calendars = primary_result.get("data", {}).get("calendars", [])
            
            if not calendars:
                logger.warning("未找到主日历")
                return []
            
            calendar_id = calendars[0].get("calendar_id")
            
            # 查询事件
            arguments = {
                "calendar_id": calendar_id,
                "start_time": start_time,
                "end_time": end_time
            }
            
            result = await self.call_tool("calendar_v4_calendar_event_list", arguments)
            events = result.get("data", {}).get("items", [])
            
            return [self._format_event(event) for event in events]
            
        except Exception as e:
            logger.error(f"获取日历事件失败: {str(e)}")
            return []
    
    def _format_event(self, event: Dict[str, Any]) -> Dict[str, Any]:
        """格式化事件数据"""
        return {
            "id": event.get("event_id"),
            "title": event.get("summary", ""),
            "description": event.get("description", ""),
            "start_date": event.get("start_time", {}).get("timestamp"),
            "end_date": event.get("end_time", {}).get("timestamp"),
            "location": event.get("location", ""),
            "status": event.get("status", "confirmed")
        }
    
    async def close(self):
        """关闭 MCP 服务"""
        if self.process:
            try:
                self.process.terminate()
                await self.process.wait()
                logger.info("飞书 MCP 服务已关闭")
            except Exception as e:
                logger.error(f"关闭 MCP 服务失败: {str(e)}")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.tools_cache.keys())


# 全局客户端实例
_feishu_mcp_client: Optional[FeishuMCPClient] = None

def get_feishu_mcp_client() -> FeishuMCPClient:
    """获取飞书 MCP 客户端实例"""
    global _feishu_mcp_client
    if _feishu_mcp_client is None:
        app_id = os.getenv("FEISHU_APP_ID")
        app_secret = os.getenv("FEISHU_APP_SECRET")
        
        if not app_id or not app_secret:
            raise Exception("请设置 FEISHU_APP_ID 和 FEISHU_APP_SECRET 环境变量")
        
        _feishu_mcp_client = FeishuMCPClient(app_id, app_secret)
    
    return _feishu_mcp_client


async def test_feishu_mcp():
    """测试飞书 MCP 客户端"""
    client = get_feishu_mcp_client()
    
    try:
        # 启动服务
        await client.start()
        
        # 显示可用工具
        tools = client.get_available_tools()
        print(f"可用工具: {tools}")
        
        # 测试创建事件
        event_data = {
            "title": "MCP 测试会议",
            "description": "通过飞书 MCP 创建的测试事件",
            "start_date": "2025-07-10T15:00:00",
            "end_date": "2025-07-10T16:00:00",
            "location": "会议室A"
        }
        
        result = await client.create_calendar_event(event_data)
        print(f"创建事件结果: {result}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
    finally:
        await client.close()


if __name__ == "__main__":
    # 设置环境变量进行测试
    os.environ["FEISHU_APP_ID"] = "your_app_id"
    os.environ["FEISHU_APP_SECRET"] = "your_app_secret"
    
    asyncio.run(test_feishu_mcp())
