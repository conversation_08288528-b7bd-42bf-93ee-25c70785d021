#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书MCP适配器
将飞书MCP服务集成到现有的工作流引擎中
"""

import asyncio
import json
import logging
import os
import subprocess
from typing import Dict, List, Any, Optional
from datetime import datetime

from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET

logger = logging.getLogger(__name__)


class FeishuMCPAdapter:
    """飞书MCP适配器 - 连接自然语言处理和飞书MCP服务"""
    
    def __init__(self, connect_to_existing=True):
        self.app_id = FEISHU_CLIENT_ID
        self.app_secret = FEISHU_CLIENT_SECRET
        self.process = None
        self.tools_cache = {}
        self.is_connected = False
        self.connect_to_existing = connect_to_existing
        
    async def start_mcp_service(self):
        """启动或连接到飞书MCP服务"""
        try:
            if self.connect_to_existing:
                # 尝试连接到已运行的MCP服务
                logger.info("尝试连接到已运行的飞书MCP服务...")
                await self._connect_to_existing_service()
            else:
                # 启动新的MCP服务
                await self._start_new_service()

            # 初始化连接
            await self._initialize_connection()
            self.is_connected = True

        except Exception as e:
            logger.error(f"连接MCP服务失败: {str(e)}")
            raise

    async def _connect_to_existing_service(self):
        """连接到已运行的MCP服务"""
        # 对于已运行的服务，我们需要模拟一个连接
        # 实际上，我们将直接调用MCP工具，假设服务已经在运行
        logger.info("假设MCP服务已在运行，准备连接...")
        # 这里我们不需要启动进程，只需要准备好调用接口

    async def _start_new_service(self):
        """启动新的MCP服务"""
        # 构建启动命令 - 使用用户提供的命令格式
        cmd = [
            "npx", "-y", "@larksuiteoapi/lark-mcp", "mcp",
            "-a", self.app_id,
            "-s", self.app_secret,
            "--oauth",
            "--token-mode", "user_access_token",
            "-t", "calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.instanceView,calendar.v4.calendarEvent.create"
        ]

        logger.info(f"启动飞书MCP服务: {' '.join(cmd)}")

        # 启动进程
        self.process = await asyncio.create_subprocess_exec(
            *cmd,
            stdin=asyncio.subprocess.PIPE,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )

        # 等待服务启动
        await asyncio.sleep(3)

        if self.process.returncode is not None:
            stderr = await self.process.stderr.read()
            raise Exception(f"MCP服务启动失败: {stderr.decode()}")

        logger.info("飞书MCP服务启动成功")
    
    async def _initialize_connection(self):
        """初始化MCP连接"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "feishu-calendar-assistant",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await self._send_request(init_request)
            logger.info("MCP连接初始化成功")
            
            # 获取工具列表
            await self._load_tools()
            
        except Exception as e:
            logger.error(f"MCP连接初始化失败: {str(e)}")
            raise
    
    async def _send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送MCP请求"""
        if not self.process or self.process.returncode is not None:
            raise Exception("MCP服务未运行")
        
        try:
            # 发送请求
            request_data = json.dumps(request) + "\n"
            self.process.stdin.write(request_data.encode())
            await self.process.stdin.drain()
            
            # 读取响应
            response_line = await self.process.stdout.readline()
            if not response_line:
                raise Exception("未收到MCP响应")
            
            response = json.loads(response_line.decode().strip())
            
            if "error" in response:
                raise Exception(f"MCP错误: {response['error']}")
            
            return response
            
        except Exception as e:
            logger.error(f"MCP请求失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """加载可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self._send_request(request)
            tools = response.get("result", {}).get("tools", [])
            
            for tool in tools:
                self.tools_cache[tool["name"]] = tool
            
            logger.info(f"加载了 {len(self.tools_cache)} 个工具: {list(self.tools_cache.keys())}")
            
        except Exception as e:
            logger.error(f"加载工具失败: {str(e)}")
            raise
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用MCP工具"""
        if not self.is_connected:
            await self.start_mcp_service()
            
        if tool_name not in self.tools_cache:
            raise Exception(f"工具不存在: {tool_name}")
        
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_request(request)
            result = response.get("result", {})
            
            logger.info(f"工具调用成功: {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}: {str(e)}")
            raise
    
    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件 - 自然语言接口"""
        try:
            # 转换为MCP工具参数格式
            arguments = {
                "summary": event_data.get("title", event_data.get("summary", "")),
                "description": event_data.get("description", ""),
                "start_time": {
                    "timestamp": event_data.get("start_time", event_data.get("start_date", ""))
                },
                "end_time": {
                    "timestamp": event_data.get("end_time", event_data.get("end_date", ""))
                },
                "location": event_data.get("location", ""),
                "attendee_ability": "can_see_others",
                "free_busy_status": "busy",
                "visibility": "default"
            }
            
            # 调用MCP工具
            result = await self.call_tool("calendar.v4.calendarEvent.create", arguments)
            
            return {
                "success": True,
                "event_id": result.get("data", {}).get("event", {}).get("event_id"),
                "message": "事件创建成功",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "事件创建失败"
            }
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            result = await self.call_tool("calendar.v4.calendar.list", {})
            
            return {
                "success": True,
                "calendars": result.get("data", {}).get("calendar_list", []),
                "message": "获取日历列表成功",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "获取日历列表失败"
            }
    
    async def search_calendar_events(self, query: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """搜索日历事件"""
        try:
            arguments = {
                "query": query
            }
            
            if start_time:
                arguments["start_time"] = {"timestamp": start_time}
            if end_time:
                arguments["end_time"] = {"timestamp": end_time}
            
            result = await self.call_tool("calendar.v4.calendarEvent.search", arguments)
            
            return {
                "success": True,
                "events": result.get("data", {}).get("items", []),
                "message": "搜索事件成功",
                "raw_result": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "搜索事件失败"
            }
    
    async def close(self):
        """关闭MCP服务"""
        if self.process:
            self.process.terminate()
            await self.process.wait()
            self.is_connected = False
            logger.info("飞书MCP服务已关闭")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return list(self.tools_cache.keys())


# 全局适配器实例
_feishu_mcp_adapter: Optional[FeishuMCPAdapter] = None

def get_feishu_mcp_adapter() -> FeishuMCPAdapter:
    """获取飞书MCP适配器实例"""
    global _feishu_mcp_adapter
    if _feishu_mcp_adapter is None:
        _feishu_mcp_adapter = FeishuMCPAdapter()
    return _feishu_mcp_adapter


# 测试代码已移除，保持代码整洁
