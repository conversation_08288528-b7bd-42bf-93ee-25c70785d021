#!/usr/bin/env python3
"""
使用正确过滤条件获取飞书日历
添加 is_deleted=false 和 is_third_party=false 过滤条件
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional, List

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class FilteredCalendarTesterV2:
    """使用正确过滤条件的日历测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 界面中显示的日历列表
        self.expected_calendars = [
            "IF ZHANG",
            "我的任务",
            "喜多多计划", 
            "真宝虾计划",
            "B站创作计划",
            "大宝暑假计划",
            "小宝暑假计划",
            "小红书计划"
        ]
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        user_id_vars = ['TEST_USER_ID', 'FEISHU_USER_ID', 'USER_ID']
        for var in user_id_vars:
            user_id = os.environ.get(var)
            if user_id:
                self.user_id = user_id
                print(f"   用户 ID: {user_id}")
                break
        
        if not self.user_token:
            print("❌ 未找到用户访问令牌")
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误 ({response.status_code}): {response.text}")
                return None
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_calendars_with_filters(self, include_deleted: bool = False, include_third_party: bool = False) -> List[Dict[str, Any]]:
        """使用过滤条件获取日历"""
        all_calendars = []
        page_token = None
        page_num = 1
        
        filter_desc = []
        if not include_deleted:
            filter_desc.append("排除已删除")
        if not include_third_party:
            filter_desc.append("排除第三方")
        
        print(f"\n📅 获取日历列表 ({', '.join(filter_desc)})")
        print("="*60)
        
        while True:
            print(f"📄 获取第 {page_num} 页...")
            
            # 构建参数，添加过滤条件
            arguments = {
                "page_size": 50,
                "is_deleted": include_deleted,  # false = 不包含已删除的日历
                "is_third_party": include_third_party  # false = 不包含第三方日历
            }
            
            if page_token:
                arguments["page_token"] = page_token
            
            print(f"   🔍 过滤参数: is_deleted={include_deleted}, is_third_party={include_third_party}")
            
            # 调用工具
            result = self.call_mcp_tool("calendar.v4.calendar.list", arguments)
            
            if not result or not result.get('success'):
                print(f"❌ 第 {page_num} 页获取失败")
                break
            
            # 解析响应
            content = result.get('result', {}).get('content', [])
            if not content:
                print(f"❌ 第 {page_num} 页响应内容为空")
                break
            
            try:
                feishu_response = json.loads(content[0].get('text', '{}'))
                
                if feishu_response.get('code') != 0:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                    break
                
                data = feishu_response.get('data', {})
                calendars = data.get('calendar_list', [])
                
                print(f"✅ 第 {page_num} 页获取到 {len(calendars)} 个日历")
                
                # 添加到总列表
                all_calendars.extend(calendars)
                
                # 检查是否有更多数据
                has_more = data.get('has_more', False)
                page_token = data.get('page_token', '')
                
                if not has_more or not page_token:
                    print(f"✅ 已获取所有数据，共 {page_num} 页")
                    break
                
                page_num += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ 第 {page_num} 页响应解析失败: {e}")
                break
        
        return all_calendars
    
    def display_calendars(self, calendars: List[Dict[str, Any]], title: str):
        """显示日历列表"""
        print(f"\n📅 {title}")
        print("="*60)
        print(f"📊 总计: {len(calendars)} 个日历")
        
        if not calendars:
            print("❌ 没有找到任何日历")
            return
        
        # 按类型分组显示
        primary_calendars = []
        shared_calendars = []
        other_calendars = []
        
        for cal in calendars:
            cal_type = cal.get('type', 'unknown')
            if cal_type == 'primary':
                primary_calendars.append(cal)
            elif cal_type == 'shared':
                shared_calendars.append(cal)
            else:
                other_calendars.append(cal)
        
        # 显示主日历
        if primary_calendars:
            print(f"\n🏠 主日历 ({len(primary_calendars)} 个):")
            for i, cal in enumerate(primary_calendars, 1):
                self._display_calendar_info(cal, i)
        
        # 显示共享日历
        if shared_calendars:
            print(f"\n👥 共享日历 ({len(shared_calendars)} 个):")
            for i, cal in enumerate(shared_calendars, 1):
                self._display_calendar_info(cal, i)
        
        # 显示其他日历
        if other_calendars:
            print(f"\n📋 其他日历 ({len(other_calendars)} 个):")
            for i, cal in enumerate(other_calendars, 1):
                self._display_calendar_info(cal, i)
    
    def _display_calendar_info(self, cal: Dict[str, Any], index: int):
        """显示单个日历信息"""
        print(f"  {index}. {cal.get('summary', '未命名日历')}")
        print(f"     📋 ID: {cal.get('calendar_id', 'N/A')}")
        print(f"     🏷️  类型: {cal.get('type', 'N/A')}")
        print(f"     🔒 权限: {cal.get('permissions', 'N/A')}")
        print(f"     👤 角色: {cal.get('role', 'N/A')}")
        
        # 显示特殊标记
        if cal.get('is_deleted'):
            print(f"     🗑️  已删除: 是")
        if cal.get('is_third_party'):
            print(f"     🔗 第三方: 是")
        
        if cal.get('description'):
            print(f"     📝 描述: {cal.get('description')}")
    
    def compare_with_expected(self, calendars: List[Dict[str, Any]]):
        """与期望的日历列表对比"""
        print(f"\n🔍 与界面显示的日历对比:")
        print("="*60)
        print(f"界面显示: {len(self.expected_calendars)} 个日历")
        print(f"API返回: {len(calendars)} 个日历")
        
        found_calendars = [cal.get('summary', '') for cal in calendars]
        
        print(f"\n✅ 匹配情况:")
        matches = 0
        for expected in self.expected_calendars:
            found = expected in found_calendars
            status = "✅" if found else "❌"
            print(f"   {status} {expected}")
            if found:
                matches += 1
        
        print(f"\n📋 API返回的所有日历:")
        for cal_name in found_calendars:
            print(f"   - {cal_name}")
        
        # 计算匹配率
        match_rate = (matches / len(self.expected_calendars)) * 100
        print(f"\n📊 匹配率: {matches}/{len(self.expected_calendars)} ({match_rate:.1f}%)")
        
        return match_rate
    
    def run_comparison_test(self):
        """运行对比测试"""
        print("🎯 飞书日历过滤条件测试")
        print("="*60)
        
        if not self.user_token:
            print("❌ 没有有效的用户凭据，无法继续测试")
            return
        
        # 测试1: 不使用过滤条件（原始查询）
        print(f"\n1️⃣ 测试：不使用过滤条件")
        calendars_all = self.get_calendars_with_filters(include_deleted=True, include_third_party=True)
        self.display_calendars(calendars_all, "所有日历（包含已删除和第三方）")
        rate_all = self.compare_with_expected(calendars_all)
        
        # 测试2: 排除已删除的日历
        print(f"\n2️⃣ 测试：排除已删除的日历")
        calendars_no_deleted = self.get_calendars_with_filters(include_deleted=False, include_third_party=True)
        self.display_calendars(calendars_no_deleted, "排除已删除的日历")
        rate_no_deleted = self.compare_with_expected(calendars_no_deleted)
        
        # 测试3: 排除第三方日历
        print(f"\n3️⃣ 测试：排除第三方日历")
        calendars_no_third_party = self.get_calendars_with_filters(include_deleted=True, include_third_party=False)
        self.display_calendars(calendars_no_third_party, "排除第三方日历")
        rate_no_third_party = self.compare_with_expected(calendars_no_third_party)
        
        # 测试4: 同时排除已删除和第三方日历（推荐）
        print(f"\n4️⃣ 测试：排除已删除和第三方日历（推荐）")
        calendars_filtered = self.get_calendars_with_filters(include_deleted=False, include_third_party=False)
        self.display_calendars(calendars_filtered, "排除已删除和第三方日历")
        rate_filtered = self.compare_with_expected(calendars_filtered)
        
        # 总结
        print(f"\n📊 测试结果总结:")
        print("="*60)
        print(f"1. 所有日历：{len(calendars_all)} 个，匹配率 {rate_all:.1f}%")
        print(f"2. 排除已删除：{len(calendars_no_deleted)} 个，匹配率 {rate_no_deleted:.1f}%")
        print(f"3. 排除第三方：{len(calendars_no_third_party)} 个，匹配率 {rate_no_third_party:.1f}%")
        print(f"4. 双重过滤：{len(calendars_filtered)} 个，匹配率 {rate_filtered:.1f}%")
        
        # 推荐最佳方案
        best_rate = max(rate_all, rate_no_deleted, rate_no_third_party, rate_filtered)
        if rate_filtered == best_rate:
            print(f"\n🎉 推荐使用：排除已删除和第三方日历（匹配率最高：{rate_filtered:.1f}%）")
        else:
            print(f"\n💡 最佳匹配率：{best_rate:.1f}%")


def main():
    """主函数"""
    tester = FilteredCalendarTesterV2()
    tester.run_comparison_test()


if __name__ == "__main__":
    main()
