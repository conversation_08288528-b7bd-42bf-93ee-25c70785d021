"""
确认服务
处理人机协作中的确认、反馈和修改流程
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

from core.ai import get_llm
from core.mcp.working_mcp_client import get_working_mcp_client
from models.conversation import ConversationContext, ConversationState, UserFeedbackType
from utils.time_parser import TimeParser

logger = logging.getLogger(__name__)


class ConfirmationService:
    """确认服务类"""

    def __init__(self):
        self.llm = get_llm()
        self.time_parser = TimeParser()

    async def process_user_feedback(
        self, user_input: str, context: ConversationContext
    ) -> Dict[str, Any]:
        """
        处理用户反馈

        Args:
            user_input: 用户输入
            context: 会话上下文

        Returns:
            处理结果字典
        """
        try:
            # 分类用户反馈
            feedback_type = self._classify_feedback(user_input, context)

            logger.info(f"用户反馈类型: {feedback_type}")

            # 根据反馈类型处理
            if feedback_type == UserFeedbackType.CONFIRM:
                return await self._handle_confirmation(context)

            elif feedback_type == UserFeedbackType.REJECT:
                return await self._handle_rejection(context)

            elif feedback_type == UserFeedbackType.MODIFY:
                return await self._handle_modification(user_input, context)

            elif feedback_type == UserFeedbackType.SUPPLEMENT:
                return await self._handle_supplement(user_input, context)

            else:
                return await self._handle_unclear_feedback(user_input, context)

        except Exception as e:
            logger.error(f"处理用户反馈失败: {e}")
            return {
                "success": False,
                "message": "抱歉，处理您的反馈时遇到问题，请重新描述您的需求。",
                "should_clear_state": True,
            }

    def _classify_feedback(
        self, user_input: str, context: ConversationContext
    ) -> UserFeedbackType:
        """分类用户反馈"""
        user_input_lower = user_input.lower().strip()

        # 确认关键词
        confirm_keywords = [
            "确认",
            "是的",
            "好的",
            "可以",
            "同意",
            "对",
            "是",
            "ok",
            "yes",
            "y",
            "执行",
        ]
        if any(keyword in user_input_lower for keyword in confirm_keywords):
            return UserFeedbackType.CONFIRM

        # 拒绝关键词
        reject_keywords = [
            "取消",
            "不要",
            "算了",
            "拒绝",
            "不",
            "no",
            "n",
            "不行",
            "不可以",
            "停止",
        ]
        if any(keyword in user_input_lower for keyword in reject_keywords):
            return UserFeedbackType.REJECT

        # 修改关键词
        modify_keywords = ["修改", "改", "更改", "调整", "换", "改成", "改为", "变更"]
        if any(keyword in user_input_lower for keyword in modify_keywords):
            return UserFeedbackType.MODIFY

        # 如果在等待补充信息状态，且不是确认/拒绝，则认为是补充信息
        if context.state == ConversationState.WAITING_SUPPLEMENT:
            return UserFeedbackType.SUPPLEMENT

        # 如果包含新的时间、地点等信息，认为是修改
        if any(
            keyword in user_input
            for keyword in ["点", "时", "分", "明天", "后天", "下周", "会议室", "地点"]
        ):
            return UserFeedbackType.MODIFY

        # 默认认为是修改建议
        return UserFeedbackType.MODIFY

    async def _handle_confirmation(
        self, context: ConversationContext
    ) -> Dict[str, Any]:
        """处理用户确认 - 执行真实的MCP操作"""
        try:
            pending_operation = context.pending_operation
            if not pending_operation:
                return {
                    "success": False,
                    "message": "没有找到待确认的操作。",
                    "should_clear_state": True,
                }

            # 执行真实的MCP操作
            logger.info(f"执行确认的操作: {pending_operation}")
            mcp_result = await self._execute_mcp_operation(pending_operation)

            action = pending_operation.get("action", "处理")
            title = pending_operation.get("title", "事件")

            if mcp_result.get("success"):
                # 根据操作类型调整消息
                if action == "create_calendar":
                    success_message = f"✅ 已成功创建日历：{title}"
                else:
                    success_message = f"✅ 已成功{action}日历事件：{title}"

                # 显示详细的事件信息
                if action in ["create_event", "create"]:
                    # 显示格式化的时间
                    if mcp_result.get("formatted_time"):
                        success_message += f"\n📅 时间：{mcp_result['formatted_time']}"
                    elif pending_operation.get("start_time"):
                        success_message += f"\n📅 时间：{pending_operation['start_time']}"

                    # 显示地点
                    if pending_operation.get("location"):
                        success_message += f"\n📍 地点：{pending_operation['location']}"

                    # 显示事件ID和日历链接
                    if mcp_result.get("event_id"):
                        success_message += f"\n🆔 事件ID：{mcp_result['event_id']}"

                    if mcp_result.get("calendar_link"):
                        success_message += f"\n🔗 查看事件：{mcp_result['calendar_link']}"

                    # 显示时间戳信息（用于调试）
                    if mcp_result.get("start_timestamp") and mcp_result.get("end_timestamp"):
                        from datetime import datetime
                        start_dt = datetime.fromtimestamp(int(mcp_result["start_timestamp"]))
                        end_dt = datetime.fromtimestamp(int(mcp_result["end_timestamp"]))
                        success_message += f"\n⏰ UTC时间：{start_dt.strftime('%Y-%m-%d %H:%M:%S')} - {end_dt.strftime('%H:%M:%S')}"
                        success_message += f"\n🕐 时间戳：{mcp_result['start_timestamp']} - {mcp_result['end_timestamp']}"
                else:
                    # 非事件创建操作的通用信息显示
                    if pending_operation.get("start_time"):
                        success_message += f"\n📅 时间：{pending_operation['start_time']}"

                    if pending_operation.get("location"):
                        success_message += f"\n📍 地点：{pending_operation['location']}"

                    # 添加MCP返回的事件ID（如果有）
                    if mcp_result.get("event_id"):
                        success_message += f"\n🆔 事件ID：{mcp_result['event_id']}"

                # 检查是否有后续复合操作步骤
                next_step = context.get_next_compound_step()
                if next_step:
                    context.advance_compound_step()

                    # 设置下一步操作
                    next_message = f"{success_message}\n\n"
                    next_message += f"继续执行步骤 {context.compound_operation.get('current_step', 2)}:\n"
                    next_message += f"创建事件 '{next_step.get('title')}'"
                    if next_step.get('start_time'):
                        next_message += f" ({next_step.get('start_time')})"
                    next_message += f" 在日历 '{next_step.get('target_calendar')}' 中"
                    next_message += "\n\n请回复 '确认' 继续，'取消' 停止。"

                    context.set_waiting_confirmation(next_step, next_message)

                    return {
                        "success": True,
                        "message": next_message,
                        "should_clear_state": False,  # 不清除状态，继续下一步
                        "operation_completed": False,
                        "has_next_step": True
                    }

                return {
                    "success": True,
                    "message": success_message,
                    "should_clear_state": True,
                    "operation_completed": True,
                    "operation_result": pending_operation,
                    "mcp_result": mcp_result,
                }
            else:
                # MCP操作失败
                error_message = f"❌ {action}日历事件失败：{mcp_result.get('message', '未知错误')}"
                return {
                    "success": False,
                    "message": error_message,
                    "should_clear_state": True,
                    "mcp_result": mcp_result,
                }

        except Exception as e:
            logger.error(f"处理确认失败: {e}")
            return {
                "success": False,
                "message": f"执行操作时遇到问题：{str(e)}",
                "should_clear_state": True,
            }

    async def _execute_mcp_operation(self, operation: Dict[str, Any]) -> Dict[str, Any]:
        """执行MCP操作"""
        try:
            adapter = get_working_mcp_client()
            action = operation.get("action")

            logger.info(f"执行MCP操作: {action}")

            if action == "create_calendar":
                # 创建日历
                calendar_data = {
                    "summary": operation.get("title", "新日历"),
                    "description": operation.get("description", "")
                }

                result = await adapter.create_calendar(calendar_data)
                return result

            elif action == "create_event" or action == "create":
                # 创建事件
                event_data = {
                    "title": operation.get("title", operation.get("summary", "新事件")),
                    "description": operation.get("description", ""),
                    "start_time": operation.get("start_time", ""),
                    "end_time": operation.get("end_time", ""),
                    "location": operation.get("location", ""),
                    "target_calendar": operation.get("target_calendar", ""),
                    # 传递LLM解析的时间戳
                    "start_timestamp": operation.get("start_timestamp", ""),
                    "end_timestamp": operation.get("end_timestamp", "")
                }

                result = await adapter.create_calendar_event(event_data)
                return result

            elif action == "query_calendars" or action == "query":
                # 查询日历列表
                result = await adapter.list_calendars()
                return result

            elif action == "update_calendar":
                # 更新日历
                calendar_data = {
                    "calendar_name": operation.get("title", ""),
                    "new_summary": operation.get("new_title", operation.get("title", "")),
                    "description": operation.get("description", "")
                }
                result = await adapter.update_calendar(calendar_data)
                return result

            elif action == "delete_calendar":
                # 删除日历
                calendar_name = operation.get("title", "")
                result = await adapter.delete_calendar(calendar_name)
                return result

            elif action == "search_events" or action == "search":
                # 搜索事件
                query = operation.get("title", operation.get("description", operation.get("query", "")))
                result = await adapter.search_calendar_events(query)
                return result

            elif action == "update_event" or action == "update":
                # 更新事件（暂时返回成功，实际需要实现）
                return {
                    "success": True,
                    "message": "事件更新功能正在开发中"
                }

            elif action == "delete_event" or action == "delete":
                # 删除事件（暂时返回成功，实际需要实现）
                return {
                    "success": True,
                    "message": "事件删除功能正在开发中"
                }

            else:
                return {
                    "success": False,
                    "message": f"不支持的操作类型: {action}"
                }

        except Exception as e:
            logger.error(f"执行MCP操作失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"执行操作时出现错误: {str(e)}"
            }

    async def _handle_rejection(self, context: ConversationContext) -> Dict[str, Any]:
        """处理用户拒绝"""
        return {
            "success": True,
            "message": "好的，已取消操作。还有什么我可以帮您的吗？",
            "should_clear_state": True,
            "operation_cancelled": True,
        }

    async def _handle_modification(
        self, user_input: str, context: ConversationContext
    ) -> Dict[str, Any]:
        """处理用户修改建议"""
        try:
            # 获取原始操作
            pending_operation = context.pending_operation or {}
            original_request = context.original_request or ""

            # 构建修改提示
            modification_prompt = f"""
用户原始请求：{original_request}
当前计划：{pending_operation}
用户修改建议：{user_input}

请分析用户的修改建议，更新日历事件信息。请以JSON格式返回：
{{
    "action": "操作类型(create/query/update/delete)",
    "title": "事件标题",
    "start_time": "开始时间描述",
    "end_time": "结束时间描述（如果有）",
    "location": "地点（如果有）",
    "attendees": ["参与者列表"],
    "description": "描述（如果有）",
    "confidence": 0.0-1.0的置信度,
    "changes_made": ["修改的字段列表"]
}}
"""

            messages = [{"role": "system", "content": modification_prompt}]

            response = self.llm.invoke(messages)
            content = response.content.strip()

            # 解析JSON响应
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            updated_operation = json.loads(content)

            # 解析时间信息
            time_info = self._parse_time_expressions(user_input)
            if time_info:
                updated_operation.update(time_info)

            # 生成新的确认消息
            confirmation_message = self._generate_confirmation_message(
                updated_operation
            )

            return {
                "success": True,
                "message": confirmation_message,
                "should_clear_state": False,
                "updated_operation": updated_operation,
                "requires_confirmation": True,
            }

        except Exception as e:
            logger.error(f"处理修改建议失败: {e}")
            return {
                "success": False,
                "message": "抱歉，理解您的修改建议时遇到问题。请重新描述您想要修改的内容。",
                "should_clear_state": False,
            }

    async def _handle_supplement(
        self, user_input: str, context: ConversationContext
    ) -> Dict[str, Any]:
        """处理用户补充信息"""
        try:
            # 获取缺失字段
            missing_fields = context.confirmation_data.get("missing_fields", [])
            pending_operation = context.pending_operation or {}

            # 从用户输入中提取补充信息
            supplement_info = await self._extract_supplement_info(
                user_input, missing_fields
            )

            # 更新操作信息
            updated_operation = {**pending_operation, **supplement_info}

            # 检查是否还有缺失字段
            still_missing = self._check_missing_fields(updated_operation)

            if still_missing:
                # 还有缺失字段，继续等待补充
                message = f"感谢您提供的信息。还需要以下信息：{', '.join(still_missing)}\n请继续补充。"
                return {
                    "success": True,
                    "message": message,
                    "should_clear_state": False,
                    "updated_operation": updated_operation,
                    "still_missing": still_missing,
                }
            else:
                # 信息完整，生成确认消息
                confirmation_message = self._generate_confirmation_message(
                    updated_operation
                )
                return {
                    "success": True,
                    "message": confirmation_message,
                    "should_clear_state": False,
                    "updated_operation": updated_operation,
                    "requires_confirmation": True,
                }

        except Exception as e:
            logger.error(f"处理补充信息失败: {e}")
            return {
                "success": False,
                "message": "抱歉，处理您提供的信息时遇到问题。请重新提供。",
                "should_clear_state": False,
            }

    async def _handle_unclear_feedback(
        self, user_input: str, context: ConversationContext
    ) -> Dict[str, Any]:
        """处理不明确的反馈"""
        return {
            "success": True,
            "message": f"我不太理解您的意思。您可以：\n• 回复'确认'来执行操作\n• 回复'取消'来取消操作\n• 或者告诉我具体要修改什么",
            "should_clear_state": False,
        }

    def _parse_time_expressions(self, user_input: str) -> Dict[str, Any]:
        """解析时间表达式"""
        try:
            result = {}

            # 使用时间解析器
            relative_time = self.time_parser.parse_relative_time(user_input)
            if relative_time:
                result["start_time"] = relative_time.strftime("%Y-%m-%d")

            time_of_day = self.time_parser.parse_time_of_day(user_input)
            if time_of_day:
                result["start_time"] = (
                    result.get("start_time", "")
                    + f" {time_of_day[0]:02d}:{time_of_day[1]:02d}"
                )

            duration = self.time_parser.parse_duration(user_input)
            if duration:
                result["duration_minutes"] = duration

            return result

        except Exception as e:
            logger.error(f"时间解析失败: {e}")
            return {}

    async def _extract_supplement_info(
        self, user_input: str, missing_fields: List[str]
    ) -> Dict[str, Any]:
        """提取补充信息"""
        # 简化实现，实际可以使用更复杂的NLP
        supplement_info = {}

        # 提取标题
        if "title" in missing_fields or "标题" in missing_fields:
            # 简单提取，实际可以用NER
            supplement_info["title"] = user_input.strip()

        # 提取地点
        if "location" in missing_fields or "地点" in missing_fields:
            location_keywords = ["在", "地点", "会议室", "办公室", "楼"]
            for keyword in location_keywords:
                if keyword in user_input:
                    # 简单提取地点信息
                    parts = user_input.split(keyword)
                    if len(parts) > 1:
                        supplement_info["location"] = parts[1].strip()
                    break

        # 提取时间信息
        time_info = self._parse_time_expressions(user_input)
        supplement_info.update(time_info)

        return supplement_info

    def _check_missing_fields(self, operation: Dict[str, Any]) -> List[str]:
        """检查缺失字段"""
        missing = []

        action = operation.get("action", "")

        if action == "create":
            if not operation.get("title"):
                missing.append("事件标题")
            if not operation.get("start_time"):
                missing.append("开始时间")

        return missing

    def _generate_confirmation_message(self, operation: Dict[str, Any]) -> str:
        """生成确认消息"""
        action_text = {
            "create": "创建",
            "update": "修改",
            "delete": "删除",
            "query": "查询",
        }.get(operation.get("action", ""), "处理")

        message = f"请确认{action_text}以下日历事件：\n"

        if operation.get("title"):
            message += f"📝 标题：{operation['title']}\n"

        if operation.get("start_time"):
            message += f"📅 时间：{operation['start_time']}\n"

        if operation.get("location"):
            message += f"📍 地点：{operation['location']}\n"

        if operation.get("attendees"):
            message += f"👥 参与者：{', '.join(operation['attendees'])}\n"

        message += "\n请回复 '确认' 继续，'取消' 放弃，或提供修改建议。"

        return message
