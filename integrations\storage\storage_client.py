import json
import logging
import os
import time
import traceback
from datetime import datetime
from typing import Any, Dict, Optional

from config import STORAGE_TYPE

# 设置日志
logger = logging.getLogger(__name__)

# 禁用hpack.hpack的DEBUG日志，仅当根日志级别不是DEBUG时
if logger.parent.level > logging.DEBUG:
    logging.getLogger("hpack.hpack").setLevel(logging.INFO)
# logging.getLogger('httpx').setLevel(logging.WARNING)
# logging.getLogger('httpcore').setLevel(logging.WARNING)

# 内存存储
store = {}

# 文件存储路径
STORAGE_FILE = "tokens.json"

# 导入Supabase客户端（仅当使用Supabase存储时）
if STORAGE_TYPE == "supabase":
    try:
        from .supabase_client import get_supabase_client

        logger.info("已启用Supabase存储")
    except ImportError:
        logger.error("未找到supabase模块，请安装: pip install supabase")
        logger.warning("降级到文件存储")
        STORAGE_TYPE = "file"
    except Exception as e:
        logger.error(f"初始化Supabase客户端失败: {str(e)}")
        logger.warning("降级到文件存储")
        STORAGE_TYPE = "file"


class TokenStorage:
    """封装令牌存储的类，支持多种存储方式"""

    def __init__(self, storage_type=None):
        """初始化令牌存储

        Args:
            storage_type: 存储类型，可选值: "memory", "file", "supabase"
        """
        self.storage_type = storage_type or STORAGE_TYPE
        logger.info(f"使用存储类型: {self.storage_type}")

        if self.storage_type == "supabase":
            try:
                self.supabase = get_supabase_client()
                self.table_name = "feishu_tokens"
            except Exception as e:
                logger.error(f"初始化Supabase存储失败: {str(e)}")
                logger.warning("降级到文件存储")
                self.storage_type = "file"

        # 如果使用文件存储，加载数据
        if self.storage_type == "file":
            self._load_from_file()

    def _load_from_file(self):
        """从文件加载数据"""
        global store
        if os.path.exists(STORAGE_FILE):
            try:
                with open(STORAGE_FILE, "r", encoding="utf-8") as f:
                    store = json.load(f)
                logger.info(f"已从{STORAGE_FILE}加载{len(store)}条token记录")
            except Exception as e:
                logger.error(f"加载token文件失败: {e}")

    def _save_to_file(self):
        """保存数据到文件"""
        try:
            with open(STORAGE_FILE, "w", encoding="utf-8") as f:
                json.dump(store, f, ensure_ascii=False, indent=2)
            logger.info(f"已保存{len(store)}条token记录到{STORAGE_FILE}")
        except Exception as e:
            logger.error(f"保存token文件失败: {e}")

    def save_token(self, user_id: str, token_data: Dict[str, Any]):
        """保存token

        Args:
            user_id: 用户标识
            token_data: 令牌数据，包含access_token、refresh_token和access_token_expire等
        """
        global store
        if self.storage_type == "supabase":
            # Supabase存储
            try:
                app_id = os.environ.get("FEISHU_CLIENT_ID", "default_app_id")
                tenant_key = user_id  # 使用user_id作为tenant_key

                now = int(time.time())
                expires_at = int(
                    float(token_data.get("access_token_expire"))
                )  # 确保转换为整数

                # 检查是否已存在记录
                try:
                    # 使用新版API格式
                    response = (
                        self.supabase.from_(self.table_name)
                        .select("*")
                        .eq("app_id", app_id)
                        .eq("tenant_key", tenant_key)
                        .execute()
                    )

                    data = response.data if hasattr(response, "data") else []

                    if data and len(data) > 0:
                        # 更新现有记录
                        self.supabase.from_(self.table_name).update(
                            {
                                "access_token": token_data.get("access_token"),
                                "refresh_token": token_data.get("refresh_token"),
                                "expires_at": expires_at,
                                "updated_at": now,
                            }
                        ).eq("app_id", app_id).eq("tenant_key", tenant_key).execute()
                        logger.info(f"已更新用户 {user_id} 的token到Supabase")
                    else:
                        # 创建新记录
                        self.supabase.from_(self.table_name).insert(
                            {
                                "app_id": app_id,
                                "tenant_key": tenant_key,
                                "access_token": token_data.get("access_token"),
                                "refresh_token": token_data.get("refresh_token"),
                                "expires_at": expires_at,
                                "created_at": now,
                                "updated_at": now,
                            }
                        ).execute()
                        logger.info(f"已保存用户 {user_id} 的token到Supabase")
                except Exception as e:
                    logger.error(f"Supabase操作失败: {str(e)}")
                    logger.error(traceback.format_exc())
                    # 降级到内存存储
                    store[user_id] = token_data
            except Exception as e:
                logger.error(f"保存token到Supabase失败: {str(e)}")
                logger.error(traceback.format_exc())
                # 降级到内存存储
                store[user_id] = token_data
        elif self.storage_type == "file":
            # 文件存储
            store[user_id] = token_data
            self._save_to_file()
        else:
            # 内存存储
            store[user_id] = token_data

    def get_token(self, user_id: str) -> Optional[Dict[str, Any]]:
        """获取指定用户的token

        Args:
            user_id: 用户标识

        Returns:
            Dict或None: 返回token数据，如果不存在则返回None
        """
        global store
        if self.storage_type == "supabase":
            # Supabase存储
            try:
                app_id = os.environ.get("FEISHU_CLIENT_ID", "default_app_id")
                tenant_key = user_id  # 使用user_id作为tenant_key

                response = (
                    self.supabase.from_(self.table_name)
                    .select("*")
                    .eq("app_id", app_id)
                    .eq("tenant_key", tenant_key)
                    .execute()
                )

                data = response.data if hasattr(response, "data") else []

                if data and len(data) > 0:
                    token_data = data[0]
                    # 转换为应用内部使用的格式
                    return {
                        "access_token": token_data.get("access_token"),
                        "refresh_token": token_data.get("refresh_token"),
                        "access_token_expire": token_data.get("expires_at"),
                    }
                return None
            except Exception as e:
                logger.error(f"从Supabase获取token失败: {str(e)}")
                logger.error(traceback.format_exc())
                # 降级到内存存储
                return store.get(user_id)
        else:
            # 内存或文件存储
            return store.get(user_id)

    def get_all_tokens(self) -> Dict[str, Dict[str, Any]]:
        """获取所有保存的token

        Returns:
            Dict: 所有token数据，键为user_id，值为token数据
        """
        global store
        if self.storage_type == "supabase":
            # Supabase存储
            try:
                app_id = os.environ.get("FEISHU_CLIENT_ID", "default_app_id")

                response = (
                    self.supabase.from_(self.table_name)
                    .select("*")
                    .eq("app_id", app_id)
                    .execute()
                )

                data = response.data if hasattr(response, "data") else []

                result = {}
                if data:
                    for token_data in data:
                        tenant_key = token_data.get("tenant_key")
                        result[tenant_key] = {
                            "access_token": token_data.get("access_token"),
                            "refresh_token": token_data.get("refresh_token"),
                            "access_token_expire": token_data.get("expires_at"),
                        }
                return result
            except Exception as e:
                logger.error(f"从Supabase获取所有token失败: {str(e)}")
                logger.error(traceback.format_exc())
                # 降级到内存存储
                return store
        else:
            # 内存或文件存储
            return store

    def get_expiring_tokens(self, threshold_seconds=600) -> Dict[str, Dict[str, Any]]:
        """获取即将过期的token

        Args:
            threshold_seconds: 过期阈值，单位为秒，默认为600秒（10分钟）

        Returns:
            字典，键为user_id，值为token数据
        """
        global store
        now = int(time.time())  # 确保是整数
        expiring = {}

        if self.storage_type == "supabase":
            # Supabase存储
            try:
                app_id = os.environ.get("FEISHU_CLIENT_ID", "default_app_id")
                expire_threshold = now + threshold_seconds

                response = (
                    self.supabase.from_(self.table_name)
                    .select("*")
                    .eq("app_id", app_id)
                    .lt("expires_at", expire_threshold)
                    .execute()
                )

                data = response.data if hasattr(response, "data") else []

                if data:
                    for token_data in data:
                        tenant_key = token_data.get("tenant_key")
                        expiring[tenant_key] = {
                            "access_token": token_data.get("access_token"),
                            "refresh_token": token_data.get("refresh_token"),
                            "access_token_expire": token_data.get("expires_at"),
                        }
                return expiring
            except Exception as e:
                logger.error(f"从Supabase获取即将过期的token失败: {str(e)}")
                # 降级到内存存储
                for user_id, token_data in store.items():
                    if token_data["access_token_expire"] - now <= threshold_seconds:
                        expiring[user_id] = token_data
                return expiring
        else:
            # 内存或文件存储
            for user_id, token_data in store.items():
                if token_data["access_token_expire"] - now <= threshold_seconds:
                    expiring[user_id] = token_data
            return expiring

    def is_token_expired(self, expire_ts) -> bool:
        """检查token是否已过期

        Args:
            expire_ts: 过期时间戳

        Returns:
            bool: 如果已过期则返回True，否则返回False
        """
        return int(time.time()) >= int(float(expire_ts))


# 创建默认的TokenStorage实例
token_storage = TokenStorage()


# 为了向后兼容，提供与之前相同的函数接口
def save_token(user_id, token_data):
    token_storage.save_token(user_id, token_data)


def get_token(user_id):
    return token_storage.get_token(user_id)


def get_all_tokens():
    return token_storage.get_all_tokens()


def get_expiring_tokens(threshold_seconds=600):
    return token_storage.get_expiring_tokens(threshold_seconds)


def is_token_expired(expire_ts):
    return token_storage.is_token_expired(expire_ts)


# 初始化时加载数据（仅适用于文件存储，为保持向后兼容）
if STORAGE_TYPE == "file":
    if os.path.exists(STORAGE_FILE):
        try:
            with open(STORAGE_FILE, "r", encoding="utf-8") as f:
                store = json.load(f)
            print(f"已从{STORAGE_FILE}加载{len(store)}条token记录")
        except Exception as e:
            print(f"加载token文件失败: {e}")
