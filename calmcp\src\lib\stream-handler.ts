/**
 * Streamable Response Handler
 * 处理流式响应的工具类
 */

import type { StreamChunk, StreamableConfig, StreamableResponse } from '@/types/mcp';

export class StreamHandler {
  private config: StreamableConfig;

  constructor(config: Partial<StreamableConfig> = {}) {
    this.config = {
      timeout: config.timeout || 30000,
      bufferSize: config.bufferSize || 1024,
      enableCompression: config.enableCompression || false
    };
  }

  /**
   * 创建流式响应
   */
  createStreamableResponse(
    generator: AsyncGenerator<any, void, unknown>
  ): StreamableResponse {
    const encoder = new TextEncoder();
    let streamId = this.generateStreamId();

    const stream = new ReadableStream({
      async start(controller) {
        try {
          // 发送开始标记
          const startChunk: StreamChunk = {
            id: streamId,
            type: 'start',
            timestamp: Date.now()
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(startChunk)}\n\n`));

          // 处理数据流
          for await (const data of generator) {
            const dataChunk: StreamChunk = {
              id: streamId,
              type: 'data',
              data,
              timestamp: Date.now()
            };
            controller.enqueue(encoder.encode(`data: ${JSON.stringify(dataChunk)}\n\n`));
          }

          // 发送结束标记
          const endChunk: StreamChunk = {
            id: streamId,
            type: 'end',
            timestamp: Date.now()
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(endChunk)}\n\n`));
          controller.close();
        } catch (error) {
          // 发送错误信息
          const errorChunk: StreamChunk = {
            id: streamId,
            type: 'error',
            error: error instanceof Error ? error.message : String(error),
            timestamp: Date.now()
          };
          controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
          controller.close();
        }
      }
    });

    return {
      stream,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Headers': 'Cache-Control'
      }
    };
  }

  /**
   * 创建批量处理的流式响应
   */
  createBatchStreamableResponse(
    items: any[],
    processor: (item: any) => Promise<any>
  ): StreamableResponse {
    const generator = this.createBatchGenerator(items, processor);
    return this.createStreamableResponse(generator);
  }

  /**
   * 创建批量处理生成器
   */
  private async* createBatchGenerator(
    items: any[],
    processor: (item: any) => Promise<any>
  ): AsyncGenerator<any, void, unknown> {
    const batchSize = Math.min(this.config.bufferSize, items.length);
    
    for (let i = 0; i < items.length; i += batchSize) {
      const batch = items.slice(i, i + batchSize);
      const results = await Promise.allSettled(
        batch.map(item => processor(item))
      );

      for (const result of results) {
        if (result.status === 'fulfilled') {
          yield result.value;
        } else {
          yield { error: result.reason };
        }
      }

      // 添加进度信息
      yield {
        progress: {
          current: Math.min(i + batchSize, items.length),
          total: items.length,
          percentage: Math.round((Math.min(i + batchSize, items.length) / items.length) * 100)
        }
      };
    }
  }

  /**
   * 创建实时数据流
   */
  createRealTimeStream(
    dataSource: () => Promise<any>,
    interval: number = 1000
  ): StreamableResponse {
    const generator = this.createRealTimeGenerator(dataSource, interval);
    return this.createStreamableResponse(generator);
  }

  /**
   * 创建实时数据生成器
   */
  private async* createRealTimeGenerator(
    dataSource: () => Promise<any>,
    interval: number
  ): AsyncGenerator<any, void, unknown> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < this.config.timeout) {
      try {
        const data = await dataSource();
        yield data;
        await this.sleep(interval);
      } catch (error) {
        yield { error: error instanceof Error ? error.message : String(error) };
        break;
      }
    }
  }

  /**
   * 创建分页数据流
   */
  createPaginatedStream(
    fetcher: (pageToken?: string) => Promise<{ data: any[]; nextPageToken?: string }>,
    maxPages: number = 10
  ): StreamableResponse {
    const generator = this.createPaginatedGenerator(fetcher, maxPages);
    return this.createStreamableResponse(generator);
  }

  /**
   * 创建分页数据生成器
   */
  private async* createPaginatedGenerator(
    fetcher: (pageToken?: string) => Promise<{ data: any[]; nextPageToken?: string }>,
    maxPages: number
  ): AsyncGenerator<any, void, unknown> {
    let pageToken: string | undefined;
    let pageCount = 0;

    while (pageCount < maxPages) {
      try {
        const result = await fetcher(pageToken);
        
        // 逐个发送数据项
        for (const item of result.data) {
          yield item;
        }

        // 发送分页信息
        yield {
          pagination: {
            page: pageCount + 1,
            hasMore: !!result.nextPageToken,
            itemCount: result.data.length
          }
        };

        if (!result.nextPageToken) {
          break;
        }

        pageToken = result.nextPageToken;
        pageCount++;
      } catch (error) {
        yield { error: error instanceof Error ? error.message : String(error) };
        break;
      }
    }
  }

  /**
   * 生成流ID
   */
  private generateStreamId(): string {
    return `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 睡眠函数
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 创建错误流
   */
  createErrorStream(error: Error): StreamableResponse {
    const encoder = new TextEncoder();
    const streamId = this.generateStreamId();

    const stream = new ReadableStream({
      start(controller) {
        const errorChunk: StreamChunk = {
          id: streamId,
          type: 'error',
          error: error.message,
          timestamp: Date.now()
        };
        controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
        controller.close();
      }
    });

    return {
      stream,
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive'
      }
    };
  }
}
