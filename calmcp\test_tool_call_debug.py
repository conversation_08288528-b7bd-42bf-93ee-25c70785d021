#!/usr/bin/env python3
"""
调试工具调用问题
"""

import requests
import json

def test_tool_call():
    """测试工具调用并显示详细错误信息"""
    
    url = "http://localhost:3000/api/mcp/tools/call"
    
    # 测试简化工具
    payload = {
        "name": "calendar_list",
        "arguments": {"page_size": 50}
    }
    
    print("🔧 测试简化工具调用...")
    print(f"URL: {url}")
    print(f"Payload: {json.dumps(payload, indent=2)}")
    
    try:
        response = requests.post(url, json=payload, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")
    
    print("\n" + "="*60)
    
    # 测试官方工具
    payload2 = {
        "name": "calendar.v4.calendar.list",
        "arguments": {"page_size": 50}
    }
    
    print("🔧 测试官方工具调用...")
    print(f"Payload: {json.dumps(payload2, indent=2)}")
    
    try:
        response = requests.post(url, json=payload2, timeout=30)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print(f"错误响应: {response.text}")
            
    except Exception as e:
        print(f"请求异常: {e}")

if __name__ == "__main__":
    test_tool_call()
