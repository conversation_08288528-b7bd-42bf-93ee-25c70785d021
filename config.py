import os

from dotenv import load_dotenv

# 获取当前环境
ENV = os.environ.get("FEISHU_ENV", "local")  # 默认为本地环境
print(f"当前运行环境: {ENV}")

# 根据环境加载不同的配置文件
env_file = f".env.{ENV}"
if os.path.exists(env_file):
    load_dotenv(env_file)
    print(f"已加载环境配置: {env_file}")
else:
    # 如果特定环境的配置文件不存在，尝试加载默认配置
    if os.path.exists(".env"):
        load_dotenv(".env")
        print("已加载默认环境配置: .env")
    else:
        print("警告: 未找到环境配置文件，使用代码中的默认值")

# 飞书应用配置
FEISHU_CLIENT_ID = os.environ.get("FEISHU_CLIENT_ID", "cli_a76a68f612bf900c")
FEISHU_CLIENT_SECRET = os.environ.get(
    "FEISHU_CLIENT_SECRET", "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC"
)

# 根据环境选择重定向URI
if ENV == "production":
    REDIRECT_URI = os.environ.get(
        "REDIRECT_URI", "https://feishu-coze-plugin.onrender.com/auth/callback"
    )
else:
    REDIRECT_URI = os.environ.get("REDIRECT_URI", "http://localhost:5000/auth/callback")

# 应用URL配置
APP_URL = os.environ.get(
    "APP_URL",
    (
        "http://localhost:5000"
        if ENV == "local"
        else "https://feishu-coze-plugin.onrender.com"
    ),
)

# 时区配置
TIMEZONE = os.environ.get("TZ", "Asia/Shanghai")

# Supabase配置
SUPABASE_URL = os.environ.get(
    "SUPABASE_URL", "https://hxxyeuiybmqgykaddljz.supabase.co"
)
SUPABASE_KEY = os.environ.get(
    "SUPABASE_KEY",
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imh4eHlldWl5Ym1xZ3lrYWRkbGp6Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjYxOTU2MCwiZXhwIjoyMDYyMTk1NTYwfQ.xRNvKTgJs9Z_d39D0x0-zAFikHifHLCI8ycaOuwnipQ",
)

# 存储配置
# 可选值: "memory"(内存), "file"(文件), "supabase"(Supabase数据库)
STORAGE_TYPE = os.environ.get(
    "STORAGE_TYPE", "supabase" if (SUPABASE_URL and SUPABASE_KEY) else "file"
)

# AI/LLM配置
SILICONFLOW_API_KEY = os.environ.get(
    "SILICONFLOW_API_KEY", "sk-meymmjxsongcbfpmppynturctxgdfillrzhevwumnxijpgye"
)
LLM_MODEL = os.environ.get("LLM_MODEL", "Qwen/Qwen3-14B")  # 默认模型
LLM_TEMPERATURE = float(os.environ.get("LLM_TEMPERATURE", "0.7"))  # 默认温度
LLM_MAX_TOKENS = int(os.environ.get("LLM_MAX_TOKENS", "2048"))  # 最大生成token数

# 工作流配置
MAX_RETRY_ATTEMPTS = 3  # 最大重试次数
DEFAULT_LANGUAGE = "zh-CN"  # 默认语言

# 日历配置
DEFAULT_MEETING_DURATION = 60  # 默认会议时长（分钟）
DEFAULT_REMINDER_TIME = 15  # 默认提醒时间（分钟）

# 日志配置
LOG_LEVEL = os.environ.get("LOG_LEVEL", "INFO")
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# 项目根目录
from pathlib import Path

ROOT_DIR = Path(__file__).parent.absolute()
