#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动真实飞书MCP服务器
"""

import os
import sys
import logging
import subprocess
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("start_real_server")


def check_dependencies():
    """检查依赖"""
    logger.info("🔍 检查依赖...")
    
    try:
        import fastapi
        import uvicorn
        import aiohttp
        logger.info("✅ 基础依赖检查通过")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖: {e}")
        logger.info("💡 请运行: pip install fastapi uvicorn aiohttp")
        return False
    
    try:
        from integrations.feishu import FeishuCalendarLark
        from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
        logger.info("✅ 飞书集成依赖检查通过")
        logger.info(f"📋 飞书应用ID: {FEISHU_CLIENT_ID}")
    except ImportError as e:
        logger.error(f"❌ 飞书集成依赖检查失败: {e}")
        return False
    
    return True


def check_config():
    """检查配置"""
    logger.info("⚙️ 检查配置...")
    
    try:
        from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
        
        if not FEISHU_CLIENT_ID or FEISHU_CLIENT_ID == "your_client_id":
            logger.error("❌ 飞书应用ID未配置")
            logger.info("💡 请在config.py中设置正确的FEISHU_CLIENT_ID")
            return False
        
        if not FEISHU_CLIENT_SECRET or FEISHU_CLIENT_SECRET == "your_client_secret":
            logger.error("❌ 飞书应用密钥未配置")
            logger.info("💡 请在config.py中设置正确的FEISHU_CLIENT_SECRET")
            return False
        
        logger.info("✅ 配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False


def start_server():
    """启动服务器"""
    logger.info("🚀 启动真实飞书MCP服务器...")
    
    try:
        # 检查服务器文件是否存在
        server_file = "feishu_mcp_server_real.py"
        if not os.path.exists(server_file):
            logger.error(f"❌ 服务器文件不存在: {server_file}")
            return False
        
        # 启动服务器
        logger.info("📡 启动HTTP服务器...")
        logger.info("🌐 服务器地址: http://localhost:8000")
        logger.info("📋 API文档: http://localhost:8000/docs")
        logger.info("🏥 健康检查: http://localhost:8000/health")
        logger.info("=" * 50)
        
        # 使用subprocess启动服务器
        cmd = [sys.executable, server_file]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 等待服务器启动
        logger.info("⏳ 等待服务器启动...")
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            logger.info("✅ 服务器启动成功!")
            logger.info("💡 按 Ctrl+C 停止服务器")
            
            try:
                # 实时显示服务器输出
                for line in process.stdout:
                    print(line.rstrip())
            except KeyboardInterrupt:
                logger.info("⏹️ 正在停止服务器...")
                process.terminate()
                process.wait()
                logger.info("✅ 服务器已停止")
        else:
            logger.error("❌ 服务器启动失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 启动服务器失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    logger.info("🎭 真实飞书MCP服务器启动器")
    logger.info("=" * 40)
    
    # 1. 检查依赖
    if not check_dependencies():
        return False
    
    # 2. 检查配置
    if not check_config():
        return False
    
    # 3. 启动服务器
    if not start_server():
        return False
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("⏹️ 启动被用户中断")
    except Exception as e:
        logger.error(f"❌ 启动异常: {e}")
        sys.exit(1) 