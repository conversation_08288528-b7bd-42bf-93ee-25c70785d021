#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的飞书MCP适配器
直接使用飞书API，不依赖MCP服务
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
import httpx

from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET

logger = logging.getLogger(__name__)


class SimpleFeishuAdapter:
    """简化的飞书适配器 - 直接调用飞书API"""
    
    def __init__(self):
        self.app_id = FEISHU_CLIENT_ID
        self.app_secret = FEISHU_CLIENT_SECRET
        self.access_token = None
        self.is_connected = False
        
    async def start_mcp_service(self):
        """初始化服务（获取访问令牌）"""
        try:
            logger.info("初始化飞书API连接...")
            
            # 获取app access token
            await self._get_app_access_token()
            
            self.is_connected = True
            logger.info("✅ 飞书API连接成功")
            
        except Exception as e:
            logger.error(f"初始化飞书API失败: {str(e)}")
            raise
    
    async def _get_app_access_token(self):
        """获取应用访问令牌"""
        url = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal"
        
        payload = {
            "app_id": self.app_id,
            "app_secret": self.app_secret
        }
        
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload)
            result = response.json()
            
            if result.get("code") == 0:
                self.access_token = result["app_access_token"]
                logger.info("成功获取应用访问令牌")
            else:
                raise Exception(f"获取访问令牌失败: {result}")
    
    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件"""
        try:
            # 注意：这里需要用户访问令牌，而不是应用访问令牌
            # 为了演示，我们返回一个模拟的成功响应
            logger.info(f"模拟创建日历事件: {event_data}")
            
            return {
                "success": True,
                "event_id": f"mock_event_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "message": "事件创建成功（模拟）",
                "note": "这是模拟响应，实际需要用户授权和用户访问令牌"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "事件创建失败"
            }
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            logger.info("模拟获取日历列表")
            
            # 模拟日历数据
            mock_calendars = [
                {"calendar_id": "cal_001", "summary": "工作日历", "type": "primary"},
                {"calendar_id": "cal_002", "summary": "个人日历", "type": "shared"},
                {"calendar_id": "cal_003", "summary": "项目日历", "type": "shared"},
                {"calendar_id": "cal_004", "summary": "会议室预订", "type": "shared"},
                {"calendar_id": "cal_005", "summary": "培训安排", "type": "shared"}
            ]
            
            return {
                "success": True,
                "calendars": mock_calendars,
                "message": "获取日历列表成功（模拟）",
                "note": "这是模拟数据，实际需要用户授权和用户访问令牌"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "获取日历列表失败"
            }
    
    async def search_calendar_events(self, query: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """搜索日历事件"""
        try:
            logger.info(f"模拟搜索日历事件: {query}")
            
            # 模拟搜索结果
            mock_events = []
            
            if "项目" in query:
                mock_events = [
                    {
                        "event_id": "event_001",
                        "summary": "项目进度会议",
                        "start_time": "2025-07-16T15:00:00+08:00",
                        "end_time": "2025-07-16T16:00:00+08:00",
                        "location": "会议室A"
                    },
                    {
                        "event_id": "event_002", 
                        "summary": "项目评审会议",
                        "start_time": "2025-07-18T10:00:00+08:00",
                        "end_time": "2025-07-18T11:30:00+08:00",
                        "location": "会议室B"
                    }
                ]
            elif "会议" in query:
                mock_events = [
                    {
                        "event_id": "event_003",
                        "summary": "周例会",
                        "start_time": "2025-07-17T09:00:00+08:00",
                        "end_time": "2025-07-17T10:00:00+08:00",
                        "location": "线上会议"
                    }
                ]
            
            return {
                "success": True,
                "events": mock_events,
                "message": f"搜索到 {len(mock_events)} 个相关事件（模拟）",
                "note": "这是模拟数据，实际需要用户授权和用户访问令牌"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "message": "搜索事件失败"
            }
    
    async def close(self):
        """关闭连接"""
        self.is_connected = False
        logger.info("飞书API连接已关闭")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [
            "calendar.v4.calendar.list",
            "calendar.v4.calendarEvent.create", 
            "calendar.v4.calendarEvent.search"
        ]


# 全局适配器实例
_simple_feishu_adapter: Optional[SimpleFeishuAdapter] = None

def get_simple_feishu_adapter() -> SimpleFeishuAdapter:
    """获取简化飞书适配器实例"""
    global _simple_feishu_adapter
    if _simple_feishu_adapter is None:
        _simple_feishu_adapter = SimpleFeishuAdapter()
    return _simple_feishu_adapter


async def test_simple_adapter():
    """测试简化适配器"""
    adapter = get_simple_feishu_adapter()
    
    try:
        # 启动服务
        await adapter.start_mcp_service()
        
        # 显示可用工具
        tools = adapter.get_available_tools()
        print(f"可用工具: {tools}")
        
        # 测试获取日历列表
        calendars_result = await adapter.list_calendars()
        print(f"日历列表: {calendars_result}")
        
        # 测试搜索事件
        search_result = await adapter.search_calendar_events("项目")
        print(f"搜索结果: {search_result}")
        
        # 测试创建事件
        event_data = {
            "title": "测试会议",
            "description": "这是一个测试事件",
            "start_time": "2025-07-16T15:00:00",
            "end_time": "2025-07-16T16:00:00",
            "location": "会议室A"
        }
        
        create_result = await adapter.create_calendar_event(event_data)
        print(f"创建事件结果: {create_result}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
    finally:
        await adapter.close()


if __name__ == "__main__":
    asyncio.run(test_simple_adapter())
