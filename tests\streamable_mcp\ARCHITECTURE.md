# 飞书MCP Streamable架构流程图

## 🏗️ 整体架构

```mermaid
graph TB
    subgraph "用户层"
        A[用户/开发者] --> B[test_calendar_view.py]
        A --> C[自定义客户端脚本]
    end
    
    subgraph "客户端层"
        B --> D[StreamableMCPClient]
        C --> D
        D --> E[HTTP客户端库]
    end
    
    subgraph "服务器层"
        E --> F[FastAPI MCP服务器]
        F --> G[MCP协议处理器]
        G --> H[MCP工具集合]
    end
    
    subgraph "飞书集成层"
        H --> I[FeishuCalendarLark]
        I --> J[飞书API客户端]
        J --> K[OAuth认证]
        K --> L[访问令牌管理]
    end
    
    subgraph "外部服务"
        L --> M[飞书开放平台]
        M --> N[日历API]
        M --> O[用户认证API]
    end
    
    subgraph "数据存储"
        P[Supabase数据库]
        L --> P
        P --> Q[用户令牌存储]
    end
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style I fill:#e8f5e8
    style M fill:#fff3e0
```

## 🔄 数据流图

```mermaid
sequenceDiagram
    participant User as 用户
    participant Client as MCP客户端
    participant Server as MCP服务器
    participant Feishu as 飞书API
    participant DB as 数据库
    
    User->>Client: 调用日历查看
    Client->>Server: HTTP POST /health
    Server-->>Client: 健康状态
    
    Client->>Server: HTTP POST /mcp/initialize
    Server-->>Client: MCP初始化响应
    
    Client->>Server: HTTP POST /mcp/tools/list
    Server-->>Client: 工具列表
    
    Client->>Server: HTTP POST /mcp/tools/call
    Note over Server: 解析工具调用
    Server->>DB: 查询用户令牌
    DB-->>Server: 返回访问令牌
    
    Server->>Feishu: 调用日历API
    Note over Feishu: 验证令牌
    Feishu-->>Server: 返回日历数据
    
    Server->>Server: 数据格式转换
    Server-->>Client: MCP格式响应
    Client-->>User: 显示日历信息
```

## 🛠️ 组件详细架构

### 1. 客户端组件 (StreamableMCPClient)

```mermaid
graph LR
    subgraph "客户端架构"
        A[StreamableMCPClient] --> B[HTTP会话管理]
        A --> C[请求构建器]
        A --> D[响应解析器]
        A --> E[错误处理器]
        
        B --> F[aiohttp.Session]
        C --> G[JSON-RPC 2.0]
        D --> H[MCP响应格式]
        E --> I[异常处理]
    end
    
    style A fill:#e3f2fd
    style F fill:#f3e5f5
```

### 2. 服务器组件 (FastAPI MCP Server)

```mermaid
graph TB
    subgraph "服务器架构"
        A[FastAPI应用] --> B[CORS中间件]
        A --> C[超时中间件]
        A --> D[MCP路由]
        
        D --> E[/mcp/initialize]
        D --> F[/mcp/tools/list]
        D --> G[/mcp/tools/call]
        D --> H[/health]
        
        G --> I[MCP工具分发器]
        I --> J[list_calendars]
        I --> K[get_calendar_events]
        I --> L[create_calendar_event]
        I --> M[get_today_events]
        
        J --> N[FeishuCalendarLark]
        K --> N
        L --> N
        M --> N
    end
    
    style A fill:#f3e5f5
    style N fill:#e8f5e8
```

### 3. 飞书集成组件

```mermaid
graph LR
    subgraph "飞书集成"
        A[FeishuCalendarLark] --> B[API客户端]
        A --> C[令牌管理]
        A --> D[错误处理]
        
        B --> E[HTTP请求]
        C --> F[访问令牌]
        C --> G[刷新令牌]
        
        E --> H[飞书开放平台]
        F --> H
        G --> H
    end
    
    style A fill:#e8f5e8
    style H fill:#fff3e0
```

## 📊 工具调用流程

```mermaid
flowchart TD
    A[用户调用工具] --> B[客户端构建请求]
    B --> C[发送HTTP请求]
    C --> D[服务器接收请求]
    D --> E[验证MCP协议]
    E --> F[解析工具名称]
    F --> G{工具类型}
    
    G -->|list_calendars| H[获取日历列表]
    G -->|get_calendar_events| I[获取日历事件]
    G -->|create_calendar_event| J[创建日历事件]
    G -->|get_today_events| K[获取今天事件]
    
    H --> L[调用飞书API]
    I --> L
    J --> L
    K --> L
    
    L --> M[处理API响应]
    M --> N[格式转换]
    N --> O[返回MCP响应]
    O --> P[客户端解析]
    P --> Q[用户获得结果]
    
    style A fill:#e1f5fe
    style L fill:#e8f5e8
    style Q fill:#e1f5fe
```

## 🔐 认证流程

```mermaid
sequenceDiagram
    participant App as 应用
    participant OAuth as OAuth服务
    participant User as 用户
    participant API as 飞书API
    participant DB as 数据库
    
    App->>OAuth: 发起认证请求
    OAuth->>User: 重定向到飞书
    User->>OAuth: 授权确认
    OAuth->>App: 返回授权码
    App->>OAuth: 交换访问令牌
    OAuth->>App: 返回访问令牌
    App->>DB: 存储令牌
    App->>API: 使用令牌调用API
    API->>App: 返回数据
```

## 📁 文件结构关系

```
tests/streamable_mcp/
├── feishu_mcp_server_real.py      # MCP服务器 (FastAPI)
├── feishu_mcp_client_streamable.py # MCP客户端库
├── test_calendar_view.py          # 测试脚本
├── start_real_server.py           # 服务器启动器
├── mcp_streamable_config.json     # 配置文件
└── mcp_server_config.json         # 服务器配置
```

## 🔧 关键技术栈

- **协议**: MCP (Model Context Protocol) v2024-11-05
- **传输**: HTTP/JSON-RPC 2.0
- **服务器**: FastAPI + Uvicorn
- **客户端**: aiohttp (异步HTTP)
- **集成**: 飞书开放平台API
- **存储**: Supabase (PostgreSQL)
- **认证**: OAuth 2.0

## 🎯 设计特点

1. **异步架构**: 全异步设计，支持高并发
2. **协议标准化**: 遵循MCP协议规范
3. **模块化设计**: 客户端、服务器、集成层分离
4. **错误处理**: 完善的异常处理和重试机制
5. **可扩展性**: 易于添加新的MCP工具
6. **安全性**: OAuth认证和令牌管理 