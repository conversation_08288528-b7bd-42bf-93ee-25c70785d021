/**
 * MCP Tools Definition
 * 定义所有可用的 MCP 工具
 * 包含简化工具和飞书官方工具
 */

import type { MCPTool } from '@/types/mcp';

// 导入核心工具集
let FEISHU_CORE_TOOLS: MCPTool[] = [];
try {
  // 导入核心工具适配器
  const { ALL_FEISHU_CALENDAR_TOOLS } = require('./tools/tool-adapter-core');
  FEISHU_CORE_TOOLS = ALL_FEISHU_CALENDAR_TOOLS || [];
  console.log(`✅ 成功加载 ${FEISHU_CORE_TOOLS.length} 个核心工具`);
} catch (error) {
  console.warn('⚠️  核心工具加载失败，使用简化工具:', error);
}

export const CALENDAR_TOOLS: MCPTool[] = [
  {
    name: 'calendar_list',
    description: '获取用户的日历列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '每页返回的日历数量，最小值50，默认50',
          minimum: 50,
          maximum: 200
        },
        page_token: {
          type: 'string',
          description: '分页标记，用于获取下一页数据'
        }
      }
    }
  },
  {
    name: 'calendar_event_create',
    description: '创建新的日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        summary: {
          type: 'string',
          description: '事件标题'
        },
        description: {
          type: 'string',
          description: '事件描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区，如 Asia/Shanghai'
            }
          },
          required: ['timestamp']
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区，如 Asia/Shanghai'
            }
          },
          required: ['timestamp']
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '详细地址'
            }
          }
        },
        attendees: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['user', 'chat', 'resource'],
                description: '参与者类型'
              },
              attendee_id: {
                type: 'string',
                description: '参与者ID'
              },
              is_optional: {
                type: 'boolean',
                description: '是否为可选参与者'
              }
            },
            required: ['type', 'attendee_id']
          }
        },
        reminders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              minutes: {
                type: 'number',
                description: '提前提醒的分钟数'
              }
            },
            required: ['minutes']
          }
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '事件可见性'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '忙闲状态'
        }
      },
      required: ['calendar_id', 'summary', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar_event_search',
    description: '搜索日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        query: {
          type: 'string',
          description: '搜索关键词'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '搜索开始时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          }
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '搜索结束时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          }
        },
        page_size: {
          type: 'number',
          description: '每页返回的事件数量',
          minimum: 1,
          maximum: 100
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar_event_update',
    description: '更新日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '事件ID'
        },
        summary: {
          type: 'string',
          description: '事件标题'
        },
        description: {
          type: 'string',
          description: '事件描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          }
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间戳（秒）'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          }
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '详细地址'
            }
          }
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar_event_delete',
    description: '删除日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '事件ID'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar_event_get',
    description: '获取单个日历事件详情',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '事件ID'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar_event_list',
    description: '获取日历事件列表',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        start_time: {
          type: 'string',
          description: '开始时间戳（秒）'
        },
        end_time: {
          type: 'string',
          description: '结束时间戳（秒）'
        },
        page_size: {
          type: 'number',
          description: '每页返回的事件数量',
          minimum: 1,
          maximum: 100
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        }
      },
      required: ['calendar_id']
    }
  }
];
// 使用核心工具集（精简版）
export const ALL_TOOLS = [
  ...FEISHU_CORE_TOOLS     // 核心工具（9个精选工具）
];

// 导出工具统计信息
export const TOOL_STATS = {
  simple: CALENDAR_TOOLS.length,
  core: FEISHU_CORE_TOOLS.length,
  total: ALL_TOOLS.length
};

