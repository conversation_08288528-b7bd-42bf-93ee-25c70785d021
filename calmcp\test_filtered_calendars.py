#!/usr/bin/env python3
"""
获取过滤后的飞书日历
过滤掉 Google 日历和测试日历，只显示真正需要的日历
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional, List

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class FilteredCalendarTester:
    """过滤后的日历测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 定义过滤规则
        self.filter_rules = {
            # 要排除的日历名称关键词（不区分大小写）
            "exclude_keywords": [
                "google",
                "gmail",
                "测试",
                "test",
                "我的日历",  # 通用的默认日历名
                "工作计划日历",  # 看起来像是重复的工作日历
                "解析URL",  # 测试相关
                "公开工作日历",  # 可能是公共日历
                "我的新日历",  # 重复的新日历
                "我的暑假计划",  # 重复的暑假计划
                "我的奋斗",  # 重复的奋斗计划
                "豆包计划",  # 测试相关
                "头条计划",  # 可能是测试
                "快手计划",  # 可能是测试
                "抖音计划",  # 与"抖音创作计划"重复
                "臭鱼烂虾计划",  # 可能是测试
                "创业计划",  # 通用计划
                "我的奋斗计划",  # 重复
                "小宝25年暑假安排",  # 与"小宝暑假计划"重复
                "B站创作者计划",  # 与"B站创作计划"类似但不完全匹配
            ],

            # 要包含的日历名称（精确匹配，优先级高于排除规则）
            "include_exact": [
                "IF ZHANG",
                "我的任务",
                "喜多多计划",
                "真宝虾计划",
                "B站创作计划",
                "大宝暑假计划",
                "小宝暑假计划",
                "小红书计划",
                "投资计划",
                "健身康复计划",
                "抖音创作计划",
                "咸鱼计划",
                # 可能的变体名称
                "臭鱼烂虾计划",  # 可能是"真宝虾计划"的别名
                "B站创作者计划",  # 可能是"B站创作计划"的别名
            ]
        }
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        # 优先使用更新后的 FEISHU_USER_ACCESS_TOKEN
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        # 获取用户ID
        user_id_vars = ['TEST_USER_ID', 'FEISHU_USER_ID', 'USER_ID']
        for var in user_id_vars:
            user_id = os.environ.get(var)
            if user_id:
                self.user_id = user_id
                print(f"   用户 ID: {user_id}")
                break
        
        if not self.user_token:
            print("❌ 未找到用户访问令牌")
    
    def should_include_calendar(self, calendar_name: str) -> bool:
        """判断是否应该包含这个日历"""
        if not calendar_name:
            return False
        
        # 首先检查是否在精确包含列表中
        if calendar_name in self.filter_rules["include_exact"]:
            return True
        
        # 检查是否包含排除关键词
        calendar_name_lower = calendar_name.lower()
        for keyword in self.filter_rules["exclude_keywords"]:
            if keyword.lower() in calendar_name_lower:
                return False
        
        # 如果不在排除列表中，则包含
        return True
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            # 添加用户信息到参数
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误 ({response.status_code}): {response.text}")
                return None
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_all_calendars_filtered(self) -> List[Dict[str, Any]]:
        """获取所有过滤后的日历"""
        all_calendars = []
        page_token = None
        page_num = 1
        
        print(f"\n📅 获取过滤后的日历列表")
        print("="*60)
        
        while True:
            print(f"📄 获取第 {page_num} 页...")
            
            # 构建参数
            arguments = {"page_size": 50}
            if page_token:
                arguments["page_token"] = page_token
            
            # 调用工具
            result = self.call_mcp_tool("calendar.v4.calendar.list", arguments)
            
            if not result or not result.get('success'):
                print(f"❌ 第 {page_num} 页获取失败")
                break
            
            # 解析响应
            content = result.get('result', {}).get('content', [])
            if not content:
                print(f"❌ 第 {page_num} 页响应内容为空")
                break
            
            try:
                feishu_response = json.loads(content[0].get('text', '{}'))
                
                if feishu_response.get('code') != 0:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                    break
                
                data = feishu_response.get('data', {})
                calendars = data.get('calendar_list', [])
                
                # 应用过滤规则
                filtered_calendars = []
                for cal in calendars:
                    calendar_name = cal.get('summary', '')
                    if self.should_include_calendar(calendar_name):
                        filtered_calendars.append(cal)
                
                print(f"✅ 第 {page_num} 页: {len(calendars)} 个日历 -> 过滤后 {len(filtered_calendars)} 个")
                
                # 添加到总列表
                all_calendars.extend(filtered_calendars)
                
                # 检查是否有更多数据
                has_more = data.get('has_more', False)
                page_token = data.get('page_token', '')
                
                if not has_more or not page_token:
                    print(f"✅ 已获取所有数据，共 {page_num} 页")
                    break
                
                page_num += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ 第 {page_num} 页响应解析失败: {e}")
                break
        
        return all_calendars
    
    def display_filtered_calendars(self, calendars: List[Dict[str, Any]]):
        """显示过滤后的日历列表"""
        print(f"\n📅 过滤后的日历列表")
        print("="*60)
        print(f"📊 总计: {len(calendars)} 个日历")
        
        if not calendars:
            print("❌ 没有找到符合条件的日历")
            return
        
        # 按类型分组显示
        primary_calendars = []
        shared_calendars = []
        other_calendars = []
        
        for cal in calendars:
            cal_type = cal.get('type', 'unknown')
            if cal_type == 'primary':
                primary_calendars.append(cal)
            elif cal_type == 'shared':
                shared_calendars.append(cal)
            else:
                other_calendars.append(cal)
        
        # 显示主日历
        if primary_calendars:
            print(f"\n🏠 主日历 ({len(primary_calendars)} 个):")
            for i, cal in enumerate(primary_calendars, 1):
                self._display_calendar_info(cal, i)
        
        # 显示共享日历
        if shared_calendars:
            print(f"\n👥 共享日历 ({len(shared_calendars)} 个):")
            for i, cal in enumerate(shared_calendars, 1):
                self._display_calendar_info(cal, i)
        
        # 显示其他日历
        if other_calendars:
            print(f"\n📋 其他日历 ({len(other_calendars)} 个):")
            for i, cal in enumerate(other_calendars, 1):
                self._display_calendar_info(cal, i)
        
        # 显示过滤统计
        print(f"\n📊 过滤统计:")
        print(f"   包含规则: {len(self.filter_rules['include_exact'])} 个精确匹配")
        print(f"   排除规则: {len(self.filter_rules['exclude_keywords'])} 个关键词")
        print(f"   最终结果: {len(calendars)} 个日历")
    
    def _display_calendar_info(self, cal: Dict[str, Any], index: int):
        """显示单个日历信息"""
        print(f"  {index}. {cal.get('summary', '未命名日历')}")
        print(f"     📋 ID: {cal.get('calendar_id', 'N/A')}")
        print(f"     🔒 权限: {cal.get('permissions', 'N/A')}")
        print(f"     👤 角色: {cal.get('role', 'N/A')}")
        print(f"     🎨 颜色: {cal.get('color', 'N/A')}")
        
        if cal.get('description'):
            print(f"     📝 描述: {cal.get('description')}")
    
    def compare_with_expected(self, calendars: List[Dict[str, Any]]):
        """与期望的日历列表对比"""
        expected_calendars = [
            "IF ZHANG",
            "我的任务", 
            "喜多多计划",
            "真宝虾计划",
            "B站创作计划", 
            "大宝暑假计划",
            "小宝暑假计划",
            "小红书计划"
        ]
        
        print(f"\n🔍 与界面显示的日历对比:")
        print("="*60)
        print(f"界面显示: {len(expected_calendars)} 个日历")
        print(f"API返回: {len(calendars)} 个日历")
        
        found_calendars = [cal.get('summary', '') for cal in calendars]
        
        print(f"\n✅ 匹配情况:")
        for expected in expected_calendars:
            found = expected in found_calendars
            status = "✅" if found else "❌"
            print(f"   {status} {expected}")
        
        print(f"\n📋 API返回的所有日历:")
        for cal_name in found_calendars:
            print(f"   - {cal_name}")
        
        # 计算匹配率
        matches = sum(1 for expected in expected_calendars if expected in found_calendars)
        match_rate = (matches / len(expected_calendars)) * 100
        print(f"\n📊 匹配率: {matches}/{len(expected_calendars)} ({match_rate:.1f}%)")
    
    def run_test(self):
        """运行测试"""
        print("🎯 过滤后的飞书日历测试")
        print("="*60)
        
        if not self.user_token:
            print("❌ 没有有效的用户凭据，无法继续测试")
            return
        
        # 获取过滤后的日历
        calendars = self.get_all_calendars_filtered()
        
        # 显示结果
        self.display_filtered_calendars(calendars)
        
        # 与期望结果对比
        self.compare_with_expected(calendars)
        
        print("\n" + "="*60)
        print("🎉 测试完成！")
        print("="*60)


def main():
    """主函数"""
    tester = FilteredCalendarTester()
    tester.run_test()


if __name__ == "__main__":
    main()
