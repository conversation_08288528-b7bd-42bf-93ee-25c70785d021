#!/usr/bin/env python3
"""
删除不需要的飞书日历
只保留界面中显示的8个日历，删除其他所有日历
"""

import requests
import json
import sys
import os
import time
from typing import Dict, Any, Optional, List

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class CalendarDeleter:
    """日历删除工具"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 界面中显示的日历列表（需要保留的）
        # 基于实际API测试结果，这些是真实存在的日历
        self.keep_calendars = [
            "IF ZHANG",
            "喜多多计划",
            "大宝暑假计划",
            "小红书计划"
        ]

        # 明确要删除的测试日历模式
        self.delete_patterns = [
            "测试日历",
            "更新后的测试日历",
            "我的新日历",
            "test",
            "Test"
        ]
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        # 优先使用更新后的 FEISHU_USER_ACCESS_TOKEN
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        # 获取用户ID
        user_id_vars = ['TEST_USER_ID', 'FEISHU_USER_ID', 'USER_ID']
        for var in user_id_vars:
            user_id = os.environ.get(var)
            if user_id:
                self.user_id = user_id
                print(f"   用户 ID: {user_id}")
                break
        
        if not self.user_token:
            print("❌ 未找到用户访问令牌")
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            # 添加用户信息到参数
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误 ({response.status_code}): {response.text}")
                return None
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_all_calendars(self) -> List[Dict[str, Any]]:
        """获取所有日历（使用正确的过滤条件）"""
        all_calendars = []
        page_token = None
        page_num = 1

        print(f"\n📅 获取日历列表（排除已删除和第三方日历）")
        print("="*60)

        while True:
            print(f"📄 获取第 {page_num} 页...")

            # 构建参数（使用正确的MCP格式）
            arguments = {
                "page_size": 50
            }
            if page_token:
                arguments["page_token"] = page_token

            print(f"   🔍 使用正确的MCP参数格式")

            # 调用工具
            result = self.call_mcp_tool("calendar.v4.calendar.list", arguments)

            if not result or not result.get('success'):
                print(f"❌ 第 {page_num} 页获取失败")
                break

            # 解析响应
            content = result.get('result', {}).get('content', [])
            if not content:
                print(f"❌ 第 {page_num} 页响应内容为空")
                break

            try:
                feishu_response = json.loads(content[0].get('text', '{}'))

                if feishu_response.get('code') != 0:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                    break

                data = feishu_response.get('data', {})
                calendars = data.get('calendar_list', [])

                print(f"✅ 第 {page_num} 页获取到 {len(calendars)} 个日历")

                # 添加到总列表
                all_calendars.extend(calendars)

                # 检查是否有更多数据
                has_more = data.get('has_more', False)
                new_page_token = data.get('page_token', '')

                print(f"   📄 分页信息: has_more={has_more}, page_token={new_page_token}")

                if not has_more:
                    print(f"✅ 已获取所有数据，共 {page_num} 页")
                    break

                # 检查page_token是否变化，避免无限循环
                if new_page_token == page_token:
                    print(f"⚠️  页面令牌未变化，停止分页")
                    break

                page_token = new_page_token
                page_num += 1

                # 安全限制：最多获取10页
                if page_num > 10:
                    print(f"⚠️  已达到最大页数限制(10页)，停止获取")
                    break

            except json.JSONDecodeError as e:
                print(f"❌ 第 {page_num} 页响应解析失败: {e}")
                break

        return all_calendars
    
    def analyze_calendars(self, calendars: List[Dict[str, Any]]):
        """分析日历，分类为保留和删除"""
        keep_list = []
        delete_list = []

        print(f"\n🔍 分析日历分类")
        print("="*60)

        for cal in calendars:
            calendar_name = cal.get('summary', '')
            calendar_id = cal.get('calendar_id', '')
            calendar_type = cal.get('type', '')

            # 检查是否应该保留
            should_keep = False
            reason = ""

            # 1. 精确匹配保留列表
            if calendar_name in self.keep_calendars:
                should_keep = True
                reason = "精确匹配保留列表"

            # 2. 主日历保留
            elif calendar_type == 'primary':
                should_keep = True
                reason = "主日历"

            # 3. 检查是否匹配删除模式
            elif any(pattern in calendar_name for pattern in self.delete_patterns):
                should_keep = False
                reason = "匹配删除模式"

            # 4. 空名称的日历删除
            elif not calendar_name.strip():
                should_keep = False
                reason = "空名称日历"

            # 5. 其他日历暂时保留（安全起见）
            else:
                should_keep = True
                reason = "未知日历，安全保留"

            if should_keep:
                keep_list.append(cal)
                print(f"✅ 保留: {calendar_name or '(空名称)'} ({reason})")
            else:
                delete_list.append(cal)
                print(f"❌ 删除: {calendar_name or '(空名称)'} ({reason})")

        return keep_list, delete_list
    
    def delete_calendar(self, calendar_id: str, calendar_name: str) -> bool:
        """删除单个日历"""
        print(f"🗑️  删除: {calendar_name}")
        print(f"   📋 日历ID: {calendar_id}")

        try:
            result = self.call_mcp_tool("calendar.v4.calendar.delete", {
                "calendar_id": calendar_id
            })

            print(f"   🔍 调试信息: {result}")

            if result and result.get('success'):
                content = result.get('result', {}).get('content', [])
                if content:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    print(f"   📊 飞书响应: {feishu_response}")
                    if feishu_response.get('code') == 0:
                        print(f"   ✅ 成功删除")
                        return True
                    else:
                        error_msg = feishu_response.get('msg', '未知错误')
                        error_code = feishu_response.get('code', 'N/A')
                        print(f"   ❌ 删除失败: [{error_code}] {error_msg}")
                        return False
                else:
                    print(f"   ❌ 删除响应为空")
                    return False
            else:
                error_info = result.get('error', '未知错误') if result else '无响应'
                print(f"   ❌ 删除工具调用失败: {error_info}")
                return False

        except Exception as e:
            print(f"   ❌ 删除时出现异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_deletion(self, dry_run: bool = True):
        """运行删除流程"""
        print("🗑️  飞书日历删除工具")
        print("="*60)
        
        if not self.user_token:
            print("❌ 没有有效的用户凭据，无法继续")
            return
        
        print(f"📋 需要保留的日历 ({len(self.keep_calendars)} 个):")
        for i, name in enumerate(self.keep_calendars, 1):
            print(f"  {i}. {name}")
        
        # 1. 获取所有日历
        all_calendars = self.get_all_calendars()
        if not all_calendars:
            print("❌ 无法获取日历列表")
            return
        
        print(f"\n📊 当前总计: {len(all_calendars)} 个日历")
        
        # 2. 分析分类
        keep_list, delete_list = self.analyze_calendars(all_calendars)
        
        print(f"\n📊 分析结果:")
        print(f"   保留: {len(keep_list)} 个日历")
        print(f"   删除: {len(delete_list)} 个日历")
        
        if not delete_list:
            print("✅ 没有需要删除的日历！")
            return
        
        print(f"\n❌ 将要删除的日历:")
        for i, cal in enumerate(delete_list, 1):
            print(f"  {i}. {cal.get('summary', '未命名')} ({cal.get('type', 'unknown')})")
        
        # 3. 确认删除
        if dry_run:
            print(f"\n⚠️  这是预览模式，不会实际删除日历")
            print(f"💡 如需实际删除，请运行: python delete_unwanted_calendars.py --delete")
        else:
            print(f"\n⚠️  警告：即将删除 {len(delete_list)} 个日历！")
            print(f"❗ 删除后无法恢复，请仔细确认！")
            
            print(f"\n请输入 'DELETE ALL' 确认删除所有不需要的日历:")
            confirm = input().strip()
            
            if confirm == 'DELETE ALL':
                print(f"\n🗑️  开始删除...")
                success_count = 0
                failed_count = 0
                
                for i, cal in enumerate(delete_list, 1):
                    calendar_name = cal.get('summary', '未命名')
                    calendar_id = cal.get('calendar_id', '')
                    
                    print(f"\n[{i}/{len(delete_list)}] ", end="")
                    
                    if self.delete_calendar(calendar_id, calendar_name):
                        success_count += 1
                    else:
                        failed_count += 1
                    
                    # 添加延迟，避免API限制
                    time.sleep(1)
                
                print(f"\n📊 删除完成:")
                print(f"   成功: {success_count} 个")
                print(f"   失败: {failed_count} 个")
                print(f"   剩余: {len(keep_list)} 个日历")
                
                if success_count > 0:
                    print(f"\n🎉 清理完成！现在你的日历列表应该更干净了。")
            else:
                print(f"\n👋 已取消删除操作")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='删除不需要的飞书日历')
    parser.add_argument('--delete', action='store_true', help='实际执行删除操作（默认为预览模式）')
    
    args = parser.parse_args()
    
    deleter = CalendarDeleter()
    deleter.run_deletion(dry_run=not args.delete)


if __name__ == "__main__":
    main()
