# Scripts Directory

## 目录结构

- `debug/` - 调试脚本
- `temp/` - 临时脚本
- `cli.py` - 命令行工具
- `deployment_check.py` - 部署检查脚本
- `run_local_tests.py` - 本地测试脚本
- `run_tests.py` - 完整测试脚本

## 使用说明

### 本地开发测试
```bash
python scripts/run_local_tests.py
```

### 部署前检查
```bash
python scripts/deployment_check.py
```

### 调试工具
```bash
# 查看所有事件
python scripts/debug/show_all_events.py

# 调试飞书API
python scripts/debug/debug_feishu_api.py

# 调试事件
python scripts/debug/debug_events.py
```
