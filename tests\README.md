# 测试指南

本项目采用真实数据测试方法，使用真实的API客户端和存储系统进行测试，确保测试结果的可靠性和真实性。

## 🚀 快速开始

### 1. 环境设置

```bash
# 1. 复制测试环境配置模板
cp test.env.example test.env

# 2. 编辑配置文件，填入飞书应用信息
# 必需：FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
# 可选：TEST_ACCESS_TOKEN（用于真实API测试）

# 3. 安装测试依赖
pip install -r ../requirements-test.txt
```

### 2. 运行测试

```bash
# 使用统一测试运行器（推荐）
python run_tests.py

# 运行所有测试
python run_tests.py --type all -v

# 生成覆盖率报告
python run_tests.py --type all --coverage
```

## 📁 测试结构

```
tests/
├── unit/                # 单元测试
│   ├── test_api/       # API相关测试
│   ├── test_core/      # 核心功能测试
│   ├── test_models/    # 数据模型测试
│   └── test_services/  # 服务层测试
├── integration/         # 集成测试
│   ├── test_auth_flow.py      # 认证流程测试
│   ├── test_calendar_operations.py # 日历操作测试
│   └── test_chat_flow.py      # 聊天流程测试
├── e2e/                # 端到端测试
│   ├── test_calendar_api.py   # 日历API测试
│   └── test_chat_workflows.py # 聊天工作流测试
├── performance/        # 性能测试
│   └── test_api_performance.py # API性能测试
├── examples/           # 测试示例
├── conftest.py         # pytest配置和共享fixture
├── run_tests.py        # 统一测试运行器
└── test.env.example    # 测试环境配置模板
```

## 🧪 测试类型

### 单元测试 (Unit Tests)
- **目标**: 测试单个函数和类的功能
- **特点**: 快速执行，无外部依赖
- **运行**: `python run_tests.py --type unit`

### 集成测试 (Integration Tests)
- **目标**: 测试模块间的交互
- **特点**: 使用真实的客户端和存储
- **运行**: `python run_tests.py --type integration`

### 端到端测试 (E2E Tests)
- **目标**: 测试完整的用户流程
- **特点**: 可能涉及真实API调用
- **运行**: `python run_tests.py --type e2e`

### 性能测试 (Performance Tests)
- **目标**: 测试系统性能指标
- **特点**: 测量响应时间和并发能力
- **运行**: `python run_tests.py --type performance`

## 🔧 测试特性

### 真实数据测试
- **无Mock**: 完全使用真实的API客户端和存储系统
- **真实环境**: 测试结果更接近生产环境
- **数据隔离**: 使用时间戳创建唯一的测试数据

### 智能环境检查
- **自动验证**: 自动检查必需的环境变量
- **智能跳过**: 缺少配置时自动跳过相关测试
- **详细提示**: 提供具体的配置指导

### 自动清理
- **测试隔离**: 每个测试使用独立的数据
- **自动清理**: 测试完成后自动清理测试数据
- **无副作用**: 测试不会影响其他测试或生产数据

## 📊 测试报告

### 覆盖率报告
```bash
# 生成HTML覆盖率报告
python run_tests.py --coverage

# 查看报告
open htmlcov/index.html
```

### 详细输出
```bash
# 显示详细的测试输出
python run_tests.py --type all -v

# 显示失败的详细信息
python run_tests.py --type all -v --tb=long
```

## 🛠️ 故障排除

### 常见问题

1. **环境变量缺失**
```bash
# 运行测试时会自动检查并提示缺失的环境变量
python run_tests.py
```

2. **网络测试失败**
```bash
# 设置环境变量启用网络测试
export ALLOW_NETWORK_TESTS=true
export TEST_ACCESS_TOKEN=your_real_token
```

3. **测试数据冲突**
```bash
# 测试使用时间戳创建唯一数据，通常不会冲突
# 如有问题，检查存储配置是否正确
```

### 调试技巧

1. **单独运行失败的测试**
```bash
pytest tests/unit/test_specific.py::test_function_name -v
```

2. **使用调试模式**
```bash
pytest --pdb tests/unit/test_specific.py
```

3. **查看详细日志**
```bash
LOG_LEVEL=DEBUG python run_tests.py --type unit -v
```

## 📝 编写测试

### 测试命名规范
- 文件名: `test_*.py`
- 类名: `Test*`
- 函数名: `test_*`

### 测试示例
```python
import pytest
from integrations.feishu import get_feishu_client

class TestFeishuClient:
    def test_client_creation(self):
        """测试客户端创建"""
        client = get_feishu_client()
        assert client is not None
        assert hasattr(client, 'app_id')

    @pytest.mark.asyncio
    async def test_async_operation(self):
        """测试异步操作"""
        client = get_feishu_client()
        result = await client.some_async_method()
        assert result is not None
```

### 使用Fixture
```python
def test_with_fixture(real_feishu_client, test_user_id):
    """使用fixture的测试"""
    result = real_feishu_client.get_calendars(test_user_id)
    assert isinstance(result, dict)
```

## 🎯 最佳实践

1. **测试隔离**: 每个测试应该独立，不依赖其他测试的结果
2. **清晰命名**: 测试名称应该清楚地描述测试的目的
3. **单一职责**: 每个测试只验证一个功能点
4. **适当断言**: 使用具体的断言，避免过于宽泛的检查
5. **错误处理**: 测试异常情况和边界条件
6. **文档化**: 为复杂的测试添加注释说明

## 🔄 持续集成

测试在CI/CD流程中自动运行：

1. **代码推送** → 触发测试
2. **测试通过** → 允许合并
3. **主分支更新** → 运行完整测试套件

查看CI状态：[GitHub Actions](https://github.com/ifcheung2012/feishu-coze-plugin/actions)