import time

import requests

from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET, REDIRECT_URI
from integrations.storage import get_token, save_token

# 飞书Token URL
FEISHU_TOKEN_URL = "https://open.feishu.cn/open-apis/authen/v1/access_token"


def get_authorize_url(state):
    return (
        f"https://open.feishu.cn/open-apis/authen/v1/authorize"
        f"?app_id={FEISHU_CLIENT_ID}&redirect_uri={REDIRECT_URI}&state={state}"
    )


def fetch_access_token(code):
    url = FEISHU_TOKEN_URL
    data = {
        "grant_type": "authorization_code",
        "code": code,
        "app_id": FEISHU_CLIENT_ID,
        "app_secret": FEISHU_CLIENT_SECRET,
    }
    resp = requests.post(url, json=data).json()
    if "data" in resp:
        token_info = resp["data"]
        token_info["expires_at"] = int(time.time()) + token_info["expires_in"]
        save_token(token_info["open_id"], token_info)
        return token_info
    else:
        raise Exception(f"获取token失败: {resp}")


def refresh_access_token(open_id):
    token_info = get_token(open_id)
    if not token_info:
        raise Exception("未找到token")
    if int(time.time()) < token_info["expires_at"] - 60:
        return token_info
    # 刷新token
    url = "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token"
    data = {
        "grant_type": "refresh_token",
        "refresh_token": token_info["refresh_token"],
        "app_id": APP_ID,
        "app_secret": APP_SECRET,
    }
    resp = requests.post(url, json=data).json()
    if "data" in resp:
        new_token_info = resp["data"]
        new_token_info["expires_at"] = int(time.time()) + new_token_info["expires_in"]
        save_token(open_id, new_token_info)
        return new_token_info
    else:
        raise Exception(f"刷新token失败: {resp}")
