# 测试环境配置模板
# 复制此文件为 test.env 并填写实际值

# ===== 必需配置 =====
# 飞书应用配置（必需）
FEISHU_CLIENT_ID=your_feishu_app_id
FEISHU_CLIENT_SECRET=your_feishu_app_secret

# 重定向URI（测试环境）
REDIRECT_URI=http://localhost:5000/auth/callback

# ===== 可选配置 =====
# 真实API测试（可选，用于网络测试）
# TEST_ACCESS_TOKEN=u-xxxxxxxxxx
# TEST_USER_ID=ou_xxxxxxxxxx

# 网络测试控制（可选）
# ALLOW_NETWORK_TESTS=true

# 存储配置（可选，默认使用内存存储）
STORAGE_TYPE=memory

# 时区设置（可选）
TIMEZONE=Asia/Shanghai

# 日志配置（可选）
LOG_LEVEL=INFO

# 测试环境标志（自动设置）
TESTING=true

# ===== 配置说明 =====
#
# 必需配置：
# - FEISHU_CLIENT_ID: 飞书应用的App ID
# - FEISHU_CLIENT_SECRET: 飞书应用的App Secret
#
# 可选配置：
# - TEST_ACCESS_TOKEN: 用于真实API测试的访问令牌
# - TEST_USER_ID: 测试用户的Open ID
# - ALLOW_NETWORK_TESTS: 是否允许网络测试
#
# 获取飞书应用配置：
# 1. 访问 https://open.feishu.cn/app
# 2. 创建或选择应用
# 3. 在"凭证与基础信息"中获取 App ID 和 App Secret
# 4. 在"安全设置"中设置重定向URL
#
# 测试类型：
# - 单元测试：只需要基本配置，不需要真实API
# - 集成测试：需要基本配置，测试内部模块交互
# - 网络测试：需要 TEST_ACCESS_TOKEN 进行真实API调用