# Copyright (c) 2025 Bytedance Ltd. and/or its affiliates
# SPDX-License-Identifier: MIT

"""
基于LLM的智能时间解析服务
使用大语言模型来理解和解析自然语言时间表达式
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Tuple
import os
import warnings
# 忽略Pydantic兼容性警告
warnings.filterwarnings("ignore", category=UserWarning, module="pydantic")
warnings.filterwarnings("ignore", category=DeprecationWarning, module="langchain")

import config

logger = logging.getLogger(__name__)

try:
    from langchain_openai import ChatOpenAI
    LANGCHAIN_AVAILABLE = True
except Exception:
    # 静默处理LangChain不可用的情况
    LANGCHAIN_AVAILABLE = False
    ChatOpenAI = None

class LLMTimeParser:
    """基于LLM的时间解析器"""
    
    def __init__(self):
        if not LANGCHAIN_AVAILABLE:
            # 静默处理，不输出任何信息
            self.llm = None
            return

        try:
            # 使用SiliconFlow API配置
            self.llm = ChatOpenAI(
                model=config.LLM_MODEL,
                api_key=config.SILICONFLOW_API_KEY,
                base_url="https://api.siliconflow.cn/v1",
                temperature=0.1  # 低温度确保一致性
            )
        except Exception:
            # 静默处理LLM初始化失败
            self.llm = None
    
    async def parse_time_expression(self, text: str, base_time: Optional[datetime] = None) -> Dict[str, Any]:
        """
        使用LLM解析时间表达式
        
        Args:
            text: 包含时间信息的文本
            base_time: 基准时间，默认为当前时间
            
        Returns:
            解析结果字典，包含开始时间、结束时间等信息
        """
        if base_time is None:
            base_time = datetime.now()

        # 如果LLM不可用，使用简化的时间解析
        if self.llm is None:
            return self._simple_time_parse(text, base_time)

        try:
            # 构建时间解析提示
            prompt = self._build_time_parsing_prompt(text, base_time)

            # 调用LLM解析时间
            response = self.llm.invoke([{"role": "user", "content": prompt}])
            result_text = response.content.strip()

            # 解析LLM返回的JSON结果
            if result_text.startswith("```json"):
                result_text = result_text[7:]
            if result_text.endswith("```"):
                result_text = result_text[:-3]
            result_text = result_text.strip()

            result = json.loads(result_text)

            # 验证和标准化结果
            return self._validate_and_normalize_result(result, base_time)
        except Exception:
            # 静默降级到简化解析
            return self._simple_time_parse(text, base_time)
    
    def _build_time_parsing_prompt(self, text: str, base_time: datetime) -> str:
        """构建时间解析提示"""
        current_time_str = base_time.strftime("%Y-%m-%d %H:%M:%S")
        weekday = base_time.strftime("%A")
        
        prompt = f"""
你是一个专业的时间解析助手。请分析以下文本中的时间信息，并返回标准化的时间数据。

当前时间: {current_time_str} ({weekday})
时区: Asia/Shanghai (GMT+8)

需要解析的文本: "{text}"

请分析文本中的时间信息，并返回JSON格式的结果：

{{
    "has_time": true/false,
    "start_time": "YYYY-MM-DD HH:MM:SS",
    "end_time": "YYYY-MM-DD HH:MM:SS",
    "duration_minutes": 60,
    "is_all_day": false,
    "confidence": 0.95,
    "parsed_expressions": ["下午3点"],
    "notes": "解析说明"
}}

解析规则：
1. 识别相对时间：今天、明天、后天、下周等
2. 识别具体时间：上午9点、下午3点、晚上8点等
3. 识别时间范围：从9点到11点、2小时等
4. 默认会议时长为1小时
5. 如果只有日期没有时间，设置为全天事件
6. 时间格式使用24小时制
7. 所有时间都基于Asia/Shanghai时区

示例：
- "明天下午3点" -> 明天15:00-16:00
- "今天上午9点半" -> 今天09:30-10:30
- "下周一早上10点开会" -> 下周一10:00-11:00
- "后天全天" -> 后天全天事件

请仔细分析文本，返回准确的时间信息。
"""
        return prompt
    
    def _validate_and_normalize_result(self, result: Dict[str, Any], base_time: datetime) -> Dict[str, Any]:
        """验证和标准化LLM解析结果"""
        try:
            # 确保必要字段存在
            if not result.get("has_time", False):
                return {
                    "success": False,
                    "message": "文本中未找到时间信息"
                }
            
            start_time_str = result.get("start_time")
            end_time_str = result.get("end_time")
            
            if not start_time_str:
                return {
                    "success": False,
                    "message": "未能解析出开始时间"
                }
            
            # 解析时间字符串
            start_time = datetime.strptime(start_time_str, "%Y-%m-%d %H:%M:%S")
            
            if end_time_str:
                end_time = datetime.strptime(end_time_str, "%Y-%m-%d %H:%M:%S")
            else:
                # 默认1小时后结束
                end_time = start_time + timedelta(hours=1)
            
            # 转换为时间戳
            start_timestamp = str(int(start_time.timestamp()))
            end_timestamp = str(int(end_time.timestamp()))
            
            # 静默处理，不输出日志
            
            return {
                "success": True,
                "start_time": start_time_str,
                "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
                "start_timestamp": start_timestamp,
                "end_timestamp": end_timestamp,
                "duration_minutes": result.get("duration_minutes", 60),
                "is_all_day": result.get("is_all_day", False),
                "confidence": result.get("confidence", 0.8),
                "parsed_expressions": result.get("parsed_expressions", []),
                "notes": result.get("notes", "")
            }
            
        except Exception as e:
            # 静默处理验证失败
            raise ValueError(f"时间解析失败: {e}")

    def _simple_time_parse(self, text: str, base_time: datetime) -> Dict[str, Any]:
        """简化的时间解析方法 - 使用正则表达式"""
        import re

        # 默认时间
        start_time = base_time.replace(hour=15, minute=0, second=0, microsecond=0)  # 默认下午3点

        # 解析时间表达式
        time_patterns = [
            (r"下午\s*(\d{1,2})点", lambda m: int(m.group(1)) + 12 if int(m.group(1)) < 12 else int(m.group(1))),
            (r"上午\s*(\d{1,2})点", lambda m: int(m.group(1))),
            (r"晚上\s*(\d{1,2})点", lambda m: int(m.group(1)) + 12 if int(m.group(1)) < 12 else int(m.group(1))),
            (r"(\d{1,2})点", lambda m: int(m.group(1))),
        ]

        for pattern, hour_func in time_patterns:
            match = re.search(pattern, text)
            if match:
                try:
                    hour = hour_func(match)
                    start_time = base_time.replace(hour=hour, minute=0, second=0, microsecond=0)
                    # 静默处理，不输出任何信息
                    break
                except:
                    continue

        # 解析日期表达式
        if "今天" in text:
            # 保持当前日期
            pass
        elif "明天" in text:
            start_time = start_time + timedelta(days=1)
        elif "后天" in text:
            start_time = start_time + timedelta(days=2)

        end_time = start_time + timedelta(hours=1)

        return {
            "success": True,
            "start_time": start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": end_time.strftime("%Y-%m-%d %H:%M:%S"),
            "start_timestamp": str(int(start_time.timestamp())),
            "end_timestamp": str(int(end_time.timestamp())),
            "duration_minutes": 60,
            "is_all_day": False,
            "confidence": 0.8,
            "parsed_expressions": [text],
            "notes": "使用简化正则表达式解析"
        }


# 全局实例
_llm_time_parser = None

def get_llm_time_parser() -> LLMTimeParser:
    """获取LLM时间解析器实例"""
    global _llm_time_parser
    if _llm_time_parser is None:
        _llm_time_parser = LLMTimeParser()
    return _llm_time_parser
