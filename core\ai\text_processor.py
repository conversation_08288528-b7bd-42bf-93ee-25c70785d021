"""
文本处理器
提供文本预处理、清理、格式化等功能
"""

import logging
import re
from typing import Any, Dict, List

logger = logging.getLogger(__name__)


class TextProcessor:
    """文本处理器"""

    @staticmethod
    def clean_text(text: str) -> str:
        """
        清理文本

        Args:
            text: 原始文本

        Returns:
            清理后的文本
        """
        if not text:
            return ""

        # 去除多余空白
        text = re.sub(r"\s+", " ", text.strip())

        # 去除特殊字符（保留中文、英文、数字、常用标点）
        text = re.sub(r"[^\w\s\u4e00-\u9fff.,!?;:()（），。！？；：]", "", text)

        return text

    @staticmethod
    def extract_keywords(text: str) -> List[str]:
        """
        提取关键词

        Args:
            text: 输入文本

        Returns:
            关键词列表
        """
        # 简单的关键词提取（基于词频）
        words = re.findall(r"\w+", text.lower())

        # 过滤停用词
        stop_words = {
            "的",
            "了",
            "在",
            "是",
            "我",
            "你",
            "他",
            "她",
            "它",
            "们",
            "the",
            "a",
            "an",
            "and",
            "or",
            "but",
            "in",
            "on",
            "at",
            "to",
            "for",
        }

        keywords = [word for word in words if word not in stop_words and len(word) > 1]

        # 去重并返回
        return list(set(keywords))

    @staticmethod
    def detect_language(text: str) -> str:
        """
        检测文本语言

        Args:
            text: 输入文本

        Returns:
            语言代码 ('zh', 'en', 'unknown')
        """
        if not text:
            return "unknown"

        # 简单的语言检测
        chinese_chars = len(re.findall(r"[\u4e00-\u9fff]", text))
        english_chars = len(re.findall(r"[a-zA-Z]", text))

        if chinese_chars > english_chars:
            return "zh"
        elif english_chars > 0:
            return "en"
        else:
            return "unknown"
