#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地MCP服务配置
统一管理本地MCP服务的配置信息
"""

from pathlib import Path

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent.parent

# 本地MCP服务配置
LOCAL_MCP_CONFIG = {
    # 服务器脚本文件
    "server_script": "feishu_mcp_server_simple.py",
    
    # 服务器脚本路径
    "server_script_path": PROJECT_ROOT / "feishu_mcp_server_simple.py",
    
    # 服务名称
    "service_name": "feishu-calendar-local",
    
    # 服务版本
    "service_version": "1.0.0",
    
    # 支持的工具列表
    "supported_tools": [
        "list_calendars",
        "get_today_events", 
        "get_calendar_events",
        "create_calendar_event"
    ],
    
    # OAuth配置
    "oauth": {
        "callback_url": "http://localhost:5000/callback",
        "callback_port": 5000,
        "timeout_seconds": 300  # 5分钟
    },
    
    # 日志配置
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    }
}

# MCP协议配置
MCP_PROTOCOL_CONFIG = {
    "protocol_version": "2024-11-05",
    "client_info": {
        "name": "feishu-calendar-assistant",
        "version": "1.0.0"
    },
    "capabilities": {
        "tools": {}
    }
}

# 错误消息配置
ERROR_MESSAGES = {
    "service_not_running": "本地MCP服务未运行",
    "connection_failed": "连接本地MCP服务失败",
    "tool_not_found": "工具不存在",
    "oauth_required": "需要OAuth认证",
    "oauth_timeout": "OAuth认证超时",
    "oauth_failed": "OAuth认证失败"
}

# 成功消息配置
SUCCESS_MESSAGES = {
    "service_started": "本地MCP服务启动成功",
    "service_stopped": "本地MCP服务停止成功",
    "connection_established": "成功连接到本地MCP服务",
    "tool_called": "工具调用成功",
    "oauth_completed": "OAuth认证完成"
}

def get_local_mcp_config():
    """获取本地MCP配置"""
    return LOCAL_MCP_CONFIG

def get_mcp_protocol_config():
    """获取MCP协议配置"""
    return MCP_PROTOCOL_CONFIG

def get_error_message(key: str) -> str:
    """获取错误消息"""
    return ERROR_MESSAGES.get(key, f"未知错误: {key}")

def get_success_message(key: str) -> str:
    """获取成功消息"""
    return SUCCESS_MESSAGES.get(key, f"操作成功: {key}")

def validate_config():
    """验证配置"""
    errors = []
    
    # 检查服务器脚本是否存在
    if not LOCAL_MCP_CONFIG["server_script_path"].exists():
        errors.append(f"服务器脚本不存在: {LOCAL_MCP_CONFIG['server_script_path']}")
    
    # 检查必要的配置项
    required_keys = ["server_script", "service_name", "supported_tools"]
    for key in required_keys:
        if key not in LOCAL_MCP_CONFIG:
            errors.append(f"缺少必要配置项: {key}")
    
    return errors

if __name__ == "__main__":
    # 验证配置
    errors = validate_config()
    if errors:
        print("❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("✅ 配置验证通过")
        print(f"服务脚本: {LOCAL_MCP_CONFIG['server_script_path']}")
        print(f"支持的工具: {', '.join(LOCAL_MCP_CONFIG['supported_tools'])}")
