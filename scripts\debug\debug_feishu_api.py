#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试飞书API，获取真实的日历信息
"""

import asyncio
import logging

from integrations.feishu import get_calendars, get_user_info
from integrations.storage import get_token

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_USER_ID = "ou_57792fdcc9ab9970117ac3dbdf8a5b25"


async def debug_feishu_api():
    """调试飞书API"""
    logger.info("🔍 开始调试飞书API")

    # 获取用户token
    token_data = get_token(TEST_USER_ID)
    if not token_data:
        logger.error("❌ 未找到用户token，请先授权")
        return

    access_token = token_data["access_token"]
    logger.info(f"✅ 获取到用户token: {access_token[:20]}...")

    # 获取用户信息
    logger.info("👤 获取用户信息...")
    user_info = await get_user_info(access_token)
    logger.info(f"用户信息: {user_info}")

    # 获取日历列表
    logger.info("📅 获取日历列表...")
    calendars_result = await get_calendars(access_token)
    logger.info(f"日历列表结果: {calendars_result}")

    if calendars_result.get("code") == 0:
        # 飞书API返回的是 calendar_list 不是 items
        calendars = calendars_result.get("data", {}).get("calendar_list", [])
        logger.info(f"✅ 找到 {len(calendars)} 个日历:")

        for i, calendar in enumerate(calendars):
            cal_id = calendar.get("calendar_id", "N/A")
            summary = calendar.get("summary", "N/A")
            role = calendar.get("role", "N/A")
            type_val = calendar.get("type", "N/A")

            logger.info(f"  📅 {i+1}. {summary}")
            logger.info(f"      ID: {cal_id}")
            logger.info(f"      角色: {role}")
            logger.info(f"      类型: {type_val}")
            logger.info("")

        # 找到主日历
        primary_calendar = None
        for calendar in calendars:
            if calendar.get("type") == "primary":
                primary_calendar = calendar
                break

        if not primary_calendar:
            # 尝试找owner角色的日历
            for calendar in calendars:
                if calendar.get("role") == "owner":
                    primary_calendar = calendar
                    break

        if primary_calendar:
            logger.info(f"🎯 主日历: {primary_calendar.get('summary', 'N/A')}")
            logger.info(f"   ID: {primary_calendar.get('calendar_id', 'N/A')}")
            return primary_calendar.get("calendar_id")
        else:
            logger.warning("⚠️ 未找到主日历")
            if calendars:
                first_cal = calendars[0]
                logger.info(f"使用第一个日历: {first_cal.get('summary', 'N/A')}")
                logger.info(f"   ID: {first_cal.get('calendar_id', 'N/A')}")
                return first_cal.get("calendar_id")
    else:
        logger.error(f"❌ 获取日历列表失败: {calendars_result}")

    return None


if __name__ == "__main__":
    asyncio.run(debug_feishu_api())
