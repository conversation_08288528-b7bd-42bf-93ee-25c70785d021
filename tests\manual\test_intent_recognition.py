#!/usr/bin/env python3
"""
简化的意图识别测试脚本
"""

import asyncio
import logging

from models.chat import ChatRequest
from services.chat_service import ChatService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_intent_recognition():
    """测试意图识别功能"""
    print("🧠 测试意图识别功能")
    print("=" * 50)

    chat_service = ChatService()

    test_cases = [
        ("明天下午3点安排产品评审会议", "calendar"),
        ("查看今天的日程安排", "calendar"),
        ("你好", "chat"),
        ("帮我写一首诗", "media"),
        ("记录今天的工作总结", "journal"),
    ]

    for i, (message, expected_intent) in enumerate(test_cases, 1):
        print(f"\n{i}. 测试消息: {message}")
        print(f"   期望意图: {expected_intent}")

        request = ChatRequest(
            user_id=f"test_intent_user_{i}",  # 每个测试用例使用不同的用户ID
            message=message,
        )

        try:
            response = await chat_service.process_message(request)

            print(f"   ✅ 识别意图: {response.intent}")
            print(f"   📊 置信度: {response.confidence}")
            print(f"   🎯 匹配: {'✅' if response.intent == expected_intent else '❌'}")
            print(f"   💬 响应: {response.message[:80]}...")

        except Exception as e:
            print(f"   ❌ 错误: {e}")


async def test_multi_agent_simple():
    """简单测试多代理工作流"""
    print("\n🤖 测试多代理工作流")
    print("=" * 50)

    chat_service = ChatService()

    test_message = "明天下午3点安排产品评审会议"
    print(f"测试消息: {test_message}")

    request = ChatRequest(user_id="test_multi_agent_user", message=test_message)

    try:
        # 使用多代理工作流
        response = await chat_service.process_message_with_agents(request)

        print(f"✅ 成功: {response.success}")
        print(f"🎯 意图: {response.intent}")
        print(f"📊 置信度: {response.confidence}")
        print(f"💬 响应: {response.message}")

        if response.data:
            print(f"📋 数据: {response.data}")

        if response.context:
            print(f"🔄 上下文状态: {response.context.get('state', 'unknown')}")

    except Exception as e:
        print(f"❌ 错误: {e}")
        logger.error(f"多代理测试失败: {e}", exc_info=True)


async def main():
    """主函数"""
    print("🚀 开始意图识别测试")
    print("=" * 60)

    try:
        # 1. 测试意图识别
        await test_intent_recognition()

        # 2. 测试多代理工作流
        await test_multi_agent_simple()

        print("\n✅ 所有测试完成")

    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
