"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/tools/route";
exports.ids = ["app/api/mcp/tools/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/tools/route.ts */ \"(rsc)/./src/app/api/mcp/tools/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/tools/route\",\n        pathname: \"/api/mcp/tools\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/tools/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\mcp\\\\tools\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/mcp/tools/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/mcp/tools/route.ts":
/*!****************************************!*\
  !*** ./src/app/api/mcp/tools/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mcp-tools */ \"(rsc)/./src/lib/mcp-tools.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/**\n * Next.js API Route for MCP Tools\n * GET /api/mcp/tools - 获取工具列表\n */ \n\n\nasync function GET(request) {\n    try {\n        const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.apiRequest(\"GET\", \"/api/mcp/tools\", {\n            requestId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: {\n                tools: _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__.ALL_TOOLS,\n                count: _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__.ALL_TOOLS.length\n            }\n        });\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Failed to get tools list\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2FwaS9tY3AvdG9vbHMvcm91dGUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUFBOzs7Q0FHQyxHQUV1RDtBQUNaO0FBQ047QUFFL0IsZUFBZUcsSUFBSUMsT0FBb0I7SUFDNUMsSUFBSTtRQUNGLE1BQU1DLFlBQVksQ0FBQyxJQUFJLEVBQUVDLEtBQUtDLEdBQUcsR0FBRyxDQUFDLEVBQUVDLEtBQUtDLE1BQU0sR0FBR0MsUUFBUSxDQUFDLElBQUlDLE1BQU0sQ0FBQyxHQUFHLEdBQUcsQ0FBQztRQUVoRlQsK0NBQU1BLENBQUNVLFVBQVUsQ0FBQyxPQUFPLGtCQUFrQjtZQUFFUDtRQUFVO1FBRXZELE9BQU9MLHFEQUFZQSxDQUFDYSxJQUFJLENBQUM7WUFDdkJDLFNBQVM7WUFDVEMsTUFBTTtnQkFDSkMsT0FBT2YscURBQVNBO2dCQUNoQmdCLE9BQU9oQixxREFBU0EsQ0FBQ2lCLE1BQU07WUFDekI7UUFDRjtJQUVGLEVBQUUsT0FBT0MsT0FBTztRQUNkakIsK0NBQU1BLENBQUNpQixLQUFLLENBQUMsNEJBQTRCQTtRQUV6QyxPQUFPbkIscURBQVlBLENBQUNhLElBQUksQ0FBQztZQUN2QkMsU0FBUztZQUNUSyxPQUFPQSxpQkFBaUJDLFFBQVFELE1BQU1FLE9BQU8sR0FBRztRQUNsRCxHQUFHO1lBQUVDLFFBQVE7UUFBSTtJQUNuQjtBQUNGIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbWNwLy4vc3JjL2FwcC9hcGkvbWNwL3Rvb2xzL3JvdXRlLnRzP2M3MDQiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBOZXh0LmpzIEFQSSBSb3V0ZSBmb3IgTUNQIFRvb2xzXG4gKiBHRVQgL2FwaS9tY3AvdG9vbHMgLSDojrflj5blt6XlhbfliJfooahcbiAqL1xuXG5pbXBvcnQgeyBOZXh0UmVxdWVzdCwgTmV4dFJlc3BvbnNlIH0gZnJvbSAnbmV4dC9zZXJ2ZXInO1xuaW1wb3J0IHsgQUxMX1RPT0xTIH0gZnJvbSAnQC9saWIvbWNwLXRvb2xzJztcbmltcG9ydCB7IGxvZ2dlciB9IGZyb20gJ0AvbGliL2xvZ2dlcic7XG5cbmV4cG9ydCBhc3luYyBmdW5jdGlvbiBHRVQocmVxdWVzdDogTmV4dFJlcXVlc3QpIHtcbiAgdHJ5IHtcbiAgICBjb25zdCByZXF1ZXN0SWQgPSBgYXBpXyR7RGF0ZS5ub3coKX1fJHtNYXRoLnJhbmRvbSgpLnRvU3RyaW5nKDM2KS5zdWJzdHIoMiwgOSl9YDtcbiAgICBcbiAgICBsb2dnZXIuYXBpUmVxdWVzdCgnR0VUJywgJy9hcGkvbWNwL3Rvb2xzJywgeyByZXF1ZXN0SWQgfSk7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXG4gICAgICBkYXRhOiB7XG4gICAgICAgIHRvb2xzOiBBTExfVE9PTFMsXG4gICAgICAgIGNvdW50OiBBTExfVE9PTFMubGVuZ3RoXG4gICAgICB9XG4gICAgfSk7XG4gICAgXG4gIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgbG9nZ2VyLmVycm9yKCdGYWlsZWQgdG8gZ2V0IHRvb2xzIGxpc3QnLCBlcnJvciBhcyBFcnJvcik7XG4gICAgXG4gICAgcmV0dXJuIE5leHRSZXNwb25zZS5qc29uKHtcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxuICAgICAgZXJyb3I6IGVycm9yIGluc3RhbmNlb2YgRXJyb3IgPyBlcnJvci5tZXNzYWdlIDogJ1Vua25vd24gZXJyb3InXG4gICAgfSwgeyBzdGF0dXM6IDUwMCB9KTtcbiAgfVxufVxuIl0sIm5hbWVzIjpbIk5leHRSZXNwb25zZSIsIkFMTF9UT09MUyIsImxvZ2dlciIsIkdFVCIsInJlcXVlc3QiLCJyZXF1ZXN0SWQiLCJEYXRlIiwibm93IiwiTWF0aCIsInJhbmRvbSIsInRvU3RyaW5nIiwic3Vic3RyIiwiYXBpUmVxdWVzdCIsImpzb24iLCJzdWNjZXNzIiwiZGF0YSIsInRvb2xzIiwiY291bnQiLCJsZW5ndGgiLCJlcnJvciIsIkVycm9yIiwibWVzc2FnZSIsInN0YXR1cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/tools/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! winston */ \"(rsc)/./node_modules/winston/lib/winston.js\");\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(winston__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Logger Utility\n * 统一的日志记录工具\n */ \n\nclass Logger {\n    constructor(){\n        const logLevel = process.env.LOG_LEVEL || \"info\";\n        const logFile = process.env.LOG_FILE || \"./logs/mcp-server.log\";\n        // 确保日志目录存在\n        const logDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(logFile);\n        this.logger = winston__WEBPACK_IMPORTED_MODULE_0___default().createLogger({\n            level: logLevel,\n            format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.timestamp(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.errors({\n                stack: true\n            }), winston__WEBPACK_IMPORTED_MODULE_0___default().format.json()),\n            defaultMeta: {\n                service: \"calmcp\"\n            },\n            transports: [\n                // 错误日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: path__WEBPACK_IMPORTED_MODULE_1___default().join(logDir, \"error.log\"),\n                    level: \"error\",\n                    maxsize: 5242880,\n                    maxFiles: 5\n                }),\n                // 所有日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: logFile,\n                    maxsize: 5242880,\n                    maxFiles: 5\n                })\n            ]\n        });\n        // 开发环境下添加控制台输出\n        if (true) {\n            this.logger.add(new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).Console({\n                format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.colorize(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.simple())\n            }));\n        }\n    }\n    formatMessage(message, context) {\n        if (!context) return message;\n        const contextStr = Object.entries(context).map(([key, value])=>`${key}=${value}`).join(\" \");\n        return `${message} [${contextStr}]`;\n    }\n    info(message, context) {\n        this.logger.info(this.formatMessage(message, context), context);\n    }\n    error(message, error, context) {\n        const logContext = {\n            ...context\n        };\n        if (error) {\n            logContext.error = error.message;\n            logContext.stack = error.stack;\n        }\n        this.logger.error(this.formatMessage(message, context), logContext);\n    }\n    warn(message, context) {\n        this.logger.warn(this.formatMessage(message, context), context);\n    }\n    debug(message, context) {\n        this.logger.debug(this.formatMessage(message, context), context);\n    }\n    // MCP 特定的日志方法\n    mcpRequest(toolName, args, context) {\n        this.info(`MCP tool called: ${toolName}`, {\n            ...context,\n            toolName,\n            args: JSON.stringify(args)\n        });\n    }\n    mcpResponse(toolName, success, duration, context) {\n        this.info(`MCP tool completed: ${toolName}`, {\n            ...context,\n            toolName,\n            success,\n            duration: `${duration}ms`\n        });\n    }\n    mcpError(toolName, error, context) {\n        this.error(`MCP tool failed: ${toolName}`, error, {\n            ...context,\n            toolName\n        });\n    }\n    streamStart(streamId, context) {\n        this.info(`Stream started: ${streamId}`, {\n            ...context,\n            streamId\n        });\n    }\n    streamEnd(streamId, itemCount, duration, context) {\n        this.info(`Stream completed: ${streamId}`, {\n            ...context,\n            streamId,\n            itemCount,\n            duration: `${duration}ms`\n        });\n    }\n    streamError(streamId, error, context) {\n        this.error(`Stream failed: ${streamId}`, error, {\n            ...context,\n            streamId\n        });\n    }\n    // API 请求日志\n    apiRequest(method, path, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            method,\n            path\n        });\n    }\n    apiResponse(method, path, status, duration, context) {\n        this.info(`API ${method} ${path} ${status}`, {\n            ...context,\n            method,\n            path,\n            status,\n            duration: `${duration}ms`\n        });\n    }\n    // 飞书 API 日志\n    feishuRequest(api, context) {\n        this.info(`Feishu API called: ${api}`, {\n            ...context,\n            feishuApi: api\n        });\n    }\n    feishuResponse(api, code, duration, context) {\n        this.info(`Feishu API completed: ${api}`, {\n            ...context,\n            feishuApi: api,\n            feishuCode: code,\n            duration: `${duration}ms`\n        });\n    }\n    feishuError(api, error, context) {\n        this.error(`Feishu API failed: ${api}`, error, {\n            ...context,\n            feishuApi: api\n        });\n    }\n}\n// 导出单例实例\nconst logger = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp-tools.ts":
/*!******************************!*\
  !*** ./src/lib/mcp-tools.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_TOOLS: () => (/* binding */ ALL_TOOLS),\n/* harmony export */   CALENDAR_TOOLS: () => (/* binding */ CALENDAR_TOOLS),\n/* harmony export */   TOOL_STATS: () => (/* binding */ TOOL_STATS)\n/* harmony export */ });\n/**\r\n * MCP Tools Definition\r\n * 定义所有可用的 MCP 工具\r\n * 包含简化工具和飞书官方工具\r\n */ // 尝试导入官方工具（如果可用）\nlet FEISHU_OFFICIAL_TOOLS = [];\ntry {\n    // 导入所有官方工具适配器\n    const { ALL_FEISHU_CALENDAR_TOOLS } = __webpack_require__(/*! ./tools/tool-adapter */ \"(rsc)/./src/lib/tools/tool-adapter.ts\");\n    FEISHU_OFFICIAL_TOOLS = ALL_FEISHU_CALENDAR_TOOLS || [];\n    console.log(`✅ 成功加载 ${FEISHU_OFFICIAL_TOOLS.length} 个官方工具`);\n} catch (error) {\n    console.warn(\"⚠️  官方工具加载失败，使用简化工具:\", error);\n}\nconst CALENDAR_TOOLS = [\n    {\n        name: \"calendar_list\",\n        description: \"获取用户的日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的日历数量，最小值50，默认50\",\n                    minimum: 50,\n                    maximum: 200\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，用于获取下一页数据\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar_event_create\",\n        description: \"创建新的日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区，如 Asia/Shanghai\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区，如 Asia/Shanghai\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"详细地址\"\n                        }\n                    }\n                },\n                attendees: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            type: {\n                                type: \"string\",\n                                enum: [\n                                    \"user\",\n                                    \"chat\",\n                                    \"resource\"\n                                ],\n                                description: \"参与者类型\"\n                            },\n                            attendee_id: {\n                                type: \"string\",\n                                description: \"参与者ID\"\n                            },\n                            is_optional: {\n                                type: \"boolean\",\n                                description: \"是否为可选参与者\"\n                            }\n                        },\n                        required: [\n                            \"type\",\n                            \"attendee_id\"\n                        ]\n                    }\n                },\n                reminders: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            minutes: {\n                                type: \"number\",\n                                description: \"提前提醒的分钟数\"\n                            }\n                        },\n                        required: [\n                            \"minutes\"\n                        ]\n                    }\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"事件可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_search\",\n        description: \"搜索日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"搜索开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"搜索结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的事件数量\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_update\",\n        description: \"更新日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"详细地址\"\n                        }\n                    }\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_delete\",\n        description: \"删除日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_get\",\n        description: \"获取单个日历事件详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_list\",\n        description: \"获取日历事件列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间戳（秒）\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间戳（秒）\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的事件数量\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n// 合并简化工具和官方工具\nconst ALL_TOOLS = [\n    ...CALENDAR_TOOLS,\n    ...FEISHU_OFFICIAL_TOOLS // 官方工具（如果可用）\n];\n// 导出工具统计信息\nconst TOOL_STATS = {\n    simple: CALENDAR_TOOLS.length,\n    official: FEISHU_OFFICIAL_TOOLS.length,\n    total: ALL_TOOLS.length\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-adapter.ts":
/*!***************************************!*\
  !*** ./src/lib/tools/tool-adapter.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CALENDAR_ACL_TOOLS: () => (/* binding */ CALENDAR_ACL_TOOLS),\n/* harmony export */   CALENDAR_EVENT_ATTENDEE_TOOLS: () => (/* binding */ CALENDAR_EVENT_ATTENDEE_TOOLS),\n/* harmony export */   CALENDAR_EVENT_EXTENDED_TOOLS: () => (/* binding */ CALENDAR_EVENT_EXTENDED_TOOLS),\n/* harmony export */   CALENDAR_EVENT_MEETING_CHAT_TOOLS: () => (/* binding */ CALENDAR_EVENT_MEETING_CHAT_TOOLS),\n/* harmony export */   COMMON_CALENDAR_TOOLS: () => (/* binding */ COMMON_CALENDAR_TOOLS),\n/* harmony export */   EXTENDED_CALENDAR_TOOLS: () => (/* binding */ EXTENDED_CALENDAR_TOOLS),\n/* harmony export */   OTHER_CALENDAR_TOOLS: () => (/* binding */ OTHER_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器\n * 手动定义常用的飞书日历工具，避免复杂的 Zod 转换\n */ /**\n * 手动定义的常用飞书日历工具\n * 基于官方 API 文档创建，避免 Zod 转换复杂性\n */ /**\n * 常用的日历工具（简化版）\n */ const COMMON_CALENDAR_TOOLS = [\n    // 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最小值50\",\n                    minimum: 50,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.list\",\n        description: \"获取日历事件列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间（时间戳）\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间（时间戳）\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    description: \"开始时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                end_time: {\n                    type: \"object\",\n                    description: \"结束时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.search\",\n        description: \"搜索日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"搜索开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"搜索结束时间\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历访问控制工具\n */ const CALENDAR_ACL_TOOLS = [\n    {\n        name: \"calendar.v4.calendarAcl.create\",\n        description: \"创建日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                role: {\n                    type: \"string\",\n                    enum: [\n                        \"unknown\",\n                        \"free_busy_reader\",\n                        \"reader\",\n                        \"writer\",\n                        \"owner\"\n                    ],\n                    description: \"访问权限角色\"\n                },\n                scope: {\n                    type: \"object\",\n                    properties: {\n                        type: {\n                            type: \"string\",\n                            enum: [\n                                \"user\"\n                            ],\n                            description: \"权限生效范围类型\"\n                        },\n                        user_id: {\n                            type: \"string\",\n                            description: \"用户ID\"\n                        }\n                    },\n                    required: [\n                        \"type\"\n                    ]\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"role\",\n                \"scope\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.delete\",\n        description: \"删除日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                acl_id: {\n                    type: \"string\",\n                    description: \"访问控制ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"acl_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.list\",\n        description: \"获取日历访问控制列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最小值10\",\n                    minimum: 10\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.subscription\",\n        description: \"订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.unsubscription\",\n        description: \"取消订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 扩展的日历管理工具\n */ const EXTENDED_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendar.create\",\n        description: \"创建共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色（RGB int32值）\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.delete\",\n        description: \"删除共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.patch\",\n        description: \"更新日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.primary\",\n        description: \"获取主日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.search\",\n        description: \"搜索日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 200\n                }\n            },\n            required: [\n                \"query\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscribe\",\n        description: \"订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscription\",\n        description: \"订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscribe\",\n        description: \"取消订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscription\",\n        description: \"取消订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件参与者工具\n */ const CALENDAR_EVENT_ATTENDEE_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventAttendee.batchDelete\",\n        description: \"批量删除日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_ids: {\n                    type: \"array\",\n                    items: {\n                        type: \"string\"\n                    },\n                    description: \"参与人ID列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_ids\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.chatMembersBatchCreate\",\n        description: \"批量添加群成员为日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                chat_id: {\n                    type: \"string\",\n                    description: \"群聊ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.create\",\n        description: \"添加日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendees: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            type: {\n                                type: \"string\",\n                                enum: [\n                                    \"user\",\n                                    \"chat\",\n                                    \"resource\",\n                                    \"third_party\"\n                                ],\n                                description: \"参与人类型\"\n                            },\n                            attendee_id: {\n                                type: \"string\",\n                                description: \"参与人ID\"\n                            },\n                            rsvp_status: {\n                                type: \"string\",\n                                enum: [\n                                    \"needs_action\",\n                                    \"accept\",\n                                    \"tentative\",\n                                    \"decline\",\n                                    \"removed\"\n                                ],\n                                description: \"参与状态\"\n                            },\n                            is_optional: {\n                                type: \"boolean\",\n                                description: \"是否为可选参与人\"\n                            },\n                            display_name: {\n                                type: \"string\",\n                                description: \"参与人名称\"\n                            }\n                        },\n                        required: [\n                            \"type\",\n                            \"attendee_id\"\n                        ]\n                    },\n                    description: \"参与人列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendees\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.list\",\n        description: \"获取日程参与人列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendeeChatMember.list\",\n        description: \"获取日程参与人群成员列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_id: {\n                    type: \"string\",\n                    description: \"参与人ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件扩展工具\n */ const CALENDAR_EVENT_EXTENDED_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"获取日程实例视图\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"结束时间\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"日程公开范围\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与人权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"日程占用的忙闲状态\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"地点纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"地点经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                reminders: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            minutes: {\n                                type: \"number\",\n                                description: \"提前多少分钟提醒\"\n                            }\n                        }\n                    },\n                    description: \"日程提醒列表\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则\"\n                },\n                status: {\n                    type: \"string\",\n                    enum: [\n                        \"tentative\",\n                        \"confirmed\",\n                        \"cancelled\"\n                    ],\n                    description: \"日程状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instances\",\n        description: \"获取重复日程的实例\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    }\n];\n/**\n * 会议聊天工具\n */ const CALENDAR_EVENT_MEETING_CHAT_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.create\",\n        description: \"创建会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.delete\",\n        description: \"删除会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.patch\",\n        description: \"更新会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingMinute.create\",\n        description: \"创建会议纪要\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                content: {\n                    type: \"string\",\n                    description: \"会议纪要内容\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"content\"\n            ]\n        }\n    }\n];\n/**\n * 其他日历工具\n */ const OTHER_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.reply\",\n        description: \"回复日程邀请\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                rsvp_status: {\n                    type: \"string\",\n                    enum: [\n                        \"accept\",\n                        \"tentative\",\n                        \"decline\"\n                    ],\n                    description: \"回复状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"rsvp_status\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.subscription\",\n        description: \"订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.unsubscription\",\n        description: \"取消订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.create\",\n        description: \"创建Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                admin_account: {\n                    type: \"string\",\n                    description: \"管理员账号\"\n                },\n                exchange_account: {\n                    type: \"string\",\n                    description: \"Exchange账号\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"admin_account\",\n                \"exchange_account\",\n                \"user_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.delete\",\n        description: \"删除Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.get\",\n        description: \"获取Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.freebusy.list\",\n        description: \"查询忙闲信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                time_min: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                time_max: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                room_id: {\n                    type: \"string\",\n                    description: \"会议室ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"time_min\",\n                \"time_max\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.setting.generateCaldavConf\",\n        description: \"生成CalDAV配置\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                device_name: {\n                    type: \"string\",\n                    description: \"设备名称\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.create\",\n        description: \"创建请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                timezone: {\n                    type: \"string\",\n                    description: \"时区\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"请假开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"请假结束时间\"\n                },\n                title: {\n                    type: \"string\",\n                    description: \"请假标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"请假描述\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"user_id\",\n                \"timezone\",\n                \"start_time\",\n                \"end_time\",\n                \"title\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.delete\",\n        description: \"删除请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                timeoff_event_id: {\n                    type: \"string\",\n                    description: \"请假日程ID\"\n                }\n            },\n            required: [\n                \"timeoff_event_id\"\n            ]\n        }\n    }\n];\n/**\n * 完整的日历工具（包含所有 43 个工具）\n */ const ALL_FEISHU_CALENDAR_TOOLS = [\n    ...COMMON_CALENDAR_TOOLS,\n    ...CALENDAR_ACL_TOOLS,\n    ...EXTENDED_CALENDAR_TOOLS,\n    ...CALENDAR_EVENT_ATTENDEE_TOOLS,\n    ...CALENDAR_EVENT_EXTENDED_TOOLS,\n    ...CALENDAR_EVENT_MEETING_CHAT_TOOLS,\n    ...OTHER_CALENDAR_TOOLS\n];\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return ALL_FEISHU_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数（简化版本）\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/readable-stream","vendor-chunks/winston","vendor-chunks/color","vendor-chunks/async","vendor-chunks/logform","vendor-chunks/safe-stable-stringify","vendor-chunks/@colors","vendor-chunks/fecha","vendor-chunks/winston-transport","vendor-chunks/string_decoder","vendor-chunks/@dabh","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/stack-trace","vendor-chunks/triple-beam","vendor-chunks/ms","vendor-chunks/kuler","vendor-chunks/safe-buffer","vendor-chunks/one-time","vendor-chunks/inherits","vendor-chunks/fn.name","vendor-chunks/enabled","vendor-chunks/colorspace","vendor-chunks/is-stream","vendor-chunks/simple-swizzle","vendor-chunks/text-hex","vendor-chunks/is-arrayish","vendor-chunks/util-deprecate"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();