#!/usr/bin/env python3
"""
详细分析日历脚本
检查所有日历的详细信息，找出可能的无标题或无效日历
"""

import requests
import json
import time
from typing import List, Dict, Any

class CalendarAnalyzer:
    def __init__(self):
        self.base_url = "http://localhost:3000/api/mcp/tools/call"
        self.headers = {
            "Content-Type": "application/json"
        }
        
    def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用MCP工具"""
        payload = {
            "name": tool_name,
            "arguments": arguments or {}
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            result = response.json()
            
            # 解析MCP响应格式
            if result.get("success") and "result" in result:
                content = result["result"].get("content", [])
                if content and content[0].get("type") == "text":
                    try:
                        # 解析文本内容中的JSON
                        text_content = content[0]["text"]
                        parsed_data = json.loads(text_content)
                        return parsed_data
                    except json.JSONDecodeError:
                        print(f"❌ JSON解析失败: {text_content}")
                        return None
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None

    def get_all_calendars(self) -> List[Dict[str, Any]]:
        """获取所有日历"""
        all_calendars = []
        page_token = None
        page_count = 0
        
        while True:
            page_count += 1
            print(f"📅 正在获取第 {page_count} 页日历列表...")
            
            arguments = {"page_size": 50}
            if page_token:
                arguments["page_token"] = page_token
                
            result = self.call_tool("calendar_list", arguments)
            
            if not result or "data" not in result:
                print("❌ 获取日历列表失败")
                break
                
            data = result["data"]
            calendars = data.get("calendar_list", [])
            all_calendars.extend(calendars)
            
            print(f"✅ 第 {page_count} 页获取到 {len(calendars)} 个日历")
            
            # 检查是否有更多数据
            if not data.get("has_more", False):
                print(f"📄 已获取所有页面，共 {len(all_calendars)} 个日历")
                break
                
            page_token = data.get("page_token")
            if not page_token:
                print("📄 没有更多页面")
                break
                
            time.sleep(0.5)  # 避免请求过快
            
        return all_calendars

    def analyze_calendar_details(self, calendar: Dict[str, Any]) -> Dict[str, Any]:
        """分析单个日历的详细信息"""
        calendar_id = calendar.get("calendar_id")
        summary = calendar.get("summary", "").strip()
        summary_alias = calendar.get("summary_alias", "").strip()
        calendar_type = calendar.get("type", "")
        permissions = calendar.get("permissions", "")
        role = calendar.get("role", "")
        description = calendar.get("description", "").strip()
        color = calendar.get("color", "")
        
        # 检查各种可能的无标题情况
        analysis = {
            "calendar_id": calendar_id,
            "summary": summary,
            "summary_alias": summary_alias,
            "type": calendar_type,
            "permissions": permissions,
            "role": role,
            "description": description,
            "color": color,
            "issues": [],
            "suspicious": False
        }
        
        # 检查无标题情况
        if not summary and not summary_alias:
            analysis["issues"].append("无标题")
            analysis["suspicious"] = True
            
        # 检查空字符串标题
        if summary == "" and summary_alias == "":
            analysis["issues"].append("空标题")
            analysis["suspicious"] = True
            
        # 检查只有空格的标题
        if summary.strip() == "" and summary_alias.strip() == "":
            analysis["issues"].append("空白标题")
            analysis["suspicious"] = True
            
        # 检查可疑的权限
        if permissions == "show_only_free_busy":
            analysis["issues"].append("仅显示忙闲状态")
            
        # 检查描述为空的情况
        if not description:
            analysis["issues"].append("无描述")
            
        # 检查重复的日历（通过标题判断）
        # 这个会在后续分析中处理
            
        return analysis

    def analyze_all_calendars(self) -> None:
        """分析所有日历"""
        print("🚀 开始详细分析日历")
        print("=" * 60)
        
        # 获取所有日历
        calendars = self.get_all_calendars()
        if not calendars:
            print("❌ 没有获取到任何日历")
            return
            
        print(f"\n📊 总共获取到 {len(calendars)} 个日历")
        print()
        
        # 分析每个日历
        analyses = []
        title_counts = {}  # 统计标题出现次数
        
        for i, calendar in enumerate(calendars, 1):
            analysis = self.analyze_calendar_details(calendar)
            analyses.append(analysis)
            
            # 统计标题
            title = analysis["summary"] or analysis["summary_alias"] or "无标题"
            title_counts[title] = title_counts.get(title, 0) + 1
            
            # 打印可疑的日历
            if analysis["suspicious"]:
                print(f"⚠️  可疑日历 {i}:")
                print(f"   ID: {analysis['calendar_id']}")
                print(f"   标题: '{analysis['summary']}'")
                print(f"   别名: '{analysis['summary_alias']}'")
                print(f"   类型: {analysis['type']}")
                print(f"   问题: {', '.join(analysis['issues'])}")
                print()
        
        # 检查重复标题
        print("📈 标题统计:")
        for title, count in sorted(title_counts.items(), key=lambda x: x[1], reverse=True):
            if count > 1:
                print(f"   🔄 '{title}': {count} 次")
        
        print()
        
        # 统计信息
        suspicious_count = sum(1 for a in analyses if a["suspicious"])
        primary_count = sum(1 for a in analyses if a["type"] == "primary")
        google_count = sum(1 for a in analyses if a["type"] == "google")
        shared_count = sum(1 for a in analyses if a["type"] == "shared")
        
        print("📊 统计信息:")
        print(f"   🔒 主日历: {primary_count} 个")
        print(f"   🌐 Google日历: {google_count} 个")
        print(f"   📅 共享日历: {shared_count} 个")
        print(f"   ⚠️  可疑日历: {suspicious_count} 个")
        print()
        
        # 显示所有可疑日历的详细信息
        if suspicious_count > 0:
            print("🔍 可疑日历详细信息:")
            print("-" * 60)
            for i, analysis in enumerate(analyses, 1):
                if analysis["suspicious"]:
                    print(f"{i}. 日历ID: {analysis['calendar_id']}")
                    print(f"   标题: '{analysis['summary']}'")
                    print(f"   别名: '{analysis['summary_alias']}'")
                    print(f"   类型: {analysis['type']}")
                    print(f"   权限: {analysis['permissions']}")
                    print(f"   角色: {analysis['role']}")
                    print(f"   描述: '{analysis['description']}'")
                    print(f"   问题: {', '.join(analysis['issues'])}")
                    print()
        else:
            print("✅ 没有发现可疑的日历")
            
        # 显示重复标题的日历
        duplicate_titles = {title: count for title, count in title_counts.items() if count > 1}
        if duplicate_titles:
            print("🔄 重复标题的日历:")
            print("-" * 60)
            for title, count in duplicate_titles.items():
                print(f"标题 '{title}' 出现 {count} 次:")
                for analysis in analyses:
                    if (analysis["summary"] == title or analysis["summary_alias"] == title):
                        print(f"  - {analysis['calendar_id']} ({analysis['type']})")
                print()

def main():
    analyzer = CalendarAnalyzer()
    analyzer.analyze_all_calendars()

if __name__ == "__main__":
    main() 