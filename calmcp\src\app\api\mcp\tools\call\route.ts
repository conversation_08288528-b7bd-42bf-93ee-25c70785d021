/**
 * Next.js API Route for MCP Tool Calls
 * POST /api/mcp/tools/call - 调用 MCP 工具
 */

import { NextRequest, NextResponse } from 'next/server';
import { OfficialFeishuClient } from '@/lib/feishu-official-client';
import { logger } from '@/lib/logger';
import type { MCPToolCall, MCPToolResult } from '@/types/mcp';

// 创建飞书客户端工厂函数
function createFeishuClient(userAccessToken?: string): OfficialFeishuClient {
  return new OfficialFeishuClient(
    process.env.FEISHU_APP_ID!,
    process.env.FEISHU_APP_SECRET!,
    userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN
  );
}

async function callTool(name: string, args: any, userAccessToken?: string): Promise<MCPToolResult> {
  const startTime = Date.now();

  try {
    logger.feishuRequest(name);

    // 创建使用指定token的飞书客户端
    const feishuClient = createFeishuClient(userAccessToken);

    // 使用简化的通用 API 调用方法
    const result = await feishuClient.callApi(name, args);

    const duration = Date.now() - startTime;
    logger.feishuResponse(name, result.code, duration);

    return {
      content: [{
        type: 'text',
        text: JSON.stringify(result, null, 2)
      }],
      isError: result.code !== 0
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.feishuError(name, error as Error);

    return {
      content: [{
        type: 'text',
        text: `Error: ${error instanceof Error ? error.message : String(error)}`
      }],
      isError: true
    };
  }
}

export async function POST(request: NextRequest) {
  try {
    const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const body = await request.json() as MCPToolCall;
    const { name, arguments: args } = body;

    // 从请求头中获取Authorization token
    const authHeader = request.headers.get('authorization');
    let userAccessToken: string | undefined;

    if (authHeader && authHeader.startsWith('Bearer ')) {
      userAccessToken = authHeader.substring(7); // 移除 "Bearer " 前缀
      console.log(`🔑 使用请求头中的token: ${userAccessToken.substring(0, 20)}...`);
    } else {
      console.log(`🔑 使用环境变量中的token`);
    }

    logger.mcpRequest(name, args, { requestId });
    const startTime = Date.now();

    const result = await callTool(name, args, userAccessToken);
    const duration = Date.now() - startTime;

    logger.mcpResponse(name, !result.isError, duration, { requestId });

    return NextResponse.json({
      success: !result.isError,
      result
    });

  } catch (error) {
    logger.mcpError('unknown', error as Error);

    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
