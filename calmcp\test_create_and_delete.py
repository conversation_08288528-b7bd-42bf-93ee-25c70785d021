#!/usr/bin/env python3
"""
测试创建和删除日历功能
"""

import requests
import json
import sys
import os
import time

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


def call_mcp_tool(tool_name, arguments):
    """调用 MCP 工具"""
    try:
        response = requests.post(
            "http://localhost:3000/api/mcp/tools/call",
            json={
                "name": tool_name,
                "arguments": arguments
            },
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                content = result.get('result', {}).get('content', [])
                if content:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    return feishu_response
        
        print(f"❌ 工具调用失败: {response.status_code} - {response.text}")
        return None
        
    except Exception as e:
        print(f"❌ 工具调用异常: {e}")
        return None


def test_create_and_delete():
    """测试创建和删除日历"""
    print("🧪 测试创建和删除日历功能")
    print("="*60)
    
    # 1. 创建测试日历
    print("1️⃣ 创建测试日历...")
    
    test_calendar_name = f"删除测试日历_{int(time.time())}"
    
    create_result = call_mcp_tool("calendar.v4.calendar.create", {
        "summary": test_calendar_name,
        "description": "这是一个用于测试删除功能的日历",
        "permissions": "private"
    })
    
    if not create_result:
        print("❌ 创建日历失败")
        return
    
    if create_result.get('code') != 0:
        print(f"❌ 创建日历失败: {create_result.get('msg')}")
        return
    
    calendar_data = create_result.get('data', {}).get('calendar', {})
    calendar_id = calendar_data.get('calendar_id')
    
    if not calendar_id:
        print("❌ 创建日历成功但未获取到日历ID")
        return
    
    print(f"✅ 创建日历成功!")
    print(f"   名称: {test_calendar_name}")
    print(f"   ID: {calendar_id}")
    
    # 2. 验证日历存在
    print(f"\n2️⃣ 验证日历存在...")
    
    list_result = call_mcp_tool("calendar.v4.calendar.list", {"page_size": 100})
    
    if list_result and list_result.get('code') == 0:
        calendars = list_result.get('data', {}).get('calendar_list', [])
        found = any(cal.get('calendar_id') == calendar_id for cal in calendars)
        
        if found:
            print(f"✅ 验证成功: 日历存在于列表中")
        else:
            print(f"❌ 验证失败: 日历不在列表中")
            return
    else:
        print(f"❌ 获取日历列表失败")
        return
    
    # 3. 删除日历
    print(f"\n3️⃣ 删除日历...")
    
    delete_result = call_mcp_tool("calendar.v4.calendar.delete", {
        "calendar_id": calendar_id
    })
    
    if not delete_result:
        print("❌ 删除日历失败")
        return
    
    if delete_result.get('code') != 0:
        print(f"❌ 删除日历失败: {delete_result.get('msg')}")
        return
    
    print(f"✅ 删除日历成功!")
    
    # 4. 验证日历已删除
    print(f"\n4️⃣ 验证日历已删除...")
    
    # 等待一下，确保删除操作生效
    time.sleep(2)
    
    verify_result = call_mcp_tool("calendar.v4.calendar.list", {"page_size": 100})
    
    if verify_result and verify_result.get('code') == 0:
        calendars = verify_result.get('data', {}).get('calendar_list', [])
        still_exists = any(cal.get('calendar_id') == calendar_id for cal in calendars)
        
        if not still_exists:
            print(f"✅ 验证成功: 日历已被删除")
            print(f"🎉 创建和删除功能测试通过!")
        else:
            print(f"⚠️  日历仍然存在，可能删除未生效")
    else:
        print(f"❌ 验证失败: 无法获取日历列表")


def test_delete_existing_test_calendar():
    """删除现有的测试日历"""
    print("\n🧹 清理现有测试日历")
    print("="*60)
    
    # 获取所有日历
    list_result = call_mcp_tool("calendar.v4.calendar.list", {"page_size": 100})
    
    if not list_result or list_result.get('code') != 0:
        print("❌ 获取日历列表失败")
        return
    
    calendars = list_result.get('data', {}).get('calendar_list', [])
    
    # 找到测试日历
    test_calendars = []
    for cal in calendars:
        name = cal.get('summary', '')
        if any(pattern in name for pattern in ['测试', 'test', '删除测试']):
            test_calendars.append(cal)
    
    if not test_calendars:
        print("✅ 没有找到需要清理的测试日历")
        return
    
    print(f"📋 找到 {len(test_calendars)} 个测试日历:")
    for i, cal in enumerate(test_calendars, 1):
        name = cal.get('summary', '未命名')
        cal_id = cal.get('calendar_id', 'N/A')
        print(f"  {i}. {name}")
        print(f"     ID: {cal_id}")
    
    # 删除第一个测试日历作为演示
    if test_calendars:
        test_cal = test_calendars[0]
        cal_name = test_cal.get('summary', '未命名')
        cal_id = test_cal.get('calendar_id')
        
        print(f"\n🗑️  删除测试日历: {cal_name}")
        
        delete_result = call_mcp_tool("calendar.v4.calendar.delete", {
            "calendar_id": cal_id
        })
        
        if delete_result and delete_result.get('code') == 0:
            print(f"✅ 删除成功!")
        else:
            print(f"❌ 删除失败: {delete_result.get('msg') if delete_result else '未知错误'}")


if __name__ == "__main__":
    # 先清理现有测试日历
    test_delete_existing_test_calendar()
    
    # 然后测试完整的创建和删除流程
    test_create_and_delete()
