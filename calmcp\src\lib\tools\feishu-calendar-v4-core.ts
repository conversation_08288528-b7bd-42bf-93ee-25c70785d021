import { z } from 'zod';

// 核心日历方法 - 只保留项目必需的功能（7个精选工具）
export type calendarV4CoreToolName =
  // 📅 日历管理
  | 'calendar.v4.calendar.list'           // 查看日历列表
  | 'calendar.v4.calendar.get'            // 获取单个日历详情

  // 📝 日程管理 (核心功能)
  | 'calendar.v4.calendarEvent.list'      // 列出日程（基础功能）
  | 'calendar.v4.calendarEvent.get'       // 获取单个日程
  | 'calendar.v4.calendarEvent.create'    // 创建日程
  | 'calendar.v4.calendarEvent.patch'     // 更新日程
  | 'calendar.v4.calendarEvent.delete';   // 删除日程

// 移除的方法（SDK不支持或冗余）：
// | 'calendar.v4.calendarEvent.instanceView' // ❌ 飞书SDK不支持此方法
// | 'calendar.v4.calendarEvent.search'    // ❌ 功能与list重叠

// 📅 日历列表
export const calendarV4CalendarList = {
  project: 'calendar',
  name: 'calendar.v4.calendar.list',
  sdkName: 'calendar.v4.calendar.list',
  path: '/open-apis/calendar/v4/calendars',
  httpMethod: 'GET',
  description: '[Feishu/Lark]-日历-日历管理-获取日历列表-调用该接口以当前身份（应用或用户）获取日历列表',
  accessTokens: ['tenant', 'user'],
  schema: {
    params: z.object({
      page_size: z.number().describe('分页大小，最大值为 1000').optional(),
      page_token: z.string().describe('分页标记，第一次请求不填，表示从头开始遍历').optional(),
      sync_token: z.string().describe('同步标记，用于增量同步').optional(),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📅 获取单个日历
export const calendarV4CalendarGet = {
  project: 'calendar',
  name: 'calendar.v4.calendar.get',
  sdkName: 'calendar.v4.calendar.get',
  path: '/open-apis/calendar/v4/calendars/:calendar_id',
  httpMethod: 'GET',
  description: '[Feishu/Lark]-日历-日历管理-获取日历-调用该接口以当前身份（应用或用户）获取指定日历的信息',
  accessTokens: ['tenant', 'user'],
  schema: {
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 日程列表
export const calendarV4CalendarEventList = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.list',
  sdkName: 'calendar.v4.calendarEvent.list',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events',
  httpMethod: 'GET',
  description: '[Feishu/Lark]-日历-日程管理-获取日程列表-调用该接口以当前身份（应用或用户）获取指定日历的日程列表',
  accessTokens: ['tenant', 'user'],
  schema: {
    params: z.object({
      start_time: z.string().describe('开始时间，Unix 时间戳，单位为秒').optional(),
      end_time: z.string().describe('结束时间，Unix 时间戳，单位为秒').optional(),
      page_size: z.number().describe('分页大小，最大值为 1000').optional(),
      page_token: z.string().describe('分页标记').optional(),
      sync_token: z.string().describe('同步标记').optional(),
      user_id_type: z.enum(['open_id', 'union_id', 'user_id']).describe('用户ID类型').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 日程视图 (包含重复日程展开) - SDK不支持，暂时保留定义
export const calendarV4CalendarEventInstanceView = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.instanceView',
  sdkName: 'calendar.v4.calendarEvent.instanceView',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events/instance_view',
  httpMethod: 'GET',
  description: '[Feishu/Lark]-日历-日程管理-查询日程视图-调用该接口以用户身份查询指定日历下的日程视图。会按照重复日程的重复性规则展开成多个日程实例',
  accessTokens: ['tenant', 'user'],
  schema: {
    params: z.object({
      start_time: z.string().describe('开始时间，Unix 时间戳，单位为秒'),
      end_time: z.string().describe('结束时间，Unix 时间戳，单位为秒'),
      user_id_type: z.enum(['open_id', 'union_id', 'user_id']).describe('用户ID类型').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 获取单个日程
export const calendarV4CalendarEventGet = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.get',
  sdkName: 'calendar.v4.calendarEvent.get',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events/:event_id',
  httpMethod: 'GET',
  description: '[Feishu/Lark]-日历-日程管理-获取日程-调用该接口以当前身份（应用或用户）获取指定日程的详细信息',
  accessTokens: ['tenant', 'user'],
  schema: {
    params: z.object({
      user_id_type: z.enum(['open_id', 'union_id', 'user_id']).describe('用户ID类型').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
      event_id: z.string().describe('日程 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 创建日程
export const calendarV4CalendarEventCreate = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.create',
  sdkName: 'calendar.v4.calendarEvent.create',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events',
  httpMethod: 'POST',
  description: '[Feishu/Lark]-日历-日程管理-创建日程-调用该接口以当前身份（应用或用户）在指定日历上创建一个日程',
  accessTokens: ['tenant', 'user'],
  schema: {
    data: z.object({
      summary: z.string().describe('日程标题'),
      description: z.string().describe('日程描述').optional(),
      start_time: z.object({
        timestamp: z.string().describe('开始时间，Unix 时间戳，单位为秒'),
        timezone: z.string().describe('时区').optional(),
      }).describe('开始时间'),
      end_time: z.object({
        timestamp: z.string().describe('结束时间，Unix 时间戳，单位为秒'),
        timezone: z.string().describe('时区').optional(),
      }).describe('结束时间'),
      location: z.object({
        name: z.string().describe('地点名称').optional(),
        address: z.string().describe('地点地址').optional(),
        latitude: z.number().describe('纬度').optional(),
        longitude: z.number().describe('经度').optional(),
      }).describe('日程地点').optional(),
      color: z.number().describe('日程颜色').optional(),
      reminders: z.array(z.object({
        minutes: z.number().describe('提前多少分钟提醒'),
      })).describe('提醒设置').optional(),
      recurrence: z.string().describe('重复规则，RRULE 格式').optional(),
      visibility: z.enum(['default', 'public', 'private']).describe('可见性').optional(),
      attendee_ability: z.enum(['none', 'can_see_others', 'can_invite_others', 'can_modify_event']).describe('参与者权限').optional(),
      free_busy_status: z.enum(['busy', 'free']).describe('忙闲状态').optional(),
    }),
    params: z.object({
      user_id_type: z.enum(['open_id', 'union_id', 'user_id']).describe('用户ID类型').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 更新日程
export const calendarV4CalendarEventPatch = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.patch',
  sdkName: 'calendar.v4.calendarEvent.patch',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events/:event_id',
  httpMethod: 'PATCH',
  description: '[Feishu/Lark]-日历-日程管理-更新日程-调用该接口以当前身份（应用或用户）更新指定日程的信息',
  accessTokens: ['tenant', 'user'],
  schema: {
    data: z.object({
      summary: z.string().describe('日程标题').optional(),
      description: z.string().describe('日程描述').optional(),
      start_time: z.object({
        timestamp: z.string().describe('开始时间，Unix 时间戳，单位为秒'),
        timezone: z.string().describe('时区').optional(),
      }).describe('开始时间').optional(),
      end_time: z.object({
        timestamp: z.string().describe('结束时间，Unix 时间戳，单位为秒'),
        timezone: z.string().describe('时区').optional(),
      }).describe('结束时间').optional(),
      location: z.object({
        name: z.string().describe('地点名称').optional(),
        address: z.string().describe('地点地址').optional(),
        latitude: z.number().describe('纬度').optional(),
        longitude: z.number().describe('经度').optional(),
      }).describe('日程地点').optional(),
      color: z.number().describe('日程颜色').optional(),
      visibility: z.enum(['default', 'public', 'private']).describe('可见性').optional(),
      attendee_ability: z.enum(['none', 'can_see_others', 'can_invite_others', 'can_modify_event']).describe('参与者权限').optional(),
      free_busy_status: z.enum(['busy', 'free']).describe('忙闲状态').optional(),
    }),
    params: z.object({
      user_id_type: z.enum(['open_id', 'union_id', 'user_id']).describe('用户ID类型').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
      event_id: z.string().describe('日程 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};

// 📝 删除日程
export const calendarV4CalendarEventDelete = {
  project: 'calendar',
  name: 'calendar.v4.calendarEvent.delete',
  sdkName: 'calendar.v4.calendarEvent.delete',
  path: '/open-apis/calendar/v4/calendars/:calendar_id/events/:event_id',
  httpMethod: 'DELETE',
  description: '[Feishu/Lark]-日历-日程管理-删除日程-调用该接口以当前身份（应用或用户）删除指定日程',
  accessTokens: ['tenant', 'user'],
  schema: {
    params: z.object({
      need_notification: z.boolean().describe('是否给日程参与人发送bot通知').optional(),
    }),
    path: z.object({
      calendar_id: z.string().describe('日历 ID'),
      event_id: z.string().describe('日程 ID'),
    }),
    useUAT: z.boolean().describe('使用用户身份请求, 否则使用应用身份').optional(),
  },
};



// 导出所有核心工具（7个精选工具）
export const calendarV4CoreTools = [
  // 📅 日历管理
  calendarV4CalendarList,
  calendarV4CalendarGet,

  // 📝 日程管理 (核心功能)
  calendarV4CalendarEventList,         // 📋 基础日程列表查询
  calendarV4CalendarEventGet,
  calendarV4CalendarEventCreate,
  calendarV4CalendarEventPatch,
  calendarV4CalendarEventDelete,
];
