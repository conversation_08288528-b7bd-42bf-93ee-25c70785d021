#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token桥接服务 - 将Supabase中的token提供给MCP服务使用
"""

import os
import logging
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 导入项目的token存储
from integrations.storage import get_token

logger = logging.getLogger(__name__)

class TokenBridgeService:
    """Token桥接服务"""
    
    def __init__(self, port: int = 3001):
        self.port = port
        self.app = FastAPI(title="Feishu Token Bridge Service")
        self._setup_routes()
        self._setup_cors()
        
    def _setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "ok", "service": "token-bridge"}
        
        @self.app.get("/api/token/{user_id}")
        async def get_user_token(user_id: str):
            """获取用户token - 使用项目现有的token管理系统"""
            try:
                # 直接使用项目现有的token获取函数
                # 这个函数已经包含了从Supabase获取token的逻辑
                token_data = get_token(user_id)
                if not token_data:
                    raise HTTPException(status_code=404, detail="Token not found")

                # 项目中的定时刷新服务会自动处理token刷新
                # 这里只需要检查token状态，不需要手动刷新
                return {
                    "access_token": token_data["access_token"],
                    "expires_at": token_data["access_token_expire"],
                    "status": "valid"
                }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取用户token失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
        
        @self.app.get("/api/token/default")
        async def get_default_token():
            """获取默认用户的token（用于MCP服务）"""
            # 这里可以配置默认用户ID，或者从环境变量读取
            default_user_id = os.getenv("DEFAULT_USER_ID", "default_user")
            return await get_user_token(default_user_id)
        
        @self.app.post("/api/token/refresh/{user_id}")
        async def refresh_user_token(user_id: str):
            """刷新用户token - 委托给项目现有的token刷新服务"""
            try:
                # 项目中已有完整的token刷新机制
                # 这里只是触发一次检查，实际刷新由定时服务处理
                from services.token_refresh_service import refresh_expiring_tokens

                # 触发token刷新检查
                await refresh_expiring_tokens()

                # 重新获取token
                token_data = get_token(user_id)
                if not token_data:
                    raise HTTPException(status_code=404, detail="Token not found")

                return {
                    "access_token": token_data["access_token"],
                    "expires_at": token_data["access_token_expire"],
                    "status": "checked"
                }

            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"刷新用户token失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
    
    def run(self):
        """启动服务"""
        logger.info(f"启动Token桥接服务，端口: {self.port}")
        uvicorn.run(
            self.app,
            host="0.0.0.0",
            port=self.port,
            log_level="info"
        )

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动服务
    service = TokenBridgeService()
    service.run()

if __name__ == "__main__":
    main()
