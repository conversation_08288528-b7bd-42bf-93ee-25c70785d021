#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Token桥接服务 - 将Supabase中的token提供给MCP服务使用
"""

import os
import json
import time
import logging
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

# 导入项目的token存储
from integrations.storage import get_token, is_token_expired
from integrations.feishu import get_feishu_client

logger = logging.getLogger(__name__)

class TokenBridgeService:
    """Token桥接服务"""
    
    def __init__(self, port: int = 3001):
        self.port = port
        self.app = FastAPI(title="Feishu Token Bridge Service")
        self._setup_routes()
        self._setup_cors()
        
    def _setup_cors(self):
        """设置CORS"""
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
    
    def _setup_routes(self):
        """设置路由"""
        
        @self.app.get("/health")
        async def health_check():
            """健康检查"""
            return {"status": "ok", "service": "token-bridge"}
        
        @self.app.get("/api/token/{user_id}")
        async def get_user_token(user_id: str):
            """获取用户token"""
            try:
                # 从Supabase获取token
                token_data = get_token(user_id)
                if not token_data:
                    raise HTTPException(status_code=404, detail="Token not found")
                
                # 检查token是否过期
                if is_token_expired(token_data["access_token_expire"]):
                    # 尝试刷新token
                    refreshed_token = await self._refresh_token(user_id, token_data)
                    if refreshed_token:
                        token_data = refreshed_token
                    else:
                        raise HTTPException(status_code=401, detail="Token expired and refresh failed")
                
                return {
                    "access_token": token_data["access_token"],
                    "expires_at": token_data["access_token_expire"],
                    "status": "valid"
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"获取用户token失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
        
        @self.app.get("/api/token/default")
        async def get_default_token():
            """获取默认用户的token（用于MCP服务）"""
            # 这里可以配置默认用户ID，或者从环境变量读取
            default_user_id = os.getenv("DEFAULT_USER_ID", "default_user")
            return await get_user_token(default_user_id)
        
        @self.app.post("/api/token/refresh/{user_id}")
        async def refresh_user_token(user_id: str):
            """刷新用户token"""
            try:
                token_data = get_token(user_id)
                if not token_data:
                    raise HTTPException(status_code=404, detail="Token not found")
                
                refreshed_token = await self._refresh_token(user_id, token_data)
                if not refreshed_token:
                    raise HTTPException(status_code=401, detail="Token refresh failed")
                
                return {
                    "access_token": refreshed_token["access_token"],
                    "expires_at": refreshed_token["access_token_expire"],
                    "status": "refreshed"
                }
                
            except HTTPException:
                raise
            except Exception as e:
                logger.error(f"刷新用户token失败: {str(e)}")
                raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")
    
    async def _refresh_token(self, user_id: str, token_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """刷新token"""
        try:
            from integrations.storage import save_token
            from datetime import datetime, timezone
            
            # 使用飞书客户端刷新token
            feishu_client = get_feishu_client()
            refreshed = await feishu_client.refresh_token(token_data["refresh_token"])
            
            if "code" in refreshed and refreshed["code"] != 0:
                logger.error(f"Token刷新失败: {refreshed}")
                return None
            
            new_token = refreshed["data"]
            new_token_data = {
                "access_token": new_token["access_token"],
                "refresh_token": new_token["refresh_token"],
                "access_token_expire": int(datetime.now(timezone.utc).timestamp()) + new_token["expires_in"],
            }
            
            # 保存新token到Supabase
            save_token(user_id, new_token_data)
            logger.info(f"用户 {user_id} 的token刷新成功")
            
            return new_token_data
            
        except Exception as e:
            logger.error(f"刷新token失败: {str(e)}")
            return None
    
    def run(self):
        """启动服务"""
        logger.info(f"启动Token桥接服务，端口: {self.port}")
        uvicorn.run(
            self.app,
            host="0.0.0.0",
            port=self.port,
            log_level="info"
        )

def main():
    """主函数"""
    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 启动服务
    service = TokenBridgeService()
    service.run()

if __name__ == "__main__":
    main()
