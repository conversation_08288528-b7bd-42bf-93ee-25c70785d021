"""
认证相关路由
"""

import json
import logging
from datetime import datetime, timezone

from fastapi import APIRouter, HTTPException, Request, Response
from fastapi.responses import JSO<PERSON>esponse

from config import FEISHU_CLIENT_ID, REDIRECT_URI, FEISHU_CLIENT_SECRET
from integrations.feishu import get_feishu_client, FeishuCalendarLark
from integrations.storage import save_token
from api.routers.event import get_calendar_events, ensure_valid_token

from ..models.auth import CallbackResponse, LoginResponse

logger = logging.getLogger(__name__)

router = APIRouter(tags=["认证"])


@router.get("/login")
async def login():
    """飞书登录授权入口，重定向到飞书授权页面"""
    # 构建飞书授权URL
    auth_url = f"https://open.feishu.cn/open-apis/authen/v1/index?app_id={FEISHU_CLIENT_ID}&redirect_uri={REDIRECT_URI}"
    # 重定向到飞书授权页面
    return Response(status_code=302, headers={"Location": auth_url})


@router.get("/auth/callback")
async def callback(request: Request):
    """OAuth回调处理"""
    code = request.query_params.get("code")
    state = request.query_params.get("state")

    if code is None:
        # 构建飞书授权URL
        auth_url = f"https://open.feishu.cn/open-apis/authen/v1/index?app_id={FEISHU_CLIENT_ID}&redirect_uri={REDIRECT_URI}"
        # 重定向到飞书授权页面
        return Response(status_code=302, headers={"Location": auth_url})

    if not code:
        raise HTTPException(status_code=400, detail="Missing code parameter")

    logger.info(f"Received code: {code}")
    # 获取统一的飞书客户端
    feishu_client = get_feishu_client()

    res = await feishu_client.exchange_code(code)
    logger.info(f"Exchange code response: {json.dumps(res, ensure_ascii=False)}")

    if "code" in res and res["code"] != 0:
        error_msg = res.get("msg", "Unknown error")
        raise HTTPException(status_code=400, detail=f"Feishu API error: {error_msg}")

    data = res.get("data")
    if not data:
        raise HTTPException(
            status_code=500, detail=f"Invalid response from Feishu API: {res}"
        )

    access_token = data.get("access_token")
    refresh_token_val = data.get("refresh_token")
    expire = data.get("expires_in")
    expire_ts = int(datetime.now(timezone.utc).timestamp()) + expire

    user_info = await feishu_client.get_user_info(access_token)
    logger.info(f"User info response: {json.dumps(user_info, ensure_ascii=False)}")

    if "code" in user_info and user_info["code"] != 0:
        error_msg = user_info.get("msg", "Unknown error")
        raise HTTPException(
            status_code=400, detail=f"Failed to get user info: {error_msg}"
        )

    user_id = user_info["data"]["open_id"]
    save_token(
        user_id,
        {
            "access_token": access_token,
            "refresh_token": refresh_token_val,
            "access_token_expire": expire_ts,
        },
    )

    return JSONResponse(
        {"msg": "授权成功", "user_id": user_id, "state": state}, status_code=200
    )


@router.get("/auth/callback/calendars")
async def callback_calendars(user_id: str):
    """
    扣子平台专用：获取用户日历列表，路径为 /auth/callback/calendars
    """
    feishu_client = FeishuCalendarLark(FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET)
    result = feishu_client.get_calendars(user_id)
    return JSONResponse(content=result)


@router.get("/auth/callback/calendars/{calendar_id}/events")
async def callback_calendar_events(
    calendar_id: str,
    user_id: str,
    start_time: str = None,
    end_time: str = None,
    page_token: str = None,
    page_size: int = 100,
    query: str = None,
):
    """
    扣子平台专用：获取指定日历的事件列表，路径为 /auth/callback/calendars/{calendar_id}/events
    """
    access_token = await ensure_valid_token(user_id)
    result = await get_calendar_events(
        access_token,
        calendar_id=calendar_id,
        start_time=start_time,
        end_time=end_time,
        page_token=page_token,
        page_size=page_size,
        query=query,
    )
    return result

# ========== MCP风格日历相关路由 ==========
from api.routers.calendar import (
    primary_calendar,
    calendar_detail,
    create_new_calendar,
    update_calendar_info,
    delete_calendar_by_id,
)
from api.routers.event import (
    create_calendar_event,
    update_calendar_event,
    get_event_details,
    delete_calendar_event,
    today_events,
    week_events,
    search_events_get,
    search_events_post,
)
from ..models.calendar import CalendarCreate, CalendarUpdate
from fastapi import Body

# 获取主日历
@router.get("/auth/callback/calendars/primary")
async def mcp_primary_calendar(user_id: str):
    return await primary_calendar(user_id)

# 获取日历详情
@router.get("/auth/callback/calendars/{calendar_id}")
async def mcp_calendar_detail(user_id: str, calendar_id: str):
    return await calendar_detail(user_id, calendar_id)

# 创建日历
@router.post("/auth/callback/calendars")
async def mcp_create_calendar(user_id: str, calendar: CalendarCreate):
    return await create_new_calendar(user_id, calendar)

# 更新日历
@router.patch("/auth/callback/calendars/{calendar_id}")
async def mcp_update_calendar(user_id: str, calendar_id: str, calendar: CalendarUpdate):
    return await update_calendar_info(user_id, calendar_id, calendar)

# 删除日历
@router.delete("/auth/callback/calendars/{calendar_id}")
async def mcp_delete_calendar(user_id: str, calendar_id: str):
    return await delete_calendar_by_id(user_id, calendar_id)

# ========== MCP风格事件相关路由 ==========

# 创建事件
@router.post("/auth/callback/calendars/{calendar_id}/events")
async def mcp_create_event(user_id: str, calendar_id: str, event_data: dict = Body(...)):
    return await create_calendar_event(user_id, calendar_id, event_data)

# 更新事件
@router.patch("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def mcp_update_event(user_id: str, calendar_id: str, event_id: str, request: Request):
    return await update_calendar_event(user_id, calendar_id, event_id, request)

# 获取事件详情
@router.get("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def mcp_get_event(user_id: str, calendar_id: str, event_id: str):
    return await get_event_details(user_id, calendar_id, event_id)

# 删除事件
@router.delete("/auth/callback/calendars/{calendar_id}/events/{event_id}")
async def mcp_delete_event(user_id: str, calendar_id: str, event_id: str):
    return await delete_calendar_event(user_id, calendar_id, event_id)

# 获取今天事件
@router.get("/auth/callback/events/today")
async def mcp_today_events(user_id: str, calendar_id: str = "primary", query: str = None):
    return await today_events(user_id, calendar_id, query)

# 获取本周事件
@router.get("/auth/callback/events/week")
async def mcp_week_events(user_id: str, calendar_id: str = "primary", query: str = None):
    return await week_events(user_id, calendar_id, query)

# 搜索事件（GET）
@router.get("/auth/callback/events/search")
async def mcp_search_events_get(
    user_id: str,
    query: str,
    calendar_id: str = "primary",
    page_token: str = None,
    page_size: int = 50,
):
    return await search_events_get(user_id, query, calendar_id, page_token, page_size)

# 搜索事件（POST）
@router.post("/auth/callback/events/search")
async def mcp_search_events_post(user_id: str, search_data: dict = Body(...)):
    # 兼容直接dict传参
    from ..models.event import SearchQuery
    if isinstance(search_data, dict):
        search_query = SearchQuery(**search_data)
    else:
        search_query = search_data
    return await search_events_post(user_id, search_query)
