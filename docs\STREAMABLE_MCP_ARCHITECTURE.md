# Streamable MCP架构设计文档

## 概述

本文档描述了基于Streamable HTTP模式的MCP（Model Context Protocol）架构设计，该架构支持远程访问和网络通信，适用于分布式部署场景。

## 架构特点

### 1. HTTP/HTTPS协议支持
- 使用标准HTTP/HTTPS协议进行通信
- 支持RESTful API设计
- 兼容现有的Web基础设施

### 2. 实时通信支持
- WebSocket连接用于实时双向通信
- 支持事件推送和状态同步
- 低延迟的实时数据交换

### 3. 分布式部署
- 支持跨网络访问
- 负载均衡友好
- 容器化部署支持

### 4. 标准化接口
- 遵循MCP协议规范
- JSON-RPC 2.0格式
- 完整的API文档

## 系统架构

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   MCP Client    │ ◄──────────────► │  MCP Server     │
│                 │                  │                 │
│ - HTTP Client   │                  │ - FastAPI App   │
│ - WebSocket     │                  │ - MCP Tools     │
│ - JSON-RPC      │                  │ - Calendar API  │
└─────────────────┘                  └─────────────────┘
                                              │
                                              ▼
                                    ┌─────────────────┐
                                    │  飞书日历API     │
                                    │                 │
                                    │ - OAuth认证     │
                                    │ - 日历管理      │
                                    │ - 事件操作      │
                                    └─────────────────┘
```

## 核心组件

### 1. MCP服务器 (`feishu_mcp_server_streamable.py`)

#### 主要功能
- **HTTP服务器**: 基于FastAPI的Web服务器
- **MCP协议处理**: 处理MCP请求和响应
- **工具管理**: 管理可用的MCP工具
- **WebSocket支持**: 实时通信支持

#### 关键端点
```python
# HTTP端点
GET  /              # 服务器信息
GET  /health        # 健康检查
GET  /docs          # API文档
POST /mcp/initialize # MCP初始化
POST /mcp/tools/list # 获取工具列表
POST /mcp/tools/call # 调用工具

# WebSocket端点
WS   /ws            # WebSocket连接
```

#### MCP工具
1. **list_calendars**: 获取用户日历列表
2. **get_calendar_events**: 获取日历事件
3. **create_calendar_event**: 创建日历事件
4. **get_today_events**: 获取今天的事件

### 2. MCP客户端 (`feishu_mcp_client_streamable.py`)

#### 主要功能
- **HTTP客户端**: 基于aiohttp的异步HTTP客户端
- **WebSocket客户端**: 实时通信客户端
- **MCP协议实现**: 客户端MCP协议处理
- **错误处理**: 完善的错误处理机制

#### 核心方法
```python
class StreamableMCPClient:
    async def initialize() -> Dict[str, Any]      # 初始化连接
    async def list_tools() -> Dict[str, Any]      # 获取工具列表
    async def call_tool(name, arguments) -> Dict  # 调用工具
    async def health_check() -> Dict[str, Any]    # 健康检查
```

### 3. 配置管理 (`mcp_streamable_config.json`)

#### 配置项
```json
{
  "mcpServers": {
    "feishu-calendar-streamable": {
      "transport": {
        "type": "http",
        "url": "http://localhost:8000"
      }
    }
  },
  "httpServer": {
    "host": "0.0.0.0",
    "port": 8000
  },
  "websocket": {
    "enabled": true,
    "path": "/ws"
  }
}
```

## 通信协议

### 1. HTTP通信

#### 请求格式
```json
{
  "jsonrpc": "2.0",
  "id": "unique_id",
  "method": "tools/call",
  "params": {
    "name": "list_calendars",
    "arguments": {
      "user_id": "user123"
    }
  }
}
```

#### 响应格式
```json
{
  "jsonrpc": "2.0",
  "id": "unique_id",
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"success\": true, \"calendars\": [...]}"
      }
    ]
  }
}
```

### 2. WebSocket通信

#### 连接建立
```javascript
const ws = new WebSocket('ws://localhost:8000/ws');
```

#### 消息格式
```json
{
  "type": "mcp_request",
  "data": {
    "jsonrpc": "2.0",
    "id": "ws_1",
    "method": "tools/call",
    "params": {...}
  }
}
```

## 部署方案

### 1. 本地开发
```bash
# 启动服务器
python start_streamable_mcp.py

# 测试客户端
python feishu_mcp_client_streamable.py
```

### 2. Docker部署
```dockerfile
FROM python:3.10-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8000

CMD ["python", "feishu_mcp_server_streamable.py"]
```

### 3. 云部署
- **AWS**: 使用ECS或Lambda
- **Azure**: 使用App Service或Functions
- **GCP**: 使用Cloud Run或App Engine

## 安全考虑

### 1. 认证授权
- OAuth 2.0认证
- API密钥验证
- JWT令牌支持

### 2. 网络安全
- HTTPS加密传输
- CORS配置
- 请求频率限制

### 3. 数据安全
- 敏感数据加密
- 访问日志记录
- 错误信息脱敏

## 性能优化

### 1. 连接池管理
- HTTP连接复用
- WebSocket连接管理
- 资源自动释放

### 2. 缓存策略
- 响应缓存
- 工具结果缓存
- 用户会话缓存

### 3. 异步处理
- 异步HTTP请求
- 异步工具调用
- 并发连接支持

## 监控和日志

### 1. 健康检查
```python
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }
```

### 2. 日志记录
- 请求日志
- 错误日志
- 性能指标

### 3. 指标收集
- 请求计数
- 响应时间
- 错误率

## 扩展性设计

### 1. 插件架构
- 工具插件系统
- 认证插件支持
- 存储插件接口

### 2. 微服务支持
- 服务发现
- 负载均衡
- 服务网格

### 3. 多租户支持
- 租户隔离
- 资源配额
- 权限管理

## 使用示例

### 1. 基本使用
```python
async with StreamableMCPClient("http://localhost:8000") as client:
    await client.initialize()
    tools = await client.list_tools()
    result = await client.call_tool("list_calendars", {"user_id": "user123"})
```

### 2. WebSocket使用
```python
async with aiohttp.ClientSession() as session:
    async with session.ws_connect("ws://localhost:8000/ws") as ws:
        await ws.send_str(json.dumps({"type": "test", "message": "Hello"}))
        async for msg in ws:
            print(f"收到: {msg.data}")
```

## 故障排除

### 1. 常见问题
- 连接超时
- 认证失败
- 工具调用错误

### 2. 调试方法
- 启用详细日志
- 使用API文档测试
- 检查网络连接

### 3. 错误处理
- 重试机制
- 降级策略
- 错误恢复

## 总结

Streamable MCP架构提供了：

1. **标准化接口**: 遵循MCP协议规范
2. **网络支持**: HTTP/HTTPS和WebSocket通信
3. **分布式部署**: 支持跨网络访问
4. **实时通信**: WebSocket实时数据交换
5. **扩展性**: 插件化和微服务支持
6. **安全性**: 完善的认证和授权机制
7. **可观测性**: 监控、日志和指标收集

该架构适用于需要远程访问、实时通信和分布式部署的MCP应用场景。 