# Supabase Token集成使用指南

## 概述

本文档介绍如何使用存储在Supabase中的飞书token来替代MCP服务的OAuth流程，实现更稳定的token管理。

## 架构说明

### 传统MCP架构
```
用户 → LLM客户端 → MCP服务 → 飞书API (使用MCP自己的OAuth token)
```

### Supabase Token架构
```
用户 → LLM客户端 → Token桥接服务 → Supabase → 获取token
                ↓
              MCP服务 → 飞书API (使用Supabase中的token)
```

## 组件说明

### 1. Token桥接服务 (`core/mcp/token_bridge_service.py`)

**功能**：
- 从Supabase获取用户token
- 自动刷新过期的token
- 提供HTTP API供MCP服务调用

**API端点**：
- `GET /api/token/{user_id}` - 获取指定用户的token
- `GET /api/token/default` - 获取默认用户的token
- `POST /api/token/refresh/{user_id}` - 刷新指定用户的token
- `GET /health` - 健康检查

### 2. Supabase Token MCP客户端 (`core/mcp/supabase_token_mcp_client.py`)

**功能**：
- 从Token桥接服务获取token
- 调用MCP工具时自动附加正确的token
- 处理token刷新逻辑

### 3. 增强的LLM聊天客户端 (`simple_llm_calendar_chat.py`)

**新增功能**：
- 支持 `--supabase-token` 参数启用Supabase Token模式
- 支持 `--user-id` 参数指定用户ID
- 自动降级到普通MCP客户端（如果Supabase Token不可用）

## 使用方法

### 方法1：使用启动脚本（推荐）

```bash
python start_with_supabase_token.py
```

这个脚本会：
1. 自动启动Token桥接服务
2. 启动使用Supabase Token的LLM聊天客户端
3. 程序退出时自动清理服务

### 方法2：手动启动

#### 步骤1：启动Token桥接服务
```bash
python -m core.mcp.token_bridge_service
```

#### 步骤2：启动MCP服务（普通方式）
```bash
npx -y @larksuiteoapi/lark-mcp mcp -a ******************** -s EVumG3wCHsDBeJRfpbmJkfRhzCns73jC --oauth --token-mode user_access_token -t calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.list,calendar.v4.calendarEvent.create,calendar.v4.calendarEvent.search --mode stdio
```

#### 步骤3：启动LLM聊天客户端
```bash
python simple_llm_calendar_chat.py --supabase-token --user-id your_user_id
```

## 配置说明

### 环境变量

```bash
# 默认用户ID（可选）
DEFAULT_USER_ID=your_default_user_id

# Supabase配置（已在项目中配置）
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key

# 飞书应用配置（已在项目中配置）
FEISHU_CLIENT_ID=your_app_id
FEISHU_CLIENT_SECRET=your_app_secret
```

### 服务端口

- Token桥接服务：`http://localhost:3001`
- MCP服务：`http://localhost:3000`

## 优势

### 1. Token管理优势
- ✅ **自动刷新**：Supabase中的token会定时刷新，无需手动处理
- ✅ **持久化存储**：token存储在Supabase中，重启服务不会丢失
- ✅ **多用户支持**：可以为不同用户管理不同的token

### 2. 稳定性优势
- ✅ **避免OAuth超时**：不依赖MCP服务的OAuth流程
- ✅ **降级机制**：如果Supabase Token不可用，自动降级到普通模式
- ✅ **错误恢复**：Token过期时自动刷新

### 3. 开发优势
- ✅ **统一token管理**：与项目现有的token管理系统集成
- ✅ **调试友好**：详细的日志输出，便于问题排查
- ✅ **配置灵活**：支持命令行参数和环境变量配置

## 故障排除

### 1. Token桥接服务启动失败
```bash
# 检查端口是否被占用
netstat -an | grep 3001

# 检查Supabase配置
python -c "from integrations.storage import get_token; print('Supabase配置正常')"
```

### 2. Token获取失败
```bash
# 检查用户是否存在token
curl http://localhost:3001/api/token/your_user_id

# 检查token是否过期
curl http://localhost:3001/api/token/refresh/your_user_id -X POST
```

### 3. MCP工具调用失败
- 确保MCP服务正常运行
- 检查Token桥接服务日志
- 验证token是否有效

## 注意事项

1. **用户ID配置**：确保使用正确的用户ID，该用户在Supabase中必须有有效的token
2. **网络连接**：Token桥接服务需要能够访问Supabase和飞书API
3. **服务依赖**：Token桥接服务必须在LLM聊天客户端之前启动
4. **资源清理**：使用完毕后记得停止Token桥接服务

## 测试验证

启动服务后，可以通过以下方式验证：

```bash
# 1. 检查Token桥接服务
curl http://localhost:3001/health

# 2. 测试token获取
curl http://localhost:3001/api/token/default

# 3. 在LLM聊天中测试
# 输入：查看我的日历列表
# 应该能够成功获取日历数据
```
