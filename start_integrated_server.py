#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
整合后的飞书插件服务器启动脚本
包含原有API和MCP协议支持
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("integrated_server")


def check_dependencies():
    """检查依赖"""
    logger.info("🔍 检查依赖...")
    
    try:
        import fastapi
        import uvicorn
        import aiohttp
        logger.info("✅ 基础依赖检查通过")
    except ImportError as e:
        logger.error(f"❌ 缺少依赖: {e}")
        logger.info("💡 请运行: pip install fastapi uvicorn aiohttp")
        return False
    
    try:
        from integrations.feishu import FeishuCalendarLark
        from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
        logger.info("✅ 飞书集成依赖检查通过")
        logger.info(f"📋 飞书应用ID: {FEISHU_CLIENT_ID}")
    except ImportError as e:
        logger.error(f"❌ 飞书集成依赖检查失败: {e}")
        return False
    
    return True


def check_config():
    """检查配置"""
    logger.info("⚙️ 检查配置...")
    
    try:
        from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
        
        if not FEISHU_CLIENT_ID or FEISHU_CLIENT_ID == "your_client_id":
            logger.error("❌ 飞书应用ID未配置")
            logger.info("💡 请在config.py中设置正确的FEISHU_CLIENT_ID")
            return False
        
        if not FEISHU_CLIENT_SECRET or FEISHU_CLIENT_SECRET == "your_client_secret":
            logger.error("❌ 飞书应用密钥未配置")
            logger.info("💡 请在config.py中设置正确的FEISHU_CLIENT_SECRET")
            return False
        
        logger.info("✅ 配置检查通过")
        return True
        
    except Exception as e:
        logger.error(f"❌ 配置检查失败: {e}")
        return False


def start_server():
    """启动整合后的服务器"""
    logger.info("🚀 启动整合后的飞书插件服务器...")
    
    try:
        # 检查main.py是否存在
        main_file = "main.py"
        if not os.path.exists(main_file):
            logger.error(f"❌ 主服务文件不存在: {main_file}")
            return False
        
        # 启动服务器
        logger.info("📡 启动整合服务器...")
        logger.info("🌐 服务器地址: http://localhost:5000")
        logger.info("📋 API文档: http://localhost:5000/docs")
        logger.info("🏥 健康检查: http://localhost:5000/health")
        logger.info("🔧 MCP协议: http://localhost:5000/mcp/")
        logger.info("=" * 60)
        logger.info("📋 可用服务:")
        logger.info("  - 原有API: /api/* (聊天、日历、事件、认证)")
        logger.info("  - MCP协议: /mcp/* (日历工具)")
        logger.info("  - 健康检查: /health")
        logger.info("=" * 60)
        
        # 使用subprocess启动服务器
        import subprocess
        cmd = [sys.executable, main_file]
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 等待服务器启动
        logger.info("⏳ 等待服务器启动...")
        import time
        time.sleep(3)
        
        # 检查进程是否还在运行
        if process.poll() is None:
            logger.info("✅ 整合服务器启动成功!")
            logger.info("💡 按 Ctrl+C 停止服务器")
            
            try:
                # 实时显示服务器输出
                for line in process.stdout:
                    print(line.rstrip())
            except KeyboardInterrupt:
                logger.info("⏹️ 正在停止服务器...")
                process.terminate()
                process.wait()
                logger.info("✅ 服务器已停止")
        else:
            logger.error("❌ 服务器启动失败")
            return False
            
    except Exception as e:
        logger.error(f"❌ 启动服务器失败: {e}")
        return False
    
    return True


def main():
    """主函数"""
    logger.info("🎭 整合后的飞书插件服务器启动器")
    logger.info("=" * 50)
    logger.info("📋 功能说明:")
    logger.info("  - 原有API服务 (端口5000)")
    logger.info("  - MCP协议支持 (集成到主服务)")
    logger.info("  - 统一的飞书集成")
    logger.info("=" * 50)
    
    # 1. 检查依赖
    if not check_dependencies():
        return False
    
    # 2. 检查配置
    if not check_config():
        return False
    
    # 3. 启动服务器
    if not start_server():
        return False
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if not success:
            sys.exit(1)
    except KeyboardInterrupt:
        logger.info("⏹️ 启动被用户中断")
    except Exception as e:
        logger.error(f"❌ 启动异常: {e}")
        sys.exit(1) 