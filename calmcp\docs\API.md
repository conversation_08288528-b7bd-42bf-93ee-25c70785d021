# CalMCP API 文档

## 概述

CalMCP 提供基于 REST API 的 MCP (Model Context Protocol) 服务，支持标准模式和流式模式的工具调用。

## 基础信息

- **Base URL**: `http://localhost:3001`
- **Content-Type**: `application/json`
- **协议版本**: MCP 2024-11-05

## 端点列表

### 1. 健康检查

**GET** `/api/health`

获取服务健康状态。

**响应示例**:
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "version": "1.0.0",
  "service": "calmcp",
  "tools": {
    "count": 7,
    "available": ["calendar_list", "calendar_event_create", ...]
  },
  "environment": {
    "nodeEnv": "development",
    "hasFeishuConfig": true
  }
}
```

### 2. 获取工具列表

**GET** `/api/mcp/tools`

获取所有可用的 MCP 工具。

**响应示例**:
```json
{
  "success": true,
  "data": {
    "tools": [
      {
        "name": "calendar_list",
        "description": "获取用户的日历列表",
        "inputSchema": {
          "type": "object",
          "properties": {
            "page_size": {
              "type": "number",
              "description": "每页返回的日历数量，默认10"
            }
          }
        }
      }
    ],
    "count": 7
  }
}
```

### 3. 工具调用（标准模式）

**POST** `/api/mcp/tools/call`

调用指定的 MCP 工具。

**请求体**:
```json
{
  "name": "calendar_list",
  "arguments": {
    "page_size": 10,
    "page_token": "optional_token"
  }
}
```

**响应示例**:
```json
{
  "success": true,
  "result": {
    "content": [
      {
        "type": "text",
        "text": "{\"code\": 0, \"msg\": \"success\", \"data\": {...}}"
      }
    ],
    "isError": false
  }
}
```

### 4. 工具调用（流式模式）

**POST** `/api/mcp/tools/stream`

以流式方式调用 MCP 工具，适合大数据量处理。

**请求体**:
```json
{
  "name": "calendar_event_search",
  "arguments": {
    "calendar_id": "cal_xxx",
    "query": "会议"
  }
}
```

**响应格式**: `text/event-stream`

**流式响应示例**:
```
data: {"id":"stream_123","type":"start","timestamp":1640995200000}

data: {"id":"stream_123","type":"data","data":{"event_id":"evt_1"},"timestamp":1640995201000}

data: {"id":"stream_123","type":"data","data":{"event_id":"evt_2"},"timestamp":1640995202000}

data: {"id":"stream_123","type":"end","timestamp":1640995203000}
```

## 工具详细说明

### calendar_list

获取用户的日历列表。

**参数**:
- `page_size` (number, optional): 每页返回数量，默认 10
- `page_token` (string, optional): 分页标记

**示例**:
```bash
curl -X POST http://localhost:3001/api/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calendar_list",
    "arguments": {
      "page_size": 5
    }
  }'
```

### calendar_event_create

创建新的日历事件。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `summary` (string, required): 事件标题
- `start_time` (object, required): 开始时间
- `end_time` (object, required): 结束时间
- `description` (string, optional): 事件描述
- `location` (object, optional): 地点信息
- `attendees` (array, optional): 参与者列表

**示例**:
```bash
curl -X POST http://localhost:3001/api/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calendar_event_create",
    "arguments": {
      "calendar_id": "cal_xxx",
      "summary": "团队会议",
      "start_time": {
        "timestamp": "1640995200",
        "timezone": "Asia/Shanghai"
      },
      "end_time": {
        "timestamp": "1640998800",
        "timezone": "Asia/Shanghai"
      },
      "description": "讨论项目进展",
      "location": {
        "name": "会议室A"
      }
    }
  }'
```

### calendar_event_search

搜索日历事件。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `query` (string, optional): 搜索关键词
- `start_time` (object, optional): 搜索开始时间
- `end_time` (object, optional): 搜索结束时间
- `page_size` (number, optional): 每页返回数量
- `page_token` (string, optional): 分页标记

### calendar_event_update

更新日历事件。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `event_id` (string, required): 事件 ID
- 其他参数同 `calendar_event_create`（可选）

### calendar_event_delete

删除日历事件。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `event_id` (string, required): 事件 ID

### calendar_event_get

获取单个日历事件详情。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `event_id` (string, required): 事件 ID

### calendar_event_list

获取日历事件列表。

**参数**:
- `calendar_id` (string, required): 日历 ID
- `start_time` (string, optional): 开始时间戳
- `end_time` (string, optional): 结束时间戳
- `page_size` (number, optional): 每页返回数量
- `page_token` (string, optional): 分页标记

## 错误处理

### 错误响应格式

```json
{
  "success": false,
  "error": "错误描述"
}
```

### 常见错误码

- `400` - 请求参数错误
- `401` - 认证失败
- `403` - 权限不足
- `404` - 资源不存在
- `500` - 服务器内部错误

### 飞书 API 错误

当飞书 API 返回错误时，响应中的 `result.isError` 为 `true`，错误信息包含在 `result.content[0].text` 中。

## 流式响应

### 流式数据格式

每个流式消息都是 JSON 格式，包含以下字段：

- `id`: 流 ID
- `type`: 消息类型（start/data/error/end）
- `data`: 数据内容（仅 data 类型）
- `error`: 错误信息（仅 error 类型）
- `timestamp`: 时间戳

### 分页流

对于支持分页的工具（如 `calendar_list`），流式响应会自动处理分页，逐个返回数据项。

### 进度信息

批量处理时，流中会包含进度信息：

```json
{
  "id": "stream_123",
  "type": "data",
  "data": {
    "progress": {
      "current": 50,
      "total": 100,
      "percentage": 50
    }
  },
  "timestamp": 1640995200000
}
```

## 客户端示例

### JavaScript/Node.js

```javascript
// 标准调用
const response = await fetch('http://localhost:3001/api/mcp/tools/call', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'calendar_list',
    arguments: { page_size: 10 }
  })
});

const result = await response.json();

// 流式调用
const streamResponse = await fetch('http://localhost:3001/api/mcp/tools/stream', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'calendar_event_search',
    arguments: { calendar_id: 'cal_xxx', query: '会议' }
  })
});

const reader = streamResponse.body.getReader();
const decoder = new TextDecoder();

while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = decoder.decode(value);
  const lines = chunk.split('\n');
  
  for (const line of lines) {
    if (line.startsWith('data: ')) {
      const data = JSON.parse(line.slice(6));
      console.log('Stream data:', data);
    }
  }
}
```

### Python

```python
import requests
import json

# 标准调用
response = requests.post(
    'http://localhost:3001/api/mcp/tools/call',
    json={
        'name': 'calendar_list',
        'arguments': {'page_size': 10}
    }
)

result = response.json()

# 流式调用
import sseclient

response = requests.post(
    'http://localhost:3001/api/mcp/tools/stream',
    json={
        'name': 'calendar_event_search',
        'arguments': {'calendar_id': 'cal_xxx', 'query': '会议'}
    },
    stream=True
)

client = sseclient.SSEClient(response)
for event in client.events():
    data = json.loads(event.data)
    print('Stream data:', data)
```
