{"name": "calmcp", "version": "1.0.0", "description": "Feishu Calendar MCP Service with Next.js and Streamable Mode", "main": "index.js", "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start -p 3000", "lint": "next lint", "mcp:dev": "tsx src/mcp-server/index.ts", "mcp:build": "tsc", "mcp:start": "node dist/mcp-server/index.js", "test": "node scripts/test-mcp.js", "test:sdk": "node scripts/test-sdk.js", "docker:build": "docker build -t calmcp .", "docker:run": "docker run -p 3000:3000 -p 3002:3002 calmcp", "docker:compose": "docker-compose up -d", "logs": "mkdir -p logs"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@modelcontextprotocol/sdk": "^1.0.0", "@larksuiteoapi/node-sdk": "^1.27.0", "express": "^4.18.0", "cors": "^2.8.5", "dotenv": "^16.0.0", "winston": "^3.10.0", "zod": "^3.22.0", "axios": "^1.6.0", "ws": "^8.14.0", "server-sent-events": "^1.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/express": "^4.17.0", "@types/cors": "^2.8.0", "@types/ws": "^8.5.0", "typescript": "^5.0.0", "tsx": "^4.0.0", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0"}, "keywords": ["mcp", "feishu", "calendar", "nextjs", "streamable"], "author": "IF", "license": "MIT"}