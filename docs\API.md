# 📖 API 文档

## 🌐 API 概览

智能日历助手提供RESTful API接口，支持自然语言交互的日历管理功能。

### 基础信息
- **Base URL**: `http://localhost:8000`
- **API Version**: v1
- **Content-Type**: `application/json`
- **Authentication**: 暂不需要（开发阶段）

## 🔗 API 端点

### 1. 聊天接口

#### POST /chat/
处理用户的自然语言输入，返回智能响应。

**请求参数**:
```json
{
  "message": "string",           // 必需：用户消息
  "user_id": "string",          // 可选：用户ID，默认为"default_user"
  "context": {                  // 可选：上下文信息
    "key": "value"
  }
}
```

**响应格式**:
```json
{
  "message": "string",          // 助手回复消息
  "intent": "string",           // 识别的意图类型
  "confidence": 0.95,           // 置信度 (0.0-1.0)
  "success": true,              // 处理是否成功
  "data": {                     // 额外数据
    "action": "create",
    "entities": {...},
    "operation": {...}
  },
  "context": {                  // 会话上下文
    "state": "waiting_confirmation",
    "conversation_summary": "..."
  }
}
```

**示例请求**:
```bash
curl -X POST "http://localhost:8000/chat/" \
  -H "Content-Type: application/json" \
  -d '{
    "message": "明天下午3点安排一个产品评审会议",
    "user_id": "user123"
  }'
```

**示例响应**:
```json
{
  "message": "请确认创建以下日历事件：\n📝 标题：产品评审会议\n📅 时间：2025-07-14 15:00\n请回复 '确认' 继续，'取消' 放弃，或提供修改建议。",
  "intent": "calendar",
  "confidence": 0.95,
  "success": true,
  "data": {
    "action": "create",
    "operation": {
      "title": "产品评审会议",
      "start_time": "2025-07-14 15:00",
      "action": "create"
    }
  },
  "context": {
    "state": "waiting_confirmation",
    "conversation_summary": "用户: 明天下午3点安排一个产品评审会议"
  }
}
```

### 2. 健康检查

#### GET /health/
检查服务健康状态。

**响应格式**:
```json
{
  "status": "healthy",
  "timestamp": "2025-01-13T10:30:00Z",
  "version": "2.0.0",
  "components": {
    "llm_client": "healthy",
    "time_parser": "healthy",
    "conversation_manager": "healthy"
  }
}
```

### 3. 系统信息

#### GET /info/
获取系统信息和配置。

**响应格式**:
```json
{
  "name": "Intelligent Calendar Assistant",
  "version": "2.0.0",
  "description": "基于大语言模型的智能日历管理系统",
  "features": [
    "intent_recognition",
    "human_collaboration",
    "multi_turn_conversation",
    "time_parsing"
  ],
  "supported_intents": [
    "calendar",
    "chat",
    "journal",
    "media"
  ],
  "supported_actions": [
    "create",
    "query",
    "update",
    "delete"
  ]
}
```

## 🎯 意图类型说明

### Calendar 意图
处理日历相关操作。

**支持的操作**:
- `create`: 创建日历事件
- `query`: 查询日程安排
- `update`: 修改日历事件
- `delete`: 删除日历事件

**示例输入**:
```
"明天下午3点安排会议"          → create
"查看下周的日程安排"           → query
"修改明天的会议时间"           → update
"取消今天的约会"              → delete
```

### Chat 意图
处理日常聊天对话。

**示例输入**:
```
"你好"
"今天天气怎么样？"
"你能做什么？"
```

### Journal 意图
处理日记记录相关操作。

**示例输入**:
```
"记录今天的工作总结"
"写一篇关于项目进展的日记"
```

### Media 意图
处理媒体创作相关操作。

**示例输入**:
```
"帮我写一首诗"
"创作一个故事"
```

## 🔄 会话状态管理

### 状态类型
- `normal`: 正常对话状态
- `waiting_confirmation`: 等待用户确认
- `waiting_supplement`: 等待用户补充信息

### 状态转换流程
```
normal → waiting_confirmation → normal
normal → waiting_supplement → waiting_confirmation → normal
```

### 人机协作流程

#### 1. 确认流程
```json
// 第一步：系统要求确认
{
  "message": "请确认创建以下日历事件：...",
  "context": {"state": "waiting_confirmation"}
}

// 第二步：用户确认
{
  "message": "确认"
}

// 第三步：系统执行操作
{
  "message": "✅ 已成功创建日历事件：产品评审会议",
  "context": {"state": "normal"}
}
```

#### 2. 修改流程
```json
// 第一步：系统要求确认
{
  "message": "请确认创建以下日历事件：...",
  "context": {"state": "waiting_confirmation"}
}

// 第二步：用户提出修改
{
  "message": "改成下午4点"
}

// 第三步：系统更新并重新确认
{
  "message": "已更新时间，请确认：...",
  "context": {"state": "waiting_confirmation"}
}
```

## 🕐 时间解析能力

### 支持的时间表达

#### 相对时间
```
"今天"     → 2025-01-13
"明天"     → 2025-01-14
"后天"     → 2025-01-15
"下周"     → 2025-01-20
"下个月"   → 2025-02-13
```

#### 具体时间
```
"下午3点"        → 15:00
"上午9点半"      → 09:30
"晚上8点"        → 20:00
"中午12点"       → 12:00
```

#### 重复模式
```
"每天"          → daily
"每周"          → weekly
"每月"          → monthly
"工作日"        → weekdays
"周末"          → weekends
```

#### 时间范围
```
"8点到10点"     → 08:00-10:00
"下午2点到4点"  → 14:00-16:00
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "message": "错误描述",
  "intent": "error",
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "details": "详细错误信息"
  }
}
```

### 常见错误码
- `INVALID_INPUT`: 输入参数无效
- `INTENT_RECOGNITION_FAILED`: 意图识别失败
- `TIME_PARSING_ERROR`: 时间解析错误
- `LLM_SERVICE_ERROR`: LLM服务错误
- `INTERNAL_SERVER_ERROR`: 内部服务器错误

## 📊 性能指标

### 响应时间
- **意图识别**: < 500ms
- **实体提取**: < 300ms
- **时间解析**: < 100ms
- **总响应时间**: < 2s

### 准确率
- **意图识别准确率**: 95%+
- **时间解析准确率**: 90%+
- **实体提取准确率**: 90%+

## 🔧 开发工具

### API 测试
```bash
# 使用 curl 测试
curl -X POST "http://localhost:8000/chat/" \
  -H "Content-Type: application/json" \
  -d '{"message": "明天开会", "user_id": "test"}'

# 使用 httpie 测试
http POST localhost:8000/chat/ message="明天开会" user_id="test"
```

### Python 客户端示例
```python
import requests

class CalendarAssistantClient:
    def __init__(self, base_url="http://localhost:8000"):
        self.base_url = base_url
    
    def chat(self, message: str, user_id: str = "default"):
        response = requests.post(
            f"{self.base_url}/chat/",
            json={"message": message, "user_id": user_id}
        )
        return response.json()
    
    def health_check(self):
        response = requests.get(f"{self.base_url}/health/")
        return response.json()

# 使用示例
client = CalendarAssistantClient()
result = client.chat("明天下午3点安排会议")
print(result)
```

### JavaScript 客户端示例
```javascript
class CalendarAssistantClient {
    constructor(baseUrl = 'http://localhost:8000') {
        this.baseUrl = baseUrl;
    }
    
    async chat(message, userId = 'default') {
        const response = await fetch(`${this.baseUrl}/chat/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                message: message,
                user_id: userId
            })
        });
        return await response.json();
    }
    
    async healthCheck() {
        const response = await fetch(`${this.baseUrl}/health/`);
        return await response.json();
    }
}

// 使用示例
const client = new CalendarAssistantClient();
client.chat('明天下午3点安排会议').then(result => {
    console.log(result);
});
```

## 📚 相关文档

- [产品需求文档 (PRD)](./PRD.md)
- [技术架构文档](./TECH_ARCHITECTURE.md)
- [开发指南](./DEVELOPMENT.md)
- [部署指南](./DEPLOYMENT.md)

---

## 🔄 更新日志

### v2.0.0 (2025-01-13)
- 新增人机协作确认机制
- 增强时间解析能力
- 完善多轮对话支持
- 优化意图识别精度

### v1.0.0 (历史版本)
- 基础聊天接口
- 简单意图识别
- 基础时间解析
