"""
意图识别单元测试
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest

# 临时注释掉不存在的导入，等待实际模块实现
# from core.ai.intent_classifier import IntentClassifier, IntentType
# from models.chat import ChatMessage, IntentResult


# 创建临时的mock类用于测试
class IntentType:
    CALENDAR_CREATE = "calendar_create"
    CALENDAR_QUERY = "calendar_query"
    CALENDAR_UPDATE = "calendar_update"
    CALENDAR_DELETE = "calendar_delete"
    CALENDAR_CREATE_SUPPLEMENT = "calendar_create_supplement"
    CHAT_GREETING = "chat_greeting"
    CHAT_THANKS = "chat_thanks"
    CHAT_HELP = "chat_help"
    CHAT_GENERAL = "chat_general"
    CLARIFICATION_NEEDED = "clarification_needed"
    MULTI_INTENT = "multi_intent"
    CONFIRMATION_YES = "confirmation_yes"
    CONFIRMATION_NO = "confirmation_no"
    UNKNOWN = "unknown"


class ChatMessage:
    def __init__(self, content, user_id):
        self.content = content
        self.user_id = user_id


class IntentResult:
    def __init__(self, intent, confidence=0.9, explanation="", sub_intents=None):
        self.intent = intent
        self.confidence = confidence
        self.explanation = explanation
        self.sub_intents = sub_intents or []


class IntentClassifier:
    async def classify_intent(self, message, context=None):
        # 简单的mock实现
        content = message.content.lower()

        if "会议" in content or "安排" in content:
            return IntentResult(IntentType.CALENDAR_CREATE, 0.9)
        elif "查看" in content or "安排" in content:
            return IntentResult(IntentType.CALENDAR_QUERY, 0.9)
        elif "你好" in content:
            return IntentResult(IntentType.CHAT_GREETING, 0.95)
        else:
            return IntentResult(IntentType.UNKNOWN, 0.3)


class TestIntentClassifier:
    """意图识别器测试"""

    def setup_method(self):
        """测试前准备"""
        self.classifier = IntentClassifier()

    @pytest.mark.asyncio
    async def test_calendar_intent_recognition(self):
        """测试日历相关意图识别"""
        test_cases = [
            # 创建事件意图
            {
                "message": "明天下午3点安排一个会议",
                "expected_intent": IntentType.CALENDAR_CREATE,
                "expected_confidence": 0.9,
            },
            {
                "message": "帮我预定下周一上午10点的会议室",
                "expected_intent": IntentType.CALENDAR_CREATE,
                "expected_confidence": 0.85,
            },
            # 查询事件意图
            {
                "message": "今天有什么安排？",
                "expected_intent": IntentType.CALENDAR_QUERY,
                "expected_confidence": 0.9,
            },
            {
                "message": "查看本周的日程",
                "expected_intent": IntentType.CALENDAR_QUERY,
                "expected_confidence": 0.88,
            },
            # 修改事件意图
            {
                "message": "把明天的会议改到后天",
                "expected_intent": IntentType.CALENDAR_UPDATE,
                "expected_confidence": 0.85,
            },
            # 删除事件意图
            {
                "message": "取消今天下午的会议",
                "expected_intent": IntentType.CALENDAR_DELETE,
                "expected_confidence": 0.87,
            },
        ]

        for case in test_cases:
            message = ChatMessage(content=case["message"], user_id="test_user")
            result = await self.classifier.classify_intent(message)

            assert result.intent == case["expected_intent"]
            assert result.confidence >= case["expected_confidence"]

    @pytest.mark.asyncio
    async def test_chat_intent_recognition(self):
        """测试聊天意图识别"""
        test_cases = [
            {
                "message": "你好",
                "expected_intent": IntentType.CHAT_GREETING,
                "expected_confidence": 0.95,
            },
            {
                "message": "谢谢你的帮助",
                "expected_intent": IntentType.CHAT_THANKS,
                "expected_confidence": 0.9,
            },
            {
                "message": "你能做什么？",
                "expected_intent": IntentType.CHAT_HELP,
                "expected_confidence": 0.85,
            },
            {
                "message": "今天天气怎么样？",
                "expected_intent": IntentType.CHAT_GENERAL,
                "expected_confidence": 0.8,
            },
        ]

        for case in test_cases:
            message = ChatMessage(content=case["message"], user_id="test_user")
            result = await self.classifier.classify_intent(message)

            assert result.intent == case["expected_intent"]
            assert result.confidence >= case["expected_confidence"]

    @pytest.mark.asyncio
    async def test_context_aware_classification(self):
        """测试上下文感知的意图识别"""
        # 模拟对话历史
        conversation_history = [
            ChatMessage(content="明天下午3点安排一个会议", user_id="test_user"),
            ChatMessage(content="好的，我来帮您安排", user_id="assistant"),
            ChatMessage(content="会议主题是什么？", user_id="assistant"),
        ]

        # 用户回复会议主题
        current_message = ChatMessage(content="项目进度讨论", user_id="test_user")

        result = await self.classifier.classify_intent(
            current_message, context=conversation_history
        )

        # 应该识别为日历创建的补充信息，而不是普通聊天
        assert result.intent == IntentType.CALENDAR_CREATE_SUPPLEMENT
        assert result.confidence >= 0.8

    @pytest.mark.asyncio
    async def test_ambiguous_intent_handling(self):
        """测试模糊意图处理"""
        # 模糊的消息
        ambiguous_message = ChatMessage(content="明天", user_id="test_user")

        result = await self.classifier.classify_intent(ambiguous_message)

        # 应该识别为需要澄清
        assert result.intent == IntentType.CLARIFICATION_NEEDED
        assert result.confidence < 0.7
        assert "需要更多信息" in result.explanation

    @pytest.mark.asyncio
    async def test_multi_intent_detection(self):
        """测试多意图检测"""
        # 包含多个意图的消息
        multi_intent_message = ChatMessage(
            content="帮我查看今天的安排，然后安排明天下午的会议", user_id="test_user"
        )

        result = await self.classifier.classify_intent(multi_intent_message)

        # 应该检测到多个意图
        assert result.intent == IntentType.MULTI_INTENT
        assert len(result.sub_intents) == 2
        assert IntentType.CALENDAR_QUERY in [si.intent for si in result.sub_intents]
        assert IntentType.CALENDAR_CREATE in [si.intent for si in result.sub_intents]

    @pytest.mark.asyncio
    async def test_confirmation_intent(self):
        """测试确认意图识别"""
        confirmation_cases = [
            {"message": "是的", "expected": True},
            {"message": "好的", "expected": True},
            {"message": "确认", "expected": True},
            {"message": "不是", "expected": False},
            {"message": "取消", "expected": False},
            {"message": "不要", "expected": False},
        ]

        for case in confirmation_cases:
            message = ChatMessage(content=case["message"], user_id="test_user")
            result = await self.classifier.classify_intent(message)

            if case["expected"]:
                assert result.intent == IntentType.CONFIRMATION_YES
            else:
                assert result.intent == IntentType.CONFIRMATION_NO

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """测试错误处理"""
        # 空消息
        empty_message = ChatMessage(content="", user_id="test_user")
        result = await self.classifier.classify_intent(empty_message)
        assert result.intent == IntentType.UNKNOWN

        # 非常长的消息
        long_message = ChatMessage(content="a" * 10000, user_id="test_user")
        result = await self.classifier.classify_intent(long_message)
        assert result is not None  # 应该能处理而不崩溃

    @pytest.mark.asyncio
    async def test_intent_confidence_thresholds(self):
        """测试意图置信度阈值"""
        # 低置信度的消息
        low_confidence_message = ChatMessage(content="嗯嗯", user_id="test_user")

        result = await self.classifier.classify_intent(low_confidence_message)

        # 低置信度应该返回UNKNOWN或CLARIFICATION_NEEDED
        assert result.intent in [IntentType.UNKNOWN, IntentType.CLARIFICATION_NEEDED]
        assert result.confidence < 0.5
