#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书MCP服务器测试
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from core.mcp.feishu_mcp_server import FeishuMCPServer
from models.calendar import Calendar, CalendarEvent, TimeInfo, Location, Attendee


class TestFeishuMCPServer:
    """飞书MCP服务器测试类"""
    
    @pytest.fixture
    def server(self):
        """创建测试服务器实例"""
        return FeishuMCPServer()
    
    @pytest.fixture
    def mock_calendar_service(self):
        """模拟日历服务"""
        mock_service = AsyncMock()
        return mock_service
    
    def test_server_initialization(self, server):
        """测试服务器初始化"""
        assert server.server is not None
        assert server.calendar_client is not None
        assert server.server.name == "feishu-calendar"
    
    @pytest.mark.asyncio
    async def test_handle_list_calendars_success(self, server):
        """测试获取日历列表成功"""
        # 模拟日历数据
        mock_calendars = [
            Calendar(
                id="cal_123",
                name="工作日历",
                description="工作相关日程",
                color="#1976d2",
                permissions="private"
            ),
            Calendar(
                id="cal_456",
                name="个人日历",
                description="个人事务",
                color="#4caf50",
                permissions="private"
            )
        ]
        
        # 模拟服务调用
        server.calendar_client.get_calendars = MagicMock(return_value={
            "code": 0,
            "data": {
                "calendar_list": [
                    {
                        "calendar_id": "cal_123",
                        "summary": "工作日历",
                        "description": "工作相关日程",
                        "color": "#1976d2",
                        "permissions": "private"
                    },
                    {
                        "calendar_id": "cal_456",
                        "summary": "个人日历",
                        "description": "个人事务",
                        "color": "#4caf50",
                        "permissions": "private"
                    }
                ]
            }
        })
        
        # 调用处理方法
        result = await server._handle_list_calendars({"user_id": "test_user"})
        
        # 验证结果
        assert len(result) == 1
        assert result[0].type == "text"
        
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert len(response_data["calendars"]) == 2
        assert response_data["calendars"][0]["id"] == "cal_123"
        assert response_data["calendars"][0]["name"] == "工作日历"
    
    @pytest.mark.asyncio
    async def test_handle_list_calendars_error(self, server):
        """测试获取日历列表失败"""
        # 模拟服务异常
        server.calendar_client.get_calendars = MagicMock(
            side_effect=Exception("API调用失败")
        )
        
        # 调用处理方法
        result = await server._handle_list_calendars({"user_id": "test_user"})
        
        # 验证错误响应
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is False
        assert "API调用失败" in response_data["error"]
    
    @pytest.mark.asyncio
    async def test_handle_create_calendar_event_success(self, server):
        """测试创建日历事件成功"""
        # 模拟创建成功响应
        mock_response = {"success": True, "message": "事件创建成功", "event_id": "event_123"}
        server.calendar_service.create_calendar_event = AsyncMock(return_value=mock_response)
        
        # 准备测试参数
        arguments = {
            "user_id": "test_user",
            "title": "测试会议",
            "description": "这是一个测试会议",
            "start_time": "2025-07-16T14:00:00+08:00",
            "end_time": "2025-07-16T15:00:00+08:00",
            "location": "会议室A",
            "attendees": ["<EMAIL>", "<EMAIL>"]
        }
        
        # 调用处理方法
        result = await server._handle_create_calendar_event(arguments)
        
        # 验证结果
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert response_data["message"] == "事件创建成功"
        
        # 验证服务调用参数
        server.calendar_service.create_calendar_event.assert_called_once()
        call_args = server.calendar_service.create_calendar_event.call_args
        assert call_args[0][0] == "test_user"  # user_id
        
        # 验证事件对象
        event = call_args[0][1]
        assert event.summary == "测试会议"
        assert event.description == "这是一个测试会议"
        assert event.location.name == "会议室A"
        assert len(event.attendees) == 2
        assert event.attendees[0].email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_handle_get_calendar_events_success(self, server):
        """测试获取日历事件成功"""
        # 模拟事件数据
        mock_events = [
            CalendarEvent(
                id="event_123",
                summary="测试事件",
                description="测试描述",
                start_time=TimeInfo(date_time="2025-07-16T14:00:00+08:00"),
                end_time=TimeInfo(date_time="2025-07-16T15:00:00+08:00"),
                location=Location(name="会议室A"),
                attendees=[Attendee(email="<EMAIL>")]
            )
        ]
        
        server.calendar_service.get_calendar_events = AsyncMock(return_value=mock_events)
        
        # 调用处理方法
        result = await server._handle_get_calendar_events({
            "user_id": "test_user",
            "calendar_id": "primary"
        })
        
        # 验证结果
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert len(response_data["events"]) == 1
        
        event = response_data["events"][0]
        assert event["id"] == "event_123"
        assert event["title"] == "测试事件"
        assert event["location"] == "会议室A"
        assert event["attendees"] == ["<EMAIL>"]
    
    @pytest.mark.asyncio
    async def test_handle_update_calendar_event_success(self, server):
        """测试更新日历事件成功"""
        mock_response = {"success": True, "message": "事件更新成功"}
        server.calendar_service.update_calendar_event = AsyncMock(return_value=mock_response)
        
        arguments = {
            "user_id": "test_user",
            "event_id": "event_123",
            "title": "更新后的标题",
            "start_time": "2025-07-16T15:00:00+08:00",
            "end_time": "2025-07-16T16:00:00+08:00"
        }
        
        result = await server._handle_update_calendar_event(arguments)
        
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert response_data["message"] == "事件更新成功"
    
    @pytest.mark.asyncio
    async def test_handle_delete_calendar_event_success(self, server):
        """测试删除日历事件成功"""
        mock_response = {"success": True, "message": "事件删除成功"}
        server.calendar_service.delete_calendar_event = AsyncMock(return_value=mock_response)
        
        arguments = {
            "user_id": "test_user",
            "event_id": "event_123"
        }
        
        result = await server._handle_delete_calendar_event(arguments)
        
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert response_data["message"] == "事件删除成功"
    
    @pytest.mark.asyncio
    @patch('core.mcp.feishu_mcp_server.get_today_events')
    async def test_handle_get_today_events_success(self, mock_get_today_events, server):
        """测试获取今天事件成功"""
        mock_response = {
            "success": True,
            "events": [
                {
                    "id": "event_today_1",
                    "title": "晨会",
                    "start_time": "2025-07-16T09:00:00+08:00",
                    "end_time": "2025-07-16T09:30:00+08:00"
                }
            ]
        }
        mock_get_today_events.return_value = mock_response
        
        arguments = {"user_id": "test_user"}
        result = await server._handle_get_today_events(arguments)
        
        assert len(result) == 1
        response_data = json.loads(result[0].text)
        assert response_data["success"] is True
        assert len(response_data["events"]) == 1
        assert response_data["events"][0]["title"] == "晨会"
    
    def test_time_format_parsing(self, server):
        """测试时间格式解析"""
        # 测试不同的时间格式
        time_formats = [
            "2025-07-16T14:00:00+08:00",
            "2025-07-16T14:00:00Z",
            "2025-07-16T14:00:00"
        ]
        
        for time_str in time_formats:
            try:
                # 模拟时间解析逻辑
                parsed_time = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
                assert parsed_time is not None
            except ValueError:
                pytest.fail(f"时间格式解析失败: {time_str}")


class TestMCPServerIntegration:
    """MCP服务器集成测试"""
    
    @pytest.mark.asyncio
    async def test_server_tools_registration(self):
        """测试工具注册"""
        server = FeishuMCPServer()
        
        # 获取注册的工具列表
        tools = await server.server._list_tools_handler()
        
        # 验证工具数量和名称
        expected_tools = [
            "list_calendars",
            "get_calendar_events", 
            "create_calendar_event",
            "update_calendar_event",
            "delete_calendar_event",
            "get_today_events"
        ]
        
        tool_names = [tool.name for tool in tools]
        for expected_tool in expected_tools:
            assert expected_tool in tool_names
    
    @pytest.mark.asyncio
    async def test_tool_schema_validation(self):
        """测试工具模式验证"""
        server = FeishuMCPServer()
        tools = await server.server._list_tools_handler()
        
        # 验证每个工具都有正确的模式定义
        for tool in tools:
            assert tool.name is not None
            assert tool.description is not None
            assert tool.inputSchema is not None
            assert tool.inputSchema.type == "object"
            assert "properties" in tool.inputSchema.model_dump()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
