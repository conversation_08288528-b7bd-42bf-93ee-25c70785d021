# 🚀 部署指南

## 📋 部署概览

智能日历助手支持多种部署方式，从本地开发到生产环境部署。本指南将详细介绍各种部署选项。

## 🏠 本地部署

### 开发环境部署

1. **环境准备**
   ```bash
   # 克隆项目
   git clone https://github.com/ifcheung2012/feishu-coze-plugin.git
   cd feishu-coze-plugin
   
   # 创建虚拟环境
   python -m venv venv
   source venv/bin/activate  # Linux/macOS
   # 或 venv\Scripts\activate  # Windows
   
   # 安装依赖
   pip install -r requirements.txt
   ```

2. **配置环境变量**
   ```bash
   # 复制环境变量模板
   cp .env.example .env
   
   # 编辑 .env 文件
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_MODEL=gpt-4o-mini
   DEBUG=true
   LOG_LEVEL=DEBUG
   ```

3. **启动服务**
   ```bash
   # 命令行模式
   python cli.py -i
   
   # API服务模式（开发）
   python main.py --debug
   
   # 或使用 uvicorn
   uvicorn main:app --reload --host 0.0.0.0 --port 8000
   ```

### 生产环境配置

1. **环境变量配置**
   ```bash
   # 生产环境 .env
   OPENAI_API_KEY=your_production_api_key
   OPENAI_MODEL=gpt-4o-mini
   DEBUG=false
   LOG_LEVEL=INFO
   
   # 飞书配置
   FEISHU_APP_ID=your_feishu_app_id
   FEISHU_APP_SECRET=your_feishu_app_secret
   
   # 数据库配置（如果需要）
   DATABASE_URL=postgresql://user:password@localhost/dbname
   
   # Redis配置（如果需要）
   REDIS_URL=redis://localhost:6379/0
   ```

2. **使用 Gunicorn 部署**
   ```bash
   # 安装 gunicorn
   pip install gunicorn
   
   # 启动服务
   gunicorn main:app \
     --workers 4 \
     --worker-class uvicorn.workers.UvicornWorker \
     --bind 0.0.0.0:8000 \
     --access-logfile - \
     --error-logfile -
   ```

## 🐳 Docker 部署

### 1. 构建 Docker 镜像

创建 `Dockerfile`:
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装 Python 依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["gunicorn", "main:app", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker", "--bind", "0.0.0.0:8000"]
```

构建镜像:
```bash
docker build -t intelligent-calendar-assistant:latest .
```

### 2. 运行 Docker 容器

```bash
# 基础运行
docker run -d \
  --name calendar-assistant \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_api_key \
  intelligent-calendar-assistant:latest

# 带环境变量文件运行
docker run -d \
  --name calendar-assistant \
  -p 8000:8000 \
  --env-file .env \
  intelligent-calendar-assistant:latest
```

### 3. Docker Compose 部署

创建 `docker-compose.yml`:
```yaml
version: '3.8'

services:
  calendar-assistant:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL}
      - DEBUG=false
      - LOG_LEVEL=INFO
    env_file:
      - .env
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 可选：Redis 缓存
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  # 可选：PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: calendar_assistant
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

volumes:
  postgres_data:
```

启动服务:
```bash
docker-compose up -d
```

## ☁️ 云平台部署

### 1. Render.com 部署

1. **准备部署文件**
   
   创建 `render.yaml`:
   ```yaml
   services:
     - type: web
       name: intelligent-calendar-assistant
       env: python
       buildCommand: pip install -r requirements.txt
       startCommand: gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT
       envVars:
         - key: OPENAI_API_KEY
           sync: false
         - key: OPENAI_MODEL
           value: gpt-4o-mini
         - key: DEBUG
           value: false
   ```

2. **部署步骤**
   - 连接 GitHub 仓库
   - 配置环境变量
   - 部署应用

### 2. Heroku 部署

1. **准备文件**
   
   创建 `Procfile`:
   ```
   web: gunicorn main:app --workers 4 --worker-class uvicorn.workers.UvicornWorker --bind 0.0.0.0:$PORT
   ```
   
   创建 `runtime.txt`:
   ```
   python-3.9.18
   ```

2. **部署命令**
   ```bash
   # 安装 Heroku CLI
   # 登录 Heroku
   heroku login
   
   # 创建应用
   heroku create your-app-name
   
   # 设置环境变量
   heroku config:set OPENAI_API_KEY=your_api_key
   heroku config:set OPENAI_MODEL=gpt-4o-mini
   
   # 部署
   git push heroku main
   ```

### 3. AWS 部署

#### 使用 AWS Lambda + API Gateway

1. **安装 Serverless Framework**
   ```bash
   npm install -g serverless
   npm install serverless-python-requirements
   ```

2. **创建 serverless.yml**
   ```yaml
   service: intelligent-calendar-assistant
   
   provider:
     name: aws
     runtime: python3.9
     region: us-east-1
     environment:
       OPENAI_API_KEY: ${env:OPENAI_API_KEY}
       OPENAI_MODEL: gpt-4o-mini
   
   functions:
     api:
       handler: lambda_handler.handler
       events:
         - http:
             path: /{proxy+}
             method: ANY
             cors: true
   
   plugins:
     - serverless-python-requirements
   
   custom:
     pythonRequirements:
       dockerizePip: true
   ```

3. **创建 Lambda 处理器**
   ```python
   # lambda_handler.py
   from mangum import Mangum
   from main import app
   
   handler = Mangum(app)
   ```

4. **部署**
   ```bash
   serverless deploy
   ```

## 🔧 配置管理

### 环境变量配置

#### 必需配置
```bash
# LLM 配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini

# 或使用其他 LLM
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
```

#### 可选配置
```bash
# 应用配置
DEBUG=false
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# 飞书配置
FEISHU_APP_ID=your_feishu_app_id
FEISHU_APP_SECRET=your_feishu_app_secret

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost/dbname

# Redis 配置
REDIS_URL=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your_secret_key_here
ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
```

### 配置文件管理

创建 `config/settings.py`:
```python
import os
from typing import Optional

class Settings:
    # 应用配置
    DEBUG: bool = os.getenv("DEBUG", "false").lower() == "true"
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    HOST: str = os.getenv("HOST", "0.0.0.0")
    PORT: int = int(os.getenv("PORT", "8000"))
    
    # LLM 配置
    OPENAI_API_KEY: Optional[str] = os.getenv("OPENAI_API_KEY")
    OPENAI_MODEL: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    
    # 飞书配置
    FEISHU_APP_ID: Optional[str] = os.getenv("FEISHU_APP_ID")
    FEISHU_APP_SECRET: Optional[str] = os.getenv("FEISHU_APP_SECRET")

settings = Settings()
```

## 📊 监控和日志

### 1. 日志配置

```python
# config/logging.py
import logging
import sys
from config.settings import settings

def setup_logging():
    logging.basicConfig(
        level=getattr(logging, settings.LOG_LEVEL),
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(sys.stdout),
            logging.FileHandler('app.log') if not settings.DEBUG else logging.NullHandler()
        ]
    )
```

### 2. 健康检查

```python
# api/health.py
from fastapi import APIRouter
from datetime import datetime

router = APIRouter()

@router.get("/health/")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "2.0.0"
    }
```

### 3. 性能监控

使用 Prometheus + Grafana:
```python
# 安装依赖
pip install prometheus-client

# 添加指标
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_DURATION = Histogram('request_duration_seconds', 'Request duration')

@app.middleware("http")
async def metrics_middleware(request, call_next):
    start_time = time.time()
    response = await call_next(request)
    duration = time.time() - start_time
    
    REQUEST_COUNT.labels(method=request.method, endpoint=request.url.path).inc()
    REQUEST_DURATION.observe(duration)
    
    return response
```

## 🔒 安全配置

### 1. HTTPS 配置

```bash
# 使用 Let's Encrypt
sudo apt install certbot
sudo certbot --nginx -d your-domain.com

# 或使用反向代理
# nginx.conf
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. 环境隔离

```bash
# 开发环境
export ENV=development
export DEBUG=true

# 测试环境
export ENV=testing
export DEBUG=false

# 生产环境
export ENV=production
export DEBUG=false
```

## 🚨 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 查找占用端口的进程
   lsof -i :8000
   
   # 杀死进程
   kill -9 <PID>
   ```

2. **依赖问题**
   ```bash
   # 重新安装依赖
   pip install --force-reinstall -r requirements.txt
   
   # 清理缓存
   pip cache purge
   ```

3. **内存不足**
   ```bash
   # 增加 swap 空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

### 日志分析

```bash
# 查看应用日志
tail -f app.log

# 查看 Docker 日志
docker logs -f calendar-assistant

# 查看系统资源
htop
df -h
free -h
```

---

## 📚 相关文档

- [产品需求文档 (PRD)](./PRD.md)
- [技术架构文档](./TECH_ARCHITECTURE.md)
- [开发指南](./DEVELOPMENT.md)
- [API文档](./API.md)
