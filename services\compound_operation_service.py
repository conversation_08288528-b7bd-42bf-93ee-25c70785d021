#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
复合操作服务
处理包含多个步骤的复杂操作
"""

import logging
import re
from typing import Dict, List, Any, Optional
from core.ai import get_llm

logger = logging.getLogger(__name__)


class CompoundOperationService:
    """复合操作服务"""
    
    def __init__(self):
        self.llm = get_llm()
    
    async def analyze_compound_operation(self, user_input: str) -> Dict[str, Any]:
        """分析是否为复合操作"""
        try:
            system_prompt = """你是一个复合操作分析助手。请分析用户输入是否包含多个操作步骤。

常见的复合操作模式：
1. "创建日历XXX，然后在该日历下创建事件YYY"
2. "先创建日历，再添加事件"
3. "创建多个日历事件"
4. "查询日历，然后搜索事件"

请分析用户输入并返回JSON格式：
{
    "is_compound": true/false,
    "operations": [
        {
            "step": 1,
            "action": "操作类型",
            "description": "操作描述",
            "dependencies": ["依赖的前置步骤"]
        }
    ],
    "execution_strategy": "sequential/parallel",
    "confidence": 0.0-1.0
}"""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ]
            
            response = self.llm.invoke(messages)
            content = response.content.strip()
            
            # 解析JSON响应
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()
            
            import json
            result = json.loads(content)
            return result
            
        except Exception as e:
            logger.error(f"复合操作分析失败: {e}")
            return {
                "is_compound": False,
                "operations": [],
                "execution_strategy": "sequential",
                "confidence": 0.0
            }
    
    async def extract_calendar_and_event_info(self, user_input: str) -> Dict[str, Any]:
        """使用LLM从复合操作中提取日历和事件信息"""
        try:
            # 导入LLM时间解析器
            from services.llm_time_parser import get_llm_time_parser

            # 使用LLM提取详细信息
            prompt = f"""
分析以下用户输入，提取日历和事件的详细信息：

用户输入: "{user_input}"

请提取以下信息并返回JSON格式：
{{
    "calendar_name": "日历名称",
    "event_title": "事件标题",
    "time_expression": "完整的时间表达式",
    "has_both": true/false,
    "confidence": 0.95
}}

提取规则：
1. calendar_name: 从"创建日历XXX"中提取日历名称
2. event_title: 从事件描述中提取事件标题（如"部门会议"、"项目讨论"等）
3. time_expression: 提取完整的时间表达式（如"今天下午3点"、"明天上午9点"等）
4. has_both: 判断是否同时包含日历创建和事件创建
5. confidence: 提取的置信度

示例：
输入："创建日历我的工作计划，然后在该日历下创建日程事件，明天下午2点召开项目会议"
输出：
{{
    "calendar_name": "我的工作计划",
    "event_title": "项目会议",
    "time_expression": "明天下午2点",
    "has_both": true,
    "confidence": 0.95
}}

请仔细分析并提取准确信息。
"""

            response = self.llm.invoke([{"role": "user", "content": prompt}])
            result_text = response.content.strip()

            # 清理JSON格式
            if result_text.startswith("```json"):
                result_text = result_text[7:]
            if result_text.endswith("```"):
                result_text = result_text[:-3]
            result_text = result_text.strip()

            import json
            extraction_result = json.loads(result_text)

            # 如果有时间表达式，使用LLM时间解析器解析
            time_result = {}
            if extraction_result.get("time_expression"):
                time_parser = get_llm_time_parser()
                time_result = await time_parser.parse_time_expression(extraction_result["time_expression"])

            # 合并结果
            result = {
                "calendar_name": extraction_result.get("calendar_name"),
                "event_title": extraction_result.get("event_title"),
                "time_info": extraction_result.get("time_expression"),
                "has_both": extraction_result.get("has_both", False),
                "confidence": extraction_result.get("confidence", 0.8)
            }

            # 添加时间解析结果
            if time_result.get("success"):
                result.update({
                    "start_time": time_result.get("start_time"),
                    "end_time": time_result.get("end_time"),
                    "start_timestamp": time_result.get("start_timestamp"),
                    "end_timestamp": time_result.get("end_timestamp"),
                    "time_confidence": time_result.get("confidence", 0.8)
                })

            logger.info(f"LLM信息提取结果: {result}")
            return result

        except Exception as e:
            logger.error(f"LLM信息提取失败: {e}")
            return {
                "calendar_name": None,
                "event_title": None,
                "time_info": None,
                "has_both": False,
                "confidence": 0.0
            }
    
    async def create_execution_plan(self, compound_analysis: Dict[str, Any], extracted_info: Dict[str, Any]) -> List[Dict[str, Any]]:
        """创建执行计划"""
        try:
            plan = []
            
            if extracted_info.get("has_both"):
                # 有日历和事件信息，创建两步计划
                
                # 步骤1：创建日历
                plan.append({
                    "step": 1,
                    "action": "create_calendar",
                    "title": extracted_info["calendar_name"],
                    "description": f"为后续事件创建的日历",
                    "dependencies": []
                })
                
                # 步骤2：创建事件
                plan.append({
                    "step": 2,
                    "action": "create_event",
                    "title": extracted_info["event_title"],
                    "target_calendar": extracted_info["calendar_name"],
                    "start_time": extracted_info["time_info"],
                    "dependencies": ["step_1"]
                })
            
            elif compound_analysis.get("is_compound"):
                # 使用AI分析的结果
                for op in compound_analysis.get("operations", []):
                    plan.append({
                        "step": op.get("step", len(plan) + 1),
                        "action": op.get("action"),
                        "description": op.get("description"),
                        "dependencies": op.get("dependencies", [])
                    })
            
            return plan
            
        except Exception as e:
            logger.error(f"创建执行计划失败: {e}")
            return []
    
    def is_compound_operation(self, user_input: str) -> bool:
        """快速判断是否为复合操作"""
        compound_indicators = [
            "然后", "接着", "再", "之后", "同时",
            "创建日历.*创建", "创建.*然后", "先.*再",
            "多个", "几个", "批量"
        ]
        
        for indicator in compound_indicators:
            if re.search(indicator, user_input):
                return True
        
        return False


# 全局实例
_compound_service: Optional[CompoundOperationService] = None

def get_compound_operation_service() -> CompoundOperationService:
    """获取复合操作服务实例"""
    global _compound_service
    if _compound_service is None:
        _compound_service = CompoundOperationService()
    return _compound_service
