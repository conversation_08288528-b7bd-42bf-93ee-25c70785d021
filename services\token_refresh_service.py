import asyncio
import logging
import os
import time
from datetime import datetime, timezone

import aiohttp

from config import APP_URL, FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
from integrations.feishu import refresh_token
from integrations.feishu.api_client import (
    FeishuAPIError,
    FeishuAuthError,
    FeishuNetworkError,
)
from integrations.storage import TokenStorage, get_expiring_tokens, save_token

# 设置日志
logger = logging.getLogger(__name__)

# 禁用hpack.hpack的DEBUG日志，仅当根日志级别不是DEBUG时
if logger.parent.level > logging.DEBUG:
    logging.getLogger("hpack.hpack").setLevel(logging.INFO)
# logging.getLogger('httpx').setLevel(logging.WARNING)
# logging.getLogger('httpcore').setLevel(logging.WARNING)

# 刷新间隔，单位为秒
REFRESH_INTERVAL = 1200  # 20分钟检查一次

# 过期阈值，单位为秒
EXPIRY_THRESHOLD = 1200  # 如果token在20分钟内过期，则刷新

# 健康检查间隔，单位为秒
HEALTH_CHECK_INTERVAL = 600  # 10分钟检查一次，确保低于Render的15分钟休眠阈值

# 创建TokenStorage实例
token_storage = TokenStorage()

# 获取应用URL，使用config.py中的APP_URL
# APP_URL = os.environ.get("APP_URL", "http://localhost:8080")
# APP_URL = os.environ.get("APP_URL", "http://localhost:5000")


async def refresh_expiring_tokens():
    """刷新即将过期的token"""
    # 首先获取所有token来显示总体状态
    from integrations.storage import get_all_tokens

    all_tokens = get_all_tokens()
    total_count = len(all_tokens)

    # 获取即将过期的token
    expiring_tokens = get_expiring_tokens(EXPIRY_THRESHOLD)
    expiring_count = len(expiring_tokens)

    # 总是显示token状态
    if expiring_count == 0:
        logger.info(
            f"Token检查完成：共{total_count}个token，其中{expiring_count}个即将过期"
        )
        return

    logger.info(
        f"Token检查完成：共{total_count}个token，其中{expiring_count}个即将过期，开始刷新"
    )

    success_count = 0
    failed_count = 0

    for user_id, token_data in expiring_tokens.items():
        try:
            logger.info(f"正在刷新用户 {user_id} 的token...")

            # 检查refresh_token是否存在
            if not token_data.get("refresh_token"):
                logger.error(f"用户 {user_id} 缺少refresh_token，跳过刷新")
                failed_count += 1
                continue

            refreshed = await refresh_token(
                app_id=FEISHU_CLIENT_ID,
                app_secret=FEISHU_CLIENT_SECRET,
                refresh_token=token_data["refresh_token"],
            )

            if "code" in refreshed and refreshed["code"] != 0:
                error_code = refreshed.get("code")
                error_msg = refreshed.get("msg", "未知错误")
                logger.error(
                    f"用户 {user_id} 的token刷新失败 [错误码: {error_code}]: {error_msg}"
                )

                # 如果是认证错误，可能需要用户重新授权
                if error_code == 99991663:  # refresh_token无效
                    logger.warning(
                        f"用户 {user_id} 的refresh_token已失效，需要重新授权"
                    )

                failed_count += 1
                continue

            new_token = refreshed["data"]
            new_expire_time = (
                int(datetime.now(timezone.utc).timestamp()) + new_token["expires_in"]
            )

            save_token(
                user_id,
                {
                    "access_token": new_token["access_token"],
                    "refresh_token": new_token["refresh_token"],
                    "access_token_expire": new_expire_time,
                },
            )

            expire_time_str = datetime.fromtimestamp(new_expire_time).strftime(
                "%Y-%m-%d %H:%M:%S"
            )
            logger.info(
                f"用户 {user_id} 的token刷新成功，新的过期时间: {expire_time_str}"
            )
            success_count += 1

        except FeishuAuthError as e:
            logger.error(f"用户 {user_id} 认证错误: {str(e)}")
            failed_count += 1
        except FeishuNetworkError as e:
            logger.error(f"用户 {user_id} 网络错误: {str(e)}")
            failed_count += 1
        except FeishuAPIError as e:
            logger.error(f"用户 {user_id} API错误: {str(e)}")
            failed_count += 1
        except Exception as e:
            logger.error(f"刷新用户 {user_id} 的token时发生未知错误: {str(e)}")
            failed_count += 1

    logger.info(f"Token刷新完成：成功 {success_count} 个，失败 {failed_count} 个")


async def health_check_task():
    """定期访问健康检查路由，防止Render休眠"""
    logger.info(f"健康检查服务已启动，检查间隔: {HEALTH_CHECK_INTERVAL}秒")
    while True:
        try:
            async with aiohttp.ClientSession() as session:
                health_url = f"{APP_URL}/health"
                async with session.head(health_url) as response:
                    if response.status == 200:
                        logger.info(f"健康检查成功")
                    else:
                        logger.warning(f"健康检查失败，状态码: {response.status}")
        except Exception as e:
            logger.error(f"健康检查时发生错误: {e}")

        # 等待下一次检查
        await asyncio.sleep(HEALTH_CHECK_INTERVAL)


async def token_refresh_task():
    """定时刷新token的任务"""
    logger.info(
        f"Token自动刷新服务已启动，刷新间隔: {REFRESH_INTERVAL}秒，过期阈值: {EXPIRY_THRESHOLD}秒"
    )
    while True:
        try:
            await refresh_expiring_tokens()
        except Exception as e:
            logger.error(f"刷新token时发生错误: {e}")

        # 等待下一次刷新
        await asyncio.sleep(REFRESH_INTERVAL)


def start_token_refresh_service():
    """启动token刷新服务和健康检查服务"""
    try:
        # 获取当前事件循环
        loop = asyncio.get_running_loop()

        # 启动token刷新服务
        loop.create_task(token_refresh_task())
        logger.info(f"Token刷新服务已启动")

        # 启动健康检查服务
        loop.create_task(health_check_task())
        logger.info(f"健康检查服务已启动")

    except RuntimeError as e:
        # 如果没有运行的事件循环，记录警告
        logger.warning("没有运行的事件循环，token刷新服务将在应用启动后自动启动")
    except Exception as e:
        logger.error(f"启动token刷新服务时发生意外异常: {e}")
