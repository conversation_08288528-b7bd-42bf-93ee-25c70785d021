# 飞书MCP服务器完整指南

## 🎉 项目完成概述

我们已经成功创建了一个基于现有飞书日历API的MCP (Model Context Protocol) 服务器，使用streamable模式。该服务器可以让AI助手通过MCP协议直接操作飞书日历。

## ✅ 已完成的功能

### 1. MCP服务器架构 ✅
- ✅ 基于`mcp>=1.6.0`库实现
- ✅ 支持stdio传输模式（streamable）
- ✅ 完整的工具注册和请求路由
- ✅ 错误处理和日志记录

### 2. 飞书日历API集成 ✅
- ✅ 集成现有的`FeishuCalendarLark`客户端
- ✅ 支持用户认证和token管理
- ✅ 复用项目中已有的飞书API功能

### 3. MCP工具定义 ✅
- ✅ `list_calendars` - 获取用户日历列表
- ✅ `get_calendar_events` - 获取指定日历的事件列表
- ✅ `create_calendar_event` - 创建新的日历事件
- ✅ `update_calendar_event` - 更新现有的日历事件
- ✅ `delete_calendar_event` - 删除日历事件
- ✅ `get_today_events` - 获取今天的所有事件

### 4. 启动脚本和配置 ✅
- ✅ Python启动脚本：`start_feishu_mcp_server.py`
- ✅ Windows批处理文件：`start_feishu_mcp.bat`
- ✅ PowerShell脚本：`start_feishu_mcp.ps1`
- ✅ MCP客户端配置：`mcp_server_config.json`

### 5. 测试和验证 ✅
- ✅ 单元测试：`tests/test_feishu_mcp_server.py`
- ✅ 基本功能测试：`test_mcp_server_basic.py`
- ✅ MCP客户端测试：`test_mcp_client.py`

## 📁 文件结构

```
feishu-coze-plugin/
├── core/mcp/
│   └── feishu_mcp_server.py          # MCP服务器核心实现
├── docs/
│   ├── FEISHU_MCP_TOOLS.md           # 工具定义文档
│   ├── FEISHU_MCP_SERVER_USAGE.md    # 使用指南
│   └── FEISHU_MCP_SERVER_COMPLETE_GUIDE.md  # 完整指南
├── tests/
│   └── test_feishu_mcp_server.py     # 单元测试
├── start_feishu_mcp_server.py        # Python启动脚本
├── start_feishu_mcp.bat              # Windows批处理启动脚本
├── start_feishu_mcp.ps1              # PowerShell启动脚本
├── mcp_server_config.json            # MCP客户端配置
├── test_mcp_server_basic.py          # 基本功能测试
└── test_mcp_client.py                # MCP客户端测试
```

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

在 `.env` 文件中设置：

```env
FEISHU_CLIENT_ID=cli_a76a68f612bf900c
FEISHU_CLIENT_SECRET=EVumG3wCHsDBeJRfpbmJkfRhzCns73jC
```

### 3. 启动MCP服务器

#### 方式一：Python脚本
```bash
python start_feishu_mcp_server.py
```

#### 方式二：Windows批处理
```cmd
start_feishu_mcp.bat
```

#### 方式三：PowerShell
```powershell
.\start_feishu_mcp.ps1
```

### 4. 测试服务器功能

```bash
# 基本功能测试
python test_mcp_server_basic.py

# 单元测试
python -m pytest tests/test_feishu_mcp_server.py -v

# MCP客户端测试
python test_mcp_client.py
```

## 🔧 MCP客户端配置

### Claude Desktop配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "feishu-calendar": {
      "command": "python",
      "args": ["D:/Code/feishu-coze-plugin/start_feishu_mcp_server.py"],
      "cwd": "D:/Code/feishu-coze-plugin",
      "env": {
        "FEISHU_CLIENT_ID": "cli_a76a68f612bf900c",
        "FEISHU_CLIENT_SECRET": "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC"
      }
    }
  }
}
```

## 🛠️ 工具使用示例

### 获取日历列表

```json
{
  "name": "list_calendars",
  "arguments": {
    "user_id": "user123"
  }
}
```

### 创建日历事件

```json
{
  "name": "create_calendar_event",
  "arguments": {
    "user_id": "user123",
    "title": "项目评审会议",
    "description": "Q3项目评审",
    "start_time": "2025-07-16T14:00:00+08:00",
    "end_time": "2025-07-16T16:00:00+08:00",
    "location": "大会议室",
    "attendees": ["<EMAIL>", "<EMAIL>"]
  }
}
```

### 获取今天的事件

```json
{
  "name": "get_today_events",
  "arguments": {
    "user_id": "user123"
  }
}
```

## 🏗️ 技术架构

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │◄──►│  MCP Server      │◄──►│  Feishu API     │
│   (Claude等)    │    │  (本项目)        │    │  (现有集成)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 核心组件

1. **FeishuMCPServer** - MCP服务器主类
2. **FeishuCalendarLark** - 飞书日历API客户端
3. **Tool Handlers** - 工具处理器方法
4. **Error Handling** - 错误处理机制

## 📊 测试结果

✅ **服务器创建测试** - 通过  
✅ **工具处理器测试** - 通过  
✅ **错误处理测试** - 通过  
✅ **单元测试** - 通过  

## 🔍 故障排除

### 常见问题

1. **导入错误**
   - 确保已安装所有依赖：`pip install -r requirements.txt`
   - 检查Python路径设置

2. **MCP连接失败**
   - 确认服务器正在运行
   - 检查客户端配置中的路径
   - 查看服务器日志输出

3. **飞书API调用失败**
   - 检查环境变量配置
   - 确认用户已授权
   - 检查网络连接

### 调试模式

```bash
export LOG_LEVEL=DEBUG
python start_feishu_mcp_server.py
```

## 🎯 下一步计划

1. **功能扩展**
   - 支持更多日历操作
   - 添加事件搜索功能
   - 支持批量操作

2. **性能优化**
   - 连接池管理
   - 缓存机制
   - 异步处理优化

3. **用户体验**
   - 更友好的错误信息
   - 更详细的日志记录
   - 配置文件支持

## 📚 相关文档

- [工具定义文档](./FEISHU_MCP_TOOLS.md)
- [使用指南](./FEISHU_MCP_SERVER_USAGE.md)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [飞书开放平台文档](https://open.feishu.cn/document/)

---

**项目状态**: ✅ 完成  
**最后更新**: 2025-07-17  
**版本**: 1.0.0
