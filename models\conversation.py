"""
会话状态管理模型
支持多轮对话和人机协作的状态管理
"""

from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Literal, Optional

from pydantic import BaseModel, Field


class ConversationState(str, Enum):
    """会话状态枚举"""

    NORMAL = "normal"  # 正常对话状态
    WAITING_CONFIRMATION = "waiting_confirmation"  # 等待用户确认
    WAITING_SUPPLEMENT = "waiting_supplement"  # 等待用户补充信息
    PROCESSING = "processing"  # 处理中
    ERROR = "error"  # 错误状态


class UserFeedbackType(str, Enum):
    """用户反馈类型"""

    CONFIRM = "confirm"  # 确认
    REJECT = "reject"  # 拒绝
    MODIFY = "modify"  # 修改建议
    SUPPLEMENT = "supplement"  # 补充信息
    CANCEL = "cancel"  # 取消操作


class ConversationContext(BaseModel):
    """会话上下文模型"""

    # 基本信息
    user_id: str = Field(..., description="用户ID")
    session_id: str = Field(..., description="会话ID")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    updated_at: datetime = Field(default_factory=datetime.now, description="更新时间")

    # 会话状态
    state: ConversationState = Field(
        ConversationState.NORMAL, description="当前会话状态"
    )

    # 意图上下文
    current_intent: Optional[str] = Field(None, description="当前意图")
    intent_confidence: Optional[float] = Field(None, description="意图置信度")
    original_request: Optional[str] = Field(None, description="原始请求")

    # 待处理的操作
    pending_operation: Optional[Dict[str, Any]] = Field(
        None, description="待处理的操作"
    )
    extracted_entities: Optional[Dict[str, Any]] = Field(None, description="提取的实体")
    calendar_plan: Optional[Dict[str, Any]] = Field(None, description="日历计划")

    # 确认相关
    confirmation_message: Optional[str] = Field(None, description="确认消息")
    confirmation_data: Optional[Dict[str, Any]] = Field(
        None, description="确认相关数据"
    )

    # 对话历史
    message_history: List[Dict[str, Any]] = Field(
        default_factory=list, description="消息历史"
    )

    # 复合操作相关
    compound_operation: Optional[Dict[str, Any]] = Field(
        None, description="复合操作信息"
    )

    # 上下文数据
    context_data: Dict[str, Any] = Field(default_factory=dict, description="上下文数据")

    class Config:
        arbitrary_types_allowed = True

    def add_message(
        self, role: str, content: str, metadata: Optional[Dict[str, Any]] = None
    ):
        """添加消息到历史"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {},
        }
        self.message_history.append(message)
        self.updated_at = datetime.now()

    def set_waiting_confirmation(
        self,
        operation: Dict[str, Any],
        message: str,
        data: Optional[Dict[str, Any]] = None,
    ):
        """设置等待确认状态"""
        self.state = ConversationState.WAITING_CONFIRMATION
        self.pending_operation = operation
        self.confirmation_message = message
        self.confirmation_data = data or {}
        self.updated_at = datetime.now()

    def set_waiting_supplement(self, missing_fields: List[str], message: str):
        """设置等待补充信息状态"""
        self.state = ConversationState.WAITING_SUPPLEMENT
        self.confirmation_message = message
        self.confirmation_data = {"missing_fields": missing_fields}
        self.updated_at = datetime.now()

    def set_compound_operation(self, compound_data: Dict[str, Any]):
        """设置复合操作"""
        self.compound_operation = compound_data
        self.updated_at = datetime.now()

    def get_next_compound_step(self) -> Optional[Dict[str, Any]]:
        """获取下一个复合操作步骤"""
        if not self.compound_operation:
            return None

        current_step = self.compound_operation.get("current_step", 1)
        total_steps = self.compound_operation.get("total_steps", 1)

        if current_step < total_steps:
            next_step = current_step + 1
            return self.compound_operation.get(f"step_{next_step}")

        return None

    def advance_compound_step(self):
        """推进到下一个复合操作步骤"""
        if self.compound_operation:
            current_step = self.compound_operation.get("current_step", 1)
            self.compound_operation["current_step"] = current_step + 1
            self.updated_at = datetime.now()

    def clear_pending_state(self):
        """清除待处理状态"""
        self.state = ConversationState.NORMAL
        self.pending_operation = None
        self.confirmation_message = None
        self.confirmation_data = None
        self.compound_operation = None
        self.updated_at = datetime.now()

    def get_last_user_message(self) -> Optional[str]:
        """获取最后一条用户消息"""
        for message in reversed(self.message_history):
            if message["role"] == "user":
                return message["content"]
        return None

    def get_conversation_summary(self) -> str:
        """获取对话摘要"""
        if not self.message_history:
            return "新对话"

        recent_messages = self.message_history[-3:]  # 最近3条消息
        summary_parts = []

        for msg in recent_messages:
            role = "用户" if msg["role"] == "user" else "助手"
            content = (
                msg["content"][:50] + "..."
                if len(msg["content"]) > 50
                else msg["content"]
            )
            summary_parts.append(f"{role}: {content}")

        return " | ".join(summary_parts)


class ConversationManager:
    """会话管理器"""

    def __init__(self):
        self.conversations: Dict[str, ConversationContext] = {}

    def get_or_create_conversation(
        self, user_id: str, session_id: Optional[str] = None
    ) -> ConversationContext:
        """获取或创建会话"""
        if session_id is None:
            session_id = f"{user_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        conversation_key = f"{user_id}_{session_id}"

        if conversation_key not in self.conversations:
            self.conversations[conversation_key] = ConversationContext(
                user_id=user_id, session_id=session_id
            )

        return self.conversations[conversation_key]

    def update_conversation(self, conversation: ConversationContext):
        """更新会话"""
        conversation_key = f"{conversation.user_id}_{conversation.session_id}"
        self.conversations[conversation_key] = conversation

    def classify_user_feedback(
        self, user_input: str, context: ConversationContext
    ) -> UserFeedbackType:
        """分类用户反馈"""
        user_input_lower = user_input.lower().strip()

        # 确认关键词
        confirm_keywords = [
            "确认",
            "是的",
            "好的",
            "可以",
            "同意",
            "对",
            "是",
            "ok",
            "yes",
            "y",
        ]
        if any(keyword in user_input_lower for keyword in confirm_keywords):
            return UserFeedbackType.CONFIRM

        # 拒绝关键词
        reject_keywords = [
            "取消",
            "不要",
            "算了",
            "拒绝",
            "不",
            "no",
            "n",
            "不行",
            "不可以",
        ]
        if any(keyword in user_input_lower for keyword in reject_keywords):
            return UserFeedbackType.REJECT

        # 修改关键词
        modify_keywords = ["修改", "改", "更改", "调整", "换", "改成", "改为"]
        if any(keyword in user_input_lower for keyword in modify_keywords):
            return UserFeedbackType.MODIFY

        # 如果在等待补充信息状态，且不是确认/拒绝，则认为是补充信息
        if context.state == ConversationState.WAITING_SUPPLEMENT:
            return UserFeedbackType.SUPPLEMENT

        # 默认认为是修改建议
        return UserFeedbackType.MODIFY

    def should_skip_intent_classification(self, context: ConversationContext) -> bool:
        """判断是否应该跳过意图识别"""
        return context.state in [
            ConversationState.WAITING_CONFIRMATION,
            ConversationState.WAITING_SUPPLEMENT,
        ]

    def get_context_prompt(self, context: ConversationContext) -> str:
        """获取上下文提示"""
        if context.state == ConversationState.WAITING_CONFIRMATION:
            return f"""
当前状态：等待用户确认
原始请求：{context.original_request}
待确认操作：{context.pending_operation}
用户可以：确认、拒绝、或提供修改建议
"""
        elif context.state == ConversationState.WAITING_SUPPLEMENT:
            missing_fields = context.confirmation_data.get("missing_fields", [])
            return f"""
当前状态：等待用户补充信息
原始请求：{context.original_request}
缺失信息：{', '.join(missing_fields)}
请引导用户提供缺失的信息
"""
        else:
            return "正常对话状态"


# 全局会话管理器实例
conversation_manager = ConversationManager()
