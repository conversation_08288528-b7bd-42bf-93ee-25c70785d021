# 飞书智能日历助手

[![CI/CD Pipeline](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/ci.yml/badge.svg)](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/ci.yml)
[![Test Suite](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/test.yml/badge.svg)](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/test.yml)
[![codecov](https://codecov.io/gh/ifcheung2012/feishu-coze-plugin/branch/main/graph/badge.svg)](https://codecov.io/gh/ifcheung2012/feishu-coze-plugin)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

一个基于FastAPI的飞书日历智能助手，支持自然语言交互和日历管理，具备完整的CI/CD流程和生产级质量保证。

## ✨ 功能特性

### 🤖 智能交互
- **自然语言理解**: 支持"明天下午3点安排会议"等自然语言表达
- **智能意图识别**: 自动识别日历操作、聊天、记录等不同意图
- **多轮对话**: 上下文感知的智能交互
- **交互式补全**: 信息不完整时自动询问补充

### 📅 日历管理
- **飞书OAuth2.0授权**: 安全的用户认证和授权
- **完整日历操作**: 创建、查询、修改、删除日历事件
- **实时数据同步**: 与飞书日历实时同步
- **多日历支持**: 支持个人和共享日历

### 🔧 系统特性
- **自动令牌刷新**: 智能的令牌管理和自动刷新
- **多存储支持**: Supabase/文件/内存存储
- **RESTful API**: 完整的API接口
- **生产级质量**: 完整的测试覆盖和错误处理

### 🧪 开发特性
- **完整测试套件**: 单元测试、集成测试、端到端测试
- **CI/CD流程**: 自动化测试、构建和部署
- **代码质量保证**: Linting、格式化、安全检查
- **文档完善**: API文档和开发指南

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端/客户端    │    │   FastAPI应用    │    │   飞书开放平台   │
│                │────▶│                │────▶│                │
│  - Web界面      │    │  - API路由      │    │  - OAuth认证    │
│  - 移动应用     │    │  - 业务逻辑     │    │  - 日历API      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Supabase DB   │
                       │                │
                       │  - 用户数据     │
                       │  - 令牌存储     │
                       └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- 飞书开放平台应用
- Supabase账户（推荐）

### 安装

```bash
git clone https://github.com/ifcheung2012/feishu-coze-plugin.git
cd feishu-coze-plugin
pip install -r requirements.txt
```

### 配置

创建 `.env` 文件并填写配置：

```env
# 飞书应用配置
FEISHU_CLIENT_ID=your_feishu_app_id
FEISHU_CLIENT_SECRET=your_feishu_app_secret
REDIRECT_URI=http://localhost:5000/auth/callback
APP_URL=http://localhost:5000

# 数据库配置
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
STORAGE_TYPE=supabase

# AI配置
SILICONFLOW_API_KEY=your_ai_api_key
LLM_MODEL=Qwen/Qwen3-14B

# 系统配置
FEISHU_ENV=local
LOG_LEVEL=INFO
```

### 运行

```bash
python main.py
```

访问 http://localhost:5000

## 🧪 开发和测试

### 安装开发依赖

```bash
pip install -r requirements-dev.txt
```

### 运行测试

```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest tests/unit/          # 单元测试
pytest tests/integration/   # 集成测试
pytest tests/e2e/          # 端到端测试

# 生成覆盖率报告
pytest --cov=. --cov-report=html
```

### 代码质量检查

```bash
# 代码格式化
black .
isort .

# 代码检查
flake8 .
mypy .

# 安全检查
bandit -r .
safety check
```

### Git钩子

```bash
# 安装pre-commit钩子
pre-commit install

# 手动运行所有钩子
pre-commit run --all-files
```

## 📚 API文档

启动服务后访问：
- **Swagger UI**: http://localhost:5000/docs
- **ReDoc**: http://localhost:5000/redoc

### 主要端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 健康检查 |
| `/login` | GET | 开始OAuth认证 |
| `/auth/callback` | GET | OAuth回调 |
| `/auth/callback/calendars/` | GET | 获取日历列表 |
| `/auth/callback/events/today` | GET | 获取今日事件 |
| `/auth/callback/calendars/{id}/events` | POST | 创建事件 |
| `/auth/callback/calendars/{id}/events/{event_id}` | DELETE | 删除事件 |
| `/chat/` | POST | 智能聊天 |

## 🚀 部署

### 本地测试

```bash
# 运行部署前检查
python deployment_check.py

# 运行API端点测试
python test_api_endpoints.py
```

### GitHub Actions CI/CD

项目配置了完整的CI/CD流程：

1. **代码推送** → 自动触发测试
2. **测试通过** → 构建部署包
3. **主分支** → 自动部署到生产环境

### Render.com部署

1. 连接GitHub仓库
2. 设置环境变量（参考`.env.production`）
3. 部署服务

部署后的应用地址：https://feishu-coze-plugin.onrender.com

## 📁 项目结构

```
feishu-coze-plugin/
├── api/                    # API路由
│   ├── routers/           # 路由模块
│   └── dependencies.py   # 依赖注入
├── integrations/          # 第三方集成
│   ├── feishu/           # 飞书API客户端
│   └── storage/          # 数据存储
├── services/             # 业务服务
├── tests/                # 测试套件
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   └── e2e/             # 端到端测试
├── .github/workflows/    # GitHub Actions
├── main.py              # 应用入口
├── config.py            # 配置管理
├── requirements.txt     # 生产依赖
├── requirements-dev.txt # 开发依赖
└── pyproject.toml       # 项目配置
```

## 🔄 开发流程

1. **本地开发** → 编写代码和测试
2. **质量检查** → 运行linting和测试
3. **提交代码** → 触发CI/CD流程
4. **代码审查** → Pull Request审查
5. **合并部署** → 自动部署到生产环境

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [Issues](https://github.com/ifcheung2012/feishu-coze-plugin/issues)
2. 创建新的Issue
3. 联系维护者

---

**🎉 感谢使用飞书智能日历助手！**
