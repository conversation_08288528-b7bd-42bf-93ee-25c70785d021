"""
代理基类
定义所有代理的通用接口和行为
"""

from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class AgentType(str, Enum):
    """代理类型枚举"""

    COORDINATOR = "coordinator"
    PLANNER = "planner"
    EXECUTOR = "executor"


class Command(BaseModel):
    """代理命令模型，类似PDeerFlow的Command"""

    update: Optional[Dict[str, Any]] = None
    goto: Optional[str] = None


class AgentState(BaseModel):
    """代理状态模型"""

    user_input: Optional[str] = None
    processed_text: Optional[str] = None
    intent: Optional[str] = None
    confidence: Optional[float] = None
    messages: List[Dict[str, Any]] = []
    context: Dict[str, Any] = {}

    # 用户相关状态
    user_id: Optional[str] = None
    session_id: Optional[str] = None

    # 日历相关状态
    calendar_plan: Optional[Dict[str, Any]] = None
    extracted_entities: Optional[Dict[str, Any]] = None
    pending_confirmation: bool = False
    last_operation: Optional[str] = None
    execution_result: Optional[Dict[str, Any]] = None

    class Config:
        arbitrary_types_allowed = True


class BaseAgent(ABC):
    """代理基类"""

    def __init__(self, agent_type: AgentType):
        self.agent_type = agent_type
        self.tools = []

    @abstractmethod
    async def process(self, state: AgentState) -> Command:
        """
        处理状态并返回命令

        Args:
            state: 当前状态

        Returns:
            Command: 下一步操作命令
        """
        pass

    def bind_tools(self, tools: List[Any]) -> "BaseAgent":
        """绑定工具到代理"""
        self.tools = tools
        return self

    def get_llm_with_tools(self):
        """获取绑定了工具的LLM实例"""
        from core.ai import get_llm

        llm = get_llm()
        if self.tools:
            return llm.bind_tools(self.tools)
        return llm
