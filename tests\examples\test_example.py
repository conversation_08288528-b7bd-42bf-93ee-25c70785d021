"""
测试示例文件
展示如何编写使用真实数据和真实API的测试
"""

import pytest
import asyncio
import os
from datetime import datetime, timedelta

# 导入要测试的模块
from integrations.feishu import get_feishu_client
from integrations.storage import get_token, save_token


class TestUnitTestExample:
    """单元测试示例"""
    
    @pytest.mark.unit
    def test_simple_function(self):
        """简单函数测试示例"""
        # Arrange (准备)
        input_value = "test"
        expected_result = "TEST"
        
        # Act (执行)
        result = input_value.upper()
        
        # Assert (断言)
        assert result == expected_result
    
    @pytest.mark.unit
    @pytest.mark.asyncio
    async def test_async_function(self):
        """异步函数测试示例"""
        # 模拟异步函数
        async def async_function(value):
            await asyncio.sleep(0.1)
            return value * 2
        
        # 测试异步函数
        result = await async_function(5)
        assert result == 10
    
    @pytest.mark.unit
    def test_time_conversion(self):
        """时间转换功能测试"""
        # 测试时间戳转换
        from integrations.feishu.calendar_client import FeishuCalendarLark
        from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET

        client = FeishuCalendarLark(FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET)

        # 测试时间戳转换
        timestamp = client.convert_to_timestamp("2023-01-01T10:00:00+08:00")
        assert timestamp is not None

        # 测试ISO格式转换
        iso_time = client.timestamp_to_iso(1672531200)
        assert iso_time is not None
        assert "2023" in iso_time


class TestIntegrationExample:
    """集成测试示例 - 使用真实数据和API"""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_feishu_client_creation(self):
        """测试飞书客户端创建"""
        client = get_feishu_client()
        assert client is not None
        assert hasattr(client, 'app_id')
        assert hasattr(client, 'app_secret')

    @pytest.mark.integration
    def test_storage_operations(self):
        """测试存储操作 - 使用真实存储"""
        test_user_id = "test_user_integration"
        test_token_data = {
            "access_token": "test_access_token",
            "refresh_token": "test_refresh_token",
            "access_token_expire": int(datetime.now().timestamp()) + 7200,
            "refresh_token_expire": int(datetime.now().timestamp()) + 86400
        }

        # 保存token
        save_token(test_user_id, test_token_data)

        # 获取token
        retrieved_token = get_token(test_user_id)
        assert retrieved_token is not None
        assert retrieved_token["access_token"] == "test_access_token"

        # 清理测试数据
        # 注意：这里应该有清理逻辑，具体实现取决于存储类型


class TestE2EExample:
    """端到端测试示例 - 使用真实API"""

    @pytest.mark.e2e
    @pytest.mark.network
    def test_client_initialization_workflow(self):
        """测试客户端初始化工作流"""
        # 1. 创建客户端
        client = get_feishu_client()
        assert client is not None

        # 2. 验证客户端配置
        assert client.app_id is not None
        assert client.app_secret is not None

        # 3. 验证工具函数可用
        timestamp = client.convert_to_timestamp("2023-01-01T10:00:00+08:00")
        assert timestamp is not None

        iso_time = client.timestamp_to_iso(1672531200)
        assert iso_time is not None

    @pytest.mark.e2e
    @pytest.mark.network
    @pytest.mark.skipif(
        not os.getenv("TEST_ACCESS_TOKEN"),
        reason="需要真实的access_token进行测试"
    )
    async def test_real_api_workflow(self):
        """测试真实API调用工作流"""
        # 注意：这个测试需要有效的access_token
        access_token = os.getenv("TEST_ACCESS_TOKEN")
        client = get_feishu_client()

        # 1. 测试获取用户信息
        user_info = await client.get_user_info(access_token)
        assert "code" in user_info

        # 如果API调用成功，验证返回数据
        if user_info.get("code") == 0:
            assert "data" in user_info
            print(f"用户信息获取成功: {user_info['data']}")
        else:
            print(f"API调用返回错误: {user_info}")
            # 即使API返回错误，也不应该是程序异常


class TestPerformanceExample:
    """性能测试示例"""
    
    @pytest.mark.performance
    @pytest.mark.slow
    def test_performance_benchmark(self, benchmark):
        """性能基准测试示例"""
        def function_to_benchmark():
            # 模拟一些计算
            return sum(range(1000))
        
        # 使用pytest-benchmark进行基准测试
        result = benchmark(function_to_benchmark)
        assert result == 499500


# 测试Fixture示例
@pytest.fixture
def sample_user_data():
    """示例用户数据fixture"""
    return {
        "user_id": "test_user_123",
        "name": "测试用户",
        "email": "<EMAIL>"
    }


@pytest.fixture
def sample_calendar_data():
    """示例日历数据fixture"""
    return {
        "calendar_id": "test_calendar_123",
        "summary": "测试日历",
        "description": "这是一个测试日历"
    }


@pytest.fixture
def sample_event_data():
    """示例事件数据fixture"""
    return {
        "event_id": "test_event_123",
        "summary": "测试事件",
        "start_time": {"timestamp": "1672531200"},
        "end_time": {"timestamp": "1672534800"},
        "description": "这是一个测试事件"
    }


@pytest.fixture
def real_feishu_client():
    """真实的飞书客户端fixture"""
    return get_feishu_client()


@pytest.fixture
def test_user_id():
    """测试用户ID fixture"""
    return "test_user_" + str(int(datetime.now().timestamp()))


@pytest.fixture
def test_calendar_data():
    """测试日历数据fixture"""
    return {
        "summary": f"测试日历_{int(datetime.now().timestamp())}",
        "description": "这是一个用于测试的日历",
        "permissions": "private",
        "color": 1
    }


class TestWithFixtures:
    """使用真实数据Fixture的测试示例"""

    @pytest.mark.unit
    def test_with_user_data(self, sample_user_data):
        """使用用户数据fixture的测试"""
        assert sample_user_data["user_id"] == "test_user_123"
        assert "name" in sample_user_data

    @pytest.mark.integration
    def test_with_real_client(self, real_feishu_client):
        """使用真实客户端fixture的测试"""
        assert real_feishu_client is not None
        assert hasattr(real_feishu_client, 'app_id')
        assert hasattr(real_feishu_client, 'app_secret')

    @pytest.mark.integration
    def test_with_test_data(self, test_user_id, test_calendar_data):
        """使用测试数据fixture的测试"""
        assert test_user_id.startswith("test_user_")
        assert test_calendar_data["summary"].startswith("测试日历_")
        assert test_calendar_data["permissions"] == "private"


# 参数化测试示例
class TestParametrizedExample:
    """参数化测试示例"""
    
    @pytest.mark.unit
    @pytest.mark.parametrize("input_value,expected", [
        ("hello", "HELLO"),
        ("world", "WORLD"),
        ("test", "TEST"),
        ("", ""),
    ])
    def test_string_upper(self, input_value, expected):
        """参数化测试示例"""
        result = input_value.upper()
        assert result == expected
    
    @pytest.mark.unit
    @pytest.mark.parametrize("timestamp,expected_format", [
        (1672531200, "2023-01-01"),
        (1672617600, "2023-01-02"),
        (1672704000, "2023-01-03"),
    ])
    def test_timestamp_conversion(self, timestamp, expected_format):
        """时间戳转换参数化测试"""
        dt = datetime.fromtimestamp(timestamp)
        result = dt.strftime("%Y-%m-%d")
        assert result == expected_format


# 异常测试示例
class TestExceptionExample:
    """异常测试示例"""
    
    @pytest.mark.unit
    def test_exception_raised(self):
        """测试异常抛出"""
        with pytest.raises(ValueError, match="invalid value"):
            raise ValueError("invalid value")
    
    @pytest.mark.unit
    def test_exception_not_raised(self):
        """测试异常不被抛出"""
        # 这个测试确保函数正常执行而不抛出异常
        try:
            result = "test".upper()
            assert result == "TEST"
        except Exception as e:
            pytest.fail(f"不应该抛出异常: {e}")


# 跳过测试示例
class TestSkipExample:
    """跳过测试示例"""
    
    @pytest.mark.skip(reason="功能尚未实现")
    def test_future_feature(self):
        """跳过的测试"""
        pass
    
    @pytest.mark.skipif(
        not hasattr(asyncio, "run"),
        reason="需要Python 3.7+的asyncio.run"
    )
    def test_conditional_skip(self):
        """条件跳过的测试"""
        assert hasattr(asyncio, "run")


# 标记测试示例
class TestMarkExample:
    """测试标记示例"""
    
    @pytest.mark.auth
    @pytest.mark.unit
    def test_auth_function(self):
        """认证相关的单元测试"""
        pass
    
    @pytest.mark.calendar
    @pytest.mark.integration
    def test_calendar_integration(self):
        """日历相关的集成测试"""
        pass
    
    @pytest.mark.slow
    @pytest.mark.network
    def test_slow_network_operation(self):
        """慢速网络操作测试"""
        pass


class TestRealDataOperations:
    """真实数据操作测试"""

    @pytest.mark.integration
    def test_token_storage_lifecycle(self):
        """测试token存储的完整生命周期"""
        test_user_id = f"test_user_{int(datetime.now().timestamp())}"

        # 准备测试数据
        token_data = {
            "access_token": f"test_token_{int(datetime.now().timestamp())}",
            "refresh_token": f"refresh_token_{int(datetime.now().timestamp())}",
            "access_token_expire": int(datetime.now().timestamp()) + 7200,
            "refresh_token_expire": int(datetime.now().timestamp()) + 86400
        }

        # 1. 保存token
        save_token(test_user_id, token_data)

        # 2. 获取token
        retrieved_token = get_token(test_user_id)
        assert retrieved_token is not None
        assert retrieved_token["access_token"] == token_data["access_token"]
        assert retrieved_token["refresh_token"] == token_data["refresh_token"]

        # 3. 验证时间戳
        assert retrieved_token["access_token_expire"] == token_data["access_token_expire"]
        assert retrieved_token["refresh_token_expire"] == token_data["refresh_token_expire"]

        print(f"Token存储测试完成，用户ID: {test_user_id}")

    @pytest.mark.integration
    def test_client_factory_singleton(self):
        """测试客户端工厂的单例模式"""
        from integrations.feishu import FeishuClientFactory

        # 获取两个客户端实例
        client1 = FeishuClientFactory.get_unified_client()
        client2 = FeishuClientFactory.get_unified_client()

        # 验证是同一个实例
        assert client1 is client2

        # 强制创建新实例
        client3 = FeishuClientFactory.get_unified_client(force_new=True)
        assert client3 is not client1

        print("客户端工厂单例模式测试完成")

    @pytest.mark.integration
    @pytest.mark.network
    def test_time_conversion_with_real_data(self):
        """使用真实数据测试时间转换"""
        client = get_feishu_client()

        # 测试各种时间格式
        test_cases = [
            "2023-01-01T10:00:00+08:00",
            "2023-12-31T23:59:59+08:00",
            datetime.now(),
            int(datetime.now().timestamp())
        ]

        for test_time in test_cases:
            timestamp = client.convert_to_timestamp(test_time)
            assert timestamp is not None

            # 转换回ISO格式
            iso_time = client.timestamp_to_iso(timestamp)
            assert iso_time is not None
            assert len(iso_time) > 10  # 基本格式检查

        print(f"时间转换测试完成，测试了 {len(test_cases)} 种格式")


class TestRealAPIIntegration:
    """真实API集成测试"""

    @pytest.mark.integration
    @pytest.mark.network
    @pytest.mark.skipif(
        not os.getenv("FEISHU_CLIENT_ID") or not os.getenv("FEISHU_CLIENT_SECRET"),
        reason="需要真实的飞书应用配置"
    )
    def test_client_configuration(self):
        """测试客户端配置"""
        client = get_feishu_client()

        # 验证配置加载
        assert client.app_id == os.getenv("FEISHU_CLIENT_ID")
        assert client.app_secret == os.getenv("FEISHU_CLIENT_SECRET")

        print("客户端配置测试完成")

    @pytest.mark.integration
    @pytest.mark.network
    @pytest.mark.skipif(
        not os.getenv("TEST_USER_ID"),
        reason="需要测试用户ID"
    )
    def test_calendar_operations_structure(self):
        """测试日历操作的数据结构"""
        client = get_feishu_client()
        test_user_id = os.getenv("TEST_USER_ID")

        # 测试获取日历列表的数据结构
        result = client.get_calendars(test_user_id)

        # 验证返回结构
        assert isinstance(result, dict)
        assert "code" in result

        # 如果成功，验证数据结构
        if result.get("code") == 0:
            assert "data" in result
            print(f"日历列表获取成功: {result}")
        else:
            print(f"日历列表获取返回: {result}")
            # 即使API返回错误，结构也应该正确

        print("日历操作数据结构测试完成")
