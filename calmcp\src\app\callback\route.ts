import { NextRequest, NextResponse } from 'next/server';

/**
 * 飞书 OAuth 回调处理
 * 处理来自 http://localhost:3000/callback 的回调请求
 */
export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const code = searchParams.get('code');
  const state = searchParams.get('state');
  const error = searchParams.get('error');

  console.log('OAuth callback received:', {
    code: code ? `${code.substring(0, 10)}...` : null,
    state,
    error
  });

  // 如果有错误
  if (error) {
    return NextResponse.json({
      success: false,
      error: `OAuth error: ${error}`,
      description: searchParams.get('error_description')
    }, { status: 400 });
  }

  // 如果没有授权码
  if (!code) {
    return NextResponse.json({
      success: false,
      error: 'Missing authorization code'
    }, { status: 400 });
  }

  try {
    console.log('Attempting token exchange with code:', code.substring(0, 10) + '...');

    // 用授权码换取访问令牌
    const requestBody = {
      grant_type: 'authorization_code',
      app_id: process.env.FEISHU_APP_ID,
      app_secret: process.env.FEISHU_APP_SECRET,
      code: code
    };

    console.log('Token exchange request:', {
      ...requestBody,
      app_secret: '***hidden***'
    });

    const tokenResponse = await fetch('https://open.feishu.cn/open-apis/authen/v1/access_token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    });

    const tokenData = await tokenResponse.json();

    console.log('Token exchange response:', tokenData);

    if (tokenData.code !== 0) {
      console.error('Token exchange failed:', tokenData);
      return NextResponse.json({
        success: false,
        error: 'Failed to exchange token',
        details: tokenData
      }, { status: 400 });
    }

    // 成功获取令牌
    const userAccessToken = tokenData.data?.access_token;
    const refreshToken = tokenData.data?.refresh_token;
    const expiresIn = tokenData.data?.expires_in;

    console.log('Extracted tokens:', {
      hasAccessToken: !!userAccessToken,
      hasRefreshToken: !!refreshToken,
      expiresIn
    });

    // 返回成功页面，显示令牌信息
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>飞书 OAuth 授权成功</title>
        <meta charset="utf-8">
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 50px auto; padding: 20px; }
            .success { color: #28a745; }
            .token { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; word-break: break-all; }
            .copy-btn { background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer; }
            .instructions { background: #e9ecef; padding: 15px; border-radius: 5px; margin: 20px 0; }
        </style>
    </head>
    <body>
        <h1 class="success">🎉 飞书 OAuth 授权成功！</h1>
        
        <h2>📋 用户访问令牌</h2>
        <div class="token">
            <strong>Access Token:</strong><br>
            <code id="accessToken">${userAccessToken}</code>
            <button class="copy-btn" onclick="copyToken('accessToken')">复制</button>
        </div>
        
        <div class="token">
            <strong>Refresh Token:</strong><br>
            <code id="refreshToken">${refreshToken}</code>
            <button class="copy-btn" onclick="copyToken('refreshToken')">复制</button>
        </div>
        
        <p><strong>过期时间:</strong> ${expiresIn} 秒</p>
        
        <div class="instructions">
            <h3>🔧 下一步操作：</h3>
            <ol>
                <li>复制上面的 <strong>Access Token</strong></li>
                <li>编辑 <code>calmcp/.env.local</code> 文件</li>
                <li>将 <code>FEISHU_USER_ACCESS_TOKEN=u-your_user_access_token_here</code></li>
                <li>替换为 <code>FEISHU_USER_ACCESS_TOKEN=${userAccessToken}</code></li>
                <li>重启 CalMCP 服务：<code>npm run dev</code></li>
                <li>运行测试：<code>python test_calendar.py</code></li>
            </ol>
        </div>
        
        <script>
            function copyToken(elementId) {
                const element = document.getElementById(elementId);
                const text = element.textContent;
                navigator.clipboard.writeText(text).then(() => {
                    alert('令牌已复制到剪贴板！');
                });
            }
        </script>
    </body>
    </html>
    `;

    return new NextResponse(html, {
      headers: {
        'Content-Type': 'text/html; charset=utf-8'
      }
    });

  } catch (error) {
    console.error('OAuth callback error:', error);
    return NextResponse.json({
      success: false,
      error: 'Internal server error',
      details: error instanceof Error ? error.message : String(error)
    }, { status: 500 });
  }
}
