/**
 * Logger Utility
 * 统一的日志记录工具
 */

import winston from 'winston';
import path from 'path';

export interface LogContext {
  requestId?: string;
  userId?: string;
  toolName?: string;
  streamId?: string;
  [key: string]: any;
}

class Logger {
  private logger: winston.Logger;

  constructor() {
    const logLevel = process.env.LOG_LEVEL || 'info';
    const logFile = process.env.LOG_FILE || './logs/mcp-server.log';

    // 确保日志目录存在
    const logDir = path.dirname(logFile);
    
    this.logger = winston.createLogger({
      level: logLevel,
      format: winston.format.combine(
        winston.format.timestamp(),
        winston.format.errors({ stack: true }),
        winston.format.json()
      ),
      defaultMeta: { service: 'calmcp' },
      transports: [
        // 错误日志文件
        new winston.transports.File({
          filename: path.join(logDir, 'error.log'),
          level: 'error',
          maxsize: 5242880, // 5MB
          maxFiles: 5
        }),
        // 所有日志文件
        new winston.transports.File({
          filename: logFile,
          maxsize: 5242880, // 5MB
          maxFiles: 5
        })
      ]
    });

    // 开发环境下添加控制台输出
    if (process.env.NODE_ENV !== 'production') {
      this.logger.add(new winston.transports.Console({
        format: winston.format.combine(
          winston.format.colorize(),
          winston.format.simple()
        )
      }));
    }
  }

  private formatMessage(message: string, context?: LogContext): string {
    if (!context) return message;
    
    const contextStr = Object.entries(context)
      .map(([key, value]) => `${key}=${value}`)
      .join(' ');
    
    return `${message} [${contextStr}]`;
  }

  info(message: string, context?: LogContext): void {
    this.logger.info(this.formatMessage(message, context), context);
  }

  error(message: string, error?: Error, context?: LogContext): void {
    const logContext = { ...context };
    if (error) {
      logContext.error = error.message;
      logContext.stack = error.stack;
    }
    this.logger.error(this.formatMessage(message, context), logContext);
  }

  warn(message: string, context?: LogContext): void {
    this.logger.warn(this.formatMessage(message, context), context);
  }

  debug(message: string, context?: LogContext): void {
    this.logger.debug(this.formatMessage(message, context), context);
  }

  // MCP 特定的日志方法
  mcpRequest(toolName: string, args: any, context?: LogContext): void {
    this.info(`MCP tool called: ${toolName}`, {
      ...context,
      toolName,
      args: JSON.stringify(args)
    });
  }

  mcpResponse(toolName: string, success: boolean, duration: number, context?: LogContext): void {
    this.info(`MCP tool completed: ${toolName}`, {
      ...context,
      toolName,
      success,
      duration: `${duration}ms`
    });
  }

  mcpError(toolName: string, error: Error, context?: LogContext): void {
    this.error(`MCP tool failed: ${toolName}`, error, {
      ...context,
      toolName
    });
  }

  streamStart(streamId: string, context?: LogContext): void {
    this.info(`Stream started: ${streamId}`, {
      ...context,
      streamId
    });
  }

  streamEnd(streamId: string, itemCount: number, duration: number, context?: LogContext): void {
    this.info(`Stream completed: ${streamId}`, {
      ...context,
      streamId,
      itemCount,
      duration: `${duration}ms`
    });
  }

  streamError(streamId: string, error: Error, context?: LogContext): void {
    this.error(`Stream failed: ${streamId}`, error, {
      ...context,
      streamId
    });
  }

  // API 请求日志
  apiRequest(method: string, path: string, context?: LogContext): void {
    this.info(`API ${method} ${path}`, {
      ...context,
      method,
      path
    });
  }

  apiResponse(method: string, path: string, status: number, duration: number, context?: LogContext): void {
    this.info(`API ${method} ${path} ${status}`, {
      ...context,
      method,
      path,
      status,
      duration: `${duration}ms`
    });
  }

  // 飞书 API 日志
  feishuRequest(api: string, context?: LogContext): void {
    this.info(`Feishu API called: ${api}`, {
      ...context,
      feishuApi: api
    });
  }

  feishuResponse(api: string, code: number, duration: number, context?: LogContext): void {
    this.info(`Feishu API completed: ${api}`, {
      ...context,
      feishuApi: api,
      feishuCode: code,
      duration: `${duration}ms`
    });
  }

  feishuError(api: string, error: Error, context?: LogContext): void {
    this.error(`Feishu API failed: ${api}`, error, {
      ...context,
      feishuApi: api
    });
  }
}

// 导出单例实例
export const logger = new Logger();
export default logger;
