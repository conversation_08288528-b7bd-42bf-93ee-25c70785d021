"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/side-channel";
exports.ids = ["vendor-chunks/side-channel"];
exports.modules = {

/***/ "(rsc)/./node_modules/side-channel/index.js":
/*!********************************************!*\
  !*** ./node_modules/side-channel/index.js ***!
  \********************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar $TypeError = __webpack_require__(/*! es-errors/type */ \"(rsc)/./node_modules/es-errors/type.js\");\nvar inspect = __webpack_require__(/*! object-inspect */ \"(rsc)/./node_modules/object-inspect/index.js\");\nvar getSideChannelList = __webpack_require__(/*! side-channel-list */ \"(rsc)/./node_modules/side-channel-list/index.js\");\nvar getSideChannelMap = __webpack_require__(/*! side-channel-map */ \"(rsc)/./node_modules/side-channel-map/index.js\");\nvar getSideChannelWeakMap = __webpack_require__(/*! side-channel-weakmap */ \"(rsc)/./node_modules/side-channel-weakmap/index.js\");\n\nvar makeChannel = getSideChannelWeakMap || getSideChannelMap || getSideChannelList;\n\n/** @type {import('.')} */\nmodule.exports = function getSideChannel() {\n\t/** @typedef {ReturnType<typeof getSideChannel>} Channel */\n\n\t/** @type {Channel | undefined} */ var $channelData;\n\n\t/** @type {Channel} */\n\tvar channel = {\n\t\tassert: function (key) {\n\t\t\tif (!channel.has(key)) {\n\t\t\t\tthrow new $TypeError('Side channel does not contain ' + inspect(key));\n\t\t\t}\n\t\t},\n\t\t'delete': function (key) {\n\t\t\treturn !!$channelData && $channelData['delete'](key);\n\t\t},\n\t\tget: function (key) {\n\t\t\treturn $channelData && $channelData.get(key);\n\t\t},\n\t\thas: function (key) {\n\t\t\treturn !!$channelData && $channelData.has(key);\n\t\t},\n\t\tset: function (key, value) {\n\t\t\tif (!$channelData) {\n\t\t\t\t$channelData = makeChannel();\n\t\t\t}\n\n\t\t\t$channelData.set(key, value);\n\t\t}\n\t};\n\t// @ts-expect-error TODO: figure out why this is erroring\n\treturn channel;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvc2lkZS1jaGFubmVsL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLGlCQUFpQixtQkFBTyxDQUFDLDhEQUFnQjtBQUN6QyxjQUFjLG1CQUFPLENBQUMsb0VBQWdCO0FBQ3RDLHlCQUF5QixtQkFBTyxDQUFDLDBFQUFtQjtBQUNwRCx3QkFBd0IsbUJBQU8sQ0FBQyx3RUFBa0I7QUFDbEQsNEJBQTRCLG1CQUFPLENBQUMsZ0ZBQXNCOztBQUUxRDs7QUFFQSxXQUFXLGFBQWE7QUFDeEI7QUFDQSxlQUFlLG1DQUFtQzs7QUFFbEQsWUFBWSxxQkFBcUI7O0FBRWpDLFlBQVksU0FBUztBQUNyQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLEdBQUc7QUFDSDtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL2NhbG1jcC8uL25vZGVfbW9kdWxlcy9zaWRlLWNoYW5uZWwvaW5kZXguanM/MzFlNiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciAkVHlwZUVycm9yID0gcmVxdWlyZSgnZXMtZXJyb3JzL3R5cGUnKTtcbnZhciBpbnNwZWN0ID0gcmVxdWlyZSgnb2JqZWN0LWluc3BlY3QnKTtcbnZhciBnZXRTaWRlQ2hhbm5lbExpc3QgPSByZXF1aXJlKCdzaWRlLWNoYW5uZWwtbGlzdCcpO1xudmFyIGdldFNpZGVDaGFubmVsTWFwID0gcmVxdWlyZSgnc2lkZS1jaGFubmVsLW1hcCcpO1xudmFyIGdldFNpZGVDaGFubmVsV2Vha01hcCA9IHJlcXVpcmUoJ3NpZGUtY2hhbm5lbC13ZWFrbWFwJyk7XG5cbnZhciBtYWtlQ2hhbm5lbCA9IGdldFNpZGVDaGFubmVsV2Vha01hcCB8fCBnZXRTaWRlQ2hhbm5lbE1hcCB8fCBnZXRTaWRlQ2hhbm5lbExpc3Q7XG5cbi8qKiBAdHlwZSB7aW1wb3J0KCcuJyl9ICovXG5tb2R1bGUuZXhwb3J0cyA9IGZ1bmN0aW9uIGdldFNpZGVDaGFubmVsKCkge1xuXHQvKiogQHR5cGVkZWYge1JldHVyblR5cGU8dHlwZW9mIGdldFNpZGVDaGFubmVsPn0gQ2hhbm5lbCAqL1xuXG5cdC8qKiBAdHlwZSB7Q2hhbm5lbCB8IHVuZGVmaW5lZH0gKi8gdmFyICRjaGFubmVsRGF0YTtcblxuXHQvKiogQHR5cGUge0NoYW5uZWx9ICovXG5cdHZhciBjaGFubmVsID0ge1xuXHRcdGFzc2VydDogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0aWYgKCFjaGFubmVsLmhhcyhrZXkpKSB7XG5cdFx0XHRcdHRocm93IG5ldyAkVHlwZUVycm9yKCdTaWRlIGNoYW5uZWwgZG9lcyBub3QgY29udGFpbiAnICsgaW5zcGVjdChrZXkpKTtcblx0XHRcdH1cblx0XHR9LFxuXHRcdCdkZWxldGUnOiBmdW5jdGlvbiAoa2V5KSB7XG5cdFx0XHRyZXR1cm4gISEkY2hhbm5lbERhdGEgJiYgJGNoYW5uZWxEYXRhWydkZWxldGUnXShrZXkpO1xuXHRcdH0sXG5cdFx0Z2V0OiBmdW5jdGlvbiAoa2V5KSB7XG5cdFx0XHRyZXR1cm4gJGNoYW5uZWxEYXRhICYmICRjaGFubmVsRGF0YS5nZXQoa2V5KTtcblx0XHR9LFxuXHRcdGhhczogZnVuY3Rpb24gKGtleSkge1xuXHRcdFx0cmV0dXJuICEhJGNoYW5uZWxEYXRhICYmICRjaGFubmVsRGF0YS5oYXMoa2V5KTtcblx0XHR9LFxuXHRcdHNldDogZnVuY3Rpb24gKGtleSwgdmFsdWUpIHtcblx0XHRcdGlmICghJGNoYW5uZWxEYXRhKSB7XG5cdFx0XHRcdCRjaGFubmVsRGF0YSA9IG1ha2VDaGFubmVsKCk7XG5cdFx0XHR9XG5cblx0XHRcdCRjaGFubmVsRGF0YS5zZXQoa2V5LCB2YWx1ZSk7XG5cdFx0fVxuXHR9O1xuXHQvLyBAdHMtZXhwZWN0LWVycm9yIFRPRE86IGZpZ3VyZSBvdXQgd2h5IHRoaXMgaXMgZXJyb3Jpbmdcblx0cmV0dXJuIGNoYW5uZWw7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/side-channel/index.js\n");

/***/ })

};
;