import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 动态导入工具适配器
    const {
      ALL_FEISHU_CALENDAR_TOOLS
    } = await import('@/lib/tools/tool-adapter');

    // 动态导入工具注册表
    const {
      getToolCategories: getRegistryCategories,
      getToolStats,
      validateToolCompleteness
    } = await import('@/lib/tools/tool-registry');

    // 获取工具分类信息
    const categoryInfo = getRegistryCategories();
    
    // 为每个工具添加分类信息
    const toolsWithCategories = ALL_FEISHU_CALENDAR_TOOLS.map(tool => {
      // 查找工具所属的分类
      const category = categoryInfo.categories.find(cat => 
        cat.tools.includes(tool.name)
      );
      
      return {
        ...tool,
        category: category?.category || 'OTHERS'
      };
    });

    // 获取统计信息
    const stats = getToolStats();
    const validation = validateToolCompleteness();

    return NextResponse.json({
      success: true,
      tools: toolsWithCategories,
      categories: categoryInfo.categories,
      stats,
      validation,
      summary: {
        totalTools: ALL_FEISHU_CALENDAR_TOOLS.length,
        categoriesCount: categoryInfo.categories.length,
        isComplete: validation.isComplete
      }
    });

  } catch (error) {
    console.error('获取工具信息失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '获取工具信息失败',
      details: error instanceof Error ? error.message : '未知错误',
      tools: [],
      categories: [],
      stats: {
        totalTools: 0,
        expectedTotal: 43,
        isComplete: false,
        categories: {},
        commonToolsCount: 0
      }
    }, { status: 500 });
  }
}
