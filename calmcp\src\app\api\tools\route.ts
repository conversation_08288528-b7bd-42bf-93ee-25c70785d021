import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // 动态导入核心工具适配器
    const {
      ALL_FEISHU_CALENDAR_TOOLS
    } = await import('@/lib/tools/tool-adapter-core');

    // 简单的工具分类（基于工具名称）
    const toolsWithCategories = ALL_FEISHU_CALENDAR_TOOLS.map(tool => {
      let category = 'OTHERS';
      if (tool.name.includes('calendar.v4.calendar.')) {
        category = 'CALENDAR_MANAGEMENT';
      } else if (tool.name.includes('calendar.v4.calendarEvent.')) {
        category = 'EVENT_MANAGEMENT';
      }

      return {
        ...tool,
        category
      };
    });

    // 简单的统计信息
    const stats = {
      total: ALL_FEISHU_CALENDAR_TOOLS.length,
      categories: {
        CALENDAR_MANAGEMENT: toolsWithCategories.filter(t => t.category === 'CALENDAR_MANAGEMENT').length,
        EVENT_MANAGEMENT: toolsWithCategories.filter(t => t.category === 'EVENT_MANAGEMENT').length,
        OTHERS: toolsWithCategories.filter(t => t.category === 'OTHERS').length
      }
    };

    return NextResponse.json({
      success: true,
      tools: toolsWithCategories,
      stats,
      summary: {
        totalTools: ALL_FEISHU_CALENDAR_TOOLS.length,
        description: '核心飞书日历工具集 - 7个精选工具'
      }
    });

  } catch (error) {
    console.error('获取工具信息失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '获取工具信息失败',
      details: error instanceof Error ? error.message : '未知错误',
      tools: [],
      categories: [],
      stats: {
        totalTools: 0,
        expectedTotal: 43,
        isComplete: false,
        categories: {},
        commonToolsCount: 0
      }
    }, { status: 500 });
  }
}
