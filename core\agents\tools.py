"""
代理工具定义
实现类似PDeerFlow的工具绑定机制
"""

import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from langchain.tools import tool

logger = logging.getLogger(__name__)


@tool
def handoff_to_planner(
    intent_type: str,
    user_request: str,
    extracted_entities: Optional[Dict[str, Any]] = None,
    confidence: float = 1.0,
) -> Dict[str, Any]:
    """
    将任务移交给规划代理
    类似PDeerFlow的handoff_to_planner工具

    Args:
        intent_type: 意图类型 (calendar, chat, journal, media)
        user_request: 用户原始请求
        extracted_entities: 提取的实体信息
        confidence: 置信度

    Returns:
        移交信息字典
    """
    return {
        "intent_type": intent_type,
        "user_request": user_request,
        "extracted_entities": extracted_entities or {},
        "confidence": confidence,
        "handoff_time": datetime.now().isoformat(),
    }


@tool
def parse_calendar_time(
    text: str, base_time: Optional[str] = None, context: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    解析用户输入中的时间相关信息

    Args:
        text: 用户输入文本
        base_time: 基准时间（ISO格式字符串）
        context: 对话上下文

    Returns:
        解析结果字典
    """
    from utils.time_parser import TimeParser

    # 转换base_time
    base_dt = None
    if base_time:
        try:
            base_dt = datetime.fromisoformat(base_time)
        except:
            base_dt = datetime.now()

    parser = TimeParser()

    # 使用上下文信息辅助解析
    if context and context.get("last_mentioned_date"):
        try:
            base_dt = datetime.fromisoformat(context["last_mentioned_date"])
        except:
            pass

    return {
        "relative_time": parser.parse_relative_time(text, base_dt),
        "time_of_day": parser.parse_time_of_day(text),
        "duration": parser.parse_duration(text),
        "original_text": text,
        "base_time_used": base_dt.isoformat() if base_dt else None,
    }


@tool
def extract_calendar_entities(
    text: str, intent_type: str = "calendar"
) -> Dict[str, Any]:
    """
    从用户输入中提取日历相关实体

    Args:
        text: 用户输入文本
        intent_type: 意图类型

    Returns:
        提取的实体信息
    """
    # 这里可以使用更复杂的NER或LLM提取
    # 暂时使用简单的关键词匹配

    entities = {
        "action": None,  # create, query, update, delete
        "title": None,
        "location": None,
        "attendees": [],
        "description": None,
        "time_expressions": [],
    }

    # 简单的动作识别
    if any(word in text for word in ["创建", "安排", "添加", "新建"]):
        entities["action"] = "create"
    elif any(word in text for word in ["查询", "查看", "显示", "列出"]):
        entities["action"] = "query"
    elif any(word in text for word in ["修改", "更新", "改变"]):
        entities["action"] = "update"
    elif any(word in text for word in ["删除", "取消", "移除"]):
        entities["action"] = "delete"

    # 提取时间表达式
    time_keywords = ["今天", "明天", "后天", "下周", "下月", "点", "时", "分"]
    for keyword in time_keywords:
        if keyword in text:
            entities["time_expressions"].append(keyword)

    return entities


@tool
def confirm_calendar_operation(
    operation: str, event_details: Dict[str, Any], user_id: str
) -> Dict[str, Any]:
    """
    确认日历操作

    Args:
        operation: 操作类型
        event_details: 事件详情
        user_id: 用户ID

    Returns:
        确认信息
    """
    return {
        "operation": operation,
        "event_details": event_details,
        "user_id": user_id,
        "confirmation_required": True,
        "confirmation_message": f"请确认{operation}以下日历事件：{event_details.get('title', '未命名事件')}",
    }


class CalendarTools:
    """日历工具集合"""

    @staticmethod
    def get_coordinator_tools():
        """获取协调器工具"""
        return [handoff_to_planner]

    @staticmethod
    def get_planner_tools():
        """获取规划器工具"""
        return [
            parse_calendar_time,
            extract_calendar_entities,
            confirm_calendar_operation,
        ]

    @staticmethod
    def get_executor_tools():
        """获取执行器工具"""
        # 这里可以添加实际的日历API调用工具
        return []
