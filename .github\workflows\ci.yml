name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  PYTHON_VERSION: '3.10'

jobs:
  test:
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:13
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Cache pip dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-asyncio

    - name: Set up test environment
      run: |
        cp tests/test.env.example tests/test.env
        echo "TESTING=1" >> tests/test.env
        echo "FEISHU_ENV=test" >> tests/test.env
        echo "STORAGE_TYPE=memory" >> tests/test.env

    - name: Run linting
      run: |
        pip install flake8 black isort
        flake8 . --count --select=E9,F63,F7,F82 --show-source --statistics
        black --check .
        isort --check-only .

    - name: Run unit tests
      run: |
        pytest tests/unit/ -v --cov=. --cov-report=xml

    - name: Run integration tests
      run: |
        pytest tests/integration/ -v

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: unittests
        name: codecov-umbrella

  security:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install security tools
      run: |
        pip install bandit safety

    - name: Run security checks
      run: |
        bandit -r . -x tests/
        safety check

  build:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt

    - name: Create deployment package
      run: |
        zip -r deployment.zip . -x "tests/*" "*.git*" "__pycache__/*" "*.pyc"

    - name: Upload deployment artifact
      uses: actions/upload-artifact@v3
      with:
        name: deployment-package
        path: deployment.zip

  deploy-staging:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: staging

    steps:
    - name: Download deployment artifact
      uses: actions/download-artifact@v3
      with:
        name: deployment-package

    - name: Deploy to staging
      run: |
        echo "🚀 Deploying to staging environment..."
        echo "✅ Staging deployment completed"

  deploy-production:
    needs: [build, deploy-staging]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    environment: production

    steps:
    - name: Download deployment artifact
      uses: actions/download-artifact@v3
      with:
        name: deployment-package

    - name: Deploy to production
      env:
        RENDER_API_KEY: ${{ secrets.RENDER_API_KEY }}
        RENDER_SERVICE_ID: ${{ secrets.RENDER_SERVICE_ID }}
      run: |
        echo "🚀 Deploying to production environment..."
        # 这里可以添加实际的Render部署命令
        # curl -X POST "https://api.render.com/v1/services/$RENDER_SERVICE_ID/deploys" \
        #   -H "Authorization: Bearer $RENDER_API_KEY"
        echo "✅ Production deployment completed"

    - name: Notify deployment success
      run: |
        echo "🎉 Production deployment successful!"
        echo "🔗 Application URL: https://feishu-coze-plugin.onrender.com"
