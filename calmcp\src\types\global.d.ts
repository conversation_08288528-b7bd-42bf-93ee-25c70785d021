/**
 * Global type declarations for CalMCP
 */

declare global {
  namespace NodeJS {
    interface ProcessEnv {
      FEISHU_APP_ID: string;
      FEISHU_APP_SECRET: string;
      MCP_SERVER_PORT: string;
      MCP_SERVER_HOST: string;
      NEXT_PUBLIC_API_URL: string;
      NODE_ENV: 'development' | 'production' | 'test';
      LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
      LOG_FILE: string;
      JWT_SECRET: string;
      API_KEY: string;
      RATE_LIMIT_WINDOW_MS: string;
      RATE_LIMIT_MAX_REQUESTS: string;
      STREAM_TIMEOUT_MS: string;
      STREAM_BUFFER_SIZE: string;
    }
  }

  interface Request {
    requestId?: string;
  }
}

// Express 扩展
declare module 'express-serve-static-core' {
  interface Request {
    requestId?: string;
  }
}

export {};
