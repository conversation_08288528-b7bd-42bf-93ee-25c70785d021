#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书智能助手 - 命令行界面
集成llmcal的智能处理能力，提供命令行交互
"""

import argparse
import logging
import os
import sys
from typing import Any, Dict

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from models import ChatRequest

# 导入新架构的模块
from services import ChatService

import httpx
import json
from core.ai.llm_client import get_llm

# 配置日志
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class FeishuAssistantCLI:
    """飞书智能助手命令行界面"""

    def __init__(self):
        """初始化CLI"""
        self.chat_service = ChatService()
        self.session_id = f"cli_session_{os.getpid()}"

    def print_welcome(self):
        """打印欢迎信息"""
        print("=" * 60)
        print("🤖 飞书智能助手 - 命令行版")
        print("=" * 60)
        print("功能特点:")
        print("• 自然语言日历操作 - '明天下午3点安排会议'")
        print("• 智能意图识别 - 自动识别日历、聊天、记录等意图")
        print("• 多轮对话支持 - 上下文感知的智能交互")
        print("• 日程冲突检测 - 自动检测时间冲突")
        print()
        print("系统命令:")
        print("• help - 显示帮助信息")
        print("• list - 列出所有事件")
        print("• export <file> - 导出事件到JSON文件")
        print("• import <file> - 从JSON文件导入事件")
        print("• clear - 清屏")
        print("• exit/quit/q - 退出程序")
        print()
        print("示例:")
        print("• 明天下午3点安排产品评审会议")
        print("• 取消周五的约会")
        print("• 查看下周的行程")
        print("• 把明天的会议改到后天下午2点")
        print("=" * 60)

    def print_help(self):
        """打印帮助信息"""
        print("\n📖 帮助信息")
        print("-" * 40)
        print("🗓️  日历操作:")
        print("  • 创建事件: '明天下午3点安排会议'")
        print("  • 查询事件: '查看明天的日程'")
        print("  • 修改事件: '把会议改到下午4点'")
        print("  • 删除事件: '取消明天的会议'")
        print()
        print("💬 聊天功能:")
        print("  • 日常对话: '你好，今天天气怎么样？'")
        print("  • 获取帮助: '如何使用这个系统？'")
        print()
        print("📝 记录功能:")
        print("  • 记日记: '记录今天的工作总结'")
        print("  • 做笔记: '记录会议要点'")
        print()
        print("⚙️  系统命令:")
        print("  • help - 显示此帮助")
        print("  • list - 列出所有事件")
        print("  • export <file> - 导出数据")
        print("  • import <file> - 导入数据")
        print("  • clear - 清屏")
        print("  • exit - 退出程序")
        print("-" * 40)

    def handle_system_command(self, user_input: str) -> bool:
        """
        处理系统命令

        Args:
            user_input: 用户输入

        Returns:
            bool: 如果是系统命令返回True，否则返回False
        """
        command = user_input.strip().lower()

        if command == "help":
            self.print_help()
            return True
        elif command == "clear":
            os.system("cls" if os.name == "nt" else "clear")
            return True
        elif command in ["exit", "quit", "q"]:
            print("👋 感谢使用飞书智能助手，再见！")
            return True
        elif command == "list":
            print("📋 事件列表功能暂未实现，请使用自然语言查询：'查看我的日程'")
            return True
        elif command.startswith("export "):
            filename = command[7:].strip()
            print(f"📤 导出功能暂未实现，目标文件: {filename}")
            return True
        elif command.startswith("import "):
            filename = command[7:].strip()
            print(f"📥 导入功能暂未实现，源文件: {filename}")
            return True

        return False

    async def process_message(self, message: str) -> Dict[str, Any]:
        """
        处理用户消息

        Args:
            message: 用户消息

        Returns:
            处理结果
        """
        try:
            # 创建聊天请求
            request = ChatRequest(message=message, user_id=self.session_id)

            # 使用聊天服务处理消息
            response = await self.chat_service.process_message(request)

            return {
                "message": response.message,
                "intent": response.intent,
                "data": response.data,
                "success": response.success,
            }

        except Exception as e:
            logger.error(f"处理消息时发生错误: {str(e)}")
            return {"message": f"处理请求时发生错误: {str(e)}", "error": str(e)}

    def run_interactive(self):
        """运行交互模式"""
        self.print_welcome()

        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 > ").strip()

                if not user_input:
                    continue

                # 检查是否是退出命令
                if user_input.lower() in ["exit", "quit", "q"]:
                    print("👋 感谢使用飞书智能助手，再见！")
                    break

                # 处理系统命令
                if self.handle_system_command(user_input):
                    if user_input.lower() in ["exit", "quit", "q"]:
                        break
                    continue

                # 处理智能消息
                import asyncio

                result = asyncio.run(self.process_message(user_input))

                # 显示结果
                if "message" in result:
                    print(f"\n💬 {result['message']}")

                    # 显示额外信息
                    if "intent" in result and result["intent"]:
                        print(f"🎯 识别意图: {result['intent']}")

                    if "data" in result and result["data"]:
                        print(f"📊 相关数据: {result['data']}")
                else:
                    print("\n❌ 无法处理您的请求，请重试。")

            except KeyboardInterrupt:
                print("\n\n👋 程序被中断，退出中...")
                break
            except Exception as e:
                logger.error(f"交互过程中发生错误: {str(e)}")
                print(f"\n❌ 发生错误: {str(e)}")

    def run_single_command(self, message: str):
        """运行单个命令"""
        import asyncio

        result = asyncio.run(self.process_message(message))

        if "message" in result:
            print(result["message"])
        else:
            print("无法处理您的请求")
            return 1

        return 0


async def llm_mcp_interactive_cli():
    print("=" * 60)
    print("🤖 LLM+MCP 智能命令行 (自然语言驱动)")
    print("=" * 60)
    print("输入自然语言，LLM自动理解并调用MCP工具，最后用自然语言返回结果。输入 exit 退出。\n")
    
    # 获取MCP服务URL
    mcp_url = input("请输入MCP服务URL (默认 http://localhost:5000/mcp): ").strip() or "http://localhost:5000/mcp"
    
    # 获取用户ID
    from integrations.storage import get_token
    import os
    
    # 尝试从环境变量获取测试用户ID
    test_user_id = os.getenv("TEST_USER_ID")
    
    if test_user_id:
        print(f"🔍 发现测试用户ID: {test_user_id}")
        use_test_user = input("是否使用测试用户ID? (y/n, 默认y): ").strip().lower() != 'n'
        if use_test_user:
            user_id = test_user_id
        else:
            user_id = input("请输入用户ID: ").strip()
    else:
        user_id = input("请输入用户ID: ").strip()
    
    # 验证用户token是否存在
    token_data = get_token(user_id)
    if not token_data:
        print(f"❌ 未找到用户 {user_id} 的token数据")
        print("💡 请先完成OAuth认证:")
        print("   1. 访问: http://localhost:5000/login")
        print("   2. 完成飞书授权")
        print("   3. 重新运行此脚本")
        return
    
    print(f"✅ 用户 {user_id} 认证有效")
    
    llm = get_llm()
    while True:
        try:
            user_input = input("\n📝 请输入自然语言指令 > ").strip()
            if user_input.lower() in ["exit", "quit", "q"]:
                print("👋 退出LLM+MCP交互模式。")
                break
            
            # 1. 获取MCP工具schema
            async with httpx.AsyncClient() as client:
                tools_resp = await client.post(mcp_url, json={
                    "jsonrpc": "2.0",
                    "id": "cli-tools-list",
                    "method": "tools/list",
                    "params": {}
                })
                tools_data = tools_resp.json()
                tools = tools_data.get("result", {}).get("tools", [])
            
            if not tools:
                print("❌ 未获取到MCP工具定义，无法继续。")
                continue
            
            # 2. 构造LLM消息，包含用户ID信息
            system_prompt = f"你是一个日历助手，能够根据用户输入自动选择合适的MCP工具并构造参数。当前用户ID是: {user_id}"
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_input}
            ]
            
            # 3. LLM推理
            llm_resp = llm.invoke(messages, tools=tools)
            if llm_resp.tool_calls and len(llm_resp.tool_calls) > 0:
                for tool_call in llm_resp.tool_calls:
                    print(f"\n🛠️  LLM建议调用工具: {tool_call.name} 参数: {tool_call.args}")
                    
                    # 确保工具调用包含用户ID
                    if "user_id" not in tool_call.args:
                        tool_call.args["user_id"] = user_id
                        print(f"🔧 自动添加用户ID: {user_id}")
                    
                    # 4. 调用MCP工具
                    mcp_req = {
                        "jsonrpc": "2.0",
                        "id": "cli-tools-call",
                        "method": "tools/call",
                        "params": {
                            "name": tool_call.name,
                            "arguments": tool_call.args
                        }
                    }
                    async with httpx.AsyncClient() as client:
                        mcp_resp = await client.post(mcp_url, json=mcp_req)
                        mcp_data = mcp_resp.json()
                    tool_result = mcp_data.get("result", {}).get("content", [{}])[0].get("text", "")
                    print(f"\n📦 MCP工具原始返回: {tool_result}")
                    
                    # 5. 让LLM总结结果
                    try:
                        summary_messages = [
                            {"role": "system", "content": system_prompt},
                            {"role": "user", "content": user_input},
                            {"role": "function", "name": tool_call.name, "content": tool_result}
                        ]
                        summary_resp = llm.invoke(summary_messages)
                        print(f"\n🤖 智能助手: {summary_resp.content}")
                    except Exception as e:
                        print(f"\n🤖 工具调用结果: {tool_result}")
                        print(f"⚠️  LLM总结失败: {e}")
            else:
                # LLM没有调用工具，直接返回文本回复
                print(f"\n🤖 LLM回复: {llm_resp.content}")
        except KeyboardInterrupt:
            print("\n👋 退出LLM+MCP交互模式。")
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="飞书智能助手 - 命令行界面")
    parser.add_argument("--interactive", "-i", action="store_true", help="启动交互模式")
    parser.add_argument("--message", "-m", type=str, help="处理单个消息")
    parser.add_argument(
        "--version", "-v", action="version", version="飞书智能助手 v1.0.0"
    )
    parser.add_argument("--mcp", action="store_true", help="进入MCP HTTP交互模式")
    parser.add_argument("--llm-mcp", action="store_true", help="进入LLM+MCP智能交互模式")

    args = parser.parse_args()

    # 创建CLI实例
    cli = FeishuAssistantCLI()

    if args.message:
        # 单个消息模式
        return cli.run_single_command(args.message)
    elif args.interactive or len(sys.argv) == 1:
        # 交互模式（默认）
        cli.run_interactive()
        return 0
    elif args.llm_mcp:
        from .llm_mcp_cli import llm_mcp_interactive_cli
        import asyncio
        asyncio.run(llm_mcp_interactive_cli())
    elif args.mcp:
        from .llm_mcp_cli import mcp_interactive_cli
        import asyncio
        asyncio.run(mcp_interactive_cli())
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
