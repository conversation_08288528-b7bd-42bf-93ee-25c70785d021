/**
 * Next.js API Route for MCP Tools
 * GET /api/mcp/tools - 获取工具列表
 */

import { NextRequest, NextResponse } from 'next/server';
import { ALL_TOOLS } from '@/lib/mcp-tools';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    logger.apiRequest('GET', '/api/mcp/tools', { requestId });
    
    return NextResponse.json({
      success: true,
      data: {
        tools: ALL_TOOLS,
        count: ALL_TOOLS.length
      }
    });
    
  } catch (error) {
    logger.error('Failed to get tools list', error as Error);
    
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
