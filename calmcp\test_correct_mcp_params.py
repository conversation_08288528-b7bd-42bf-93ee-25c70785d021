#!/usr/bin/env python3
"""
测试正确的 MCP 参数格式
基于 MCP 服务器的实际实现
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class CorrectMCPTester:
    """正确的 MCP 参数格式测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_token = None
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
    
    def test_mcp_calendar_list(self):
        """测试 MCP 日历列表调用"""
        print(f"\n🧪 测试 MCP 日历列表调用")
        print("="*60)
        
        # 测试1: 基本参数（基于 MCP 服务器实现）
        print(f"\n1️⃣ 测试：基本参数（MCP 服务器期望格式）")
        result1 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 50
        })
        self._analyze_result("基本参数", result1)
        
        # 测试2: 带分页令牌
        print(f"\n2️⃣ 测试：带分页令牌")
        result2 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 100,
            "page_token": ""
        })
        self._analyze_result("带分页令牌", result2)
        
        # 测试3: 大页面大小
        print(f"\n3️⃣ 测试：大页面大小")
        result3 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 500
        })
        self._analyze_result("大页面大小", result3)
        
        # 测试4: 空参数
        print(f"\n4️⃣ 测试：空参数")
        result4 = self._call_mcp_tool("calendar.v4.calendar.list", {})
        self._analyze_result("空参数", result4)
        
        # 测试5: 最小参数
        print(f"\n5️⃣ 测试：最小参数")
        result5 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 10
        })
        self._analyze_result("最小参数", result5)
    
    def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            print(f"📤 调用工具: {tool_name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            print(f"🌐 HTTP 状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误: {response.text}")
                return None
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def _analyze_result(self, format_name: str, result: Dict[str, Any]):
        """分析结果"""
        print(f"📊 {format_name} 结果分析:")
        
        if not result:
            print(f"   ❌ 无响应")
            return
        
        success = result.get('success', False)
        print(f"   成功状态: {'✅' if success else '❌'}")
        
        if success:
            content = result.get('result', {}).get('content', [])
            if content:
                try:
                    text_content = content[0].get('text', '{}')
                    feishu_response = json.loads(text_content)
                    
                    code = feishu_response.get('code', -1)
                    msg = feishu_response.get('msg', '无消息')
                    
                    print(f"   飞书代码: {code}")
                    print(f"   飞书消息: {msg}")
                    
                    if code == 0:
                        data = feishu_response.get('data', {})
                        calendar_list = data.get('calendar_list', [])
                        has_more = data.get('has_more', False)
                        page_token = data.get('page_token', '')
                        
                        print(f"   📅 日历数量: {len(calendar_list)}")
                        print(f"   📄 有更多数据: {has_more}")
                        print(f"   🔗 分页令牌: {page_token[:20] + '...' if page_token else '无'}")
                        
                        if calendar_list:
                            print(f"   📋 日历列表:")
                            for i, cal in enumerate(calendar_list, 1):
                                name = cal.get('summary', '未命名')
                                cal_type = cal.get('type', 'unknown')
                                print(f"     {i}. {name} ({cal_type})")
                    else:
                        print(f"   ❌ 飞书API错误")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   原始内容: {text_content}")
            else:
                print(f"   ❌ 响应内容为空")
        else:
            error = result.get('error', '未知错误')
            print(f"   错误信息: {error}")
    
    def get_all_calendars_correctly(self):
        """使用正确的方式获取所有日历"""
        print(f"\n🎯 使用正确方式获取所有日历")
        print("="*60)
        
        all_calendars = []
        page_token = None
        page_num = 1
        
        while True:
            print(f"\n📄 获取第 {page_num} 页...")
            
            # 构建正确的参数
            arguments = {
                "page_size": 50
            }
            if page_token:
                arguments["page_token"] = page_token
            
            # 调用工具
            result = self._call_mcp_tool("calendar.v4.calendar.list", arguments)
            
            if not result or not result.get('success'):
                print(f"❌ 第 {page_num} 页获取失败")
                break
            
            # 解析响应
            content = result.get('result', {}).get('content', [])
            if not content:
                print(f"❌ 第 {page_num} 页响应内容为空")
                break
            
            try:
                feishu_response = json.loads(content[0].get('text', '{}'))
                
                if feishu_response.get('code') != 0:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                    break
                
                data = feishu_response.get('data', {})
                calendars = data.get('calendar_list', [])
                
                print(f"✅ 第 {page_num} 页获取到 {len(calendars)} 个日历")
                
                # 添加到总列表
                all_calendars.extend(calendars)
                
                # 检查是否有更多数据
                has_more = data.get('has_more', False)
                new_page_token = data.get('page_token', '')
                
                print(f"   📄 分页信息: has_more={has_more}, page_token={new_page_token[:20] + '...' if new_page_token else '无'}")
                
                if not has_more:
                    print(f"✅ 已获取所有数据，共 {page_num} 页")
                    break
                
                # 检查page_token是否变化，避免无限循环
                if new_page_token == page_token:
                    print(f"⚠️  页面令牌未变化，停止分页")
                    break
                
                page_token = new_page_token
                page_num += 1
                
                # 安全限制：最多获取10页
                if page_num > 10:
                    print(f"⚠️  已达到最大页数限制(10页)，停止获取")
                    break
                
            except json.JSONDecodeError as e:
                print(f"❌ 第 {page_num} 页响应解析失败: {e}")
                break
        
        # 显示最终结果
        print(f"\n📊 最终结果:")
        print(f"   总日历数: {len(all_calendars)}")
        
        if all_calendars:
            print(f"\n📅 所有日历:")
            for i, cal in enumerate(all_calendars, 1):
                name = cal.get('summary', '未命名')
                cal_type = cal.get('type', 'unknown')
                cal_id = cal.get('calendar_id', 'N/A')
                print(f"  {i}. {name} ({cal_type})")
                print(f"     ID: {cal_id}")
        
        return all_calendars


def main():
    """主函数"""
    tester = CorrectMCPTester()
    
    if not tester.user_token:
        print("❌ 没有有效的用户访问令牌，无法测试")
        return
    
    # 测试不同参数格式
    tester.test_mcp_calendar_list()
    
    # 获取所有日历
    calendars = tester.get_all_calendars_correctly()
    
    print(f"\n🎉 测试完成！获取到 {len(calendars)} 个日历")


if __name__ == "__main__":
    main()
