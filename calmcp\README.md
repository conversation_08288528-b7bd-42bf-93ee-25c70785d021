# CalMCP - Feishu Calendar MCP Service

基于 Next.js 的飞书日历 MCP (Model Context Protocol) 服务，支持 Streamable 模式，提供完整的日历增删改查操作。

## 🚀 特性

- **Next.js 框架**: 现代化的 React 全栈框架
- **Streamable 模式**: 支持流式响应，适合大数据量处理
- **飞书集成**: 完整的飞书日历 API 集成
- **TypeScript**: 完整的类型安全
- **MCP 协议**: 标准的 Model Context Protocol 实现
- **实时监控**: 内置健康检查和日志系统

## 📋 功能列表

### 日历操作
- `calendar_list` - 获取日历列表
- `calendar_event_create` - 创建日历事件
- `calendar_event_search` - 搜索日历事件
- `calendar_event_update` - 更新日历事件
- `calendar_event_delete` - 删除日历事件
- `calendar_event_get` - 获取单个事件详情
- `calendar_event_list` - 获取事件列表

### API 端点
- `GET /api/health` - 健康检查
- `GET /api/mcp/tools` - 获取工具列表
- `POST /api/mcp/tools/call` - 标准工具调用
- `POST /api/mcp/tools/stream` - 流式工具调用

## 🛠️ 安装和配置

### 1. 安装依赖

```bash
cd calmcp
npm install
```

**注意**: 项目使用官方飞书 SDK `@larksuiteoapi/node-sdk`，确保版本兼容性。

### 2. 环境配置

复制环境变量模板：
```bash
cp .env.example .env.local
```

配置环境变量：
```env
# 飞书应用配置
FEISHU_APP_ID=cli_a76a68f612bf900c
FEISHU_APP_SECRET=your_app_secret_here

# MCP 服务配置
MCP_SERVER_PORT=3002
NEXT_PUBLIC_API_URL=http://localhost:3001

# 其他配置
NODE_ENV=development
LOG_LEVEL=info
```

### 3. 启动服务

开发模式：
```bash
npm run dev
```

生产模式：
```bash
npm run build
npm start
```

独立 MCP 服务器：
```bash
npm run mcp:dev
```

## 📖 使用方法

### 1. 健康检查

```bash
curl http://localhost:3001/api/health
```

### 2. 获取工具列表

```bash
curl http://localhost:3001/api/mcp/tools
```

### 3. 调用工具（标准模式）

```bash
curl -X POST http://localhost:3001/api/mcp/tools/call \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calendar_list",
    "arguments": {
      "page_size": 10
    }
  }'
```

### 4. 调用工具（流式模式）

```bash
curl -X POST http://localhost:3001/api/mcp/tools/stream \
  -H "Content-Type: application/json" \
  -d '{
    "name": "calendar_event_search",
    "arguments": {
      "calendar_id": "your_calendar_id",
      "query": "会议"
    }
  }'
```

## 🏗️ 项目结构

```
calmcp/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API 路由
│   │   │   ├── health/        # 健康检查
│   │   │   └── mcp/           # MCP 相关 API
│   │   ├── page.tsx           # 主页面
│   │   └── layout.tsx         # 布局组件
│   ├── lib/                   # 核心库
│   │   ├── feishu-client.ts   # 飞书 API 客户端
│   │   ├── mcp-tools.ts       # MCP 工具定义
│   │   ├── stream-handler.ts  # 流式处理器
│   │   └── logger.ts          # 日志工具
│   ├── mcp-server/            # 独立 MCP 服务器
│   │   └── index.ts           # 服务器入口
│   └── types/                 # TypeScript 类型定义
│       ├── feishu.ts          # 飞书相关类型
│       └── mcp.ts             # MCP 相关类型
├── package.json
├── tsconfig.json
├── next.config.js
└── README.md
```

## 🔧 开发指南

### 添加新工具

1. 在 `src/lib/mcp-tools.ts` 中定义工具：

```typescript
{
  name: 'your_tool_name',
  description: '工具描述',
  inputSchema: {
    type: 'object',
    properties: {
      // 参数定义
    },
    required: ['required_param']
  }
}
```

2. 在 `src/lib/feishu-client.ts` 中实现功能：

```typescript
async yourToolMethod(args: YourArgsType): Promise<FeishuApiResponse<YourResponseType>> {
  // 实现逻辑
}
```

3. 在 API 路由中添加调用逻辑。

### 自定义流式处理

使用 `StreamHandler` 类创建自定义流：

```typescript
const streamHandler = new StreamHandler();

// 分页流
const paginatedStream = streamHandler.createPaginatedStream(fetcher);

// 批量处理流
const batchStream = streamHandler.createBatchStreamableResponse(items, processor);

// 实时数据流
const realTimeStream = streamHandler.createRealTimeStream(dataSource, interval);
```

## 🚀 部署

### Vercel 部署

1. 连接 GitHub 仓库到 Vercel
2. 配置环境变量
3. 自动部署

### Docker 部署

```dockerfile
FROM node:18-alpine

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

EXPOSE 3001
CMD ["npm", "start"]
```

### Railway 部署

1. 连接 GitHub 仓库
2. 配置环境变量
3. 自动部署

## 📊 监控和日志

### 日志级别
- `error` - 错误信息
- `warn` - 警告信息
- `info` - 一般信息
- `debug` - 调试信息

### 监控指标
- API 响应时间
- 工具调用成功率
- 流式处理性能
- 飞书 API 调用状态

## 🤝 贡献

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 📄 许可证

MIT License

## 🆘 支持

如有问题，请创建 Issue 或联系维护者。
