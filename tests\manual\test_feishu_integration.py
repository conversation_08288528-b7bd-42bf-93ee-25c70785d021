#!/usr/bin/env python3
"""
飞书API集成测试脚本
测试智能日历助手与飞书API的集成功能
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta

from integrations.storage import get_token
from models.chat import ChatRequest
from services.chat_service import ChatService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_intent_recognition():
    """测试意图识别功能"""
    print("\n🧠 测试意图识别功能")
    print("=" * 50)

    chat_service = ChatService()

    test_cases = [
        "明天下午3点安排产品评审会议",
        "查看今天的日程安排",
        "你好",
        "帮我写一首诗",
        "记录今天的工作总结",
    ]

    for i, message in enumerate(test_cases, 1):
        print(f"\n{i}. 测试消息: {message}")

        request = ChatRequest(
            user_id=f"test_user_{i}", message=message  # 每个测试用例使用不同的用户ID
        )

        response = await chat_service.process_message(request)

        print(f"   意图: {response.intent}")
        print(f"   置信度: {response.confidence}")
        print(f"   响应: {response.message[:100]}...")


async def test_multi_agent_workflow():
    """测试多代理工作流"""
    print("\n🤖 测试多代理工作流")
    print("=" * 50)

    chat_service = ChatService()

    test_cases = ["明天下午3点安排产品评审会议", "查看今天的日程", "你好，你能做什么？"]

    for i, message in enumerate(test_cases, 1):
        print(f"\n{i}. 测试消息: {message}")

        request = ChatRequest(
            user_id=f"test_agents_user_{i}",  # 每个测试用例使用不同的用户ID
            message=message,
        )

        # 使用多代理工作流
        response = await chat_service.process_message_with_agents(request)

        print(f"   意图: {response.intent}")
        print(f"   置信度: {response.confidence}")
        print(f"   成功: {response.success}")
        print(f"   响应: {response.message}")

        if response.data:
            print(f"   数据: {json.dumps(response.data, ensure_ascii=False, indent=2)}")


async def test_feishu_token_status():
    """测试飞书Token状态"""
    print("\n🔑 测试飞书Token状态")
    print("=" * 50)

    test_user_ids = ["test_user", "test_user_agents", "default_user"]

    for user_id in test_user_ids:
        print(f"\n检查用户 {user_id} 的Token状态:")

        try:
            token_data = get_token(user_id)
            if token_data:
                print(f"   ✅ 找到Token数据")
                print(f"   访问令牌: {token_data.get('access_token', 'N/A')[:20]}...")
                print(f"   过期时间: {token_data.get('access_token_expire', 'N/A')}")

                # 检查是否过期
                expire_time = token_data.get("access_token_expire")
                if expire_time:
                    current_time = datetime.now().timestamp()
                    if current_time < expire_time:
                        print(f"   🟢 Token有效")
                    else:
                        print(f"   🔴 Token已过期")
                else:
                    print(f"   ⚠️ 无过期时间信息")
            else:
                print(f"   ❌ 未找到Token数据")

        except Exception as e:
            print(f"   ❌ 获取Token失败: {e}")


async def test_time_parsing():
    """测试时间解析功能"""
    print("\n⏰ 测试时间解析功能")
    print("=" * 50)

    from utils.time_parser import TimeParser

    time_parser = TimeParser()

    test_cases = [
        "明天下午3点",
        "下周一上午9点半",
        "今天晚上8点",
        "后天中午12点",
        "下个月15号下午2点",
    ]

    for i, time_text in enumerate(test_cases, 1):
        print(f"\n{i}. 解析时间: {time_text}")

        try:
            result = time_parser.parse(time_text)
            print(f"   结果: {result}")

            if hasattr(result, "start_time") and result.start_time:
                print(f"   开始时间: {result.start_time}")
            if hasattr(result, "end_time") and result.end_time:
                print(f"   结束时间: {result.end_time}")

        except Exception as e:
            print(f"   ❌ 解析失败: {e}")


async def test_conversation_flow():
    """测试完整对话流程"""
    print("\n💬 测试完整对话流程")
    print("=" * 50)

    chat_service = ChatService()
    user_id = "test_conversation_user"

    conversation_steps = [
        "你好",
        "明天下午3点安排会议",
        "改成下午4点",
        "确认",
        "查看明天的日程",
    ]

    for i, message in enumerate(conversation_steps, 1):
        print(f"\n步骤 {i}: {message}")

        request = ChatRequest(user_id=user_id, message=message)

        # 使用多代理工作流
        response = await chat_service.process_message_with_agents(request)

        print(f"   响应: {response.message}")
        print(f"   状态: {response.context.get('state', 'unknown')}")

        # 模拟用户思考时间
        await asyncio.sleep(0.5)


async def main():
    """主测试函数"""
    print("🚀 开始飞书API集成测试")
    print("=" * 60)

    try:
        # 1. 测试意图识别
        await test_intent_recognition()

        # 2. 测试多代理工作流
        await test_multi_agent_workflow()

        # 3. 测试飞书Token状态
        await test_feishu_token_status()

        # 4. 测试时间解析
        await test_time_parsing()

        # 5. 测试完整对话流程
        await test_conversation_flow()

        print("\n✅ 所有测试完成")

    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
