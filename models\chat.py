"""
聊天相关数据模型
"""

from typing import Any, Dict, List, Optional

from .base import BaseModel


class ChatRequest(BaseModel):
    """智能聊天请求模型"""

    message: str
    user_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """智能聊天响应模型"""

    message: str
    intent: Optional[str] = None
    confidence: Optional[float] = None
    data: Optional[Dict[str, Any]] = None
    success: bool = True
    context: Optional[Dict[str, Any]] = None


class LLMResponse(BaseModel):
    """LLM响应模型"""

    content: str
    tool_calls: List["ToolCall"] = []


class ToolCall(BaseModel):
    """工具调用模型"""

    name: str
    args: Dict[str, Any]
    id: Optional[str] = None
