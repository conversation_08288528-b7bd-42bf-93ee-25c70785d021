#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真正的SSE MCP客户端
使用Server-Sent Events连接到您的飞书MCP服务
"""

import asyncio
import json
import logging
import httpx
from typing import Dict, List, Any, Optional
import re

logger = logging.getLogger(__name__)


class SSEMCPClient:
    """SSE MCP客户端 - 使用Server-Sent Events连接"""
    
    def __init__(self, sse_url: str = "http://127.0.0.1:3000/sse"):
        self.sse_url = sse_url
        self.is_connected = False
        self.tools_cache = []
        self.session = None
        self.request_id = 0
        self.token = "t-g1047fnxRJ5H7UO6CBEUULYTTZCJWWYX6IYDROW4"
        
    async def start_mcp_service(self):
        """连接到MCP服务"""
        try:
            logger.info(f"连接到飞书MCP SSE服务: {self.sse_url}")
            
            # 创建HTTP会话
            self.session = httpx.AsyncClient(timeout=30.0)
            
            # 测试SSE连接
            await self._test_sse_connection()
            
            self.is_connected = True
            logger.info(f"✅ 成功连接到飞书MCP服务")
            
        except Exception as e:
            logger.error(f"连接MCP服务失败: {str(e)}")
            raise
    
    async def _test_sse_connection(self):
        """测试SSE连接并获取工具列表"""
        try:
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache"
            }
            
            logger.info("开始SSE连接...")
            
            # 使用stream方式连接SSE
            async with self.session.stream('GET', self.sse_url, headers=headers) as response:
                logger.info(f"SSE响应状态: {response.status_code}")
                logger.info(f"SSE响应头: {dict(response.headers)}")
                
                if response.status_code != 200:
                    error_text = await response.aread()
                    raise Exception(f"SSE连接失败: {response.status_code}, {error_text.decode()}")
                
                # 发送初始化请求
                await self._send_initialize_request()
                
                # 读取SSE事件
                buffer = ""
                async for chunk in response.aiter_text():
                    buffer += chunk
                    
                    # 处理完整的SSE事件
                    while "\\n\\n" in buffer:
                        event, buffer = buffer.split("\\n\\n", 1)
                        await self._process_sse_event(event)
                        
                        # 如果已经获取到工具列表，就可以退出了
                        if self.tools_cache:
                            logger.info("已获取工具列表，结束SSE连接测试")
                            return
                            
        except Exception as e:
            logger.error(f"SSE连接测试失败: {str(e)}")
            raise
    
    async def _send_initialize_request(self):
        """发送初始化请求"""
        # 对于SSE，我们可能需要通过其他方式发送请求
        # 先尝试通过POST发送初始化请求
        try:
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "feishu-calendar-assistant",
                        "version": "1.0.0"
                    }
                }
            }
            
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            # 尝试发送到可能的POST端点
            post_url = self.sse_url.replace("/sse", "/rpc")
            response = await self.session.post(post_url, json=init_request, headers=headers)
            logger.info(f"初始化请求发送到 {post_url}: {response.status_code}")
            
        except Exception as e:
            logger.warning(f"发送初始化请求失败: {e}")
    
    async def _process_sse_event(self, event_data: str):
        """处理SSE事件"""
        try:
            lines = event_data.strip().split("\\n")
            data_line = None
            
            for line in lines:
                if line.startswith("data: "):
                    data_line = line[6:]  # 移除 "data: " 前缀
                    break
            
            if data_line:
                try:
                    event_json = json.loads(data_line)
                    logger.info(f"收到SSE事件: {event_json}")
                    
                    # 如果是工具列表响应
                    if event_json.get("method") == "tools/list" or "tools" in event_json.get("result", {}):
                        tools = event_json.get("result", {}).get("tools", [])
                        for tool in tools:
                            self.tools_cache.append({
                                "name": tool.get("name", ""),
                                "description": tool.get("description", ""),
                                "schema": tool.get("inputSchema", {})
                            })
                        logger.info(f"获取到 {len(tools)} 个工具")
                        
                except json.JSONDecodeError:
                    logger.debug(f"非JSON数据: {data_line}")
                    
        except Exception as e:
            logger.error(f"处理SSE事件失败: {e}")
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            # 查找日历列表工具
            calendar_tools = [t for t in self.tools_cache if "calendar.list" in t["name"]]
            
            if not calendar_tools:
                # 如果没有缓存的工具，尝试直接调用
                return await self._call_calendar_api("calendar.v4.calendar.list", {})
            
            tool_name = calendar_tools[0]["name"]
            return await self._call_calendar_api(tool_name, {})
            
        except Exception as e:
            logger.error(f"获取日历列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历列表失败: {str(e)}"
            }
    
    async def _call_calendar_api(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用日历API"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id + 1,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Content-Type": "application/json"
            }
            
            # 尝试不同的端点
            endpoints = ["/rpc", "/mcp", ""]
            for endpoint in endpoints:
                try:
                    url = self.sse_url.replace("/sse", endpoint)
                    response = await self.session.post(url, json=request, headers=headers)
                    
                    if response.status_code == 200:
                        result = response.json()
                        logger.info(f"API调用成功: {result}")
                        
                        calendars = result.get("result", {}).get("data", {}).get("calendar_list", [])
                        return {
                            "success": True,
                            "calendars": calendars,
                            "message": f"获取到 {len(calendars)} 个日历",
                            "raw_result": result
                        }
                        
                except Exception as e:
                    logger.debug(f"端点 {endpoint} 失败: {e}")
                    continue
            
            return {
                "success": False,
                "message": "所有API端点都失败"
            }
            
        except Exception as e:
            logger.error(f"调用日历API失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"API调用失败: {str(e)}"
            }
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.aclose()
        self.is_connected = False
        logger.info("SSE MCP客户端连接已关闭")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool["name"] for tool in self.tools_cache]


# 测试代码已移除，保持代码整洁
