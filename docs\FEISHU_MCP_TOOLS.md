# 飞书MCP工具定义

## 概述

本文档定义了飞书MCP服务器提供的所有工具接口，包括工具描述、参数定义和响应格式。

## 工具列表

### 1. list_calendars - 获取日历列表

**描述**: 获取用户的所有日历列表

**参数**:
```json
{
  "user_id": "string (可选) - 用户ID，如果不提供则使用默认用户"
}
```

**响应示例**:
```json
{
  "success": true,
  "calendars": [
    {
      "id": "cal_123456",
      "name": "工作日历",
      "description": "工作相关的日程安排",
      "color": "#1976d2",
      "permissions": "private"
    }
  ]
}
```

### 2. get_calendar_events - 获取日历事件

**描述**: 获取指定日历的事件列表

**参数**:
```json
{
  "user_id": "string (必需) - 用户ID",
  "calendar_id": "string (可选) - 日历ID，默认为primary",
  "start_time": "string (可选) - 开始时间 (ISO格式)",
  "end_time": "string (可选) - 结束时间 (ISO格式)"
}
```

**响应示例**:
```json
{
  "success": true,
  "events": [
    {
      "id": "event_123456",
      "title": "项目会议",
      "description": "讨论项目进度",
      "start_time": "2025-07-16T14:00:00+08:00",
      "end_time": "2025-07-16T15:00:00+08:00",
      "location": "会议室A",
      "attendees": ["<EMAIL>", "<EMAIL>"]
    }
  ]
}
```

### 3. create_calendar_event - 创建日历事件

**描述**: 创建新的日历事件

**参数**:
```json
{
  "user_id": "string (必需) - 用户ID",
  "calendar_id": "string (可选) - 日历ID，默认为primary",
  "title": "string (必需) - 事件标题",
  "description": "string (可选) - 事件描述",
  "start_time": "string (必需) - 开始时间 (ISO格式)",
  "end_time": "string (必需) - 结束时间 (ISO格式)",
  "location": "string (可选) - 事件地点",
  "attendees": "array (可选) - 参与者邮箱列表"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "事件创建成功",
  "event_id": "event_123456"
}
```

### 4. update_calendar_event - 更新日历事件

**描述**: 更新现有的日历事件

**参数**:
```json
{
  "user_id": "string (必需) - 用户ID",
  "event_id": "string (必需) - 事件ID",
  "calendar_id": "string (可选) - 日历ID",
  "title": "string (可选) - 事件标题",
  "description": "string (可选) - 事件描述",
  "start_time": "string (可选) - 开始时间 (ISO格式)",
  "end_time": "string (可选) - 结束时间 (ISO格式)",
  "location": "string (可选) - 事件地点"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "事件更新成功"
}
```

### 5. delete_calendar_event - 删除日历事件

**描述**: 删除指定的日历事件

**参数**:
```json
{
  "user_id": "string (必需) - 用户ID",
  "event_id": "string (必需) - 事件ID",
  "calendar_id": "string (可选) - 日历ID"
}
```

**响应示例**:
```json
{
  "success": true,
  "message": "事件删除成功"
}
```

### 6. get_today_events - 获取今天的事件

**描述**: 获取今天的所有日历事件

**参数**:
```json
{
  "user_id": "string (必需) - 用户ID"
}
```

**响应示例**:
```json
{
  "success": true,
  "events": [
    {
      "id": "event_123456",
      "title": "晨会",
      "start_time": "2025-07-16T09:00:00+08:00",
      "end_time": "2025-07-16T09:30:00+08:00",
      "location": "会议室B"
    }
  ]
}
```

## 错误处理

当工具调用失败时，会返回以下格式的错误响应：

```json
{
  "success": false,
  "error": "错误描述信息"
}
```

## 时间格式

所有时间参数都应使用ISO 8601格式，例如：
- `2025-07-16T14:00:00+08:00` (带时区)
- `2025-07-16T14:00:00Z` (UTC时间)
- `2025-07-16T14:00:00` (本地时间)

## 使用示例

### 创建事件示例

```json
{
  "name": "create_calendar_event",
  "arguments": {
    "user_id": "user123",
    "title": "项目评审会议",
    "description": "Q3项目评审",
    "start_time": "2025-07-16T14:00:00+08:00",
    "end_time": "2025-07-16T16:00:00+08:00",
    "location": "大会议室",
    "attendees": ["<EMAIL>", "<EMAIL>"]
  }
}
```

### 查询今天事件示例

```json
{
  "name": "get_today_events",
  "arguments": {
    "user_id": "user123"
  }
}
```
