import logging
import os
import traceback

from config import SUPABASE_KEY, SUPABASE_URL
from integrations.storage.supabase_client import get_supabase_client

logger = logging.getLogger(__name__)


def create_tables():
    """创建Supabase中的feishu_tokens表"""
    try:
        logger.info(f"正在连接到 Supabase: {SUPABASE_URL}")
        supabase = get_supabase_client()

        # 表结构SQL
        sql = """
        CREATE TABLE IF NOT EXISTS feishu_tokens (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            app_id VARCHAR NOT NULL,
            tenant_key VARCHAR NOT NULL,
            access_token TEXT NOT NULL,
            refresh_token TEXT NOT NULL,
            expires_at BIGINT NOT NULL,
            created_at BIGINT NOT NULL,
            updated_at BIGINT NOT NULL,
            UNIQUE (app_id, tenant_key)
        );
        """

        # 先尝试获取一条记录，检查表是否可访问
        logger.info("检查表是否可访问...")
        try:
            # 使用兼容的方式检查表是否存在
            # 在Supabase 2.16.0中，使用select方法
            result = supabase.table("feishu_tokens").select("*").execute()
            logger.info(f"表已存在，查询成功")
        except Exception as e:
            logger.info(f"表可能不存在或无法访问: {e}")

            # 尝试创建表
            try:
                logger.info("尝试通过函数创建表...")
                # 在Supabase 2.16.0中，使用functions调用
                result = supabase.functions.invoke("create-feishu-tokens-table", {})
                logger.info(f"函数调用结果: {result}")
            except Exception as create_e:
                logger.warning(f"无法通过函数创建表: {create_e}")
                logger.info("请手动创建表或检查Supabase权限设置")
                logger.info(f"表结构SQL: {sql}")

        logger.info("表初始化流程完成")
        return True
    except Exception as e:
        logger.error(f"初始化数据库表失败: {str(e)}")
        logger.error(traceback.format_exc())
        return False


if __name__ == "__main__":
    # 检查环境变量
    if not SUPABASE_URL or not SUPABASE_KEY:
        logger.error("请设置SUPABASE_URL和SUPABASE_KEY环境变量")
        exit(1)

    # 创建表
    if create_tables():
        logger.info("数据库初始化完成")
    else:
        logger.error("数据库初始化失败")
        exit(1)
