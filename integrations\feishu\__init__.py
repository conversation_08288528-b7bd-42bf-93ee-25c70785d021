"""
飞书集成模块
包含飞书API客户端、OAuth客户端、日历客户端等
"""

from .api_client import *
from .calendar_client import FeishuCalendarLark
from .client_factory import (
    FeishuClientFactory,
    FeishuClientManager,
    ClientType,
    get_feishu_client,
    get_calendar_client,
    client_manager,
)
from .oauth_client import *

__all__ = [
    # API客户端相关
    "exchange_code",
    "refresh_token",
    "get_today_events",
    "get_user_info",
    "get_calendars",
    "get_calendar_detail",
    "get_calendar_events",
    "create_calendar",
    "update_calendar",
    "delete_calendar",
    "subscribe_calendar",
    "unsubscribe_calendar",
    "get_event_detail",
    "create_event",
    "update_event",
    "delete_event",
    "search_calendar_events",
    "batch_create_events",
    "batch_update_events",
    "batch_delete_events",
    "create_daily_task",
    "create_task_for_date",
    "get_week_events",
    "convert_to_timestamp",
    # OAuth客户端
    "get_authorize_url",
    "fetch_access_token",
    "refresh_access_token",
    # 日历客户端
    "FeishuCalendarLark",
    # 客户端工厂相关
    "FeishuClientFactory",
    "FeishuClientManager",
    "ClientType",
    "get_feishu_client",
    "get_calendar_client",
    "client_manager",
    # 异常类
    "FeishuAPIError",
    "FeishuNetworkError",
    "FeishuAuthError",
]
