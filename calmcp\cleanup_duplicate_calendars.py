#!/usr/bin/env python3
"""
清理重复测试日历脚本
删除重复的测试日历，保留一个作为参考
"""

import requests
import json
import time
from typing import List, Dict, Any

class DuplicateCalendarCleaner:
    def __init__(self):
        self.base_url = "http://localhost:3000/api/mcp/tools/call"
        self.headers = {
            "Content-Type": "application/json"
        }
        
    def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用MCP工具"""
        payload = {
            "name": tool_name,
            "arguments": arguments or {}
        }
        
        try:
            response = requests.post(self.base_url, headers=self.headers, json=payload)
            response.raise_for_status()
            result = response.json()
            
            # 解析MCP响应格式
            if result.get("success") and "result" in result:
                content = result["result"].get("content", [])
                if content and content[0].get("type") == "text":
                    try:
                        # 解析文本内容中的JSON
                        text_content = content[0]["text"]
                        parsed_data = json.loads(text_content)
                        return parsed_data
                    except json.JSONDecodeError:
                        print(f"❌ JSON解析失败: {text_content}")
                        return None
            
            return result
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
            return None

    def get_all_calendars(self) -> List[Dict[str, Any]]:
        """获取所有日历"""
        all_calendars = []
        page_token = None
        page_count = 0
        
        while True:
            page_count += 1
            print(f"📅 正在获取第 {page_count} 页日历列表...")
            
            arguments = {"page_size": 50}
            if page_token:
                arguments["page_token"] = page_token
                
            result = self.call_tool("calendar_list", arguments)
            
            if not result or "data" not in result:
                print("❌ 获取日历列表失败")
                break
                
            data = result["data"]
            calendars = data.get("calendar_list", [])
            all_calendars.extend(calendars)
            
            print(f"✅ 第 {page_count} 页获取到 {len(calendars)} 个日历")
            
            # 检查是否有更多数据
            if not data.get("has_more", False):
                print(f"📄 已获取所有页面，共 {len(all_calendars)} 个日历")
                break
                
            page_token = data.get("page_token")
            if not page_token:
                print("📄 没有更多页面")
                break
                
            time.sleep(0.5)  # 避免请求过快
            
        return all_calendars

    def find_duplicate_calendars(self, calendars: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """查找重复的日历"""
        title_groups = {}
        
        for calendar in calendars:
            summary = calendar.get("summary", "").strip()
            summary_alias = calendar.get("summary_alias", "").strip()
            title = summary or summary_alias
            
            if title:
                if title not in title_groups:
                    title_groups[title] = []
                title_groups[title].append(calendar)
        
        # 只返回有重复的标题组
        duplicates = {title: calendars for title, calendars in title_groups.items() 
                     if len(calendars) > 1}
        
        return duplicates

    def delete_calendar(self, calendar_id: str, summary: str = "") -> bool:
        """删除日历"""
        print(f"🗑️  正在删除日历: {summary} ({calendar_id})")
        
        result = self.call_tool("calendar_delete", {"calendar_id": calendar_id})
        
        if result and "data" in result:
            print(f"✅ 删除成功: {summary}")
            return True
        else:
            print(f"❌ 删除失败: {summary}")
            if result:
                print(f"   错误信息: {result}")
            return False

    def cleanup_duplicates(self, dry_run: bool = True) -> None:
        """清理重复日历"""
        print("🚀 开始清理重复日历")
        print("=" * 50)
        
        # 获取所有日历
        calendars = self.get_all_calendars()
        if not calendars:
            print("❌ 没有获取到任何日历")
            return
            
        print(f"📊 总共获取到 {len(calendars)} 个日历")
        print()
        
        # 查找重复日历
        duplicates = self.find_duplicate_calendars(calendars)
        
        if not duplicates:
            print("✅ 没有发现重复的日历")
            return
            
        print(f"🎯 发现 {len(duplicates)} 组重复日历:")
        for title, calendar_list in duplicates.items():
            print(f"   📅 '{title}': {len(calendar_list)} 个")
        print()
        
        # 显示重复日历详情
        print("📋 重复日历详情:")
        print("-" * 50)
        for title, calendar_list in duplicates.items():
            print(f"📅 '{title}' ({len(calendar_list)} 个):")
            for i, calendar in enumerate(calendar_list, 1):
                calendar_id = calendar.get("calendar_id")
                calendar_type = calendar.get("type", "")
                permissions = calendar.get("permissions", "")
                description = calendar.get("description", "").strip()
                
                print(f"   {i}. {calendar_id}")
                print(f"      类型: {calendar_type}")
                print(f"      权限: {permissions}")
                if description:
                    print(f"      描述: {description}")
                print()
        
        if dry_run:
            print("🔍 这是预览模式，不会实际删除日历")
            print("   要实际删除，请运行: python cleanup_duplicate_calendars.py --execute")
            return
            
        # 实际删除操作
        print("⚠️  即将删除重复日历（保留第一个）:")
        for title, calendar_list in duplicates.items():
            print(f"   📅 '{title}': 保留第1个，删除其余 {len(calendar_list)-1} 个")
        
        confirm = input("\n❓ 确认删除这些重复日历吗？(输入 'yes' 确认): ")
        if confirm.lower() != 'yes':
            print("❌ 操作已取消")
            return
            
        print("\n🗑️  开始删除重复日历...")
        total_deleted = 0
        total_failed = 0
        
        for title, calendar_list in duplicates.items():
            print(f"\n📅 处理 '{title}':")
            
            # 保留第一个，删除其余的
            for i, calendar in enumerate(calendar_list[1:], 2):
                calendar_id = calendar.get("calendar_id")
                summary = calendar.get("summary", "").strip()
                
                print(f"   删除第 {i} 个: {calendar_id}")
                if self.delete_calendar(calendar_id, summary):
                    total_deleted += 1
                else:
                    total_failed += 1
                    
                time.sleep(1)  # 避免请求过快
                
        print("\n" + "=" * 50)
        print("📊 删除结果:")
        print(f"   ✅ 成功删除: {total_deleted} 个")
        print(f"   ❌ 删除失败: {total_failed} 个")

def main():
    import argparse
    
    parser = argparse.ArgumentParser(description="清理重复日历")
    parser.add_argument("--execute", action="store_true", 
                       help="实际执行删除操作（默认是预览模式）")
    
    args = parser.parse_args()
    
    cleaner = DuplicateCalendarCleaner()
    cleaner.cleanup_duplicates(dry_run=not args.execute)

if __name__ == "__main__":
    main() 