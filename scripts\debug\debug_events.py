#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试事件数据结构
"""

import asyncio
import json
import logging

from integrations.feishu import get_today_events
from integrations.storage import get_token

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_USER_ID = "ou_57792fdcc9ab9970117ac3dbdf8a5b25"
REAL_CALENDAR_ID = "<EMAIL>"


async def debug_events():
    """调试事件数据"""
    logger.info("🔍 开始调试事件数据")

    # 获取用户token
    token_data = get_token(TEST_USER_ID)
    if not token_data:
        logger.error("❌ 未找到用户token，请先授权")
        return

    access_token = token_data["access_token"]
    logger.info(f"✅ 获取到用户token: {access_token[:20]}...")

    # 获取今日事件
    logger.info("📆 获取今日事件...")
    result = await get_today_events(access_token, REAL_CALENDAR_ID)

    logger.info("📋 完整的API响应:")
    logger.info(json.dumps(result, ensure_ascii=False, indent=2))

    if result.get("code") == 0:
        events = result.get("data", {}).get("items", [])
        logger.info(f"✅ 找到 {len(events)} 个事件")

        for i, event in enumerate(events):
            logger.info(f"\n📝 事件 {i+1}:")
            logger.info(
                f"   完整事件数据: {json.dumps(event, ensure_ascii=False, indent=4)}"
            )

            # 检查各种可能的标题字段
            possible_title_fields = [
                "summary",
                "title",
                "subject",
                "name",
                "description",
            ]
            for field in possible_title_fields:
                if field in event:
                    logger.info(f"   {field}: {event[field]}")

            # 检查开始时间字段
            if "start_time" in event:
                start_time = event["start_time"]
                logger.info(
                    f"   开始时间数据: {json.dumps(start_time, ensure_ascii=False, indent=4)}"
                )

            # 检查所有字段
            logger.info(f"   所有字段: {list(event.keys())}")

            if i >= 2:  # 只显示前3个事件的详细信息
                break
    else:
        logger.error(f"❌ 获取今日事件失败: {result}")


if __name__ == "__main__":
    asyncio.run(debug_events())
