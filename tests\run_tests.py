"""
测试运行脚本
提供统一的测试执行入口，支持不同类型的测试
"""

import argparse
import os
import sys
import time
from pathlib import Path

import pytest

# 确保可以导入项目模块
PROJECT_ROOT = Path(__file__).parent.parent
sys.path.insert(0, str(PROJECT_ROOT))

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_files = [
        PROJECT_ROOT / ".env",
        PROJECT_ROOT / ".env.local",
        PROJECT_ROOT / ".env.test",
        Path(__file__).parent / "test.env"
    ]

    for env_file in env_files:
        if env_file.exists():
            load_dotenv(env_file)
            print(f"📁 已加载环境文件: {env_file}")
            break
    else:
        print("⚠️  未找到.env文件，将使用系统环境变量")

except ImportError:
    print("⚠️  python-dotenv未安装，将使用系统环境变量")


def check_environment():
    """检查测试环境"""
    print("\n🔍 检查测试环境")
    print("="*50)

    # 设置测试环境标志
    os.environ["TESTING"] = "true"

    # 检查必要的环境变量
    required_vars = ["FEISHU_CLIENT_ID", "FEISHU_CLIENT_SECRET"]
    optional_vars = ["TEST_ACCESS_TOKEN", "TEST_USER_ID", "ALLOW_NETWORK_TESTS"]

    missing_required = []
    for var in required_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var:25} | 已设置")
        else:
            print(f"❌ {var:25} | 未设置")
            missing_required.append(var)

    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var:25} | 已设置 (可选)")
        else:
            print(f"⚪ {var:25} | 未设置 (可选)")

    if missing_required:
        print(f"\n❌ 缺少必需的环境变量: {', '.join(missing_required)}")
        print("请在.env文件中设置这些变量")
        return False

    print("\n✅ 环境检查通过")
    return True


def run_tests(test_type="unit", verbose=False, coverage=False):
    """运行测试"""
    print(f"\n🚀 开始运行测试")
    print(f"测试类型: {test_type}")
    print("="*50)

    # 检查环境
    if not check_environment():
        return 1

    # 构建pytest参数
    pytest_args = []

    # 设置测试路径
    test_dir = Path(__file__).parent
    if test_type == "basic":
        pytest_args.append(str(test_dir / "test_basic_functionality.py"))
    elif test_type == "business":
        pytest_args.append(str(test_dir / "test_real_business_scenarios.py"))
    elif test_type == "network":
        pytest_args.append(str(test_dir / "test_network_integration.py"))
    elif test_type == "unit":
        pytest_args.append(str(test_dir / "unit"))
    elif test_type == "integration":
        pytest_args.append(str(test_dir / "integration"))
    elif test_type == "e2e":
        pytest_args.append(str(test_dir / "e2e"))
    elif test_type == "performance":
        pytest_args.append(str(test_dir / "performance"))
    elif test_type == "all":
        pytest_args.append(str(test_dir))
    else:
        pytest_args.append(str(test_dir / "test_basic_functionality.py"))

    # 设置输出格式
    if verbose:
        pytest_args.extend(["-v", "--tb=short"])
    else:
        pytest_args.extend(["--tb=line"])

    # 设置覆盖率
    if coverage:
        pytest_args.extend([
            "--cov=integrations",
            "--cov=api",
            "--cov=services",
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov"
        ])

    # 添加其他有用的选项
    pytest_args.extend([
        "--strict-markers",
        "--disable-warnings"
    ])

    start_time = time.time()

    print(f"执行命令: pytest {' '.join(pytest_args)}")
    result = pytest.main(pytest_args)

    duration = time.time() - start_time
    print(f"\n⏱️  测试执行时间: {duration:.2f}秒")

    if result == 0:
        print("✅ 所有测试通过")
    else:
        print("❌ 部分测试失败")

    return result


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="飞书日历插件测试运行器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python run_tests.py                    # 运行基础功能测试（推荐）
  python run_tests.py --type basic -v   # 运行基础功能测试，详细输出
  python run_tests.py --type business   # 运行业务场景测试
  python run_tests.py --type network    # 运行网络集成测试
  python run_tests.py --type all -v     # 运行所有测试，详细输出
  python run_tests.py --coverage        # 运行测试并生成覆盖率报告
        """
    )

    parser.add_argument(
        "--type",
        choices=["basic", "business", "network", "unit", "integration", "e2e", "performance", "all"],
        default="basic",
        help="测试类型: basic=基础功能测试, business=业务场景测试, network=网络集成测试, unit=单元测试, integration=集成测试, e2e=端到端测试, performance=性能测试, all=所有测试"
    )

    parser.add_argument(
        "-v", "--verbose",
        action="store_true",
        help="显示详细的测试输出"
    )

    parser.add_argument(
        "--coverage",
        action="store_true",
        help="生成代码覆盖率报告"
    )

    args = parser.parse_args()

    print("🎯 飞书日历插件测试运行器")
    print(f"开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")

    result = run_tests(args.type, args.verbose, args.coverage)

    if args.coverage and result == 0:
        print("\n📊 覆盖率报告已生成到 htmlcov/ 目录")

    sys.exit(result)


if __name__ == "__main__":
    main()
