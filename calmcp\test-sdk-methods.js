// 测试飞书SDK中可用的日历事件方法
const lark = require('@larksuiteoapi/node-sdk');

// 创建客户端
const client = new lark.Client({
  appId: 'test',
  appSecret: 'test'
});

console.log('🔍 检查飞书SDK中的日历事件方法...');

// 检查 calendar.v4.calendarEvent 对象的所有方法
const calendarEvent = client.calendar.v4.calendarEvent;

console.log('\n📋 calendar.v4.calendarEvent 对象的所有属性和方法:');
console.log(Object.getOwnPropertyNames(calendarEvent));

console.log('\n🔍 检查具体方法:');
console.log('list:', typeof calendarEvent.list);
console.log('get:', typeof calendarEvent.get);
console.log('create:', typeof calendarEvent.create);
console.log('patch:', typeof calendarEvent.patch);
console.log('delete:', typeof calendarEvent.delete);
console.log('instanceView:', typeof calendarEvent.instanceView);
console.log('instance_view:', typeof calendarEvent.instance_view);
console.log('instances:', typeof calendarEvent.instances);

// 检查原型链上的方法
console.log('\n🔍 检查原型链上的方法:');
const proto = Object.getPrototypeOf(calendarEvent);
console.log('原型方法:', Object.getOwnPropertyNames(proto));

// 尝试查看所有可能的方法名
console.log('\n🔍 尝试查找包含 "instance" 的方法:');
const allProps = [];
let obj = calendarEvent;
while (obj) {
  allProps.push(...Object.getOwnPropertyNames(obj));
  obj = Object.getPrototypeOf(obj);
}

const instanceMethods = allProps.filter(prop => 
  prop.toLowerCase().includes('instance') && 
  typeof calendarEvent[prop] === 'function'
);

console.log('包含 "instance" 的方法:', instanceMethods);
