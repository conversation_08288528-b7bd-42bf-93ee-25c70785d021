#!/usr/bin/env python3
"""
直接调用飞书API测试
绕过MCP工具，直接使用飞书SDK验证日历数据
"""

import sys
import os
from typing import Dict, Any, Optional, List

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")

# 导入飞书SDK
try:
    import lark_oapi as lark
    from lark_oapi.api.calendar.v4 import *
    print("✅ 飞书SDK导入成功")
except ImportError as e:
    print(f"❌ 飞书SDK导入失败: {e}")
    sys.exit(1)


class DirectFeishuTester:
    """直接调用飞书API的测试器"""
    
    def __init__(self):
        self.app_id = os.environ.get('FEISHU_CLIENT_ID')
        self.app_secret = os.environ.get('FEISHU_CLIENT_SECRET')
        self.user_access_token = None
        
        # 尝试多个可能的token环境变量
        token_vars = [
            'TEST_ACCESS_TOKEN',
            'FEISHU_USER_ACCESS_TOKEN', 
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_access_token = token
                print(f"✅ 找到用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        if not self.user_access_token:
            print("❌ 未找到用户访问令牌")
            print("💡 请设置以下环境变量之一:")
            for var in token_vars:
                print(f"   - {var}")
            return
        
        if not self.app_id or not self.app_secret:
            print("❌ 未找到应用凭据")
            return
        
        # 创建飞书客户端
        self.client = lark.Client.builder() \
            .app_id(self.app_id) \
            .app_secret(self.app_secret) \
            .build()
        
        print(f"✅ 飞书客户端创建成功")
        print(f"   应用ID: {self.app_id}")
    
    def test_direct_api_call(self):
        """直接调用飞书API"""
        if not self.user_access_token:
            print("❌ 没有用户访问令牌，无法测试")
            return
        
        print("\n🚀 直接调用飞书日历API")
        print("="*60)
        
        try:
            # 构建请求，直接在请求选项中设置用户访问令牌
            request = ListCalendarRequest.builder() \
                .page_size(50) \
                .build()

            # 创建请求选项，设置用户访问令牌
            option = lark.RequestOption.builder() \
                .user_access_token(self.user_access_token) \
                .build()
            
            print("📤 发送API请求...")
            print(f"   端点: calendar.v4.calendar.list")
            print(f"   页面大小: 50")
            print(f"   用户令牌: {self.user_access_token[:20]}...")
            
            # 发送请求
            response = self.client.calendar.v4.calendar.list(request, option)
            
            print(f"🌐 API响应状态: {response.code}")
            print(f"📝 响应消息: {response.msg}")
            
            if response.code == 0:
                calendars = response.data.calendar_list or []
                print(f"✅ 成功获取 {len(calendars)} 个日历")
                
                print(f"\n📅 日历列表:")
                print("="*60)
                
                for i, calendar in enumerate(calendars, 1):
                    print(f"\n  {i}. {calendar.summary or '未命名日历'}")
                    print(f"     📋 ID: {calendar.calendar_id}")
                    print(f"     🏷️  类型: {calendar.type}")
                    print(f"     🔒 权限: {calendar.permissions}")
                    print(f"     👤 角色: {calendar.role}")
                    print(f"     🎨 颜色: {calendar.color}")
                    
                    if calendar.description:
                        print(f"     📝 描述: {calendar.description}")
                    
                    if calendar.summary_alias:
                        print(f"     🔖 别名: {calendar.summary_alias}")
                
                # 检查分页
                if response.data.has_more:
                    print(f"\n📄 有更多数据，分页令牌: {response.data.page_token}")
                    
                    # 获取第二页
                    print(f"\n📄 获取第二页数据...")
                    request2 = ListCalendarRequest.builder() \
                        .page_size(50) \
                        .page_token(response.data.page_token) \
                        .build()
                    
                    response2 = self.client.calendar.v4.calendar.list(request2, option)
                    
                    if response2.code == 0:
                        calendars2 = response2.data.calendar_list or []
                        print(f"✅ 第二页获取 {len(calendars2)} 个日历")
                        
                        total_calendars = len(calendars) + len(calendars2)
                        print(f"📊 总计: {total_calendars} 个日历")
                        
                        if calendars2:
                            print(f"\n📅 第二页日历:")
                            for i, calendar in enumerate(calendars2, len(calendars) + 1):
                                print(f"  {i}. {calendar.summary or '未命名日历'}")
                    else:
                        print(f"❌ 第二页获取失败: {response2.msg}")
                else:
                    print(f"\n✅ 所有日历已获取完毕")
                
            else:
                print(f"❌ API调用失败")
                print(f"   错误代码: {response.code}")
                print(f"   错误消息: {response.msg}")
                
                # 分析常见错误
                if response.code == 99991663:
                    print("💡 错误分析: 用户访问令牌无效或已过期")
                elif response.code == 99991661:
                    print("💡 错误分析: 应用访问令牌无效")
                elif response.code == 230002:
                    print("💡 错误分析: 权限不足，需要日历权限")
                
        except Exception as e:
            print(f"❌ 调用过程中出现异常: {e}")
            import traceback
            traceback.print_exc()
    
    def compare_with_expected(self, calendars: List):
        """与期望的日历列表对比"""
        expected_calendars = [
            "IF ZHANG",
            "我的任务", 
            "喜多多计划",
            "真宝虾计划",
            "B站创作计划", 
            "大宝暑假计划",
            "小宝暑假计划",
            "小红书计划"
        ]
        
        print(f"\n🔍 与界面显示的日历对比:")
        print("="*60)
        print(f"界面显示: {len(expected_calendars)} 个日历")
        print(f"API返回: {len(calendars)} 个日历")
        
        found_calendars = [cal.summary for cal in calendars if cal.summary]
        
        print(f"\n✅ 找到的匹配:")
        for expected in expected_calendars:
            found = any(expected in found_cal for found_cal in found_calendars)
            status = "✅" if found else "❌"
            print(f"   {status} {expected}")
        
        print(f"\n📋 API返回的所有日历:")
        for cal in found_calendars:
            print(f"   - {cal}")


def main():
    """主函数"""
    print("🔧 直接飞书API测试")
    print("="*60)
    
    tester = DirectFeishuTester()
    tester.test_direct_api_call()


if __name__ == "__main__":
    main()
