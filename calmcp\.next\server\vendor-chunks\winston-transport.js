"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/winston-transport";
exports.ids = ["vendor-chunks/winston-transport"];
exports.modules = {

/***/ "(rsc)/./node_modules/winston-transport/index.js":
/*!*************************************************!*\
  !*** ./node_modules/winston-transport/index.js ***!
  \*************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\n// Expose modern transport directly as the export\nmodule.exports = __webpack_require__(/*! ./modern */ \"(rsc)/./node_modules/winston-transport/modern.js\");\n\n// Expose legacy stream\nmodule.exports.LegacyTransportStream = __webpack_require__(/*! ./legacy */ \"(rsc)/./node_modules/winston-transport/legacy.js\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2luc3Rvbi10cmFuc3BvcnQvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7O0FBRWI7QUFDQSx3R0FBb0M7O0FBRXBDO0FBQ0EsOEhBQTBEIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbWNwLy4vbm9kZV9tb2R1bGVzL3dpbnN0b24tdHJhbnNwb3J0L2luZGV4LmpzPzdkMDEiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBzdHJpY3QnO1xuXG4vLyBFeHBvc2UgbW9kZXJuIHRyYW5zcG9ydCBkaXJlY3RseSBhcyB0aGUgZXhwb3J0XG5tb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vbW9kZXJuJyk7XG5cbi8vIEV4cG9zZSBsZWdhY3kgc3RyZWFtXG5tb2R1bGUuZXhwb3J0cy5MZWdhY3lUcmFuc3BvcnRTdHJlYW0gPSByZXF1aXJlKCcuL2xlZ2FjeScpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston-transport/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston-transport/legacy.js":
/*!**************************************************!*\
  !*** ./node_modules/winston-transport/legacy.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst util = __webpack_require__(/*! util */ \"util\");\nconst { LEVEL } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst TransportStream = __webpack_require__(/*! ./modern */ \"(rsc)/./node_modules/winston-transport/modern.js\");\n\n/**\n * Constructor function for the LegacyTransportStream. This is an internal\n * wrapper `winston >= 3` uses to wrap older transports implementing\n * log(level, message, meta).\n * @param {Object} options - Options for this TransportStream instance.\n * @param {Transpot} options.transport - winston@2 or older Transport to wrap.\n */\n\nconst LegacyTransportStream = module.exports = function LegacyTransportStream(options = {}) {\n  TransportStream.call(this, options);\n  if (!options.transport || typeof options.transport.log !== 'function') {\n    throw new Error('Invalid transport, must be an object with a log method.');\n  }\n\n  this.transport = options.transport;\n  this.level = this.level || options.transport.level;\n  this.handleExceptions = this.handleExceptions || options.transport.handleExceptions;\n\n  // Display our deprecation notice.\n  this._deprecated();\n\n  // Properly bubble up errors from the transport to the\n  // LegacyTransportStream instance, but only once no matter how many times\n  // this transport is shared.\n  function transportError(err) {\n    this.emit('error', err, this.transport);\n  }\n\n  if (!this.transport.__winstonError) {\n    this.transport.__winstonError = transportError.bind(this);\n    this.transport.on('error', this.transport.__winstonError);\n  }\n};\n\n/*\n * Inherit from TransportStream using Node.js built-ins\n */\nutil.inherits(LegacyTransportStream, TransportStream);\n\n/**\n * Writes the info object to our transport instance.\n * @param {mixed} info - TODO: add param description.\n * @param {mixed} enc - TODO: add param description.\n * @param {function} callback - TODO: add param description.\n * @returns {undefined}\n * @private\n */\nLegacyTransportStream.prototype._write = function _write(info, enc, callback) {\n  if (this.silent || (info.exception === true && !this.handleExceptions)) {\n    return callback(null);\n  }\n\n  // Remark: This has to be handled in the base transport now because we\n  // cannot conditionally write to our pipe targets as stream.\n  if (!this.level || this.levels[this.level] >= this.levels[info[LEVEL]]) {\n    this.transport.log(info[LEVEL], info.message, info, this._nop);\n  }\n\n  callback(null);\n};\n\n/**\n * Writes the batch of info objects (i.e. \"object chunks\") to our transport\n * instance after performing any necessary filtering.\n * @param {mixed} chunks - TODO: add params description.\n * @param {function} callback - TODO: add params description.\n * @returns {mixed} - TODO: add returns description.\n * @private\n */\nLegacyTransportStream.prototype._writev = function _writev(chunks, callback) {\n  for (let i = 0; i < chunks.length; i++) {\n    if (this._accept(chunks[i])) {\n      this.transport.log(\n        chunks[i].chunk[LEVEL],\n        chunks[i].chunk.message,\n        chunks[i].chunk,\n        this._nop\n      );\n      chunks[i].callback();\n    }\n  }\n\n  return callback(null);\n};\n\n/**\n * Displays a deprecation notice. Defined as a function so it can be\n * overriden in tests.\n * @returns {undefined}\n */\nLegacyTransportStream.prototype._deprecated = function _deprecated() {\n  // eslint-disable-next-line no-console\n  console.error([\n    `${this.transport.name} is a legacy winston transport. Consider upgrading: `,\n    '- Upgrade docs: https://github.com/winstonjs/winston/blob/master/UPGRADE-3.0.md'\n  ].join('\\n'));\n};\n\n/**\n * Clean up error handling state on the legacy transport associated\n * with this instance.\n * @returns {undefined}\n */\nLegacyTransportStream.prototype.close = function close() {\n  if (this.transport.close) {\n    this.transport.close();\n  }\n\n  if (this.transport.__winstonError) {\n    this.transport.removeListener('error', this.transport.__winstonError);\n    this.transport.__winstonError = null;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston-transport/legacy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston-transport/modern.js":
/*!**************************************************!*\
  !*** ./node_modules/winston-transport/modern.js ***!
  \**************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nconst util = __webpack_require__(/*! util */ \"util\");\nconst Writable = __webpack_require__(/*! readable-stream/lib/_stream_writable.js */ \"(rsc)/./node_modules/readable-stream/lib/_stream_writable.js\");\nconst { LEVEL } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\n\n/**\n * Constructor function for the TransportStream. This is the base prototype\n * that all `winston >= 3` transports should inherit from.\n * @param {Object} options - Options for this TransportStream instance\n * @param {String} options.level - Highest level according to RFC5424.\n * @param {Boolean} options.handleExceptions - If true, info with\n * { exception: true } will be written.\n * @param {Function} options.log - Custom log function for simple Transport\n * creation\n * @param {Function} options.close - Called on \"unpipe\" from parent.\n */\nconst TransportStream = module.exports = function TransportStream(options = {}) {\n  Writable.call(this, { objectMode: true, highWaterMark: options.highWaterMark });\n\n  this.format = options.format;\n  this.level = options.level;\n  this.handleExceptions = options.handleExceptions;\n  this.handleRejections = options.handleRejections;\n  this.silent = options.silent;\n\n  if (options.log) this.log = options.log;\n  if (options.logv) this.logv = options.logv;\n  if (options.close) this.close = options.close;\n\n  // Get the levels from the source we are piped from.\n  this.once('pipe', logger => {\n    // Remark (indexzero): this bookkeeping can only support multiple\n    // Logger parents with the same `levels`. This comes into play in\n    // the `winston.Container` code in which `container.add` takes\n    // a fully realized set of options with pre-constructed TransportStreams.\n    this.levels = logger.levels;\n    this.parent = logger;\n  });\n\n  // If and/or when the transport is removed from this instance\n  this.once('unpipe', src => {\n    // Remark (indexzero): this bookkeeping can only support multiple\n    // Logger parents with the same `levels`. This comes into play in\n    // the `winston.Container` code in which `container.add` takes\n    // a fully realized set of options with pre-constructed TransportStreams.\n    if (src === this.parent) {\n      this.parent = null;\n      if (this.close) {\n        this.close();\n      }\n    }\n  });\n};\n\n/*\n * Inherit from Writeable using Node.js built-ins\n */\nutil.inherits(TransportStream, Writable);\n\n/**\n * Writes the info object to our transport instance.\n * @param {mixed} info - TODO: add param description.\n * @param {mixed} enc - TODO: add param description.\n * @param {function} callback - TODO: add param description.\n * @returns {undefined}\n * @private\n */\nTransportStream.prototype._write = function _write(info, enc, callback) {\n  if (this.silent || (info.exception === true && !this.handleExceptions)) {\n    return callback(null);\n  }\n\n  // Remark: This has to be handled in the base transport now because we\n  // cannot conditionally write to our pipe targets as stream. We always\n  // prefer any explicit level set on the Transport itself falling back to\n  // any level set on the parent.\n  const level = this.level || (this.parent && this.parent.level);\n\n  if (!level || this.levels[level] >= this.levels[info[LEVEL]]) {\n    if (info && !this.format) {\n      return this.log(info, callback);\n    }\n\n    let errState;\n    let transformed;\n\n    // We trap(and re-throw) any errors generated by the user-provided format, but also\n    // guarantee that the streams callback is invoked so that we can continue flowing.\n    try {\n      transformed = this.format.transform(Object.assign({}, info), this.format.options);\n    } catch (err) {\n      errState = err;\n    }\n\n    if (errState || !transformed) {\n      // eslint-disable-next-line callback-return\n      callback();\n      if (errState) throw errState;\n      return;\n    }\n\n    return this.log(transformed, callback);\n  }\n  this._writableState.sync = false;\n  return callback(null);\n};\n\n/**\n * Writes the batch of info objects (i.e. \"object chunks\") to our transport\n * instance after performing any necessary filtering.\n * @param {mixed} chunks - TODO: add params description.\n * @param {function} callback - TODO: add params description.\n * @returns {mixed} - TODO: add returns description.\n * @private\n */\nTransportStream.prototype._writev = function _writev(chunks, callback) {\n  if (this.logv) {\n    const infos = chunks.filter(this._accept, this);\n    if (!infos.length) {\n      return callback(null);\n    }\n\n    // Remark (indexzero): from a performance perspective if Transport\n    // implementers do choose to implement logv should we make it their\n    // responsibility to invoke their format?\n    return this.logv(infos, callback);\n  }\n\n  for (let i = 0; i < chunks.length; i++) {\n    if (!this._accept(chunks[i])) continue;\n\n    if (chunks[i].chunk && !this.format) {\n      this.log(chunks[i].chunk, chunks[i].callback);\n      continue;\n    }\n\n    let errState;\n    let transformed;\n\n    // We trap(and re-throw) any errors generated by the user-provided format, but also\n    // guarantee that the streams callback is invoked so that we can continue flowing.\n    try {\n      transformed = this.format.transform(\n        Object.assign({}, chunks[i].chunk),\n        this.format.options\n      );\n    } catch (err) {\n      errState = err;\n    }\n\n    if (errState || !transformed) {\n      // eslint-disable-next-line callback-return\n      chunks[i].callback();\n      if (errState) {\n        // eslint-disable-next-line callback-return\n        callback(null);\n        throw errState;\n      }\n    } else {\n      this.log(transformed, chunks[i].callback);\n    }\n  }\n\n  return callback(null);\n};\n\n/**\n * Predicate function that returns true if the specfied `info` on the\n * WriteReq, `write`, should be passed down into the derived\n * TransportStream's I/O via `.log(info, callback)`.\n * @param {WriteReq} write - winston@3 Node.js WriteReq for the `info` object\n * representing the log message.\n * @returns {Boolean} - Value indicating if the `write` should be accepted &\n * logged.\n */\nTransportStream.prototype._accept = function _accept(write) {\n  const info = write.chunk;\n  if (this.silent) {\n    return false;\n  }\n\n  // We always prefer any explicit level set on the Transport itself\n  // falling back to any level set on the parent.\n  const level = this.level || (this.parent && this.parent.level);\n\n  // Immediately check the average case: log level filtering.\n  if (\n    info.exception === true ||\n    !level ||\n    this.levels[level] >= this.levels[info[LEVEL]]\n  ) {\n    // Ensure the info object is valid based on `{ exception }`:\n    // 1. { handleExceptions: true }: all `info` objects are valid\n    // 2. { exception: false }: accepted by all transports.\n    if (this.handleExceptions || info.exception !== true) {\n      return true;\n    }\n  }\n\n  return false;\n};\n\n/**\n * _nop is short for \"No operation\"\n * @returns {Boolean} Intentionally false.\n */\nTransportStream.prototype._nop = function _nop() {\n  // eslint-disable-next-line no-undefined\n  return void undefined;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston-transport/modern.js\n");

/***/ })

};
;