# 测试指南

## 🎯 概述

本项目采用真实数据测试方法，使用真实的API客户端和存储系统进行测试，确保测试结果的可靠性和真实性。

## 🚀 快速开始

### 1. 环境设置

```bash
# 1. 复制测试环境配置模板
cp tests/test.env.example tests/test.env

# 2. 编辑配置文件，填入飞书应用信息
# 必需：FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
# 可选：TEST_ACCESS_TOKEN（用于真实API测试）

# 3. 安装测试依赖
pip install -r requirements-test.txt
```

### 2. 运行测试

```bash
# 使用统一测试运行器（推荐）
python tests/run_tests.py

# 运行基础功能测试，详细输出
python tests/run_tests.py --type basic -v

# 生成覆盖率报告
python tests/run_tests.py --type basic --coverage
```

## 📊 测试类型

| 测试类型 | 文件/目录 | 描述 | 运行时间 |
|---------|-----------|------|----------|
| **基础+业务功能测试** | `tests/test_basic_functionality.py` | 验证技术基础设施+核心业务功能（推荐） | 快速（< 30秒） |
| **单元测试** | `tests/unit/` | 测试单个函数和类的功能 | 快速（< 30秒） |
| **集成测试** | `tests/integration/` | 测试模块间的交互 | 中等（1-2分钟） |
| **端到端测试** | `tests/e2e/` | 测试完整的用户流程 | 较慢（2-5分钟） |
| **性能测试** | `tests/performance/` | 测试系统性能指标 | 较慢（3-10分钟） |

## 🧪 测试特性

- **完整业务覆盖**: 验证认证、日历管理、事件管理、工作流引擎等核心业务功能
- **真实数据测试**: 使用真实的API客户端和存储系统，无Mock
- **分层测试架构**: 基础设施测试 + 业务功能测试双重保障
- **自动环境检查**: 自动验证必需的环境变量
- **智能跳过**: 缺少配置时自动跳过相关测试
- **详细报告**: 提供执行时间、通过率、错误信息
- **自动清理**: 测试完成后自动清理测试数据

## 📋 测试命令

### 基本命令

```bash
# 运行基础功能测试（默认，推荐）
python tests/run_tests.py

# 运行特定类型的测试
python tests/run_tests.py --type basic
python tests/run_tests.py --type unit
python tests/run_tests.py --type integration
python tests/run_tests.py --type e2e
python tests/run_tests.py --type performance

# 详细输出
python tests/run_tests.py --type basic -v

# 生成覆盖率报告
python tests/run_tests.py --type basic --coverage
```

### 直接使用pytest

```bash
# 运行所有测试
pytest

# 运行特定目录的测试
pytest tests/unit/
pytest tests/integration/
pytest tests/e2e/

# 详细输出
pytest -v

# 生成覆盖率报告
pytest --cov=integrations --cov=api --cov=services --cov-report=html
```

## 🔧 故障排除

### 常见问题

1. **环境变量缺失**
```bash
# 运行测试时会自动检查并提示缺失的环境变量
python tests/run_tests.py
```

2. **网络测试失败**
```bash
# 设置环境变量启用网络测试
export ALLOW_NETWORK_TESTS=true
export TEST_ACCESS_TOKEN=your_real_token
```

3. **测试数据冲突**
```bash
# 测试使用时间戳创建唯一数据，通常不会冲突
# 如有问题，检查存储配置是否正确
```

### 调试技巧

```bash
# 单独运行失败的测试
pytest tests/unit/test_specific.py::test_function_name -v

# 使用调试模式
pytest --pdb tests/unit/test_specific.py

# 查看详细日志
LOG_LEVEL=DEBUG python tests/run_tests.py --type unit -v
```

## 📁 项目结构

```
tests/
├── unit/                # 单元测试
├── integration/         # 集成测试
├── e2e/                # 端到端测试
├── performance/        # 性能测试
├── examples/           # 测试示例
├── conftest.py         # pytest配置和共享fixture
├── run_tests.py        # 统一测试运行器
├── test.env.example    # 测试环境配置模板
└── README.md           # 详细测试指南
```

## 🎯 最佳实践

1. **开发时运行基础功能测试**：
```bash
python tests/run_tests.py
```

2. **提交前运行基础功能测试**：
```bash
python tests/run_tests.py --type basic --coverage
```

3. **部署前运行完整检查**：
```bash
python scripts/deployment_check.py
```

## 📖 更多信息

- 详细测试指南：[tests/README.md](tests/README.md)
- 测试配置模板：[tests/test.env.example](tests/test.env.example)
- 部署检查：`python scripts/deployment_check.py`

---

**测试方法**: 真实数据测试（无Mock）  
**测试工具**: pytest + 自定义运行器  
**覆盖范围**: 单元、集成、端到端、性能测试
