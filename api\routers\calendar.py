"""
日历相关路由
"""

import json
import logging

from fastapi import APIRouter, HTTPException

from integrations.feishu import get_feishu_client

from ..dependencies import ensure_valid_token
from ..models.calendar import CalendarCreate, CalendarUpdate

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/calendars", tags=["日历管理"])

# 获取统一的飞书客户端
def get_client():
    return get_feishu_client()


@router.get("/")
async def calendars(user_id: str):
    """获取用户的日历列表"""
    client = get_client()
    result = client.get_calendars(user_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"获取日历列表失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.get("/primary")
async def primary_calendar(user_id: str):
    """获取用户的主日历"""
    logger.info(f"开始获取用户 {user_id} 的主日历")
    access_token = await ensure_valid_token(user_id)

    if not access_token:
        logger.error("无法获取有效的用户访问令牌")
        raise HTTPException(
            status_code=401, detail="无法获取有效的用户访问令牌，请重新授权"
        )

    logger.info(
        f"已获取用户访问令牌，前10位: {access_token[:10] if access_token else 'None'}"
    )

    # 首先尝试使用FeishuCalendar类的方法获取主日历
    try:
        logger.info("尝试使用FeishuCalendar类获取主日历")
        # 初始化日历对象，传入用户的访问令牌
        calendar_obj = FeishuCalendarLark(FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET)
        logger.info("已初始化FeishuCalendar对象")

        # 获取主日历
        logger.info("开始调用_get_primary_calendar_id方法")
        calendar_id = calendar_obj._get_primary_calendar_id()
        logger.info(f"成功获取到主日历ID: {calendar_id}")

        # 获取日历详情
        logger.info(f"开始获取日历 {calendar_id} 的详情")
        calendar_detail = await get_calendar_detail(access_token, calendar_id)
        logger.info(
            f"日历详情API响应: {json.dumps(calendar_detail, ensure_ascii=False)}"
        )

        if "code" in calendar_detail and calendar_detail["code"] != 0:
            logger.warning(
                f"获取日历详情失败，错误码: {calendar_detail.get('code')}, 错误信息: {calendar_detail.get('msg')}"
            )
            # 如果获取详情失败，返回简单的日历ID
            logger.info("返回简单的日历ID信息")
            return {"data": {"calendar": {"calendar_id": calendar_id}}}

        logger.info("成功获取主日历信息，返回详情")
        return calendar_detail
    except Exception as e:
        logger.error(f"使用FeishuCalendar获取主日历失败: {str(e)}")
        logger.info("尝试使用备用方法获取主日历...")

    # 备用方法：从日历列表中查找主日历
    try:
        logger.info("开始使用备用方法获取日历列表")
        result = await get_calendars(access_token)
        logger.info(f"日历列表API响应: {json.dumps(result, ensure_ascii=False)}")

        if "code" in result and result["code"] != 0:
            logger.error(
                f"获取日历列表失败，错误码: {result.get('code')}, 错误信息: {result.get('msg')}"
            )
            raise HTTPException(
                status_code=400,
                detail=f"获取日历列表失败: {result.get('msg', '未知错误')}",
            )

        # 从日历列表中找出主日历
        calendars = result.get("data", {}).get("calendar_list", [])
        logger.info(f"找到 {len(calendars)} 个日历")

        # 打印所有日历信息，便于调试
        for i, cal in enumerate(calendars):
            logger.info(f"日历 {i+1}:")
            logger.info(f"  ID: {cal.get('calendar_id', 'N/A')}")
            logger.info(f"  名称: {cal.get('summary', 'N/A')}")
            logger.info(f"  类型: {cal.get('type', 'N/A')}")
            logger.info(f"  是否主日历: {cal.get('is_primary', False)}")
            logger.info(f"  角色: {cal.get('role', 'N/A')}")

        # 尝试多种方式识别主日历
        primary_calendar = None

        # 方法1：查找type为primary的日历
        primary_calendar = next(
            (cal for cal in calendars if cal.get("type") == "primary"), None
        )
        if primary_calendar:
            logger.info(
                f"方法1成功: 找到type为primary的日历: {primary_calendar.get('summary', 'N/A')}"
            )

        # 方法2：查找role为owner的日历
        if not primary_calendar:
            primary_calendar = next(
                (cal for cal in calendars if cal.get("role") == "owner"), None
            )
            if primary_calendar:
                logger.info(
                    f"方法2成功: 找到role为owner的日历: {primary_calendar.get('summary', 'N/A')}"
                )

        # 方法3：如果仍未找到，使用第一个日历
        if not primary_calendar and calendars:
            primary_calendar = calendars[0]
            logger.info(
                f"方法3成功: 未找到明确的主日历，使用第一个日历: {primary_calendar.get('summary', '未命名')}"
            )

        # 如果仍然没有日历，返回404错误
        if not primary_calendar:
            logger.error("未找到任何日历")
            raise HTTPException(status_code=404, detail="未找到任何日历")

        logger.info(
            f"最终选择的日历: {primary_calendar.get('summary', 'N/A')}, ID: {primary_calendar.get('calendar_id', 'N/A')}"
        )
        return {"data": {"calendar": primary_calendar}}
    except HTTPException:
        # 直接重新抛出HTTP异常
        raise
    except Exception as e:
        # 处理其他异常
        logger.error(f"备用方法获取主日历失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取主日历失败: {str(e)}")


@router.get("/{calendar_id}")
async def calendar_detail(user_id: str, calendar_id: str):
    """获取特定日历的详细信息"""
    access_token = await ensure_valid_token(user_id)
    result = await get_calendar_detail(access_token, calendar_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"获取日历详情失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.patch("/{calendar_id}")
async def update_calendar_info(
    user_id: str, calendar_id: str, calendar: CalendarUpdate
):
    """更新日历信息"""
    access_token = await ensure_valid_token(user_id)
    result = await update_calendar(
        access_token,
        calendar_id=calendar_id,
        summary=calendar.summary,
        description=calendar.description,
        permissions=calendar.permissions,
        color=calendar.color,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"更新日历失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.delete("/{calendar_id}")
async def delete_calendar_by_id(user_id: str, calendar_id: str):
    """删除日历"""
    access_token = await ensure_valid_token(user_id)
    result = await delete_calendar(access_token, calendar_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"删除日历失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.post("/{calendar_id}/subscribe")
async def subscribe_calendar_by_id(user_id: str, calendar_id: str):
    """订阅日历"""
    access_token = await ensure_valid_token(user_id)
    result = await subscribe_calendar(access_token, calendar_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"订阅日历失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.post("/{calendar_id}/unsubscribe")
async def unsubscribe_calendar_by_id(user_id: str, calendar_id: str):
    """取消订阅日历"""
    access_token = await ensure_valid_token(user_id)
    result = await unsubscribe_calendar(access_token, calendar_id)

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"取消订阅日历失败: {result.get('msg', '未知错误')}"
        )

    return result


@router.post("/")
async def create_new_calendar(user_id: str, calendar: CalendarCreate):
    """创建新的日历"""
    access_token = await ensure_valid_token(user_id)
    result = await create_calendar(
        access_token,
        summary=calendar.summary,
        description=calendar.description,
        permissions=calendar.permissions,
        color=calendar.color,
    )

    if "code" in result and result["code"] != 0:
        raise HTTPException(
            status_code=400, detail=f"创建日历失败: {result.get('msg', '未知错误')}"
        )

    return result
