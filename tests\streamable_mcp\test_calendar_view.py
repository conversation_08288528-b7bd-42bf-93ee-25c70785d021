#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试MCP服务查看日历
"""

import asyncio
import json
import logging
from pathlib import Path
from feishu_mcp_client_streamable import StreamableMCPClient

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_calendar_view")


async def test_calendar_view():
    """测试查看日历功能"""
    logger.info("🚀 开始测试MCP日历查看功能")
    
    # 使用默认用户ID，或者你可以从OAuth认证中获取
    user_id = "ou_57792fdcc9ab9970117ac3dbdf8a5b25"  # 或者使用实际的用户ID
    
    async with StreamableMCPClient("http://localhost:5000") as client:
        try:
            # 1. 健康检查
            logger.info("📊 执行健康检查...")
            health = await client.health_check()
            logger.info(f"✅ 服务器健康状态: {health}")
            
            # 2. 初始化MCP连接
            logger.info("🔗 初始化MCP连接...")
            init_result = await client.initialize()
            logger.info(f"✅ MCP初始化成功: {init_result.get('result', {}).get('serverInfo', {})}")
            
            # 3. 获取可用工具
            logger.info("📋 获取可用工具...")
            tools_result = await client.list_tools()
            
            # 4. 查看日历列表 - 使用正确的工具名称和参数
            logger.info("📅 获取日历列表...")
            calendars_result = await client.call_tool(
                "list_calendars",
                {
                    "user_id": user_id
                }
            )
            
            if calendars_result.get("result", {}).get("content"):
                content = calendars_result["result"]["content"][0]["text"]
                result_data = json.loads(content)
                
                if result_data.get("success") and result_data.get("calendars"):
                    calendars = result_data["calendars"]
                    logger.info(f"✅ 找到 {len(calendars)} 个日历:")
                    
                    for i, calendar in enumerate(calendars, 1):
                        logger.info(f"  {i}. {calendar.get('name', '未命名')} (ID: {calendar.get('id', 'N/A')})")
                        logger.info(f"     描述: {calendar.get('description', '无描述')}")
                        logger.info(f"     权限: {calendar.get('permissions', 'N/A')}")
                        logger.info("")
                    
                    # 5. 获取第一个日历的事件
                    if calendars:
                        first_calendar = calendars[0]
                        calendar_id = first_calendar.get('id')
                        logger.info(f"📅 获取日历 '{first_calendar.get('name')}' 的事件...")
                        
                        events_result = await client.call_tool(
                            "get_calendar_events",
                            {
                                "user_id": user_id,
                                "calendar_id": calendar_id,
                                "page_size": 10
                            }
                        )
                        
                        if events_result.get("result", {}).get("content"):
                            content = events_result["result"]["content"][0]["text"]
                            result_data = json.loads(content)
                            
                            if result_data.get("success") and result_data.get("events"):
                                events = result_data["events"]
                                logger.info(f"✅ 找到 {len(events)} 个事件:")
                                
                                for i, event in enumerate(events, 1):
                                    logger.info(f"  {i}. {event.get('title', '未命名事件')}")
                                    logger.info(f"     开始时间: {event.get('start_time', 'N/A')}")
                                    logger.info(f"     结束时间: {event.get('end_time', 'N/A')}")
                                    logger.info(f"     描述: {event.get('description', '无描述')[:100]}...")
                                    logger.info("")
                            else:
                                logger.info("📭 该日历暂无事件")
                        else:
                            logger.info("📭 该日历暂无事件")
                else:
                    error_msg = result_data.get("error", "未知错误")
                    logger.warning(f"⚠️ 获取日历失败: {error_msg}")
            else:
                logger.warning("⚠️ 未找到任何日历")
                
        except Exception as e:
            logger.error(f"❌ 测试失败: {str(e)}")
            raise


async def test_specific_calendar(calendar_id: str):
    """测试特定日历的事件"""
    logger.info(f"🎯 测试特定日历: {calendar_id}")
    
    user_id = "me"  # 或者使用实际的用户ID
    
    async with StreamableMCPClient("http://localhost:5000") as client:
        try:
            # 初始化连接
            await client.initialize()
            
            # 搜索该日历的事件
            events_result = await client.call_tool(
                "get_calendar_events",
                {
                    "user_id": user_id,
                    "calendar_id": calendar_id,
                    "page_size": 20
                }
            )
            
            if events_result.get("result", {}).get("content"):
                content = events_result["result"]["content"][0]["text"]
                result_data = json.loads(content)
                
                if result_data.get("success") and result_data.get("events"):
                    events = result_data["events"]
                    logger.info(f"✅ 找到 {len(events)} 个事件:")
                    
                    for i, event in enumerate(events, 1):
                        logger.info(f"  {i}. {event.get('title', '未命名事件')}")
                        logger.info(f"     开始时间: {event.get('start_time', 'N/A')}")
                        logger.info(f"     结束时间: {event.get('end_time', 'N/A')}")
                        logger.info(f"     描述: {event.get('description', '无描述')[:100]}...")
                        logger.info("")
                else:
                    logger.info("📭 该日历暂无事件")
            else:
                logger.info("📭 该日历暂无事件")
                
        except Exception as e:
            logger.error(f"❌ 测试失败: {str(e)}")
            raise


async def test_today_events():
    """测试获取今天的事件"""
    logger.info("📅 获取今天的事件...")
    
    user_id = "me"  # 或者使用实际的用户ID
    
    async with StreamableMCPClient("http://localhost:5000") as client:
        try:
            # 初始化连接
            await client.initialize()
            
            # 获取今天的事件
            events_result = await client.call_tool(
                "get_today_events",
                {
                    "user_id": user_id
                }
            )
            
            if events_result.get("result", {}).get("content"):
                content = events_result["result"]["content"][0]["text"]
                result_data = json.loads(content)
                
                if result_data.get("success") and result_data.get("events"):
                    events = result_data["events"]
                    logger.info(f"✅ 今天有 {len(events)} 个事件:")
                    
                    for i, event in enumerate(events, 1):
                        logger.info(f"  {i}. {event.get('title', '未命名事件')}")
                        logger.info(f"     开始时间: {event.get('start_time', 'N/A')}")
                        logger.info(f"     结束时间: {event.get('end_time', 'N/A')}")
                        logger.info(f"     日历: {event.get('calendar_name', 'N/A')}")
                        logger.info("")
                else:
                    logger.info("📭 今天没有事件")
            else:
                logger.info("📭 今天没有事件")
                
        except Exception as e:
            logger.error(f"❌ 测试失败: {str(e)}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "today":
            # 获取今天的事件
            asyncio.run(test_today_events())
        else:
            # 如果提供了日历ID，测试特定日历
            calendar_id = sys.argv[1]
            asyncio.run(test_specific_calendar(calendar_id))
    else:
        # 否则测试所有日历
        asyncio.run(test_calendar_view()) 