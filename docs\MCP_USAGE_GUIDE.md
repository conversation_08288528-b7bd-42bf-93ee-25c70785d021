# 飞书MCP集成使用指南

## 🚀 快速开始

### 1. 启动MCP服务

在一个终端窗口中，运行以下命令启动飞书MCP服务：

```bash
npx -y @larksuiteoapi/lark-mcp mcp \
  -a cli_a76a68f612bf900c \
  -s EVumG3wCHsDBeJRfpbmJkfRhzCns73jC \
  --oauth \
  --token-mode user_access_token \
  -t calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.list,calendar.v4.calendarEvent.create,calendar.v4.calendarEvent.search
```

### 2. 启动智能助手

在另一个终端窗口中，运行：

```bash
python start_mcp_chat.py
```

或者使用交互模式：

```bash
python start_mcp_chat.py -i
```

## 💬 使用示例

### 创建日历事件

**用户输入：**
```
明天下午3点安排一个项目进度会议
```

**系统响应：**
```
🤖 我理解您想要创建日历事件。

📝 事件详情：
• 标题：项目进度会议
• 时间：2025-07-16 15:00 - 16:00
• 地点：（未指定）

请确认是否创建此事件？回复 '确认' 继续，'取消' 放弃，或提供修改建议。
```

**用户确认：**
```
确认
```

**系统执行：**
```
✅ 已成功创建日历事件：项目进度会议
📅 时间：2025-07-16T15:00:00
🆔 事件ID：cal_event_123456
```

### 查询日历列表

**用户输入：**
```
查看我的日历列表
```

**系统响应：**
```
✅ 获取日历列表成功

📅 您的日历列表：
1. 工作日历
2. 个人日历
3. 项目日历
4. 会议室预订
5. 培训安排
```

### 搜索日历事件

**用户输入：**
```
搜索本周的项目会议
```

**系统响应：**
```
✅ 搜索事件成功

🔍 搜索结果：
1. 项目进度会议
   时间: 2025-07-16T15:00:00
2. 项目评审会议
   时间: 2025-07-18T10:00:00
3. 项目总结会议
   时间: 2025-07-19T14:00:00
```

## 🎮 系统命令

在聊天界面中，您可以使用以下系统命令：

- `help` - 显示帮助信息
- `status` - 显示MCP连接状态
- `tools` - 显示可用的MCP工具
- `clear` - 清屏
- `exit`/`quit`/`q` - 退出程序

## 🔧 支持的操作

### 日历事件创建
- "明天下午3点安排会议"
- "下周一上午10点开产品评审会"
- "安排一个项目讨论，时间是后天下午2点"

### 日历查询
- "查看我的日历"
- "显示日历列表"
- "我有哪些日历"

### 事件搜索
- "搜索项目相关的会议"
- "找一下本周的培训"
- "查找包含'评审'的事件"

### 多轮对话
系统支持多轮对话，可以在对话过程中：
- 补充缺失信息
- 修改事件详情
- 确认或取消操作

## ⚠️ 注意事项

### 环境要求
1. **Node.js环境** - 需要安装Node.js来运行MCP服务
2. **网络连接** - 需要能够访问飞书API
3. **权限配置** - 确保飞书应用有足够的日历权限

### 常见问题

**Q: MCP服务启动失败怎么办？**
A: 
1. 确保已安装Node.js
2. 运行 `npm install -g @larksuiteoapi/lark-mcp`
3. 检查网络连接
4. 验证飞书应用ID和密钥

**Q: 为什么无法创建事件？**
A:
1. 检查MCP服务是否正常运行
2. 确认飞书应用权限
3. 验证时间格式是否正确
4. 检查是否需要用户授权

**Q: 如何处理时间冲突？**
A: 系统会在创建事件前检查冲突，如有冲突会提示用户选择处理方式。

## 🔍 调试模式

如果遇到问题，可以启用调试模式：

```bash
# 设置日志级别为DEBUG
export LOG_LEVEL=DEBUG
python start_mcp_chat.py
```

这将显示详细的日志信息，帮助诊断问题。

## 📋 功能路线图

### 已实现功能 ✅
- [x] 自然语言意图识别
- [x] 日历事件创建
- [x] 日历列表查询
- [x] 事件搜索
- [x] 多轮对话支持
- [x] 用户确认机制

### 计划功能 📋
- [ ] 事件修改和删除
- [ ] 时间冲突检测
- [ ] 批量操作支持
- [ ] 智能提醒设置
- [ ] 会议室预订
- [ ] 参会者邀请

## 🤝 反馈和支持

如果您在使用过程中遇到问题或有改进建议，请：

1. 查看日志输出获取错误详情
2. 检查MCP服务状态
3. 验证网络和权限配置
4. 提供具体的错误信息和使用场景

---

*享受智能日历管理的便利！* 🎉
