"""
聊天相关的API模型
"""

from typing import Any, Dict, Optional

from pydantic import BaseModel


class ChatRequest(BaseModel):
    """智能聊天请求模型"""

    message: str
    user_id: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class ChatResponse(BaseModel):
    """智能聊天响应模型"""

    message: str
    intent: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    success: bool = True
