#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
显示所有今日事件的详细信息
"""

import asyncio
import logging

from integrations.feishu import get_today_events
from integrations.storage import get_token

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

TEST_USER_ID = "ou_57792fdcc9ab9970117ac3dbdf8a5b25"
REAL_CALENDAR_ID = "<EMAIL>"


async def show_all_events():
    """显示所有今日事件"""
    logger.info("📅 显示所有今日事件详情")

    # 获取用户token
    token_data = get_token(TEST_USER_ID)
    if not token_data:
        logger.error("❌ 未找到用户token，请先授权")
        return

    access_token = token_data["access_token"]

    # 获取今日事件
    result = await get_today_events(access_token, REAL_CALENDAR_ID)

    if result.get("code") == 0:
        events = result.get("data", {}).get("items", [])
        logger.info(f"📊 今日共有 {len(events)} 个事件")

        confirmed_events = []
        cancelled_events = []

        for i, event in enumerate(events):
            event_id = event.get("event_id", "unknown")[:12]
            summary = event.get("summary", "")
            status = event.get("status", "unknown")
            start_time = event.get("start_time", {}).get("iso_format", "N/A")
            end_time = event.get("end_time", {}).get("iso_format", "N/A")

            event_info = {
                "index": i + 1,
                "id": event_id,
                "title": summary if summary else f"[无标题事件]",
                "status": status,
                "start": start_time,
                "end": end_time,
            }

            if status == "confirmed":
                confirmed_events.append(event_info)
            elif status == "cancelled":
                cancelled_events.append(event_info)

        # 显示已确认的事件
        if confirmed_events:
            logger.info(f"\n✅ 已确认的事件 ({len(confirmed_events)}个):")
            for event in confirmed_events:
                logger.info(f"   📝 {event['index']}. {event['title']}")
                logger.info(f"      🕐 时间: {event['start']} - {event['end']}")
                logger.info(f"      🆔 ID: {event['id']}")
                logger.info("")

        # 显示已取消的事件
        if cancelled_events:
            logger.info(f"\n❌ 已取消的事件 ({len(cancelled_events)}个):")
            for event in cancelled_events:
                logger.info(f"   📝 {event['index']}. {event['title']}")
                logger.info(f"      🕐 时间: {event['start']} - {event['end']}")
                logger.info(f"      🆔 ID: {event['id']}")
                logger.info("")

        # 总结
        logger.info(f"📈 事件统计:")
        logger.info(f"   ✅ 已确认: {len(confirmed_events)} 个")
        logger.info(f"   ❌ 已取消: {len(cancelled_events)} 个")
        logger.info(f"   📊 总计: {len(events)} 个")

    else:
        logger.error(f"❌ 获取今日事件失败: {result}")


if __name__ == "__main__":
    asyncio.run(show_all_events())
