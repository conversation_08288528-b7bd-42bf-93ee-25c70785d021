#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地MCP客户端
连接到自开发的本地飞书MCP服务器，实现完全自主可控
"""

import asyncio
import json
import logging
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from mcp import ClientSession, StdioServerParameters
from mcp.client.stdio import stdio_client

# 配置日志
logger = logging.getLogger(__name__)


class LocalMCPClient:
    """本地MCP客户端 - 连接自开发的MCP服务器"""
    
    def __init__(self, server_script: str = "feishu_mcp_server_simple.py"):
        """
        初始化本地MCP客户端
        
        Args:
            server_script: MCP服务器脚本文件名
        """
        self.server_script = server_script
        self.session = None
        self.read_stream = None
        self.write_stream = None
        self.tools_cache = []
        self.is_connected = False
        
        # 创建服务器参数
        self.server_params = StdioServerParameters(
            command=sys.executable,
            args=[str(project_root / server_script)],
            env={}
        )
    
    async def connect(self):
        """连接到本地MCP服务器"""
        try:
            logger.info("🔗 连接到本地飞书MCP服务器...")
            
            # 创建stdio客户端连接
            self.stdio_context = stdio_client(self.server_params)
            self.read_stream, self.write_stream = await self.stdio_context.__aenter__()
            
            # 创建客户端会话
            self.session = ClientSession(self.read_stream, self.write_stream)
            await self.session.__aenter__()
            
            # 初始化连接
            await self.session.initialize()
            
            # 获取工具列表
            tools = await self.session.list_tools()
            self.tools_cache = tools.tools
            
            self.is_connected = True
            logger.info(f"✅ 成功连接到本地MCP服务器，加载了 {len(self.tools_cache)} 个工具")
            
            # 打印工具名称
            tool_names = [tool.name for tool in self.tools_cache]
            logger.info(f"可用工具: {tool_names}")
            
        except Exception as e:
            logger.error(f"连接本地MCP服务器失败: {str(e)}")
            await self.disconnect()
            raise
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.session:
                await self.session.__aexit__(None, None, None)
                self.session = None
            
            if hasattr(self, 'stdio_context'):
                await self.stdio_context.__aexit__(None, None, None)
            
            self.is_connected = False
            logger.info("本地MCP客户端连接已断开")
            
        except Exception as e:
            logger.error(f"断开连接时出错: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用MCP工具"""
        if not self.is_connected:
            await self.connect()
        
        try:
            logger.info(f"调用工具: {tool_name}，参数: {arguments}")
            
            result = await self.session.call_tool(tool_name, arguments)
            
            # 解析结果
            if result.content:
                content = result.content[0]
                if hasattr(content, 'text'):
                    try:
                        # 尝试解析JSON响应
                        response_data = json.loads(content.text)
                        logger.info(f"工具调用成功: {tool_name}")
                        return response_data
                    except json.JSONDecodeError:
                        # 如果不是JSON，返回原始文本
                        return {"success": True, "data": content.text}
                else:
                    return {"success": True, "data": str(content)}
            else:
                return {"success": False, "error": "无响应内容"}
                
        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}: {str(e)}")
            raise
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools_cache]
    
    async def list_calendars(self, user_id: str = "default_user") -> Dict[str, Any]:
        """获取日历列表"""
        return await self.call_tool("list_calendars", {"user_id": user_id})
    
    async def get_today_events(self, user_id: str = "default_user") -> Dict[str, Any]:
        """获取今天的事件"""
        return await self.call_tool("get_today_events", {"user_id": user_id})
    
    async def get_calendar_events(self, user_id: str = "default_user", 
                                 calendar_id: str = "primary",
                                 start_time: str = None, 
                                 end_time: str = None) -> Dict[str, Any]:
        """获取日历事件"""
        arguments = {
            "user_id": user_id,
            "calendar_id": calendar_id
        }
        if start_time:
            arguments["start_time"] = start_time
        if end_time:
            arguments["end_time"] = end_time
            
        return await self.call_tool("get_calendar_events", arguments)
    
    async def create_calendar_event(self, user_id: str = "default_user",
                                   title: str = "",
                                   start_time: str = "",
                                   end_time: str = "",
                                   calendar_id: str = "primary",
                                   description: str = "",
                                   location: str = "",
                                   attendees: List[str] = None) -> Dict[str, Any]:
        """创建日历事件"""
        arguments = {
            "user_id": user_id,
            "title": title,
            "start_time": start_time,
            "end_time": end_time,
            "calendar_id": calendar_id,
            "description": description,
            "location": location
        }
        if attendees:
            arguments["attendees"] = attendees
            
        return await self.call_tool("create_calendar_event", arguments)


# 全局客户端实例
_local_mcp_client = None


def get_local_mcp_client() -> LocalMCPClient:
    """获取本地MCP客户端实例（单例模式）"""
    global _local_mcp_client
    if _local_mcp_client is None:
        _local_mcp_client = LocalMCPClient()
    return _local_mcp_client


# 测试代码已移除，保持代码整洁
