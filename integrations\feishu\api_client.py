import asyncio
import functools
import json
import logging
import os
import time
import uuid
from datetime import date, datetime, timedelta
from functools import wraps
from typing import Any, Dict, List, Optional, Union

import httpx
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

from config import (
    FEISHU_CLIENT_ID,
    FEISHU_CLIENT_SECRET,
    MAX_RETRY_ATTEMPTS,
    REDIRECT_URI,
)

# 设置日志
logger = logging.getLogger(__name__)

# 全局缓存，用于存储日历ID和名称的映射关系
calendar_name_cache = {}


# 飞书API异常类
class FeishuAPIError(Exception):
    """飞书API异常"""

    def __init__(self, code: int, message: str, details: dict = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(f"飞书API错误 [{code}]: {message}")


class FeishuNetworkError(Exception):
    """飞书网络异常"""

    pass


class FeishuAuthError(Exception):
    """飞书认证异常"""

    pass


def generate_request_id():
    """生成唯一的请求ID"""
    return str(uuid.uuid4())


# 重试装饰器
def feishu_retry(func):
    """飞书API重试装饰器"""

    @retry(
        stop=stop_after_attempt(MAX_RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, FeishuNetworkError)),
        reraise=True,
    )
    @wraps(func)
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)

    return wrapper


# 跟踪函数执行时间和异常的装饰器
def api_logger(func):
    """API调用日志装饰器"""

    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 生成请求ID
        request_id = generate_request_id()

        # 记录API调用开始
        func_name = func.__name__
        logger.debug(f"[{request_id}] 开始调用API: {func_name}")

        # 记录参数（排除敏感信息）
        safe_kwargs = {
            k: v
            for k, v in kwargs.items()
            if k not in ["access_token", "app_secret", "refresh_token"]
        }
        logger.debug(f"[{request_id}] 调用参数: {safe_kwargs}")

        try:
            # 调用原始函数
            result = await func(*args, **kwargs)

            # 记录调用结果（简化版本，避免日志过大）
            if isinstance(result, dict) and "code" in result:
                logger.debug(
                    f"[{request_id}] API调用结果: code={result.get('code')}, msg={result.get('msg', '')}"
                )
            else:
                logger.debug(f"[{request_id}] API调用完成")

            return result
        except Exception as e:
            # 记录异常
            logger.error(f"[{request_id}] API调用异常: {str(e)}")
            raise

    return wrapper


# 认证相关API
@api_logger
@feishu_retry
async def exchange_code(code: str):
    """通过授权码获取访问令牌"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            payload = {
                "grant_type": "authorization_code",
                "code": code,
                "app_id": FEISHU_CLIENT_ID,
                "app_secret": FEISHU_CLIENT_SECRET,
                "redirect_uri": REDIRECT_URI,
            }

            r = await client.post(
                "https://open.feishu.cn/open-apis/authen/v1/access_token",
                json=payload,
                headers={"Content-Type": "application/json; charset=utf-8"},
            )

            result = handle_api_response(r, "获取访问令牌")

            # 检查是否成功
            if result.get("code") != 0:
                logger.error(f"获取访问令牌失败: {result}")

            return result

    except httpx.RequestError as e:
        logger.error(f"获取访问令牌网络错误: {str(e)}")
        raise FeishuNetworkError(f"网络请求失败: {str(e)}")
    except Exception as e:
        logger.error(f"获取访问令牌发生未知错误: {str(e)}")
        raise FeishuAPIError(-1, f"未知错误: {str(e)}")


@api_logger
@feishu_retry
async def refresh_token(app_id: str, app_secret: str, refresh_token: str):
    """刷新访问令牌

    参数:
        app_id: 应用ID
        app_secret: 应用密钥
        refresh_token: 刷新令牌

    返回:
        Dict: 包含新的访问令牌和刷新令牌
    """
    request_id = generate_request_id()
    logger.info(f"[{request_id}] 开始刷新访问令牌")
    start_time = time.time()

    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {"Content-Type": "application/json; charset=utf-8"}

            data = {
                "app_id": app_id,
                "app_secret": app_secret,
                "refresh_token": refresh_token,
                "grant_type": "refresh_token",
            }

            logger.debug(f"[{request_id}] 发送刷新令牌请求")

            r = await client.post(
                "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",
                headers=headers,
                json=data,
            )

            result = handle_api_response(r, "刷新令牌")

            if result.get("code") == 0 and "data" in result:
                expires_in = result["data"].get("expires_in", 0)
                logger.info(
                    f"[{request_id}] 刷新令牌成功，新令牌过期时间: {expires_in}秒"
                )
                return result
            else:
                logger.error(f"[{request_id}] 刷新令牌失败: {result}")
                if result.get("code") == 99991663:  # 刷新令牌无效
                    raise FeishuAuthError("刷新令牌已过期或无效，需要重新授权")
                return result

    except httpx.RequestError as e:
        logger.error(f"[{request_id}] 刷新令牌网络错误: {str(e)}")
        raise FeishuNetworkError(f"网络请求失败: {str(e)}")
    except FeishuAuthError:
        raise  # 重新抛出认证错误
    except Exception as e:
        logger.error(f"[{request_id}] 刷新令牌异常: {str(e)}")
        raise FeishuAPIError(-1, f"刷新令牌异常: {str(e)}")
    finally:
        elapsed_time = time.time() - start_time
        logger.debug(
            f"[{request_id}] 结束执行 refresh_token, 耗时: {elapsed_time:.4f}秒"
        )


@api_logger
@feishu_retry
async def get_user_info(access_token: str):
    """获取用户信息"""
    try:
        async with httpx.AsyncClient(timeout=30.0) as client:
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json; charset=utf-8",
            }

            r = await client.get(
                "https://open.feishu.cn/open-apis/authen/v1/user_info", headers=headers
            )

            result = handle_api_response(r, "获取用户信息")

            if result.get("code") != 0:
                logger.error(f"获取用户信息失败: {result}")

            return result

    except httpx.RequestError as e:
        logger.error(f"获取用户信息网络错误: {str(e)}")
        raise FeishuNetworkError(f"网络请求失败: {str(e)}")
    except Exception as e:
        logger.error(f"获取用户信息发生未知错误: {str(e)}")
        raise FeishuAPIError(-1, f"未知错误: {str(e)}")


# 日历相关API
@api_logger
async def get_calendars(
    access_token: str, page_token: str = None, page_size: int = 100
):
    """获取用户的日历列表"""
    logger.debug(f"获取日历列表 - 参数: page_token={page_token}, page_size={page_size}")

    try:
        async with httpx.AsyncClient() as client:
            params = {}
            if page_token:
                params["page_token"] = page_token
            if page_size:
                params["page_size"] = page_size

            logger.debug(f"获取日历列表请求参数: {params}")

            r = await client.get(
                "https://open.feishu.cn/open-apis/calendar/v4/calendars",
                headers={"Authorization": f"Bearer {access_token}"},
                params=params,
            )
            return handle_api_response(r, "获取日历列表")
    except httpx.RequestError as e:
        logger.error(f"获取日历列表请求错误: {str(e)}")
        return {"code": -1, "msg": f"请求错误: {str(e)}"}
    except Exception as e:
        logger.error(f"获取日历列表发生未知错误: {str(e)}")
        return {"code": -1, "msg": f"未知错误: {str(e)}"}


async def get_calendar_detail(access_token: str, calendar_id: str):
    """获取特定日历的详细信息"""
    logger.debug(f"获取日历详情 - 参数: calendar_id={calendar_id}")
    async with httpx.AsyncClient() as client:
        r = await client.get(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}",
            headers={"Authorization": f"Bearer {access_token}"},
        )
        return handle_api_response(r, "获取日历详情")


async def create_calendar(
    access_token: str,
    summary: str,
    description: str = "",
    permissions: str = "private",
    color: int = 1,
):
    """创建新的日历

    参数:
        access_token: 访问令牌
        summary: 日历名称
        description: 日历描述（可选）
        permissions: 权限设置，可选值: private（私有）, show_only_free_busy（仅显示忙闲）, public（公开）
        color: 日历颜色，1-10的整数
    """
    # 确保json模块可用
    import json

    logger.debug(
        f"创建日历 - 参数: summary={summary}, description={description}, permissions={permissions}, color={color}"
    )
    async with httpx.AsyncClient() as client:
        # 确保summary和description是字符串类型
        if summary is not None:
            summary = str(summary)
        if description is not None:
            description = str(description)

        data = {
            "summary": summary,
            "description": description,
            "permissions": permissions,
            "color": color,
        }
        logger.debug(f"创建日历请求数据: {data}")

        # 使用json.dumps确保中文字符正确编码
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json; charset=utf-8",
        }

        # 记录完整的请求内容
        # request_body = {"calendar": data}   API文档中没有calendar字段, 所以不使用,这段AI生成的代码是错的
        request_body = data
        logger.debug(f"完整请求体: {json.dumps(request_body, ensure_ascii=False)}")

        r = await client.post(
            "https://open.feishu.cn/open-apis/calendar/v4/calendars",
            headers=headers,
            json=request_body,
        )

        # 记录原始响应
        response_text = r.text
        logger.debug(f"原始响应: {response_text}")

        # 处理响应
        result = handle_api_response(r, "创建日历")

        return result


@api_logger
async def update_event(
    access_token: str,
    calendar_id: str,
    event_id: str,
    summary: str = None,
    start_time: dict = None,
    end_time: dict = None,
    description: str = None,
    location: dict = None,
    reminders: List[dict] = None,
    is_all_day: bool = None,
    status: str = None,
):
    """更新日历事件

    参数:
        access_token: 访问令牌
        calendar_id: 日历ID
        event_id: 事件ID
        summary: 事件标题（可选）
        start_time: 开始时间，格式为 {"timestamp": 时间戳} （可选）
        end_time: 结束时间，格式同上（可选）
        description: 事件描述（可选）
        location: 地点信息，格式为 {"name": "地点名称"} （可选）
        reminders: 提醒设置，格式为 [{"minutes": 10}, {"minutes": 30}] （可选）
        is_all_day: 是否为全天事件（可选）
        status: 事件状态，可选值为'confirmed'(默认)、'tentative'、'cancelled'（可选）
    """
    logger.debug(f"更新日历事件 - 参数: calendar_id={calendar_id}, event_id={event_id}")

    try:
        async with httpx.AsyncClient() as client:
            # 构建更新数据
            data = {}

            # 只添加非None的字段
            if summary is not None:
                data["summary"] = summary
            if description is not None:
                data["description"] = description
            if start_time is not None:
                data["start_time"] = start_time
            if end_time is not None:
                data["end_time"] = end_time
            if location is not None:
                data["location"] = location
            if reminders is not None:
                data["reminders"] = reminders
            if is_all_day is not None:
                data["is_all_day"] = is_all_day
            if status is not None:
                data["status"] = status

            # 如果没有需要更新的字段，直接返回
            if not data:
                logger.warning("没有提供任何需要更新的字段")
                return {"code": 0, "msg": "没有需要更新的字段"}

            logger.debug(f"更新事件请求数据: {json.dumps(data, ensure_ascii=False)}")

            # 发送请求
            r = await client.patch(
                f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}",
                headers={
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json; charset=utf-8",
                },
                json=data,
            )

            response = handle_api_response(r, "更新日历事件")

            # 获取日历名称并添加到响应中
            calendar_name = await get_calendar_name(access_token, calendar_id)

            # 将日历名称添加到响应中
            if "code" in response and response["code"] == 0 and "data" in response:
                response["data"]["calendar_name"] = calendar_name

                # 如果事件对象存在，也将日历名称添加到事件对象中
                if "event" in response["data"]:
                    response["data"]["event"]["calendar_name"] = calendar_name

            return response
    except Exception as e:
        error_msg = f"更新日历事件发生错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg}


async def delete_calendar(access_token: str, calendar_id: str):
    """删除日历"""
    logger.debug(f"删除日历 - 参数: calendar_id={calendar_id}")
    async with httpx.AsyncClient() as client:
        r = await client.delete(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}",
            headers={"Authorization": f"Bearer {access_token}"},
        )
        return handle_api_response(r, "删除日历")


@api_logger
async def get_today_events(
    access_token: str, calendar_id: str = "primary", query: str = None
):
    """获取今天的日历事件"""
    now = datetime.now()
    date = now.strftime("%Y-%m-%d")
    logger.debug(
        f"获取今天的日历事件 - 参数: calendar_id={calendar_id}, date={date}, query={query}"
    )

    async with httpx.AsyncClient() as client:
        # 使用ISO格式时间字符串
        start_time_iso = f"{date}T00:00:00+08:00"
        end_time_iso = f"{date}T23:59:59+08:00"

        # 转换为时间戳格式
        start_time = convert_to_timestamp(start_time_iso)
        end_time = convert_to_timestamp(end_time_iso)

        logger.debug(f"今天的时间范围: start_time={start_time}, end_time={end_time}")
        logger.debug(f"开始调用get_calendar_events获取今天事件")

        # 使用get_calendar_events函数，它已经包含了获取日历名称的逻辑
        result = await get_calendar_events(
            access_token,
            calendar_id=calendar_id,
            start_time=start_time,
            end_time=end_time,
            query=query,
        )

        logger.debug(
            f"获取今天事件完成，结果: {json.dumps(result, ensure_ascii=False, default=str)[:1000]}..."
        )
        return result


async def get_calendar_events(
    access_token: str,
    calendar_id: str,
    start_time: str = None,
    end_time: str = None,
    page_token: str = None,
    page_size: int = 100,
    query: str = None,
):
    """获取指定日历的事件列表

    参数:
        access_token: 访问令牌
        calendar_id: 日历ID
        start_time: 开始时间，ISO格式或时间戳
        end_time: 结束时间，ISO格式或时间戳
        page_token: 分页标记
        page_size: 每页大小
        query: 搜索查询字符串（可选），将用于在客户端过滤summary字段
    """
    logger.debug(
        f"获取日历事件列表 - 参数: calendar_id={calendar_id}, start_time={start_time}, end_time={end_time}, page_token={page_token}, page_size={page_size}, query={query}"
    )
    print(
        f"获取日历事件列表 - 参数: calendar_id={calendar_id}, start_time={start_time}, end_time={end_time}, page_token={page_token}, page_size={page_size}, query={query}"
    )

    async with httpx.AsyncClient() as client:
        params = {}
        if start_time:
            # 转换为时间戳格式
            timestamp = convert_to_timestamp(start_time)
            params["start_time"] = timestamp
        if end_time:
            # 转换为时间戳格式
            timestamp = convert_to_timestamp(end_time)
            params["end_time"] = timestamp
        if page_token:
            params["page_token"] = page_token
        if page_size:
            params["page_size"] = page_size
        # 不将query作为请求参数发送

        logger.debug(f"获取日历事件列表请求参数: {params}")
        print(f"获取日历事件列表请求参数: {params}")

        # 记录完整的请求URL和请求头
        request_url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events"
        request_headers = {"Authorization": f"Bearer {access_token}"}
        logger.debug(f"获取日历事件请求URL: {request_url}")
        logger.debug(
            f"获取日历事件请求头: {json.dumps(request_headers, ensure_ascii=False)}"
        )
        logger.debug(
            f"获取日历事件完整请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}"
        )
        print(f"获取日历事件请求URL: {request_url}")
        print(f"获取日历事件请求头: {json.dumps(request_headers, ensure_ascii=False)}")
        print(
            f"获取日历事件完整请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}"
        )

        r = await client.get(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events",
            headers={"Authorization": f"Bearer {access_token}"},
            params=params,
        )

        # 记录原始响应
        response_text = r.text
        logger.debug(f"获取日历事件原始响应: {response_text}")
        print(f"获取日历事件原始响应: {response_text[:1000]}...")  # 只打印前1000个字符

        response = handle_api_response(r, "获取日历事件列表")
        logger.debug(f"API响应原始数据: {json.dumps(response, ensure_ascii=False)}")
        print(
            f"API响应原始数据: {json.dumps(response, ensure_ascii=False)[:1000]}..."
        )  # 只打印前1000个字符

        # 如果有查询参数，进行客户端过滤
        if (
            query
            and "code" in response
            and response["code"] == 0
            and "data" in response
        ):
            logger.debug(
                f"过滤前数据结构: data键有以下字段: {list(response['data'].keys() if 'data' in response else [])}"
            )
            if "items" in response["data"]:
                logger.debug(f"原始items数量: {len(response['data']['items'])}")
                if response["data"]["items"]:
                    logger.debug(
                        f"第一个项目的结构: {json.dumps(response['data']['items'][0], ensure_ascii=False)}"
                    )

                filtered_items = []
                for item in response["data"]["items"]:
                    # 检查各种可能的字段名和结构
                    has_match = False
                    summary_value = None

                    # 检查summary字段
                    if "summary" in item:
                        summary_value = item["summary"]
                        logger.debug(f"找到summary字段: {summary_value}")
                        if query.lower() in str(summary_value).lower():
                            has_match = True
                            logger.debug(f"匹配成功: {query} 在 {summary_value} 中")

                    # 检查title字段，可能一些API返回使用这个名称
                    elif "title" in item:
                        summary_value = item["title"]
                        logger.debug(f"找到title字段: {summary_value}")
                        if query.lower() in str(summary_value).lower():
                            has_match = True
                            logger.debug(f"匹配成功: {query} 在 {summary_value} 中")

                    # 记录找不到匹配字段的情况
                    if summary_value is None:
                        logger.debug(
                            f"项目中没有找到summary或title字段: {json.dumps(item, ensure_ascii=False)}"
                        )

                    if has_match:
                        filtered_items.append(item)

                logger.debug(f"过滤后items数量: {len(filtered_items)}")
                response["data"]["items"] = filtered_items

        # 获取日历名称并添加到响应中
        calendar_name = await get_calendar_name(access_token, calendar_id)

        # 将日历名称添加到响应中
        if "code" in response and response["code"] == 0 and "data" in response:
            response["data"]["calendar_name"] = calendar_name

            # 为每个事件项添加日历名称
            if "items" in response["data"]:
                for item in response["data"]["items"]:
                    item["calendar_name"] = calendar_name

        return response


async def get_event_detail(access_token: str, calendar_id: str, event_id: str):
    """获取日历事件详情"""
    logger.debug(f"获取事件详情 - 参数: calendar_id={calendar_id}, event_id={event_id}")

    async with httpx.AsyncClient() as client:
        # 记录完整的请求URL和请求头
        request_url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}"
        request_headers = {"Authorization": f"Bearer {access_token}"}
        logger.debug(f"获取事件详情请求URL: {request_url}")
        logger.debug(
            f"获取事件详情请求头: {json.dumps(request_headers, ensure_ascii=False)}"
        )

        r = await client.get(request_url, headers=request_headers)

        # 记录原始响应
        response_text = r.text
        logger.debug(f"获取事件详情原始响应: {response_text}")

        response = handle_api_response(r, "获取事件详情")
        logger.debug(f"API响应原始数据: {json.dumps(response, ensure_ascii=False)}")

        # 获取日历名称并添加到响应中
        calendar_name = await get_calendar_name(access_token, calendar_id)

        # 将日历名称添加到响应中
        if "code" in response and response["code"] == 0 and "data" in response:
            response["data"]["calendar_name"] = calendar_name

            # 如果事件对象存在，也将日历名称添加到事件对象中
            if "event" in response["data"]:
                response["data"]["event"]["calendar_name"] = calendar_name

        return response


@api_logger
async def create_event(
    access_token: str,
    calendar_id: str,
    summary: str,
    start_time: dict,
    end_time: dict,
    description: str = None,
    location: dict = None,
    reminders: List[dict] = None,
    attendees: List[dict] = None,
    is_all_day: bool = False,
    recurrence: str = None,
):
    """创建日历事件

    参数:
        access_token: 访问令牌
        calendar_id: 日历ID
        summary: 事件标题
        start_time: 开始时间，格式为 {"timestamp": 时间戳} 或 {"date_time": "2023-01-01T10:00:00+08:00"}
        end_time: 结束时间，格式同上
        description: 事件描述（可选）
        location: 地点信息，格式为 {"name": "地点名称"} （可选）
        reminders: 提醒设置，格式为 [{"minutes": 10}, {"minutes": 30}] （可选）
        attendees: 参与者列表，格式为 [{"user_id": "用户ID"}] （可选）
        is_all_day: 是否为全天事件（可选）
        recurrence: 重复规则，遵循RFC 5545标准，如 "FREQ=WEEKLY;INTERVAL=1;BYDAY=MO,WE,FR"（可选）
                   支持的频率(FREQ): DAILY, WEEKLY, MONTHLY, YEARLY
                   支持的参数:
                   - INTERVAL: 间隔，如每2周一次 INTERVAL=2
                   - COUNT: 重复次数，如重复10次 COUNT=10
                   - UNTIL: 结束日期，如 UNTIL=20241231T235959Z
                   - BYDAY: 每周哪几天，如 BYDAY=MO,WE,FR (周一、三、五)
                   - BYMONTHDAY: 每月哪几天，如 BYMONTHDAY=1,15 (每月1号和15号)
                   - BYMONTH: 每年哪几个月，如 BYMONTH=1,6,12 (一月、六月、十二月)
    """
    logger.debug(f"创建日历事件 - 参数: calendar_id={calendar_id}, summary={summary}")
    logger.debug(f"开始时间: {start_time}, 类型: {type(start_time)}")
    logger.debug(f"结束时间: {end_time}, 类型: {type(end_time)}")
    logger.debug(
        f"其他参数: description={description}, location={location}, reminders={reminders}, attendees={attendees}, is_all_day={is_all_day}"
    )
    logger.debug(f"重复规则: {recurrence}")

    try:
        async with httpx.AsyncClient() as client:
            # 构建事件数据
            data = {"summary": summary}

            # 处理开始时间格式
            if start_time:
                if isinstance(start_time, str):
                    # 如果是ISO格式的时间字符串，转换为timestamp格式
                    logger.debug(
                        f"转换start_time格式，从字符串 {start_time} 到timestamp格式"
                    )
                    try:
                        # 修复可能的ISO格式问题（日期部分不是固定位数）
                        if "T" in start_time:
                            date_part, time_part = start_time.split("T", 1)
                            date_parts = date_part.split("-")
                            if len(date_parts) == 3:
                                # 确保年月日都是固定位数
                                year = date_parts[0].zfill(4)
                                month = date_parts[1].zfill(2)
                                day = date_parts[2].zfill(2)
                                start_time = f"{year}-{month}-{day}T{time_part}"
                                logger.debug(f"修复后的ISO格式: {start_time}")

                        dt = datetime.fromisoformat(start_time.replace("Z", "+00:00"))
                        timestamp = str(int(dt.timestamp()))
                        data["start_time"] = {
                            "timestamp": timestamp,
                            "timezone": "Asia/Shanghai",
                        }
                        logger.debug(f"转换后的时间戳: {timestamp}")
                    except Exception as e:
                        logger.error(f"时间格式转换错误: {str(e)}")
                        raise ValueError(f"无效的开始时间格式: {start_time}")
                else:
                    data["start_time"] = start_time

            # 处理结束时间格式
            if end_time:
                if isinstance(end_time, str):
                    # 如果是ISO格式的时间字符串，转换为timestamp格式
                    logger.debug(
                        f"转换end_time格式，从字符串 {end_time} 到timestamp格式"
                    )
                    try:
                        # 修复可能的ISO格式问题（日期部分不是固定位数）
                        if "T" in end_time:
                            date_part, time_part = end_time.split("T", 1)
                            date_parts = date_part.split("-")
                            if len(date_parts) == 3:
                                # 确保年月日都是固定位数
                                year = date_parts[0].zfill(4)
                                month = date_parts[1].zfill(2)
                                day = date_parts[2].zfill(2)
                                end_time = f"{year}-{month}-{day}T{time_part}"
                                logger.debug(f"修复后的ISO格式: {end_time}")

                        dt = datetime.fromisoformat(end_time.replace("Z", "+00:00"))
                        timestamp = str(int(dt.timestamp()))
                        data["end_time"] = {
                            "timestamp": timestamp,
                            "timezone": "Asia/Shanghai",
                        }
                        logger.debug(f"转换后的时间戳: {timestamp}")
                    except Exception as e:
                        logger.error(f"时间格式转换错误: {str(e)}")
                        raise ValueError(f"无效的结束时间格式: {end_time}")
                else:
                    data["end_time"] = end_time

            # 添加其他可选参数
            if description is not None:
                data["description"] = description

            if location is not None:
                data["location"] = location

            # 处理提醒设置，如果未提供则设置默认提醒（提前5分钟）
            if reminders is not None:
                data["reminders"] = reminders
            else:
                data["reminders"] = [{"minutes": 5}]  # 默认提前5分钟提醒

            if attendees is not None:
                data["attendees"] = attendees

            if is_all_day is not None:
                data["is_all_day"] = is_all_day

            if recurrence is not None:
                data["recurrence"] = recurrence

            # 记录完整请求数据
            logger.debug(f"创建事件请求数据: {json.dumps(data, ensure_ascii=False)}")

            # 记录完整的请求URL和请求头
            request_url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events"
            request_headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json; charset=utf-8",
            }
            logger.debug(f"创建事件请求URL: {request_url}")
            logger.debug(
                f"创建事件请求头: {json.dumps(request_headers, ensure_ascii=False)}"
            )
            logger.debug(
                f"创建事件完整请求体: {json.dumps(data, ensure_ascii=False, indent=2)}"
            )

            # 发送请求
            r = await client.post(request_url, headers=request_headers, json=data)

            # 记录原始响应
            response_text = r.text
            logger.debug(f"创建事件原始响应: {response_text}")

            response = handle_api_response(r, "创建日历事件")

            # 获取日历名称并添加到响应中
            calendar_name = await get_calendar_name(access_token, calendar_id)

            # 将日历名称添加到响应中
            if "code" in response and response["code"] == 0 and "data" in response:
                response["data"]["calendar_name"] = calendar_name

                # 如果事件对象存在，也将日历名称添加到事件对象中
                if "event" in response["data"]:
                    response["data"]["event"]["calendar_name"] = calendar_name

            return response
    except ValueError as e:
        error_msg = str(e)
        logger.error(f"创建日历事件参数错误: {error_msg}")
        return {"code": -1, "msg": error_msg}
    except Exception as e:
        error_msg = f"创建日历事件发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg}


@api_logger
async def delete_event(access_token: str, calendar_id: str, event_id: str):
    """删除日历事件"""
    logger.debug(f"删除日历事件 - 参数: calendar_id={calendar_id}, event_id={event_id}")

    async with httpx.AsyncClient() as client:
        r = await client.delete(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}",
            headers={"Authorization": f"Bearer {access_token}"},
        )
        return handle_api_response(r, "删除日历事件")


async def search_calendar_events(
    access_token: str,
    query: str,
    calendar_id: str = "primary",
    page_token: str = None,
    page_size: int = 50,
):
    """搜索日历事件

    参数:
        access_token: 访问令牌
        query: 搜索关键词
        calendar_id: 日历ID，默认为"primary"
        page_token: 分页标记
        page_size: 每页大小
    """
    logger.debug(
        f"搜索日历事件 - 参数: query={query}, calendar_id={calendar_id}, page_token={page_token}, page_size={page_size}"
    )

    async with httpx.AsyncClient() as client:
        # 构建请求参数
        params = {}
        if page_token:
            params["page_token"] = page_token
        if page_size:
            params["page_size"] = page_size

        # 构建请求体
        data = {"query": query}

        logger.debug(f"搜索日历事件请求参数: {params}")
        logger.debug(f"搜索日历事件请求体: {data}")

        # 记录完整的请求URL和请求头
        request_url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/events/search"
        request_headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json",
        }
        logger.debug(f"搜索日历事件请求URL: {request_url}")
        logger.debug(
            f"搜索日历事件请求头: {json.dumps(request_headers, ensure_ascii=False)}"
        )
        logger.debug(
            f"搜索日历事件完整请求参数: {json.dumps(params, ensure_ascii=False, indent=2)}"
        )
        logger.debug(
            f"搜索日历事件完整请求体: {json.dumps(data, ensure_ascii=False, indent=2)}"
        )

        # 发送POST请求
        r = await client.post(
            request_url, headers=request_headers, params=params, json=data
        )

        # 记录原始响应
        response_text = r.text
        logger.debug(f"搜索日历事件原始响应: {response_text}")

        response = handle_api_response(r, "搜索日历事件")
        logger.debug(f"API响应原始数据: {json.dumps(response, ensure_ascii=False)}")

        # 获取日历名称并添加到响应中
        calendar_name = await get_calendar_name(access_token, calendar_id)

        # 将日历名称添加到响应中
        if "code" in response and response["code"] == 0 and "data" in response:
            response["data"]["calendar_name"] = calendar_name

            # 为每个事件项添加日历名称
            if "items" in response["data"]:
                for item in response["data"]["items"]:
                    item["calendar_name"] = calendar_name

        return response


# 批量处理相关API
@api_logger
async def batch_create_events(access_token: str, events: List[Dict[str, Any]]):
    """批量创建日历事件

    参数:
        access_token: 访问令牌
        events: 事件列表，每个事件包含calendar_id、summary、start_time、end_time等信息

    返回:
        Dict: 包含成功和失败的事件ID列表
    """
    logger.debug(f"批量创建日历事件 - 事件数量: {len(events)}")
    logger.debug(f"批量创建事件详情: {events}")

    try:
        results = {"success": [], "failed": []}
        tasks = []

        for event in events:
            calendar_id = event.get("calendar_id", "primary")
            task = create_event(
                access_token=access_token,
                calendar_id=calendar_id,
                summary=event.get("summary", "未命名事件"),
                start_time=event.get("start_time"),
                end_time=event.get("end_time"),
                description=event.get("description"),
                location=event.get("location"),
                reminders=event.get("reminders"),
                attendees=event.get("attendees"),
                is_all_day=event.get("is_all_day", False),
                recurrence=event.get("recurrence"),
            )
            tasks.append(task)

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                results["failed"].append(
                    {"index": i, "event": events[i], "error": str(response)}
                )
            else:
                if response.get("code") == 0:
                    event_id = response.get("data", {}).get("event", {}).get("event_id")
                    if event_id:
                        results["success"].append({"index": i, "event_id": event_id})
                else:
                    results["failed"].append(
                        {
                            "index": i,
                            "event": events[i],
                            "error": response.get("msg", "未知错误"),
                        }
                    )

        logger.debug(f"批量创建事件结果: {results}")
        return results
    except Exception as e:
        error_msg = f"批量创建事件发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg, "success": [], "failed": events}


@api_logger
async def batch_update_events(access_token: str, events: List[Dict[str, Any]]):
    """批量更新日历事件

    参数:
        access_token: 访问令牌
        events: 事件列表，每个事件必须包含calendar_id和event_id，以及要更新的字段

    返回:
        Dict: 包含成功和失败的事件ID列表
    """
    logger.debug(f"批量更新日历事件 - 事件数量: {len(events)}")
    logger.debug(f"批量更新事件详情: {events}")

    try:
        results = {"success": [], "failed": []}
        tasks = []

        for event in events:
            calendar_id = event.get("calendar_id", "primary")
            event_id = event.get("event_id")

            if not event_id:
                results["failed"].append({"event": event, "error": "缺少event_id"})
                continue

            task = update_event(
                access_token=access_token,
                calendar_id=calendar_id,
                event_id=event_id,
                summary=event.get("summary"),
                start_time=event.get("start_time"),
                end_time=event.get("end_time"),
                description=event.get("description"),
                location=event.get("location"),
                reminders=event.get("reminders"),
                is_all_day=event.get("is_all_day"),
                status=event.get("status"),
            )
            tasks.append(task)

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                results["failed"].append(
                    {"index": i, "event": events[i], "error": str(response)}
                )
            else:
                if response.get("code") == 0:
                    results["success"].append(
                        {"index": i, "event_id": events[i].get("event_id")}
                    )
                else:
                    results["failed"].append(
                        {
                            "index": i,
                            "event": events[i],
                            "error": response.get("msg", "未知错误"),
                        }
                    )

        logger.debug(f"批量更新事件结果: {results}")
        return results
    except Exception as e:
        error_msg = f"批量更新事件发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg, "success": [], "failed": events}


@api_logger
async def batch_delete_events(access_token: str, events: List[Dict[str, str]]):
    """批量删除日历事件

    参数:
        access_token: 访问令牌
        events: 事件列表，每个事件必须包含calendar_id和event_id

    返回:
        Dict: 包含成功和失败的事件ID列表
    """
    logger.debug(f"批量删除日历事件 - 事件数量: {len(events)}")
    logger.debug(f"批量删除事件详情: {events}")

    try:
        results = {"success": [], "failed": []}
        tasks = []

        for event in events:
            calendar_id = event.get("calendar_id", "primary")
            event_id = event.get("event_id")

            if not event_id:
                results["failed"].append({"event": event, "error": "缺少event_id"})
                continue

            task = delete_event(
                access_token=access_token, calendar_id=calendar_id, event_id=event_id
            )
            tasks.append(task)

        responses = await asyncio.gather(*tasks, return_exceptions=True)

        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                results["failed"].append(
                    {"index": i, "event": events[i], "error": str(response)}
                )
            else:
                if response.get("code") == 0:
                    results["success"].append(
                        {"index": i, "event_id": events[i].get("event_id")}
                    )
                else:
                    results["failed"].append(
                        {
                            "index": i,
                            "event": events[i],
                            "error": response.get("msg", "未知错误"),
                        }
                    )

        logger.debug(f"批量删除事件结果: {results}")
        return results
    except Exception as e:
        error_msg = f"批量删除事件发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg, "success": [], "failed": events}


# 辅助函数，处理API响应
def handle_api_response(response, operation_name):
    """统一处理API响应，记录错误并返回标准化结果

    参数:
        response: httpx响应对象
        operation_name: 操作名称，用于日志记录

    返回:
        Dict: 处理后的响应数据
    """
    # 导入json模块，确保在所有代码路径中都可用
    import json

    try:
        # 记录原始响应状态码和头信息
        logger.debug(f"{operation_name}响应状态码: {response.status_code}")
        logger.debug(f"{operation_name}响应头: {dict(response.headers)}")

        # 尝试解析JSON响应
        try:
            response_data = response.json()
            logger.debug(f"{operation_name}响应成功解析为JSON")
        except Exception as e:
            # 如果JSON解析失败，记录原始响应文本
            logger.error(f"{operation_name}响应JSON解析失败: {str(e)}")
            logger.debug(f"原始响应文本: {response.text}")

            # 尝试手动解析JSON，处理可能的编码问题
            try:
                response_text = response.text
                logger.debug(f"尝试手动解析JSON，原始文本长度: {len(response_text)}")
                if len(response_text) > 200:
                    logger.debug(f"原始文本前200字符: {response_text[:200]}...")
                else:
                    logger.debug(f"完整原始文本: {response_text}")

                response_data = json.loads(response_text)
                logger.debug("手动JSON解析成功")
            except Exception as e2:
                logger.error(f"手动JSON解析也失败: {str(e2)}")
                return {
                    "code": -1,
                    "msg": f"响应解析错误: {str(e2)}",
                    "raw_text": response.text,
                }

        # 记录响应数据
        if "code" in response_data:
            logger.debug(f"{operation_name}响应码: {response_data.get('code')}")

        # 如果响应数据太大，只记录部分
        response_str = json.dumps(response_data, ensure_ascii=False)
        if len(response_str) > 500:
            logger.debug(f"{operation_name}响应(截断): {response_str[:500]}...")
        else:
            logger.debug(f"{operation_name}响应: {response_str}")

        # 检查HTTP状态码
        if response.status_code != 200:
            logger.error(
                f"{operation_name}失败，状态码: {response.status_code}, 响应: {json.dumps(response_data, ensure_ascii=False)}"
            )
            return {
                "code": response.status_code,
                "msg": f"HTTP错误: {response.status_code}",
                "data": response_data,
            }

        # 检查API错误码
        if response_data.get("code") != 0:
            error_msg = response_data.get("msg", "未知错误")
            error_code = response_data.get("code")
            logger.error(
                f"{operation_name}API错误，错误码: {error_code}, 消息: {error_msg}"
            )

            # 记录更详细的错误信息
            if "error" in response_data:
                error_details = response_data.get("error", {})
                if error_details:
                    logger.error(
                        f"错误详情: {json.dumps(error_details, ensure_ascii=False)}"
                    )
                    if "log_id" in error_details:
                        logger.error(f"错误日志ID: {error_details.get('log_id')}")
                    if "troubleshooter" in error_details:
                        logger.error(f"排查建议: {error_details.get('troubleshooter')}")

        # 转换时间戳为ISO格式
        if response_data.get("code") == 0 and "data" in response_data:
            # 处理单个事件的情况
            if "event" in response_data["data"]:
                event = response_data["data"]["event"]
                if "start_time" in event and "timestamp" in event["start_time"]:
                    event["start_time"]["iso_format"] = timestamp_to_iso(
                        event["start_time"]["timestamp"]
                    )
                if "end_time" in event and "timestamp" in event["end_time"]:
                    event["end_time"]["iso_format"] = timestamp_to_iso(
                        event["end_time"]["timestamp"]
                    )

                # 如果响应中包含日历名称，将其添加到事件中
                if "calendar_name" in response_data["data"]:
                    event["calendar_name"] = response_data["data"]["calendar_name"]

            # 处理事件列表的情况
            if "items" in response_data["data"]:
                for item in response_data["data"]["items"]:
                    if "start_time" in item and "timestamp" in item["start_time"]:
                        item["start_time"]["iso_format"] = timestamp_to_iso(
                            item["start_time"]["timestamp"]
                        )
                    if "end_time" in item and "timestamp" in item["end_time"]:
                        item["end_time"]["iso_format"] = timestamp_to_iso(
                            item["end_time"]["timestamp"]
                        )

                    # 如果响应中包含日历名称，将其添加到每个事件项中
                    if "calendar_name" in response_data["data"]:
                        item["calendar_name"] = response_data["data"]["calendar_name"]

        return response_data
    except Exception as e:
        logger.error(f"{operation_name}响应处理错误: {str(e)}")
        logger.debug(
            f"原始响应文本: {response.text if hasattr(response, 'text') else '无法获取响应文本'}"
        )
        return {
            "code": -1,
            "msg": f"响应解析错误: {str(e)}",
            "raw_text": response.text if hasattr(response, "text") else None,
        }


# 实用工具函数
def convert_to_timestamp(time_value):
    """将各种时间格式统一转换为时间戳字符串

    参数:
        time_value: 时间值，可以是datetime对象、时间戳字符串或ISO格式字符串

    返回:
        str: 时间戳字符串（Unix时间戳，单位为秒）
    """
    logger.debug(f"转换时间格式 - 输入: {time_value}, 类型: {type(time_value)}")

    if time_value is None:
        logger.debug("时间值为None，返回None")
        return None

    if isinstance(time_value, datetime):
        # 如果是datetime对象，直接转换为时间戳
        result = str(int(time_value.timestamp()))
        logger.debug(f"datetime对象转换为时间戳: {result}")
        return result

    if isinstance(time_value, (int, float)):
        # 如果是数字（时间戳），转为字符串
        result = str(int(time_value))
        logger.debug(f"数字转换为时间戳字符串: {result}")
        return result

    if isinstance(time_value, str):
        # 如果是字符串，判断是否已经是数字格式
        try:
            timestamp = int(time_value)  # 检查是否可以转换为整数
            logger.debug(f"字符串已经是时间戳格式: {time_value}")
            return time_value  # 已经是时间戳字符串，直接返回
        except ValueError:
            # 不是数字字符串，尝试解析为datetime
            try:
                # 处理ISO格式字符串
                logger.debug(f"尝试解析ISO格式字符串: {time_value}")
                dt = datetime.fromisoformat(time_value.replace("Z", "+00:00"))
                result = str(int(dt.timestamp()))
                logger.debug(f"ISO格式字符串转换为时间戳: {result}")
                return result
            except Exception as e:
                logger.debug(f"ISO格式解析失败: {str(e)}")
                # 如果是HH:MM格式（如"10:00"），与当前日期组合
                if ":" in time_value and len(time_value) <= 5:
                    try:
                        hour, minute = map(int, time_value.split(":"))
                        today = datetime.now().date()
                        dt = datetime.combine(
                            today, datetime.min.time().replace(hour=hour, minute=minute)
                        )
                        result = str(int(dt.timestamp()))
                        logger.debug(f"时间字符串(HH:MM)转换为今天的时间戳: {result}")
                        return result
                    except Exception as e:
                        logger.debug(f"HH:MM格式解析失败: {str(e)}")

                logger.debug(f"无法解析的时间格式，返回原值: {time_value}")
                return time_value  # 无法解析，返回原值

    # 其他类型，尝试转换为字符串
    logger.debug(f"未知类型的时间值，转为字符串: {str(time_value)}")
    return str(time_value)


def timestamp_to_iso(timestamp, timezone="Asia/Shanghai"):
    """将时间戳转换为ISO 8601格式的日期时间字符串

    参数:
        timestamp: 时间戳，可以是整数、浮点数或字符串
        timezone: 时区，默认为"Asia/Shanghai"

    返回:
        str: ISO 8601格式的日期时间字符串，如"2023-05-20T14:30:00+08:00"
    """
    logger.debug(f"将时间戳转换为ISO格式 - 输入: {timestamp}, 类型: {type(timestamp)}")

    if timestamp is None:
        logger.debug("时间戳为None，返回None")
        return None

    try:
        # 转换为整数时间戳
        if isinstance(timestamp, str):
            timestamp = int(timestamp)
        elif isinstance(timestamp, dict) and "timestamp" in timestamp:
            timestamp = int(timestamp["timestamp"])

        # 创建datetime对象
        dt = datetime.fromtimestamp(timestamp)

        # 转换为ISO格式
        iso_format = dt.strftime("%Y-%m-%dT%H:%M:%S+08:00")
        logger.debug(f"时间戳 {timestamp} 转换为ISO格式: {iso_format}")
        return iso_format
    except Exception as e:
        logger.error(f"时间戳转换为ISO格式失败: {str(e)}")
        return None


@api_logger
async def create_daily_task(
    access_token: str,
    summary: str,
    start_time: Union[datetime, str],
    duration_minutes: int = 60,
    description: str = "",
    calendar_id: str = "primary",
    location: str = None,
    reminders: List[Dict[str, int]] = None,
):
    """创建当天任务

    参数:
        access_token: 访问令牌
        summary: 任务标题
        start_time: 开始时间，可以是datetime对象或时间字符串(格式为"10:00")
        duration_minutes: 任务持续时间(分钟)
        description: 任务描述
        calendar_id: 日历ID
        location: 地点
        reminders: 提醒设置，格式为[{"minutes": 10}, {"minutes": 30}]

    返回:
        Dict: API响应结果
    """
    logger.debug(
        f"创建当天任务 - 参数: summary={summary}, start_time={start_time}, duration_minutes={duration_minutes}"
    )
    logger.debug(f"开始时间类型: {type(start_time)}")

    try:
        # 处理开始时间
        start_datetime = None
        if isinstance(start_time, str) and ":" in start_time and len(start_time) <= 5:
            # 如果是时间字符串，如"10:00"
            try:
                hour, minute = map(int, start_time.split(":"))
                today = datetime.now().date()
                start_datetime = datetime.combine(
                    today, datetime.min.time().replace(hour=hour, minute=minute)
                )
                logger.debug(f"解析时间字符串 {start_time} 为今天的 {start_datetime}")
            except Exception as e:
                error_msg = (
                    f"无法解析时间格式: {start_time}，应为HH:MM格式, 错误: {str(e)}"
                )
                logger.error(error_msg)
                raise ValueError(error_msg)
        elif isinstance(start_time, datetime):
            start_datetime = start_time
            logger.debug(f"使用提供的datetime对象: {start_datetime}")
        else:
            # 尝试使用辅助函数解析时间
            timestamp = convert_to_timestamp(start_time)
            try:
                start_datetime = datetime.fromtimestamp(int(timestamp))
                logger.debug(f"从时间戳 {timestamp} 解析为datetime: {start_datetime}")
            except Exception as e:
                error_msg = f"无法解析开始时间: {start_time}, 错误: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)

        # 计算结束时间
        end_datetime = start_datetime + timedelta(minutes=duration_minutes)
        logger.debug(f"计算结束时间: {end_datetime}, 持续时间: {duration_minutes}分钟")

        # 准备时间格式
        start_time_dict = {"timestamp": str(int(start_datetime.timestamp()))}
        end_time_dict = {"timestamp": str(int(end_datetime.timestamp()))}

        logger.debug(
            f"创建当天任务时间字典: start_time={start_time_dict}, end_time={end_time_dict}"
        )

        # 准备地点格式
        location_dict = None
        if location:
            location_dict = {"name": location}
            logger.debug(f"设置地点: {location_dict}")

        # 创建日程
        result = await create_event(
            access_token=access_token,
            calendar_id=calendar_id,
            summary=summary,
            start_time=start_time_dict,
            end_time=end_time_dict,
            description=description,
            location=location_dict,
            reminders=reminders,
        )

        logger.debug(f"创建当天任务结果: {result}")
        return result
    except ValueError as e:
        error_msg = str(e)
        logger.error(f"创建当天任务参数错误: {error_msg}")
        return {"code": -1, "msg": error_msg}
    except Exception as e:
        error_msg = f"创建当天任务发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg}


@api_logger
async def create_task_for_date(
    access_token: str,
    summary: str,
    task_date: Union[date, str],
    start_time: str,
    duration_minutes: int = 60,
    description: str = "",
    calendar_id: str = "primary",
    location: str = None,
    reminders: List[Dict[str, int]] = None,
):
    """为特定日期创建任务

    参数:
        access_token: 访问令牌
        summary: 任务标题
        task_date: 任务日期，可以是date对象或日期字符串(格式为"2023-01-01")
        start_time: 开始时间，时间字符串(格式为"10:00")
        duration_minutes: 任务持续时间(分钟)
        description: 任务描述
        calendar_id: 日历ID
        location: 地点
        reminders: 提醒设置，格式为[{"minutes": 10}, {"minutes": 30}]

    返回:
        Dict: API响应结果
    """
    logger.debug(
        f"为特定日期创建任务 - 参数: summary={summary}, task_date={task_date}, start_time={start_time}, duration_minutes={duration_minutes}"
    )

    try:
        # 处理日期
        task_date_obj = None
        if isinstance(task_date, date):
            task_date_obj = task_date
            logger.debug(f"使用提供的date对象: {task_date_obj}")
        elif isinstance(task_date, str):
            # 尝试解析日期字符串
            try:
                # 支持多种日期格式
                if "-" in task_date:
                    # 格式如 "2023-01-01"
                    task_date_obj = datetime.strptime(task_date, "%Y-%m-%d").date()
                elif "/" in task_date:
                    # 格式如 "2023/01/01"
                    task_date_obj = datetime.strptime(task_date, "%Y/%m/%d").date()
                else:
                    # 尝试其他可能的格式
                    try:
                        task_date_obj = datetime.strptime(task_date, "%Y%m%d").date()
                    except ValueError:
                        # 如果是"明天"、"后天"等描述性日期
                        if task_date == "今天":
                            task_date_obj = datetime.now().date()
                        elif task_date == "明天":
                            task_date_obj = datetime.now().date() + timedelta(days=1)
                        elif task_date == "后天":
                            task_date_obj = datetime.now().date() + timedelta(days=2)
                        else:
                            raise ValueError(f"无法解析日期格式: {task_date}")

                logger.debug(f"解析日期字符串 {task_date} 为 {task_date_obj}")
            except Exception as e:
                error_msg = f"无法解析日期格式: {task_date}, 错误: {str(e)}"
                logger.error(error_msg)
                raise ValueError(error_msg)
        else:
            error_msg = f"无效的日期类型: {type(task_date)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 处理时间
        if not isinstance(start_time, str) or ":" not in start_time:
            error_msg = f"无效的时间格式: {start_time}，应为HH:MM格式"
            logger.error(error_msg)
            raise ValueError(error_msg)

        try:
            hour, minute = map(int, start_time.split(":"))
            start_datetime = datetime.combine(
                task_date_obj, datetime.min.time().replace(hour=hour, minute=minute)
            )
            logger.debug(f"解析时间 {start_time} 为 {start_datetime}")
        except Exception as e:
            error_msg = f"无法解析时间格式: {start_time}，应为HH:MM格式, 错误: {str(e)}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        # 计算结束时间
        end_datetime = start_datetime + timedelta(minutes=duration_minutes)
        logger.debug(f"计算结束时间: {end_datetime}, 持续时间: {duration_minutes}分钟")

        # 准备时间格式
        start_time_dict = {"timestamp": str(int(start_datetime.timestamp()))}
        end_time_dict = {"timestamp": str(int(end_datetime.timestamp()))}

        logger.debug(
            f"创建特定日期任务时间字典: start_time={start_time_dict}, end_time={end_time_dict}"
        )

        # 准备地点格式
        location_dict = None
        if location:
            location_dict = {"name": location}
            logger.debug(f"设置地点: {location_dict}")

        # 创建日程
        result = await create_event(
            access_token=access_token,
            calendar_id=calendar_id,
            summary=summary,
            start_time=start_time_dict,
            end_time=end_time_dict,
            description=description,
            location=location_dict,
            reminders=reminders,
        )

        logger.debug(f"创建特定日期任务结果: {result}")
        return result
    except ValueError as e:
        error_msg = str(e)
        logger.error(f"创建特定日期任务参数错误: {error_msg}")
        return {"code": -1, "msg": error_msg}
    except Exception as e:
        error_msg = f"创建特定日期任务发生未知错误: {str(e)}"
        logger.error(error_msg)
        return {"code": -1, "msg": error_msg}


@api_logger
async def get_week_events(
    access_token: str, calendar_id: str = "primary", query: str = None
):
    """获取本周的日历事件"""
    now = datetime.now()
    # 获取本周的开始日期（周一）和结束日期（周日）
    start_of_week = now - timedelta(days=now.weekday())
    end_of_week = start_of_week + timedelta(days=6)

    # 格式化为日期字符串
    start_date = start_of_week.strftime("%Y-%m-%d")
    end_date = end_of_week.strftime("%Y-%m-%d")

    logger.debug(
        f"获取本周日历事件 - 参数: calendar_id={calendar_id}, 开始日期={start_date}, 结束日期={end_date}, query={query}"
    )

    # 使用ISO格式时间字符串
    start_time_iso = f"{start_date}T00:00:00+08:00"
    end_time_iso = f"{end_date}T23:59:59+08:00"

    # 转换为时间戳格式
    start_time = convert_to_timestamp(start_time_iso)
    end_time = convert_to_timestamp(end_time_iso)

    logger.debug(f"本周的时间范围: start_time={start_time}, end_time={end_time}")
    logger.debug(f"开始调用get_calendar_events获取本周事件")

    # 使用get_calendar_events函数，它已经包含了获取日历名称的逻辑
    result = await get_calendar_events(
        access_token,
        calendar_id=calendar_id,
        start_time=start_time,
        end_time=end_time,
        query=query,
    )

    logger.debug(
        f"获取本周事件完成，结果: {json.dumps(result, ensure_ascii=False, default=str)[:1000]}..."
    )
    return result


async def update_calendar(
    access_token: str,
    calendar_id: str,
    summary: str = None,
    description: str = None,
    permissions: str = None,
    color: int = None,
):
    """更新日历信息

    参数:
        access_token: 访问令牌
        calendar_id: 日历ID
        summary: 日历名称（可选）
        description: 日历描述（可选）
        permissions: 权限设置，可选值: private（私有）, show_only_free_busy（仅显示忙闲）, public（公开）
        color: 日历颜色，1-10的整数（可选）
    """
    # 确保json模块可用
    import json

    logger.debug(
        f"更新日历 - 参数: calendar_id={calendar_id}, summary={summary}, description={description}, permissions={permissions}, color={color}"
    )

    # 构建更新数据，只包含非None字段
    update_data = {}
    if summary is not None:
        update_data["summary"] = str(summary)
    if description is not None:
        update_data["description"] = str(description)
    if permissions is not None:
        update_data["permissions"] = permissions
    if color is not None:
        update_data["color"] = color

    # 如果没有需要更新的字段，直接返回
    if not update_data:
        logger.warning("没有提供任何需要更新的字段")
        return {
            "code": 0,
            "msg": "没有需要更新的字段",
            "data": {"calendar": {"calendar_id": calendar_id}},
        }

    logger.debug(f"更新日历请求数据: {update_data}")

    async with httpx.AsyncClient() as client:
        headers = {
            "Authorization": f"Bearer {access_token}",
            "Content-Type": "application/json; charset=utf-8",
        }

        # 记录完整的请求内容
        request_body = {"calendar": update_data}
        logger.debug(f"完整请求体: {json.dumps(request_body, ensure_ascii=False)}")

        r = await client.patch(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}",
            headers=headers,
            json=request_body,
        )

        # 记录原始响应
        response_text = r.text
        logger.debug(f"原始响应: {response_text}")

        return handle_api_response(r, "更新日历")


# 添加获取日历名称的辅助函数
async def get_calendar_name(access_token: str, calendar_id: str) -> str:
    """获取日历名称，优先从缓存获取，如果缓存中没有则从API获取

    参数:
        access_token: 访问令牌
        calendar_id: 日历ID

    返回:
        str: 日历名称，如果获取失败则返回None
    """
    global calendar_name_cache

    # 如果缓存中已有该日历的名称，直接返回
    if calendar_id in calendar_name_cache:
        logger.debug(
            f"从缓存获取日历名称: calendar_id={calendar_id}, name={calendar_name_cache[calendar_id]}"
        )
        return calendar_name_cache[calendar_id]

    # 尝试从日历列表获取
    try:
        calendars_result = await get_calendars(access_token)
        if (
            calendars_result.get("code") == 0
            and "data" in calendars_result
            and "calendar_list" in calendars_result["data"]
        ):
            calendar_list = calendars_result["data"]["calendar_list"]

            # 更新缓存中的所有日历信息
            for calendar in calendar_list:
                if "calendar_id" in calendar and "summary" in calendar:
                    calendar_name_cache[calendar["calendar_id"]] = calendar["summary"]
                    logger.debug(
                        f"缓存日历信息: calendar_id={calendar['calendar_id']}, name={calendar['summary']}"
                    )

            # 检查目标日历是否在列表中
            if calendar_id in calendar_name_cache:
                return calendar_name_cache[calendar_id]
    except Exception as e:
        logger.error(f"从日历列表获取日历名称失败: {str(e)}")

    # 如果从列表中未找到，尝试直接获取日历详情
    try:
        calendar_detail = await get_calendar_detail(access_token, calendar_id)
        if (
            calendar_detail.get("code") == 0
            and "data" in calendar_detail
            and "calendar" in calendar_detail["data"]
        ):
            calendar_name = calendar_detail["data"]["calendar"].get("summary")
            if calendar_name:
                # 更新缓存
                calendar_name_cache[calendar_id] = calendar_name
                logger.debug(
                    f"从日历详情获取并缓存日历名称: calendar_id={calendar_id}, name={calendar_name}"
                )
                return calendar_name
    except Exception as e:
        logger.error(f"获取日历详情失败: {str(e)}")

    # 如果都失败了，返回None
    return None


async def subscribe_calendar(access_token: str, calendar_id: str):
    """订阅日历"""
    logger.debug(f"订阅日历 - 参数: calendar_id={calendar_id}")
    async with httpx.AsyncClient() as client:
        r = await client.post(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/subscribe",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            },
        )
        return handle_api_response(r, "订阅日历")


async def unsubscribe_calendar(access_token: str, calendar_id: str):
    """取消订阅日历"""
    logger.debug(f"取消订阅日历 - 参数: calendar_id={calendar_id}")
    async with httpx.AsyncClient() as client:
        r = await client.post(
            f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}/unsubscribe",
            headers={
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json",
            },
        )
        return handle_api_response(r, "取消订阅日历")
