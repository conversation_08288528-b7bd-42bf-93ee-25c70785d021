"""
测试认证流程集成
"""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from main import app


class TestAuthFlow:
    """认证流程集成测试"""

    def test_login_redirect(self):
        """测试登录重定向"""
        client = TestClient(app)
        response = client.get("/login")

        assert response.status_code == 302
        assert "open.feishu.cn" in response.headers["location"]
        assert "app_id=" in response.headers["location"]

    @patch("integrations.feishu.exchange_code")
    @patch("integrations.feishu.get_user_info")
    @patch("integrations.storage.save_token")
    def test_auth_callback_success(
        self, mock_save_token, mock_get_user_info, mock_exchange_code
    ):
        """测试成功的认证回调"""
        # 模拟API响应
        mock_exchange_code.return_value = {
            "code": 0,
            "data": {
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token",
                "expires_in": 7200,
            },
        }

        mock_get_user_info.return_value = {
            "code": 0,
            "data": {
                "open_id": "test_user_id",
                "name": "Test User",
                "avatar_url": "https://example.com/avatar.jpg",
            },
        }

        client = TestClient(app)
        response = client.get("/auth/callback?code=test_code")

        assert response.status_code == 200
        assert "授权成功" in response.text

        # 验证函数调用
        mock_exchange_code.assert_called_once_with("test_code")
        mock_get_user_info.assert_called_once_with("test_access_token")
        mock_save_token.assert_called_once()

    def test_auth_callback_missing_code(self):
        """测试缺少授权码的回调"""
        client = TestClient(app)
        response = client.get("/auth/callback")

        assert response.status_code == 400

    @patch("integrations.feishu.exchange_code")
    def test_auth_callback_invalid_code(self, mock_exchange_code):
        """测试无效授权码"""
        mock_exchange_code.return_value = {
            "code": 99991663,
            "msg": "invalid authorization code",
        }

        client = TestClient(app)
        response = client.get("/auth/callback?code=invalid_code")

        assert response.status_code == 400
        assert "invalid authorization code" in response.text

    @patch("api.dependencies.get_token")
    def test_protected_endpoint_without_auth(self, mock_get_token):
        """测试未认证访问受保护端点"""
        mock_get_token.return_value = None

        client = TestClient(app)
        response = client.get("/auth/callback/calendars/?user_id=test_user")

        assert response.status_code == 401
        assert "请先授权" in response.json()["detail"]

    @patch("api.dependencies.get_token")
    @patch("integrations.feishu.get_calendars")
    def test_protected_endpoint_with_auth(self, mock_get_calendars, mock_get_token):
        """测试已认证访问受保护端点"""
        mock_get_token.return_value = {
            "access_token": "test_token",
            "refresh_token": "test_refresh",
            "access_token_expire": 9999999999,
        }

        mock_get_calendars.return_value = {
            "code": 0,
            "data": {
                "calendar_list": [
                    {"calendar_id": "test_calendar", "summary": "Test Calendar"}
                ]
            },
        }

        client = TestClient(app)
        response = client.get("/auth/callback/calendars/?user_id=test_user")

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0
        assert len(data["data"]["calendar_list"]) == 1
