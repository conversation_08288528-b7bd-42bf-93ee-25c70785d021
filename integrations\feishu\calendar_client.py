import json
import logging
import os
import time
import uuid
from datetime import date, datetime, timedelta
from typing import Any, Dict, List, Optional, Union

import httpx
from tenacity import (
    retry,
    retry_if_exception_type,
    stop_after_attempt,
    wait_exponential,
)

# 导入lark_oapi库
import lark_oapi as lark
from lark_oapi.api.calendar.v4 import *
from lark_oapi.client import Client
from lark_oapi.core.const import *
from lark_oapi.core.model.raw_request import RawRequest as Request
from lark_oapi.core.model.raw_response import RawResponse as Response
from lark_oapi.core.token import AccessTokenType

from config import TIMEZONE, MAX_RETRY_ATTEMPTS, REDIRECT_URI
from integrations.storage import get_token, is_token_expired, save_token

# 配置日志
logger = logging.getLogger("feishu_calendar_lark")

# 全局缓存，用于存储日历ID和名称的映射关系
calendar_name_cache = {}


# 飞书API异常类
class FeishuAPIError(Exception):
    """飞书API异常"""

    def __init__(self, code: int, message: str, details: dict = None):
        self.code = code
        self.message = message
        self.details = details or {}
        super().__init__(f"飞书API错误 [{code}]: {message}")


class FeishuNetworkError(Exception):
    """飞书网络异常"""
    pass


class FeishuAuthError(Exception):
    """飞书认证异常"""
    pass


def generate_request_id():
    """生成唯一的请求ID"""
    return str(uuid.uuid4())


# 重试装饰器
def feishu_retry(func):
    """飞书API重试装饰器"""

    @retry(
        stop=stop_after_attempt(MAX_RETRY_ATTEMPTS),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((httpx.RequestError, FeishuNetworkError)),
        reraise=True,
    )
    async def wrapper(*args, **kwargs):
        return await func(*args, **kwargs)

    return wrapper


def handle_api_response(response, operation_name):
    """统一处理API响应，记录错误并返回标准化结果"""
    try:
        # 尝试解析JSON响应
        try:
            response_data = response.json()
        except Exception as e:
            logger.error(f"{operation_name}响应JSON解析失败: {str(e)}")
            return {
                "code": -1,
                "msg": f"响应解析错误: {str(e)}",
                "raw_text": response.text,
            }

        # 检查HTTP状态码
        if response.status_code != 200:
            logger.error(f"{operation_name}失败，状态码: {response.status_code}")
            return {
                "code": response.status_code,
                "msg": f"HTTP错误: {response.status_code}",
                "data": response_data,
            }

        return response_data

    except Exception as e:
        logger.error(f"{operation_name}处理响应时发生错误: {str(e)}")
        return {"code": -1, "msg": f"处理响应错误: {str(e)}"}


class FeishuCalendarLark:
    """使用lark_oapi SDK实现的飞书日历操作类，集成认证和API功能"""

    def __init__(self, app_id: str, app_secret: str):
        """
        初始化日历操作类

        Args:
            app_id: 飞书应用的App ID
            app_secret: 飞书应用的App Secret
        """
        self.app_id = app_id
        self.app_secret = app_secret
        # 使用1.0.0版本的API创建Client实例
        self.client = Client()
        self.client.app_id = self.app_id
        self.client.app_secret = self.app_secret

    # ==================== 认证相关方法 ====================

    @feishu_retry
    async def exchange_code(self, code: str):
        """通过授权码获取访问令牌"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "grant_type": "authorization_code",
                    "code": code,
                    "app_id": self.app_id,
                    "app_secret": self.app_secret,
                    "redirect_uri": REDIRECT_URI,
                }

                r = await client.post(
                    "https://open.feishu.cn/open-apis/authen/v1/access_token",
                    json=payload,
                    headers={"Content-Type": "application/json; charset=utf-8"},
                )

                result = handle_api_response(r, "获取访问令牌")

                # 检查是否成功
                if result.get("code") != 0:
                    logger.error(f"获取访问令牌失败: {result}")

                return result

        except httpx.RequestError as e:
            logger.error(f"获取访问令牌网络错误: {str(e)}")
            raise FeishuNetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取访问令牌发生未知错误: {str(e)}")
            raise FeishuAPIError(-1, f"未知错误: {str(e)}")

    @feishu_retry
    async def refresh_token(self, refresh_token: str):
        """刷新访问令牌"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                payload = {
                    "grant_type": "refresh_token",
                    "refresh_token": refresh_token,
                    "app_id": self.app_id,
                    "app_secret": self.app_secret,
                }

                r = await client.post(
                    "https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",
                    json=payload,
                    headers={"Content-Type": "application/json; charset=utf-8"},
                )

                result = handle_api_response(r, "刷新访问令牌")

                if result.get("code") != 0:
                    logger.error(f"刷新访问令牌失败: {result}")

                return result

        except httpx.RequestError as e:
            logger.error(f"刷新访问令牌网络错误: {str(e)}")
            raise FeishuNetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"刷新访问令牌发生未知错误: {str(e)}")
            raise FeishuAPIError(-1, f"未知错误: {str(e)}")

    @feishu_retry
    async def get_user_info(self, access_token: str):
        """获取用户信息"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json; charset=utf-8",
                }

                r = await client.get(
                    "https://open.feishu.cn/open-apis/authen/v1/user_info", headers=headers
                )

                result = handle_api_response(r, "获取用户信息")

                if result.get("code") != 0:
                    logger.error(f"获取用户信息失败: {result}")

                return result

        except httpx.RequestError as e:
            logger.error(f"获取用户信息网络错误: {str(e)}")
            raise FeishuNetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"获取用户信息发生未知错误: {str(e)}")
            raise FeishuAPIError(-1, f"未知错误: {str(e)}")

    # ==================== 日历相关方法 ====================

    def _get_client_with_token(self, user_id: str):
        """
        获取带有用户访问令牌的客户端

        Args:
            user_id: 用户ID

        Returns:
            Client: 带有用户访问令牌的客户端
        """
        # 获取用户的token
        token_data = get_token(user_id)
        if not token_data:
            raise ValueError(f"未找到用户 {user_id} 的token数据")

        # 检查token是否过期，如果过期则刷新
        if is_token_expired(token_data["access_token_expire"]):
            # 使用refresh_token刷新access_token
            try:
                # 创建刷新token的请求
                req = Request(
                    "POST",
                    f"https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",
                    body={
                        "grant_type": "refresh_token",
                        "refresh_token": token_data["refresh_token"],
                    },
                    headers={"Content-Type": "application/json; charset=utf-8"},
                    query={},
                )

                # 发送请求
                resp = self.client.http.request(req)

                # 解析响应
                if resp.status_code != 200:
                    raise ValueError(f"刷新token失败: {resp.content}")

                resp_data = resp.json()
                if resp_data.get("code") != 0:
                    raise ValueError(f"刷新token失败: {resp_data.get('msg')}")

                # 更新token
                data = resp_data.get("data")
                save_token(
                    user_id,
                    {
                        "access_token": data["access_token"],
                        "refresh_token": data["refresh_token"],
                        "access_token_expire": int(time.time()) + data["expires_in"],
                    },
                )

                # 重新获取token数据
                token_data = get_token(user_id)

            except Exception as e:
                logger.error(f"刷新token失败: {str(e)}")
                raise ValueError(f"刷新token失败: {str(e)}")

        # 创建一个新的客户端，带有用户的访问令牌
        # 使用正确的API来设置用户访问令牌
        try:
            client = Client.builder() \
                .app_id(self.app_id) \
                .app_secret(self.app_secret) \
                .build()
            
            # 设置用户访问令牌
            client.set_user_access_token(token_data["access_token"])
            
            return client
        except AttributeError:
            # 如果set_user_access_token不存在，尝试其他方法
            try:
                client = Client.builder() \
                    .app_id(self.app_id) \
                    .app_secret(self.app_secret) \
                    .build()
                
                # 直接设置访问令牌
                client.access_token = token_data["access_token"]
                
                return client
            except Exception as e:
                logger.error(f"创建客户端失败: {str(e)}")
                raise ValueError(f"创建客户端失败: {str(e)}")

    def get_calendars(self, user_id: str, page_token: str = None, page_size: int = 100):
        """
        获取用户的日历列表

        Args:
            user_id: 用户ID
            page_token: 分页标记
            page_size: 每页大小

        Returns:
            Dict: 日历列表
        """
        try:
            # 获取用户token
            token_data = get_token(user_id)
            if not token_data:
                return {"code": -1, "msg": f"未找到用户 {user_id} 的token数据"}

            # 检查token是否过期
            if is_token_expired(token_data.get("access_token_expire", 0)):
                logger.warning(f"用户 {user_id} 的access_token已过期")
                return {"code": -1, "msg": "access_token已过期"}

            access_token = token_data["access_token"]

            # 方法1: 直接使用HTTP请求（推荐）
            try:
                logger.info(f"🔍 尝试方法1: 直接HTTP请求获取用户 {user_id} 的日历...")
                
                # 构建请求URL
                url = "https://open.feishu.cn/open-apis/calendar/v4/calendars"
                
                # 构建请求头
                headers = {
                    "Authorization": f"Bearer {access_token}",
                    "Content-Type": "application/json"
                }
                
                # 构建请求参数
                params = {}
                if page_token:
                    params["page_token"] = page_token
                if page_size:
                    params["page_size"] = page_size
                
                # 发送请求
                response = httpx.get(url, headers=headers, params=params, timeout=30)
                result = response.json()
                
                logger.info(f"📊 HTTP响应: {result}")
                
                if response.status_code == 200 and result.get("code") == 0:
                    calendars = result.get("data", {}).get("calendar_list", [])
                    page_token_result = result.get("data", {}).get("page_token")
                    has_more = result.get("data", {}).get("has_more", False)
                    
                    logger.info(f"✅ HTTP请求成功: 找到 {len(calendars)} 个日历")
                    
                    return {
                        "code": 0,
                        "data": {
                            "calendar_list": calendars,
                            "page_token": page_token_result,
                            "has_more": has_more,
                        },
                    }
                else:
                    logger.warning(f"⚠️ HTTP请求失败: {result}")
                    
            except Exception as e:
                logger.error(f"❌ HTTP请求异常: {str(e)}")
            
            # 方法2: 使用SDK（备用方案）
            try:
                logger.info(f"🔍 尝试方法2: 使用SDK获取用户 {user_id} 的日历...")
                
                client = self._get_client_with_token(user_id)

                # 构建请求
                request = ListCalendarRequest.builder()
                if page_token:
                    request.page_token(page_token)
                if page_size:
                    request.page_size(page_size)

                # 发送请求
                response = client.calendar.v4.calendar.list(request.build())

                # 检查响应
                if not response.success():
                    logger.error(f"获取日历列表失败: {response.msg}, code: {response.code}")
                    return {"code": response.code, "msg": response.msg}

                # 调试响应结构
                logger.debug(f"响应类型: {type(response.data)}")
                try:
                    logger.debug(f"响应属性: {dir(response.data)}")
                except Exception as e:
                    logger.debug(f"无法获取响应属性: {str(e)}")
                
                # 尝试不同的方式获取日历列表
                calendars = []
                page_token_result = None
                has_more = False
                
                # 方法1: 使用getattr安全访问calendars属性
                try:
                    calendars = getattr(response.data, "calendars", None)
                    if calendars is not None:
                        logger.debug(f"方法1成功: 找到 {len(calendars) if calendars else 0} 个日历")
                    else:
                        # 方法2: 访问items属性
                        calendars = getattr(response.data, "items", None)
                        if calendars is not None:
                            logger.debug(f"方法2成功: 找到 {len(calendars) if calendars else 0} 个日历")
                        else:
                            # 方法3: 访问data属性
                            data_obj = getattr(response.data, "data", None)
                            if data_obj is not None:
                                calendars = getattr(data_obj, "calendars", None)
                                if calendars is not None:
                                    logger.debug(f"方法3成功: 找到 {len(calendars) if calendars else 0} 个日历")
                            
                            # 方法4: 尝试转换为字典
                            if calendars is None:
                                try:
                                    import json
                                    response_dict = json.loads(str(response.data))
                                    if "calendars" in response_dict:
                                        calendars = response_dict["calendars"]
                                        logger.debug(f"方法4成功: 找到 {len(calendars) if calendars else 0} 个日历")
                                    elif "items" in response_dict:
                                        calendars = response_dict["items"]
                                        logger.debug(f"方法4成功: 找到 {len(calendars) if calendars else 0} 个日历")
                                except Exception as e:
                                    logger.debug(f"方法4失败: {str(e)}")
                except Exception as e:
                    logger.debug(f"属性访问失败: {str(e)}")
                    calendars = None
                
                # 获取分页信息
                try:
                    page_token_result = getattr(response.data, "page_token", None)
                    if page_token_result is None:
                        data_obj = getattr(response.data, "data", None)
                        if data_obj is not None:
                            page_token_result = getattr(data_obj, "page_token", None)
                    
                    has_more = getattr(response.data, "has_more", False)
                    if not has_more:
                        data_obj = getattr(response.data, "data", None)
                        if data_obj is not None:
                            has_more = getattr(data_obj, "has_more", False)
                except Exception as e:
                    logger.debug(f"获取分页信息失败: {str(e)}")
                    page_token_result = None
                    has_more = False
                
                # 如果仍然没有找到日历，记录详细信息
                if not calendars:
                    logger.warning("响应中没有找到日历列表")
                    logger.debug(f"完整响应数据: {response.data}")
                    # 返回空列表而不是错误
                    calendars = []
                
                logger.info(f"✅ SDK请求成功: 找到 {len(calendars)} 个日历")
                
                return {
                    "code": 0,
                    "data": {
                        "calendar_list": calendars,
                        "page_token": page_token_result,
                        "has_more": has_more,
                    },
                }
                
            except Exception as e:
                logger.error(f"❌ SDK请求异常: {str(e)}")
                import traceback
                logger.error(f"错误详情: {traceback.format_exc()}")
            
            # 如果所有方法都失败，返回错误
            logger.error("❌ 所有获取日历的方法都失败了")
            return {"code": -1, "msg": "无法获取日历列表，请检查权限和网络连接"}

        except Exception as e:
            logger.error(f"获取日历列表时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {"code": -1, "msg": f"获取日历列表时发生错误: {str(e)}"}

    def get_calendar_detail(self, user_id: str, calendar_id: str):
        """
        获取日历详情

        Args:
            user_id: 用户ID
            calendar_id: 日历ID

        Returns:
            Dict: 日历详情
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建请求
            request = GetCalendarRequest.builder().calendar_id(calendar_id).build()

            # 发送请求
            response = client.calendar.v4.calendar.get(request)

            # 检查响应
            if not response.success():
                logger.error(f"获取日历详情失败: {response.msg}, code: {response.code}")
                return {"code": response.code, "msg": response.msg}

            # 返回结果
            return {"code": 0, "data": {"calendar": response.data.calendar}}

        except Exception as e:
            logger.error(f"获取日历详情时发生错误: {str(e)}")
            return {"code": -1, "msg": f"获取日历详情时发生错误: {str(e)}"}

    def create_calendar(self, user_id: str, calendar_data: dict):
        """
        创建日历

        Args:
            user_id: 用户ID
            calendar_data: 日历数据，包含summary、description等

        Returns:
            dict: 创建结果
        """
        try:
            # 获取用户token
            token_data = get_token(user_id)
            if not token_data:
                return {"code": -1, "msg": f"未找到用户 {user_id} 的token数据"}

            # 检查token是否过期
            if is_token_expired(token_data.get("access_token_expire", 0)):
                logger.warning(f"用户 {user_id} 的access_token已过期")
                return {"code": -1, "msg": "access_token已过期"}

            access_token = token_data["access_token"]

            # 构建请求URL
            url = "https://open.feishu.cn/open-apis/calendar/v4/calendars"

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # 构建请求体
            body = {
                "summary": calendar_data.get("summary", "新日历"),
                "description": calendar_data.get("description", ""),
                "permissions": calendar_data.get("permissions", "private"),
                "color": calendar_data.get("color", 1),
                "summary_alias": calendar_data.get("summary_alias", "")
            }

            # 发送请求
            response = httpx.post(url, headers=headers, json=body, timeout=30)
            result = response.json()

            logger.info(f"创建日历API响应: {result}")
            return result

        except Exception as e:
            logger.error(f"创建日历时发生错误: {str(e)}")
            return {"code": -1, "msg": f"创建日历时发生错误: {str(e)}"}

    def update_calendar(self, user_id: str, calendar_id: str, calendar_data: dict):
        """
        更新日历

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            calendar_data: 更新的日历数据

        Returns:
            dict: 更新结果
        """
        try:
            # 获取用户token
            token_data = get_token(user_id)
            if not token_data:
                return {"code": -1, "msg": f"未找到用户 {user_id} 的token数据"}

            # 检查token是否过期
            if is_token_expired(token_data.get("access_token_expire", 0)):
                logger.warning(f"用户 {user_id} 的access_token已过期")
                return {"code": -1, "msg": "access_token已过期"}

            access_token = token_data["access_token"]

            # 构建请求URL
            url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}"

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # 构建请求体（只包含需要更新的字段）
            body = {}
            if "summary" in calendar_data:
                body["summary"] = calendar_data["summary"]
            if "description" in calendar_data:
                body["description"] = calendar_data["description"]
            if "permissions" in calendar_data:
                body["permissions"] = calendar_data["permissions"]
            if "color" in calendar_data:
                body["color"] = calendar_data["color"]
            if "summary_alias" in calendar_data:
                body["summary_alias"] = calendar_data["summary_alias"]

            # 发送请求
            response = httpx.patch(url, headers=headers, json=body, timeout=30)
            result = response.json()

            logger.info(f"更新日历API响应: {result}")
            return result

        except Exception as e:
            logger.error(f"更新日历时发生错误: {str(e)}")
            return {"code": -1, "msg": f"更新日历时发生错误: {str(e)}"}

    def delete_calendar(self, user_id: str, calendar_id: str):
        """
        删除日历

        Args:
            user_id: 用户ID
            calendar_id: 日历ID

        Returns:
            dict: 删除结果
        """
        try:
            # 获取用户token
            token_data = get_token(user_id)
            if not token_data:
                return {"code": -1, "msg": f"未找到用户 {user_id} 的token数据"}

            # 检查token是否过期
            if is_token_expired(token_data.get("access_token_expire", 0)):
                logger.warning(f"用户 {user_id} 的access_token已过期")
                return {"code": -1, "msg": "access_token已过期"}

            access_token = token_data["access_token"]

            # 构建请求URL
            url = f"https://open.feishu.cn/open-apis/calendar/v4/calendars/{calendar_id}"

            # 构建请求头
            headers = {
                "Authorization": f"Bearer {access_token}",
                "Content-Type": "application/json"
            }

            # 发送请求
            response = httpx.delete(url, headers=headers, timeout=30)
            result = response.json()

            logger.info(f"删除日历API响应: {result}")
            return result

        except Exception as e:
            logger.error(f"删除日历时发生错误: {str(e)}")
            return {"code": -1, "msg": f"删除日历时发生错误: {str(e)}"}

    def create_event(
        self,
        user_id: str,
        calendar_id: str,
        summary: str,
        start_time: Dict[str, Any],
        end_time: Dict[str, Any],
        description: str = None,
        location: Dict[str, str] = None,
        reminders: List[Dict[str, int]] = None,
        attendees: List[Dict[str, str]] = None,
        is_all_day: bool = False,
    ):
        """
        创建日历事件

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            summary: 事件标题
            start_time: 开始时间，格式为 {"date_time": "2023-01-01T10:00:00+08:00"} 或 {"timestamp": "1672537200"}
            end_time: 结束时间，格式同上
            description: 事件描述
            location: 地点信息，格式为 {"name": "地点名称"}
            reminders: 提醒设置，格式为 [{"minutes": 10}, {"minutes": 30}]
            attendees: 参与者列表，格式为 [{"user_id": "用户ID"}]
            is_all_day: 是否为全天事件

        Returns:
            Dict: 创建结果
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建事件对象
            event_builder = (
                CalendarEvent.builder().summary(summary).is_all_day(is_all_day)
            )

            # 设置时间
            if "date_time" in start_time:
                event_builder.start_time(
                    TimeInfo.builder().date_time(start_time["date_time"]).build()
                )
            elif "timestamp" in start_time:
                event_builder.start_time(
                    TimeInfo.builder().timestamp(start_time["timestamp"]).build()
                )

            if "date_time" in end_time:
                event_builder.end_time(
                    TimeInfo.builder().date_time(end_time["date_time"]).build()
                )
            elif "timestamp" in end_time:
                event_builder.end_time(
                    TimeInfo.builder().timestamp(end_time["timestamp"]).build()
                )

            # 设置描述
            if description:
                event_builder.description(description)

            # 设置地点
            if location and "name" in location:
                event_builder.location(
                    EventLocation.builder().name(location["name"]).build()
                )

            # 设置提醒
            if reminders:
                reminder_list = []
                for reminder in reminders:
                    if "minutes" in reminder:
                        reminder_list.append(
                            Reminder.builder().minutes(reminder["minutes"]).build()
                        )
                if reminder_list:
                    event_builder.reminders(reminder_list)

            # 设置参与者
            if attendees:
                attendee_list = []
                for attendee in attendees:
                    if "user_id" in attendee:
                        attendee_list.append(
                            Attendee.builder().user_id(attendee["user_id"]).build()
                        )
                if attendee_list:
                    event_builder.attendees(attendee_list)

            # 构建请求
            request = (
                CreateCalendarEventRequest.builder()
                .calendar_id(calendar_id)
                .request_body(event_builder.build())
                .build()
            )

            # 发送请求
            response = client.calendar.v4.calendar_event.create(request)

            # 检查响应
            if not response.success():
                logger.error(f"创建日历事件失败: {response.msg}, code: {response.code}")
                return {"code": response.code, "msg": response.msg}

            # 返回结果
            return {
                "code": 0,
                "data": {
                    "event": {
                        "event_id": response.data.event.event_id,
                        "summary": response.data.event.summary,
                    }
                },
            }

        except Exception as e:
            logger.error(f"创建日历事件时发生错误: {str(e)}")
            return {"code": -1, "msg": f"创建日历事件时发生错误: {str(e)}"}

    def get_event_detail(self, user_id: str, calendar_id: str, event_id: str):
        """
        获取事件详情

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            event_id: 事件ID

        Returns:
            Dict: 事件详情
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建请求
            request = (
                GetCalendarEventRequest.builder()
                .calendar_id(calendar_id)
                .event_id(event_id)
                .build()
            )

            # 发送请求
            response = client.calendar.v4.calendar_event.get(request)

            # 检查响应
            if not response.success():
                logger.error(f"获取事件详情失败: {response.msg}, code: {response.code}")
                return {"code": response.code, "msg": response.msg}

            # 返回结果
            return {"code": 0, "data": {"event": response.data.event}}

        except Exception as e:
            logger.error(f"获取事件详情时发生错误: {str(e)}")
            return {"code": -1, "msg": f"获取事件详情时发生错误: {str(e)}"}

    def update_event(
        self,
        user_id: str,
        calendar_id: str,
        event_id: str,
        summary: str = None,
        start_time: Dict[str, Any] = None,
        end_time: Dict[str, Any] = None,
        description: str = None,
        location: Dict[str, str] = None,
        reminders: List[Dict[str, int]] = None,
        is_all_day: bool = None,
    ):
        """
        更新日历事件

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            event_id: 事件ID
            summary: 事件标题
            start_time: 开始时间
            end_time: 结束时间
            description: 事件描述
            location: 地点信息
            reminders: 提醒设置
            is_all_day: 是否为全天事件

        Returns:
            Dict: 更新结果
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建事件对象
            event_builder = CalendarEvent.builder()

            # 设置标题
            if summary:
                event_builder.summary(summary)

            # 设置时间
            if start_time:
                time_info_builder = TimeInfo.builder()
                if "date_time" in start_time:
                    time_info_builder.date_time(start_time["date_time"])
                elif "timestamp" in start_time:
                    time_info_builder.timestamp(start_time["timestamp"])
                event_builder.start_time(time_info_builder.build())

            if end_time:
                time_info_builder = TimeInfo.builder()
                if "date_time" in end_time:
                    time_info_builder.date_time(end_time["date_time"])
                elif "timestamp" in end_time:
                    time_info_builder.timestamp(end_time["timestamp"])
                event_builder.end_time(time_info_builder.build())

            # 设置描述
            if description is not None:
                event_builder.description(description)

            # 设置地点
            if location and "name" in location:
                event_builder.location(
                    EventLocation.builder().name(location["name"]).build()
                )

            # 设置提醒
            if reminders:
                reminder_list = []
                for reminder in reminders:
                    if "minutes" in reminder:
                        reminder_list.append(
                            Reminder.builder().minutes(reminder["minutes"]).build()
                        )
                event_builder.reminders(reminder_list)

            # 设置是否全天
            if is_all_day is not None:
                event_builder.is_all_day(is_all_day)

            # 构建请求
            request = (
                PatchCalendarEventRequest.builder()
                .calendar_id(calendar_id)
                .event_id(event_id)
                .request_body(event_builder.build())
                .build()
            )

            # 发送请求
            response = client.calendar.v4.calendar_event.patch(request)

            # 检查响应
            if not response.success():
                logger.error(f"更新日历事件失败: {response.msg}, code: {response.code}")
                return {"code": response.code, "msg": response.msg}

            # 返回结果
            return {
                "code": 0,
                "data": {
                    "event": {
                        "event_id": response.data.event.event_id,
                        "summary": response.data.event.summary,
                    }
                },
            }

        except Exception as e:
            logger.error(f"更新日历事件时发生错误: {str(e)}")
            return {"code": -1, "msg": f"更新日历事件时发生错误: {str(e)}"}

    def delete_event(self, user_id: str, calendar_id: str, event_id: str):
        """
        删除日历事件

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            event_id: 事件ID

        Returns:
            Dict: 删除结果
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建请求
            request = (
                DeleteCalendarEventRequest.builder()
                .calendar_id(calendar_id)
                .event_id(event_id)
                .build()
            )

            # 发送请求
            response = client.calendar.v4.calendar_event.delete(request)

            # 检查响应
            if not response.success():
                logger.error(f"删除日历事件失败: {response.msg}, code: {response.code}")
                return {"code": response.code, "msg": response.msg}

            # 返回结果
            return {"code": 0, "msg": "删除成功"}

        except Exception as e:
            logger.error(f"删除日历事件时发生错误: {str(e)}")
            return {"code": -1, "msg": f"删除日历事件时发生错误: {str(e)}"}

    def get_calendar_events(
        self,
        user_id: str,
        calendar_id: str,
        start_time: str = None,
        end_time: str = None,
        page_token: str = None,
        page_size: int = 100,
    ):
        """
        获取日历事件列表

        Args:
            user_id: 用户ID
            calendar_id: 日历ID
            start_time: 开始时间，ISO格式或时间戳
            end_time: 结束时间，ISO格式或时间戳
            page_token: 分页标记
            page_size: 每页大小

        Returns:
            Dict: 事件列表
        """
        try:
            client = self._get_client_with_token(user_id)

            # 构建请求
            request = ListCalendarEventRequest.builder().calendar_id(calendar_id)

            if start_time:
                request.start_time(start_time)
            if end_time:
                request.end_time(end_time)
            if page_token:
                request.page_token(page_token)
            if page_size:
                request.page_size(page_size)

            # 发送请求
            response = client.calendar.v4.calendar_event.list(request.build())

            # 检查响应
            if not response.success():
                logger.error(
                    f"获取日历事件列表失败: {response.msg}, code: {response.code}"
                )
                return {"code": response.code, "msg": response.msg}

            # 调试响应结构
            logger.debug(f"事件响应类型: {type(response.data)}")
            logger.debug(f"事件响应属性: {dir(response.data)}")
            
            # 尝试不同的方式获取事件列表
            items = []
            page_token_result = None
            has_more = False
            
            # 方法1: 直接访问items属性
            if hasattr(response.data, "items"):
                items = response.data.items
                logger.debug(f"方法1成功: 找到 {len(items) if items else 0} 个事件")
            # 方法2: 访问events属性
            elif hasattr(response.data, "events"):
                items = response.data.events
                logger.debug(f"方法2成功: 找到 {len(items) if items else 0} 个事件")
            # 方法3: 访问data属性
            elif hasattr(response.data, "data") and hasattr(response.data.data, "items"):
                items = response.data.data.items
                logger.debug(f"方法3成功: 找到 {len(items) if items else 0} 个事件")
            # 方法4: 尝试转换为字典
            else:
                try:
                    # 尝试将响应数据转换为字典
                    import json
                    response_dict = json.loads(str(response.data))
                    if "items" in response_dict:
                        items = response_dict["items"]
                        logger.debug(f"方法4成功: 找到 {len(items) if items else 0} 个事件")
                    elif "events" in response_dict:
                        items = response_dict["events"]
                        logger.debug(f"方法4成功: 找到 {len(items) if items else 0} 个事件")
                except Exception as e:
                    logger.debug(f"方法4失败: {str(e)}")
            
            # 获取分页信息
            if hasattr(response.data, "page_token"):
                page_token_result = response.data.page_token
            elif hasattr(response.data, "data") and hasattr(response.data.data, "page_token"):
                page_token_result = response.data.data.page_token
                
            if hasattr(response.data, "has_more"):
                has_more = response.data.has_more
            elif hasattr(response.data, "data") and hasattr(response.data.data, "has_more"):
                has_more = response.data.data.has_more
            
            # 如果仍然没有找到事件，记录详细信息
            if not items:
                logger.warning("响应中没有找到事件列表")
                logger.debug(f"完整响应数据: {response.data}")
                # 返回空列表而不是错误
                items = []
            
            logger.info(f"成功获取到 {len(items)} 个事件")
            
            # 返回结果
            return {
                "code": 0,
                "data": {
                    "items": items,
                    "page_token": page_token_result,
                    "has_more": has_more,
                },
            }

        except Exception as e:
            logger.error(f"获取日历事件列表时发生错误: {str(e)}")
            import traceback
            logger.error(f"错误详情: {traceback.format_exc()}")
            return {"code": -1, "msg": f"获取日历事件列表时发生错误: {str(e)}"}

    # ==================== 工具函数 ====================

    def convert_to_timestamp(self, time_value: Union[str, int, datetime]) -> str:
        """将各种时间格式转换为时间戳字符串"""
        if isinstance(time_value, datetime):
            return str(int(time_value.timestamp()))

        if isinstance(time_value, int):
            return str(time_value)

        if isinstance(time_value, str):
            # 如果是字符串，判断是否已经是数字格式
            try:
                int(time_value)  # 检查是否可以转换为整数
                return time_value  # 已经是时间戳字符串，直接返回
            except ValueError:
                pass

            # 尝试解析ISO格式时间
            try:
                dt = datetime.fromisoformat(time_value.replace('Z', '+00:00'))
                return str(int(dt.timestamp()))
            except ValueError:
                pass

            # 尝试解析其他常见格式
            formats = [
                "%Y-%m-%d %H:%M:%S",
                "%Y-%m-%d",
                "%Y/%m/%d %H:%M:%S",
                "%Y/%m/%d",
            ]

            for fmt in formats:
                try:
                    dt = datetime.strptime(time_value, fmt)
                    return str(int(dt.timestamp()))
                except ValueError:
                    continue

        # 如果无法转换，返回字符串形式
        return str(time_value)

    def timestamp_to_iso(self, timestamp, timezone="Asia/Shanghai"):
        """将时间戳转换为ISO 8601格式的日期时间字符串"""
        try:
            if isinstance(timestamp, str):
                timestamp = int(timestamp)

            dt = datetime.fromtimestamp(timestamp)
            return dt.isoformat()
        except (ValueError, TypeError) as e:
            logger.error(f"时间戳转换失败: {timestamp}, 错误: {str(e)}")
            return str(timestamp)
