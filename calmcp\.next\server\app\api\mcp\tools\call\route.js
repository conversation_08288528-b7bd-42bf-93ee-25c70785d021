/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/tools/call/route";
exports.ids = ["app/api/mcp/tools/call/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/tools/call/route.ts */ \"(rsc)/./src/app/api/mcp/tools/call/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/tools/call/route\",\n        pathname: \"/api/mcp/tools/call\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/tools/call/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\mcp\\\\tools\\\\call\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/mcp/tools/call/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/mcp/tools/call/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/mcp/tools/call/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/feishu-official-client */ \"(rsc)/./src/lib/feishu-official-client.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/**\n * Next.js API Route for MCP Tool Calls\n * POST /api/mcp/tools/call - 调用 MCP 工具\n */ \n\n\n// 创建飞书客户端工厂函数\nfunction createFeishuClient(userAccessToken) {\n    return new _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__.OfficialFeishuClient(\"cli_a76a68f612bf900c\", \"EVumG3wCHsDBeJRfpbmJkfRhzCns73jC\", userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN);\n}\nasync function callTool(name, args, userAccessToken) {\n    const startTime = Date.now();\n    try {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuRequest(name);\n        // 创建使用指定token的飞书客户端\n        const feishuClient = createFeishuClient(userAccessToken);\n        // 使用简化的通用 API 调用方法\n        const result = await feishuClient.callApi(name, args);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuResponse(name, result.code, duration);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: JSON.stringify(result, null, 2)\n                }\n            ],\n            isError: result.code !== 0\n        };\n    } catch (error) {\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuError(name, error);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: `Error: ${error instanceof Error ? error.message : String(error)}`\n                }\n            ],\n            isError: true\n        };\n    }\n}\nasync function POST(request) {\n    try {\n        const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const body = await request.json();\n        const { name, arguments: args } = body;\n        // 从请求头中获取Authorization token\n        const authHeader = request.headers.get(\"authorization\");\n        let userAccessToken;\n        if (authHeader && authHeader.startsWith(\"Bearer \")) {\n            userAccessToken = authHeader.substring(7); // 移除 \"Bearer \" 前缀\n            console.log(`🔑 使用请求头中的token: ${userAccessToken.substring(0, 20)}...`);\n        } else {\n            console.log(`🔑 使用环境变量中的token`);\n        }\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpRequest(name, args, {\n            requestId\n        });\n        const startTime = Date.now();\n        const result = await callTool(name, args, userAccessToken);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpResponse(name, !result.isError, duration, {\n            requestId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: !result.isError,\n            result\n        });\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpError(\"unknown\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/tools/call/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/feishu-official-client.ts":
/*!*******************************************!*\
  !*** ./src/lib/feishu-official-client.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OfficialFeishuClient: () => (/* binding */ OfficialFeishuClient)\n/* harmony export */ });\n/* harmony import */ var _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @larksuiteoapi/node-sdk */ \"(rsc)/./node_modules/@larksuiteoapi/node-sdk/es/index.js\");\n/* harmony import */ var _tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tools/tool-adapter-core */ \"(rsc)/./src/lib/tools/tool-adapter-core.ts\");\n/**\n * Official Feishu SDK Client\n * 基于官方 @larksuiteoapi/node-sdk 的完整实现\n * 支持所有 calendar.v4 API\n */ \n\nclass OfficialFeishuClient {\n    constructor(appId, appSecret, userAccessToken){\n        this.client = new _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({\n            appId,\n            appSecret,\n            appType: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.AppType.SelfBuild,\n            domain: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Domain.Feishu\n        });\n        // 从环境变量获取用户访问令牌\n        this.userAccessToken = userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN;\n        console.log(\"Official Feishu client initialized:\", {\n            appId,\n            hasAppSecret: !!appSecret,\n            hasUserToken: !!this.userAccessToken,\n            userTokenPrefix: this.userAccessToken ? this.userAccessToken.substring(0, 10) + \"...\" : \"none\"\n        });\n    }\n    setUserAccessToken(token) {\n        this.userAccessToken = token;\n    }\n    /**\n   * 通用 API 调用方法\n   * 支持所有 calendar.v4 API\n   */ async callApi(method, params = {}) {\n        try {\n            console.log(`🔧 调用官方 API 方法: ${method}`, params);\n            // 验证工具和参数\n            const tool = (0,_tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__.getToolByName)(method);\n            if (!tool) {\n                console.warn(`⚠️  未知的 API 方法: ${method}`);\n                return {\n                    code: -1,\n                    msg: `Unknown method: ${method}`,\n                    data: null\n                };\n            }\n            // 验证参数\n            const validation = (0,_tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__.validateToolArguments)(method, params);\n            if (!validation.valid) {\n                console.error(`❌ 参数验证失败:`, validation.errors);\n                return {\n                    code: -1,\n                    msg: `Invalid arguments: ${validation.errors?.join(\", \")}`,\n                    data: null\n                };\n            }\n            // 根据方法名调用相应的 API\n            const result = await this._callSpecificApi(method, params);\n            console.log(`✅ API 调用成功: ${method}`, {\n                code: result.code,\n                msg: result.msg\n            });\n            return result;\n        } catch (error) {\n            console.error(`❌ API 调用失败 (${method}):`, error);\n            return {\n                code: -1,\n                msg: `API 调用失败: ${error}`,\n                data: null\n            };\n        }\n    }\n    /**\n   * 调用具体的 API 方法\n   */ async _callSpecificApi(method, params) {\n        const useUserToken = params.useUAT !== false; // 默认使用用户访问令牌\n        try {\n            let response;\n            let requestOptions = {};\n            // 根据方法名构建请求\n            switch(method){\n                // 日历管理 API\n                case \"calendar.v4.calendar.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.primary\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.primary(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.primary(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.search\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"query\",\n                            \"user_id_type\",\n                            \"page_token\",\n                            \"page_size\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.search(requestOptions);\n                    break;\n                // 日历事件 API\n                case \"calendar.v4.calendarEvent.list\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.create\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\",\n                            \"need_meeting_settings\",\n                            \"need_attendee\",\n                            \"max_attendee_num\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"need_notification\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.search\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"query\",\n                            \"filter\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.search(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instances\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\",\n                            \"page_size\",\n                            \"page_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instances(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instances(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instanceView\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instanceView(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instanceView(requestOptions);\n                    break;\n                // 任务清单管理 API\n                case \"task.v2.tasklist.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.tasklist.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.tasklist.list(requestOptions);\n                    break;\n                case \"task.v2.tasklist.get\":\n                    requestOptions = {\n                        path: {\n                            tasklist_guid: params.tasklist_guid\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.tasklist.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.tasklist.get(requestOptions);\n                    break;\n                case \"task.v2.tasklist.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"name\",\n                            \"description\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.tasklist.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.tasklist.create(requestOptions);\n                    break;\n                case \"task.v2.tasklist.patch\":\n                    requestOptions = {\n                        path: {\n                            tasklist_guid: params.tasklist_guid\n                        },\n                        data: this._buildParams(params, [\n                            \"name\",\n                            \"description\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.tasklist.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.tasklist.patch(requestOptions);\n                    break;\n                case \"task.v2.tasklist.delete\":\n                    requestOptions = {\n                        path: {\n                            tasklist_guid: params.tasklist_guid\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.tasklist.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.tasklist.delete(requestOptions);\n                    break;\n                // 任务管理 API\n                case \"task.v2.task.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"completed\",\n                            \"type\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.task.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.task.list(requestOptions);\n                    break;\n                case \"task.v2.task.get\":\n                    requestOptions = {\n                        path: {\n                            task_guid: params.task_guid\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.task.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.task.get(requestOptions);\n                    break;\n                case \"task.v2.task.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"due\",\n                            \"start\",\n                            \"completed_at\",\n                            \"extra\",\n                            \"mode\",\n                            \"is_milestone\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.task.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.task.create(requestOptions);\n                    break;\n                case \"task.v2.task.patch\":\n                    requestOptions = {\n                        path: {\n                            task_guid: params.task_guid\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"due\",\n                            \"start\",\n                            \"completed_at\",\n                            \"extra\",\n                            \"mode\",\n                            \"is_milestone\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.task.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.task.patch(requestOptions);\n                    break;\n                case \"task.v2.task.delete\":\n                    requestOptions = {\n                        path: {\n                            task_guid: params.task_guid\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.task.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.task.delete(requestOptions);\n                    break;\n                // 任务分组管理 API\n                case \"task.v2.section.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"resource_type\",\n                            \"resource_id\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.section.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.section.list(requestOptions);\n                    break;\n                case \"task.v2.section.get\":\n                    requestOptions = {\n                        path: {\n                            section_guid: params.section_guid\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.section.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.section.get(requestOptions);\n                    break;\n                case \"task.v2.section.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"name\",\n                            \"resource_type\",\n                            \"resource_id\",\n                            \"insert_before\",\n                            \"insert_after\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.task.v2.section.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.task.v2.section.create(requestOptions);\n                    break;\n                default:\n                    console.warn(`⚠️  未实现的 API 方法: ${method}`);\n                    return {\n                        code: -1,\n                        msg: `Unimplemented method: ${method}`,\n                        data: null\n                    };\n            }\n            // 统一处理响应\n            return {\n                code: response.code || 0,\n                msg: response.msg || \"success\",\n                data: response.data\n            };\n        } catch (error) {\n            console.error(`❌ 具体 API 调用失败 (${method}):`, error);\n            // 提取更详细的错误信息\n            let errorMsg = `API 调用失败: ${error.message || error}`;\n            if (error.response?.data) {\n                errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;\n            }\n            return {\n                code: -1,\n                msg: errorMsg,\n                data: null\n            };\n        }\n    }\n    /**\n   * 构建参数对象，只包含指定的字段\n   */ _buildParams(params, allowedFields) {\n        const result = {};\n        for (const field of allowedFields){\n            if (params[field] !== undefined) {\n                result[field] = params[field];\n            }\n        }\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/feishu-official-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! winston */ \"(rsc)/./node_modules/winston/lib/winston.js\");\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(winston__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Logger Utility\n * 统一的日志记录工具\n */ \n\nclass Logger {\n    constructor(){\n        const logLevel = process.env.LOG_LEVEL || \"info\";\n        const logFile = process.env.LOG_FILE || \"./logs/mcp-server.log\";\n        // 确保日志目录存在\n        const logDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(logFile);\n        this.logger = winston__WEBPACK_IMPORTED_MODULE_0___default().createLogger({\n            level: logLevel,\n            format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.timestamp(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.errors({\n                stack: true\n            }), winston__WEBPACK_IMPORTED_MODULE_0___default().format.json()),\n            defaultMeta: {\n                service: \"calmcp\"\n            },\n            transports: [\n                // 错误日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: path__WEBPACK_IMPORTED_MODULE_1___default().join(logDir, \"error.log\"),\n                    level: \"error\",\n                    maxsize: 5242880,\n                    maxFiles: 5\n                }),\n                // 所有日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: logFile,\n                    maxsize: 5242880,\n                    maxFiles: 5\n                })\n            ]\n        });\n        // 开发环境下添加控制台输出\n        if (true) {\n            this.logger.add(new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).Console({\n                format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.colorize(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.simple())\n            }));\n        }\n    }\n    formatMessage(message, context) {\n        if (!context) return message;\n        const contextStr = Object.entries(context).map(([key, value])=>`${key}=${value}`).join(\" \");\n        return `${message} [${contextStr}]`;\n    }\n    info(message, context) {\n        this.logger.info(this.formatMessage(message, context), context);\n    }\n    error(message, error, context) {\n        const logContext = {\n            ...context\n        };\n        if (error) {\n            logContext.error = error.message;\n            logContext.stack = error.stack;\n        }\n        this.logger.error(this.formatMessage(message, context), logContext);\n    }\n    warn(message, context) {\n        this.logger.warn(this.formatMessage(message, context), context);\n    }\n    debug(message, context) {\n        this.logger.debug(this.formatMessage(message, context), context);\n    }\n    // MCP 特定的日志方法\n    mcpRequest(toolName, args, context) {\n        this.info(`MCP tool called: ${toolName}`, {\n            ...context,\n            toolName,\n            args: JSON.stringify(args)\n        });\n    }\n    mcpResponse(toolName, success, duration, context) {\n        this.info(`MCP tool completed: ${toolName}`, {\n            ...context,\n            toolName,\n            success,\n            duration: `${duration}ms`\n        });\n    }\n    mcpError(toolName, error, context) {\n        this.error(`MCP tool failed: ${toolName}`, error, {\n            ...context,\n            toolName\n        });\n    }\n    streamStart(streamId, context) {\n        this.info(`Stream started: ${streamId}`, {\n            ...context,\n            streamId\n        });\n    }\n    streamEnd(streamId, itemCount, duration, context) {\n        this.info(`Stream completed: ${streamId}`, {\n            ...context,\n            streamId,\n            itemCount,\n            duration: `${duration}ms`\n        });\n    }\n    streamError(streamId, error, context) {\n        this.error(`Stream failed: ${streamId}`, error, {\n            ...context,\n            streamId\n        });\n    }\n    // API 请求日志\n    apiRequest(method, path, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            method,\n            path\n        });\n    }\n    apiResponse(method, path, status, duration, context) {\n        this.info(`API ${method} ${path} ${status}`, {\n            ...context,\n            method,\n            path,\n            status,\n            duration: `${duration}ms`\n        });\n    }\n    // 飞书 API 日志\n    feishuRequest(api, context) {\n        this.info(`Feishu API called: ${api}`, {\n            ...context,\n            feishuApi: api\n        });\n    }\n    feishuResponse(api, code, duration, context) {\n        this.info(`Feishu API completed: ${api}`, {\n            ...context,\n            feishuApi: api,\n            feishuCode: code,\n            duration: `${duration}ms`\n        });\n    }\n    feishuError(api, error, context) {\n        this.error(`Feishu API failed: ${api}`, error, {\n            ...context,\n            feishuApi: api\n        });\n    }\n}\n// 导出单例实例\nconst logger = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-adapter-core.ts":
/*!********************************************!*\
  !*** ./src/lib/tools/tool-adapter-core.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   ALL_FEISHU_TOOLS: () => (/* binding */ ALL_FEISHU_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   CORE_TASK_TOOLS: () => (/* binding */ CORE_TASK_TOOLS),\n/* harmony export */   getAllTools: () => (/* binding */ getAllTools),\n/* harmony export */   getCalendarTools: () => (/* binding */ getCalendarTools),\n/* harmony export */   getTaskTools: () => (/* binding */ getTaskTools),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器 - 核心版本\n * 包含日历和任务管理的核心工具\n */ /**\n * 核心日历工具集 - 7个精选工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情。注意：必须先调用calendar.list获取日历ID，不能直接使用日历名称\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    // 📝 日程管理 (核心功能)\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"查看指定日历中指定时间范围的日程视图(含重复日程展开)。注意：必须先调用calendar.list获取日历ID\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间，Unix 时间戳，单位为秒\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间，Unix 时间戳，单位为秒\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取单个日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则，RRULE 格式\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与者权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    }\n];\n/**\n * 核心任务管理工具集 - 精选任务工具（排除订阅、附件、评论、自定义字段）\n */ const CORE_TASK_TOOLS = [\n    // 📋 任务清单管理\n    {\n        name: \"task.v2.tasklist.list\",\n        description: \"获取任务清单列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 100\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            }\n        }\n    },\n    {\n        name: \"task.v2.tasklist.get\",\n        description: \"获取单个任务清单详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                tasklist_guid: {\n                    type: \"string\",\n                    description: \"任务清单的全局唯一ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"tasklist_guid\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.tasklist.create\",\n        description: \"创建任务清单\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                name: {\n                    type: \"string\",\n                    description: \"清单名称，不能为空，最大100个utf8字符\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"清单描述，最大1000个utf8字符\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"name\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.tasklist.patch\",\n        description: \"更新任务清单\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                tasklist_guid: {\n                    type: \"string\",\n                    description: \"要更新的任务清单GUID\"\n                },\n                name: {\n                    type: \"string\",\n                    description: \"清单名称\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"清单描述\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"tasklist_guid\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.tasklist.delete\",\n        description: \"删除任务清单\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                tasklist_guid: {\n                    type: \"string\",\n                    description: \"要删除的任务清单GUID\"\n                }\n            },\n            required: [\n                \"tasklist_guid\"\n            ]\n        }\n    },\n    // 📝 任务管理\n    {\n        name: \"task.v2.task.list\",\n        description: \"获取任务列表（我负责的任务）\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"每页的任务数量，最大值为 100\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                completed: {\n                    type: \"boolean\",\n                    description: \"是否按任务完成进行过滤。true=已完成，false=未完成，不填=不过滤\"\n                },\n                type: {\n                    type: \"string\",\n                    description: '列取任务的类型，目前只支持\"my_tasks\"（我负责的）',\n                    enum: [\n                        \"my_tasks\"\n                    ]\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            }\n        }\n    },\n    {\n        name: \"task.v2.task.get\",\n        description: \"获取任务详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                task_guid: {\n                    type: \"string\",\n                    description: \"要获取的任务GUID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"task_guid\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.task.create\",\n        description: \"创建任务\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                summary: {\n                    type: \"string\",\n                    description: \"任务标题，不能为空，支持最大3000个utf8字符\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"任务描述，支持最大3000个utf8字符\"\n                },\n                due: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"截止时间的时间戳（毫秒）\"\n                        },\n                        is_all_day: {\n                            type: \"boolean\",\n                            description: \"是否截止到一个日期\"\n                        }\n                    },\n                    description: \"任务截止时间\"\n                },\n                start: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间的时间戳（毫秒）\"\n                        },\n                        is_all_day: {\n                            type: \"boolean\",\n                            description: \"是否开始于一个日期\"\n                        }\n                    },\n                    description: \"任务开始时间\"\n                },\n                completed_at: {\n                    type: \"string\",\n                    description: \"任务的完成时刻时间戳(ms)。不填写或者设为0表示创建未完成任务\"\n                },\n                extra: {\n                    type: \"string\",\n                    description: \"调用者可以传入的任意附带到任务上的数据\"\n                },\n                mode: {\n                    type: \"number\",\n                    description: \"任务完成模式, 1=会签任务, 2=或签任务\"\n                },\n                is_milestone: {\n                    type: \"boolean\",\n                    description: \"是否是里程碑任务\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"summary\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.task.patch\",\n        description: \"更新任务\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                task_guid: {\n                    type: \"string\",\n                    description: \"要更新的任务GUID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"任务标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"任务描述\"\n                },\n                due: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"截止时间的时间戳（毫秒）\"\n                        },\n                        is_all_day: {\n                            type: \"boolean\",\n                            description: \"是否截止到一个日期\"\n                        }\n                    },\n                    description: \"任务截止时间\"\n                },\n                start: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间的时间戳（毫秒）\"\n                        },\n                        is_all_day: {\n                            type: \"boolean\",\n                            description: \"是否开始于一个日期\"\n                        }\n                    },\n                    description: \"任务开始时间\"\n                },\n                completed_at: {\n                    type: \"string\",\n                    description: \"任务的完成时刻时间戳(ms)。设为0表示标记为未完成\"\n                },\n                extra: {\n                    type: \"string\",\n                    description: \"调用者可以传入的任意附带到任务上的数据\"\n                },\n                mode: {\n                    type: \"number\",\n                    description: \"任务完成模式, 1=会签任务, 2=或签任务\"\n                },\n                is_milestone: {\n                    type: \"boolean\",\n                    description: \"是否是里程碑任务\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"task_guid\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.task.delete\",\n        description: \"删除任务\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                task_guid: {\n                    type: \"string\",\n                    description: \"要删除的任务GUID\"\n                }\n            },\n            required: [\n                \"task_guid\"\n            ]\n        }\n    },\n    // 📂 任务分组管理\n    {\n        name: \"task.v2.section.list\",\n        description: \"获取自定义分组列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 100\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                resource_type: {\n                    type: \"string\",\n                    description: \"自定义分组所属的资源类型，支持my_tasks（我负责的）和tasklist（清单）\",\n                    enum: [\n                        \"my_tasks\",\n                        \"tasklist\"\n                    ]\n                },\n                resource_id: {\n                    type: \"string\",\n                    description: '如resource_type为\"tasklist\"，这里需要填写要列取自定义分组的清单的GUID'\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"resource_type\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.section.get\",\n        description: \"获取自定义分组详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                section_guid: {\n                    type: \"string\",\n                    description: \"要获取的自定义分组GUID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"section_guid\"\n            ]\n        }\n    },\n    {\n        name: \"task.v2.section.create\",\n        description: \"创建自定义分组\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                name: {\n                    type: \"string\",\n                    description: \"自定义分组名，不允许为空，最大100个utf8字符\"\n                },\n                resource_type: {\n                    type: \"string\",\n                    description: '自定义分组的资源类型，支持\"tasklist\"（清单）或者\"my_tasks\"（我负责的）',\n                    enum: [\n                        \"tasklist\",\n                        \"my_tasks\"\n                    ]\n                },\n                resource_id: {\n                    type: \"string\",\n                    description: '自定义分组要归属的资源id。当resource_type为\"tasklist\"时这里需要填写清单的GUID'\n                },\n                insert_before: {\n                    type: \"string\",\n                    description: \"要将新分组插入到自定义分组的前面的目标分组的guid\"\n                },\n                insert_after: {\n                    type: \"string\",\n                    description: \"要将新分组插入到自定义分组的后面的目标分组的guid\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"name\",\n                \"resource_type\"\n            ]\n        }\n    }\n];\n/**\n * 合并所有工具集\n */ const ALL_FEISHU_TOOLS = [\n    ...CORE_CALENDAR_TOOLS,\n    ...CORE_TASK_TOOLS\n];\n/**\n * 导出核心工具集 (兼容原有接口)\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return ALL_FEISHU_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n/**\n * 获取所有日历工具\n */ function getCalendarTools() {\n    return CORE_CALENDAR_TOOLS;\n}\n/**\n * 获取所有任务工具\n */ function getTaskTools() {\n    return CORE_TASK_TOOLS;\n}\n/**\n * 获取所有工具\n */ function getAllTools() {\n    return ALL_FEISHU_TOOLS;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter-core.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/readable-stream","vendor-chunks/winston","vendor-chunks/color","vendor-chunks/async","vendor-chunks/logform","vendor-chunks/safe-stable-stringify","vendor-chunks/@colors","vendor-chunks/fecha","vendor-chunks/winston-transport","vendor-chunks/string_decoder","vendor-chunks/@dabh","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/stack-trace","vendor-chunks/triple-beam","vendor-chunks/ms","vendor-chunks/kuler","vendor-chunks/safe-buffer","vendor-chunks/one-time","vendor-chunks/inherits","vendor-chunks/fn.name","vendor-chunks/enabled","vendor-chunks/colorspace","vendor-chunks/is-stream","vendor-chunks/simple-swizzle","vendor-chunks/text-hex","vendor-chunks/is-arrayish","vendor-chunks/util-deprecate","vendor-chunks/@larksuiteoapi","vendor-chunks/ws","vendor-chunks/protobufjs","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/@protobufjs","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/mime-types","vendor-chunks/lodash.pickby","vendor-chunks/lodash.merge","vendor-chunks/lodash.identity","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();