/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/tools/call/route";
exports.ids = ["app/api/mcp/tools/call/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/tools/call/route.ts */ \"(rsc)/./src/app/api/mcp/tools/call/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/tools/call/route\",\n        pathname: \"/api/mcp/tools/call\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/tools/call/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\mcp\\\\tools\\\\call\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/mcp/tools/call/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/mcp/tools/call/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/mcp/tools/call/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/feishu-official-client */ \"(rsc)/./src/lib/feishu-official-client.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/**\n * Next.js API Route for MCP Tool Calls\n * POST /api/mcp/tools/call - 调用 MCP 工具\n */ \n\n\n// 创建飞书客户端工厂函数\nfunction createFeishuClient(userAccessToken) {\n    return new _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__.OfficialFeishuClient(\"cli_a76a68f612bf900c\", \"EVumG3wCHsDBeJRfpbmJkfRhzCns73jC\", userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN);\n}\nasync function callTool(name, args, userAccessToken) {\n    const startTime = Date.now();\n    try {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuRequest(name);\n        // 创建使用指定token的飞书客户端\n        const feishuClient = createFeishuClient(userAccessToken);\n        // 使用简化的通用 API 调用方法\n        const result = await feishuClient.callApi(name, args);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuResponse(name, result.code, duration);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: JSON.stringify(result, null, 2)\n                }\n            ],\n            isError: result.code !== 0\n        };\n    } catch (error) {\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuError(name, error);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: `Error: ${error instanceof Error ? error.message : String(error)}`\n                }\n            ],\n            isError: true\n        };\n    }\n}\nasync function POST(request) {\n    try {\n        const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const body = await request.json();\n        const { name, arguments: args } = body;\n        // 从请求头中获取Authorization token\n        const authHeader = request.headers.get(\"authorization\");\n        let userAccessToken;\n        if (authHeader && authHeader.startsWith(\"Bearer \")) {\n            userAccessToken = authHeader.substring(7); // 移除 \"Bearer \" 前缀\n            console.log(`🔑 使用请求头中的token: ${userAccessToken.substring(0, 20)}...`);\n        } else {\n            console.log(`🔑 使用环境变量中的token`);\n        }\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpRequest(name, args, {\n            requestId\n        });\n        const startTime = Date.now();\n        const result = await callTool(name, args, userAccessToken);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpResponse(name, !result.isError, duration, {\n            requestId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: !result.isError,\n            result\n        });\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpError(\"unknown\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/tools/call/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/feishu-official-client.ts":
/*!*******************************************!*\
  !*** ./src/lib/feishu-official-client.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OfficialFeishuClient: () => (/* binding */ OfficialFeishuClient)\n/* harmony export */ });\n/* harmony import */ var _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @larksuiteoapi/node-sdk */ \"(rsc)/./node_modules/@larksuiteoapi/node-sdk/es/index.js\");\n/* harmony import */ var _tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tools/tool-adapter-core */ \"(rsc)/./src/lib/tools/tool-adapter-core.ts\");\n/**\n * Official Feishu SDK Client\n * 基于官方 @larksuiteoapi/node-sdk 的完整实现\n * 支持所有 calendar.v4 API\n */ \n\nclass OfficialFeishuClient {\n    constructor(appId, appSecret, userAccessToken){\n        this.client = new _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({\n            appId,\n            appSecret,\n            appType: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.AppType.SelfBuild,\n            domain: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Domain.Feishu\n        });\n        // 从环境变量获取用户访问令牌\n        this.userAccessToken = userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN;\n        console.log(\"Official Feishu client initialized:\", {\n            appId,\n            hasAppSecret: !!appSecret,\n            hasUserToken: !!this.userAccessToken,\n            userTokenPrefix: this.userAccessToken ? this.userAccessToken.substring(0, 10) + \"...\" : \"none\"\n        });\n    }\n    setUserAccessToken(token) {\n        this.userAccessToken = token;\n    }\n    /**\n   * 通用 API 调用方法\n   * 支持所有 calendar.v4 API\n   */ async callApi(method, params = {}) {\n        try {\n            console.log(`🔧 调用官方 API 方法: ${method}`, params);\n            // 验证工具和参数\n            const tool = (0,_tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__.getToolByName)(method);\n            if (!tool) {\n                console.warn(`⚠️  未知的 API 方法: ${method}`);\n                return {\n                    code: -1,\n                    msg: `Unknown method: ${method}`,\n                    data: null\n                };\n            }\n            // 验证参数\n            const validation = (0,_tools_tool_adapter_core__WEBPACK_IMPORTED_MODULE_1__.validateToolArguments)(method, params);\n            if (!validation.valid) {\n                console.error(`❌ 参数验证失败:`, validation.errors);\n                return {\n                    code: -1,\n                    msg: `Invalid arguments: ${validation.errors?.join(\", \")}`,\n                    data: null\n                };\n            }\n            // 根据方法名调用相应的 API\n            const result = await this._callSpecificApi(method, params);\n            console.log(`✅ API 调用成功: ${method}`, {\n                code: result.code,\n                msg: result.msg\n            });\n            return result;\n        } catch (error) {\n            console.error(`❌ API 调用失败 (${method}):`, error);\n            return {\n                code: -1,\n                msg: `API 调用失败: ${error}`,\n                data: null\n            };\n        }\n    }\n    /**\n   * 调用具体的 API 方法\n   */ async _callSpecificApi(method, params) {\n        const useUserToken = params.useUAT !== false; // 默认使用用户访问令牌\n        try {\n            let response;\n            let requestOptions = {};\n            // 根据方法名构建请求\n            switch(method){\n                // 日历管理 API\n                case \"calendar.v4.calendar.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.primary\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.primary(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.primary(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.search\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"query\",\n                            \"user_id_type\",\n                            \"page_token\",\n                            \"page_size\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.search(requestOptions);\n                    break;\n                // 日历事件 API\n                case \"calendar.v4.calendarEvent.list\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.create\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\",\n                            \"need_meeting_settings\",\n                            \"need_attendee\",\n                            \"max_attendee_num\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"need_notification\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.search\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"query\",\n                            \"filter\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.search(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instances\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\",\n                            \"page_size\",\n                            \"page_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instances(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instances(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instanceView\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instanceView(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instanceView(requestOptions);\n                    break;\n                default:\n                    console.warn(`⚠️  未实现的 API 方法: ${method}`);\n                    return {\n                        code: -1,\n                        msg: `Unimplemented method: ${method}`,\n                        data: null\n                    };\n            }\n            // 统一处理响应\n            return {\n                code: response.code || 0,\n                msg: response.msg || \"success\",\n                data: response.data\n            };\n        } catch (error) {\n            console.error(`❌ 具体 API 调用失败 (${method}):`, error);\n            // 提取更详细的错误信息\n            let errorMsg = `API 调用失败: ${error.message || error}`;\n            if (error.response?.data) {\n                errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;\n            }\n            return {\n                code: -1,\n                msg: errorMsg,\n                data: null\n            };\n        }\n    }\n    /**\n   * 构建参数对象，只包含指定的字段\n   */ _buildParams(params, allowedFields) {\n        const result = {};\n        for (const field of allowedFields){\n            if (params[field] !== undefined) {\n                result[field] = params[field];\n            }\n        }\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/feishu-official-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! winston */ \"(rsc)/./node_modules/winston/lib/winston.js\");\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(winston__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Logger Utility\n * 统一的日志记录工具\n */ \n\nclass Logger {\n    constructor(){\n        const logLevel = process.env.LOG_LEVEL || \"info\";\n        const logFile = process.env.LOG_FILE || \"./logs/mcp-server.log\";\n        // 确保日志目录存在\n        const logDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(logFile);\n        this.logger = winston__WEBPACK_IMPORTED_MODULE_0___default().createLogger({\n            level: logLevel,\n            format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.timestamp(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.errors({\n                stack: true\n            }), winston__WEBPACK_IMPORTED_MODULE_0___default().format.json()),\n            defaultMeta: {\n                service: \"calmcp\"\n            },\n            transports: [\n                // 错误日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: path__WEBPACK_IMPORTED_MODULE_1___default().join(logDir, \"error.log\"),\n                    level: \"error\",\n                    maxsize: 5242880,\n                    maxFiles: 5\n                }),\n                // 所有日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: logFile,\n                    maxsize: 5242880,\n                    maxFiles: 5\n                })\n            ]\n        });\n        // 开发环境下添加控制台输出\n        if (true) {\n            this.logger.add(new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).Console({\n                format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.colorize(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.simple())\n            }));\n        }\n    }\n    formatMessage(message, context) {\n        if (!context) return message;\n        const contextStr = Object.entries(context).map(([key, value])=>`${key}=${value}`).join(\" \");\n        return `${message} [${contextStr}]`;\n    }\n    info(message, context) {\n        this.logger.info(this.formatMessage(message, context), context);\n    }\n    error(message, error, context) {\n        const logContext = {\n            ...context\n        };\n        if (error) {\n            logContext.error = error.message;\n            logContext.stack = error.stack;\n        }\n        this.logger.error(this.formatMessage(message, context), logContext);\n    }\n    warn(message, context) {\n        this.logger.warn(this.formatMessage(message, context), context);\n    }\n    debug(message, context) {\n        this.logger.debug(this.formatMessage(message, context), context);\n    }\n    // MCP 特定的日志方法\n    mcpRequest(toolName, args, context) {\n        this.info(`MCP tool called: ${toolName}`, {\n            ...context,\n            toolName,\n            args: JSON.stringify(args)\n        });\n    }\n    mcpResponse(toolName, success, duration, context) {\n        this.info(`MCP tool completed: ${toolName}`, {\n            ...context,\n            toolName,\n            success,\n            duration: `${duration}ms`\n        });\n    }\n    mcpError(toolName, error, context) {\n        this.error(`MCP tool failed: ${toolName}`, error, {\n            ...context,\n            toolName\n        });\n    }\n    streamStart(streamId, context) {\n        this.info(`Stream started: ${streamId}`, {\n            ...context,\n            streamId\n        });\n    }\n    streamEnd(streamId, itemCount, duration, context) {\n        this.info(`Stream completed: ${streamId}`, {\n            ...context,\n            streamId,\n            itemCount,\n            duration: `${duration}ms`\n        });\n    }\n    streamError(streamId, error, context) {\n        this.error(`Stream failed: ${streamId}`, error, {\n            ...context,\n            streamId\n        });\n    }\n    // API 请求日志\n    apiRequest(method, path, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            method,\n            path\n        });\n    }\n    apiResponse(method, path, status, duration, context) {\n        this.info(`API ${method} ${path} ${status}`, {\n            ...context,\n            method,\n            path,\n            status,\n            duration: `${duration}ms`\n        });\n    }\n    // 飞书 API 日志\n    feishuRequest(api, context) {\n        this.info(`Feishu API called: ${api}`, {\n            ...context,\n            feishuApi: api\n        });\n    }\n    feishuResponse(api, code, duration, context) {\n        this.info(`Feishu API completed: ${api}`, {\n            ...context,\n            feishuApi: api,\n            feishuCode: code,\n            duration: `${duration}ms`\n        });\n    }\n    feishuError(api, error, context) {\n        this.error(`Feishu API failed: ${api}`, error, {\n            ...context,\n            feishuApi: api\n        });\n    }\n}\n// 导出单例实例\nconst logger = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-adapter-core.ts":
/*!********************************************!*\
  !*** ./src/lib/tools/tool-adapter-core.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器 - 核心版本\n * 只保留项目必需的9个核心日历工具\n */ /**\n * 核心日历工具集 - 7个精选工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情。注意：必须先调用calendar.list获取日历ID，不能直接使用日历名称\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    // 📝 日程管理 (核心功能)\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"查看指定日历中指定时间范围的日程视图(含重复日程展开)。注意：必须先调用calendar.list获取日历ID\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间，Unix 时间戳，单位为秒\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间，Unix 时间戳，单位为秒\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取单个日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则，RRULE 格式\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与者权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    }\n];\n/**\n * 导出核心工具集 (兼容原有接口)\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return CORE_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter-core.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/readable-stream","vendor-chunks/winston","vendor-chunks/color","vendor-chunks/async","vendor-chunks/logform","vendor-chunks/safe-stable-stringify","vendor-chunks/@colors","vendor-chunks/fecha","vendor-chunks/winston-transport","vendor-chunks/string_decoder","vendor-chunks/@dabh","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/stack-trace","vendor-chunks/triple-beam","vendor-chunks/ms","vendor-chunks/kuler","vendor-chunks/safe-buffer","vendor-chunks/one-time","vendor-chunks/inherits","vendor-chunks/fn.name","vendor-chunks/enabled","vendor-chunks/colorspace","vendor-chunks/is-stream","vendor-chunks/simple-swizzle","vendor-chunks/text-hex","vendor-chunks/is-arrayish","vendor-chunks/util-deprecate","vendor-chunks/@larksuiteoapi","vendor-chunks/ws","vendor-chunks/protobufjs","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/@protobufjs","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/mime-types","vendor-chunks/lodash.pickby","vendor-chunks/lodash.merge","vendor-chunks/lodash.identity","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();