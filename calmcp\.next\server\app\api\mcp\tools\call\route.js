/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/mcp/tools/call/route";
exports.ids = ["app/api/mcp/tools/call/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("string_decoder");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/mcp/tools/call/route.ts */ \"(rsc)/./src/app/api/mcp/tools/call/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/mcp/tools/call/route\",\n        pathname: \"/api/mcp/tools/call\",\n        filename: \"route\",\n        bundlePath: \"app/api/mcp/tools/call/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\mcp\\\\tools\\\\call\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_mcp_tools_call_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/mcp/tools/call/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/mcp/tools/call/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/mcp/tools/call/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/feishu-official-client */ \"(rsc)/./src/lib/feishu-official-client.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/**\n * Next.js API Route for MCP Tool Calls\n * POST /api/mcp/tools/call - 调用 MCP 工具\n */ \n\n\n// 创建飞书客户端工厂函数\nfunction createFeishuClient(userAccessToken) {\n    return new _lib_feishu_official_client__WEBPACK_IMPORTED_MODULE_1__.OfficialFeishuClient(\"cli_a76a68f612bf900c\", \"EVumG3wCHsDBeJRfpbmJkfRhzCns73jC\", userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN);\n}\nasync function callTool(name, args, userAccessToken) {\n    const startTime = Date.now();\n    try {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuRequest(name);\n        // 创建使用指定token的飞书客户端\n        const feishuClient = createFeishuClient(userAccessToken);\n        // 使用简化的通用 API 调用方法\n        const result = await feishuClient.callApi(name, args);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuResponse(name, result.code, duration);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: JSON.stringify(result, null, 2)\n                }\n            ],\n            isError: result.code !== 0\n        };\n    } catch (error) {\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.feishuError(name, error);\n        return {\n            content: [\n                {\n                    type: \"text\",\n                    text: `Error: ${error instanceof Error ? error.message : String(error)}`\n                }\n            ],\n            isError: true\n        };\n    }\n}\nasync function POST(request) {\n    try {\n        const requestId = `api_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n        const body = await request.json();\n        const { name, arguments: args } = body;\n        // 从请求头中获取Authorization token\n        const authHeader = request.headers.get(\"authorization\");\n        let userAccessToken;\n        if (authHeader && authHeader.startsWith(\"Bearer \")) {\n            userAccessToken = authHeader.substring(7); // 移除 \"Bearer \" 前缀\n            console.log(`🔑 使用请求头中的token: ${userAccessToken.substring(0, 20)}...`);\n        } else {\n            console.log(`🔑 使用环境变量中的token`);\n        }\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpRequest(name, args, {\n            requestId\n        });\n        const startTime = Date.now();\n        const result = await callTool(name, args, userAccessToken);\n        const duration = Date.now() - startTime;\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpResponse(name, !result.isError, duration, {\n            requestId\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: !result.isError,\n            result\n        });\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.mcpError(\"unknown\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/mcp/tools/call/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/feishu-official-client.ts":
/*!*******************************************!*\
  !*** ./src/lib/feishu-official-client.ts ***!
  \*******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OfficialFeishuClient: () => (/* binding */ OfficialFeishuClient)\n/* harmony export */ });\n/* harmony import */ var _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @larksuiteoapi/node-sdk */ \"(rsc)/./node_modules/@larksuiteoapi/node-sdk/es/index.js\");\n/* harmony import */ var _tools_tool_adapter__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./tools/tool-adapter */ \"(rsc)/./src/lib/tools/tool-adapter.ts\");\n/**\n * Official Feishu SDK Client\n * 基于官方 @larksuiteoapi/node-sdk 的完整实现\n * 支持所有 calendar.v4 API\n */ \n\nclass OfficialFeishuClient {\n    constructor(appId, appSecret, userAccessToken){\n        this.client = new _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Client({\n            appId,\n            appSecret,\n            appType: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.AppType.SelfBuild,\n            domain: _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.Domain.Feishu\n        });\n        // 从环境变量获取用户访问令牌\n        this.userAccessToken = userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN;\n        console.log(\"Official Feishu client initialized:\", {\n            appId,\n            hasAppSecret: !!appSecret,\n            hasUserToken: !!this.userAccessToken,\n            userTokenPrefix: this.userAccessToken ? this.userAccessToken.substring(0, 10) + \"...\" : \"none\"\n        });\n    }\n    setUserAccessToken(token) {\n        this.userAccessToken = token;\n    }\n    /**\n   * 通用 API 调用方法\n   * 支持所有 calendar.v4 API\n   */ async callApi(method, params = {}) {\n        try {\n            console.log(`🔧 调用官方 API 方法: ${method}`, params);\n            // 验证工具和参数\n            const tool = (0,_tools_tool_adapter__WEBPACK_IMPORTED_MODULE_1__.getToolByName)(method);\n            if (!tool) {\n                console.warn(`⚠️  未知的 API 方法: ${method}`);\n                return {\n                    code: -1,\n                    msg: `Unknown method: ${method}`,\n                    data: null\n                };\n            }\n            // 验证参数\n            const validation = (0,_tools_tool_adapter__WEBPACK_IMPORTED_MODULE_1__.validateToolArguments)(method, params);\n            if (!validation.valid) {\n                console.error(`❌ 参数验证失败:`, validation.errors);\n                return {\n                    code: -1,\n                    msg: `Invalid arguments: ${validation.errors?.join(\", \")}`,\n                    data: null\n                };\n            }\n            // 根据方法名调用相应的 API\n            const result = await this._callSpecificApi(method, params);\n            console.log(`✅ API 调用成功: ${method}`, {\n                code: result.code,\n                msg: result.msg\n            });\n            return result;\n        } catch (error) {\n            console.error(`❌ API 调用失败 (${method}):`, error);\n            return {\n                code: -1,\n                msg: `API 调用失败: ${error}`,\n                data: null\n            };\n        }\n    }\n    /**\n   * 调用具体的 API 方法\n   */ async _callSpecificApi(method, params) {\n        const useUserToken = params.useUAT !== false; // 默认使用用户访问令牌\n        try {\n            let response;\n            let requestOptions = {};\n            // 根据方法名构建请求\n            switch(method){\n                // 日历管理 API\n                case \"calendar.v4.calendar.list\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.create\":\n                    requestOptions = {\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        }\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"permissions\",\n                            \"color\",\n                            \"summary_alias\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.primary\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.primary(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.primary(requestOptions);\n                    break;\n                case \"calendar.v4.calendar.search\":\n                    requestOptions = {\n                        params: this._buildParams(params, [\n                            \"query\",\n                            \"user_id_type\",\n                            \"page_token\",\n                            \"page_size\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendar.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendar.search(requestOptions);\n                    break;\n                // 日历事件 API\n                case \"calendar.v4.calendarEvent.list\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"page_size\",\n                            \"page_token\",\n                            \"sync_token\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.list(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.list(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.create\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.create(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.create(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.get\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"user_id_type\",\n                            \"need_meeting_settings\",\n                            \"need_attendee\",\n                            \"max_attendee_num\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.get(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.get(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.delete\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"need_notification\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.delete(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.delete(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.patch\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        data: this._buildParams(params, [\n                            \"summary\",\n                            \"description\",\n                            \"start_time\",\n                            \"end_time\",\n                            \"location\",\n                            \"visibility\",\n                            \"attendee_ability\",\n                            \"free_busy_status\"\n                        ]),\n                        params: this._buildParams(params, [\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.patch(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.patch(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.search\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        data: this._buildParams(params, [\n                            \"query\",\n                            \"filter\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.search(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.search(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instances\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id,\n                            event_id: params.event_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\",\n                            \"page_size\",\n                            \"page_token\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instances(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instances(requestOptions);\n                    break;\n                case \"calendar.v4.calendarEvent.instanceView\":\n                    requestOptions = {\n                        path: {\n                            calendar_id: params.calendar_id\n                        },\n                        params: this._buildParams(params, [\n                            \"start_time\",\n                            \"end_time\",\n                            \"user_id_type\"\n                        ])\n                    };\n                    response = useUserToken && this.userAccessToken ? await this.client.calendar.v4.calendarEvent.instanceView(requestOptions, _larksuiteoapi_node_sdk__WEBPACK_IMPORTED_MODULE_0__.withUserAccessToken(this.userAccessToken)) : await this.client.calendar.v4.calendarEvent.instanceView(requestOptions);\n                    break;\n                default:\n                    console.warn(`⚠️  未实现的 API 方法: ${method}`);\n                    return {\n                        code: -1,\n                        msg: `Unimplemented method: ${method}`,\n                        data: null\n                    };\n            }\n            // 统一处理响应\n            return {\n                code: response.code || 0,\n                msg: response.msg || \"success\",\n                data: response.data\n            };\n        } catch (error) {\n            console.error(`❌ 具体 API 调用失败 (${method}):`, error);\n            // 提取更详细的错误信息\n            let errorMsg = `API 调用失败: ${error.message || error}`;\n            if (error.response?.data) {\n                errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;\n            }\n            return {\n                code: -1,\n                msg: errorMsg,\n                data: null\n            };\n        }\n    }\n    /**\n   * 构建参数对象，只包含指定的字段\n   */ _buildParams(params, allowedFields) {\n        const result = {};\n        for (const field of allowedFields){\n            if (params[field] !== undefined) {\n                result[field] = params[field];\n            }\n        }\n        return result;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/feishu-official-client.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! winston */ \"(rsc)/./node_modules/winston/lib/winston.js\");\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(winston__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Logger Utility\n * 统一的日志记录工具\n */ \n\nclass Logger {\n    constructor(){\n        const logLevel = process.env.LOG_LEVEL || \"info\";\n        const logFile = process.env.LOG_FILE || \"./logs/mcp-server.log\";\n        // 确保日志目录存在\n        const logDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(logFile);\n        this.logger = winston__WEBPACK_IMPORTED_MODULE_0___default().createLogger({\n            level: logLevel,\n            format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.timestamp(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.errors({\n                stack: true\n            }), winston__WEBPACK_IMPORTED_MODULE_0___default().format.json()),\n            defaultMeta: {\n                service: \"calmcp\"\n            },\n            transports: [\n                // 错误日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: path__WEBPACK_IMPORTED_MODULE_1___default().join(logDir, \"error.log\"),\n                    level: \"error\",\n                    maxsize: 5242880,\n                    maxFiles: 5\n                }),\n                // 所有日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: logFile,\n                    maxsize: 5242880,\n                    maxFiles: 5\n                })\n            ]\n        });\n        // 开发环境下添加控制台输出\n        if (true) {\n            this.logger.add(new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).Console({\n                format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.colorize(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.simple())\n            }));\n        }\n    }\n    formatMessage(message, context) {\n        if (!context) return message;\n        const contextStr = Object.entries(context).map(([key, value])=>`${key}=${value}`).join(\" \");\n        return `${message} [${contextStr}]`;\n    }\n    info(message, context) {\n        this.logger.info(this.formatMessage(message, context), context);\n    }\n    error(message, error, context) {\n        const logContext = {\n            ...context\n        };\n        if (error) {\n            logContext.error = error.message;\n            logContext.stack = error.stack;\n        }\n        this.logger.error(this.formatMessage(message, context), logContext);\n    }\n    warn(message, context) {\n        this.logger.warn(this.formatMessage(message, context), context);\n    }\n    debug(message, context) {\n        this.logger.debug(this.formatMessage(message, context), context);\n    }\n    // MCP 特定的日志方法\n    mcpRequest(toolName, args, context) {\n        this.info(`MCP tool called: ${toolName}`, {\n            ...context,\n            toolName,\n            args: JSON.stringify(args)\n        });\n    }\n    mcpResponse(toolName, success, duration, context) {\n        this.info(`MCP tool completed: ${toolName}`, {\n            ...context,\n            toolName,\n            success,\n            duration: `${duration}ms`\n        });\n    }\n    mcpError(toolName, error, context) {\n        this.error(`MCP tool failed: ${toolName}`, error, {\n            ...context,\n            toolName\n        });\n    }\n    streamStart(streamId, context) {\n        this.info(`Stream started: ${streamId}`, {\n            ...context,\n            streamId\n        });\n    }\n    streamEnd(streamId, itemCount, duration, context) {\n        this.info(`Stream completed: ${streamId}`, {\n            ...context,\n            streamId,\n            itemCount,\n            duration: `${duration}ms`\n        });\n    }\n    streamError(streamId, error, context) {\n        this.error(`Stream failed: ${streamId}`, error, {\n            ...context,\n            streamId\n        });\n    }\n    // API 请求日志\n    apiRequest(method, path, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            method,\n            path\n        });\n    }\n    apiResponse(method, path, status, duration, context) {\n        this.info(`API ${method} ${path} ${status}`, {\n            ...context,\n            method,\n            path,\n            status,\n            duration: `${duration}ms`\n        });\n    }\n    // 飞书 API 日志\n    feishuRequest(api, context) {\n        this.info(`Feishu API called: ${api}`, {\n            ...context,\n            feishuApi: api\n        });\n    }\n    feishuResponse(api, code, duration, context) {\n        this.info(`Feishu API completed: ${api}`, {\n            ...context,\n            feishuApi: api,\n            feishuCode: code,\n            duration: `${duration}ms`\n        });\n    }\n    feishuError(api, error, context) {\n        this.error(`Feishu API failed: ${api}`, error, {\n            ...context,\n            feishuApi: api\n        });\n    }\n}\n// 导出单例实例\nconst logger = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-adapter.ts":
/*!***************************************!*\
  !*** ./src/lib/tools/tool-adapter.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CALENDAR_ACL_TOOLS: () => (/* binding */ CALENDAR_ACL_TOOLS),\n/* harmony export */   CALENDAR_EVENT_ATTENDEE_TOOLS: () => (/* binding */ CALENDAR_EVENT_ATTENDEE_TOOLS),\n/* harmony export */   CALENDAR_EVENT_EXTENDED_TOOLS: () => (/* binding */ CALENDAR_EVENT_EXTENDED_TOOLS),\n/* harmony export */   CALENDAR_EVENT_MEETING_CHAT_TOOLS: () => (/* binding */ CALENDAR_EVENT_MEETING_CHAT_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   EXTENDED_CALENDAR_TOOLS: () => (/* binding */ EXTENDED_CALENDAR_TOOLS),\n/* harmony export */   OTHER_CALENDAR_TOOLS: () => (/* binding */ OTHER_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器\n * 使用核心日历工具集，只保留项目必需的功能\n */ /**\n * 核心日历工具集 - 只保留项目必需的功能\n * 基于 feishu-calendar-v4-core.ts 定义\n */ /**\n * 核心日历工具（精简版）- 9个核心工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.list\",\n        description: \"获取日历事件列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间（时间戳）\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间（时间戳）\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    description: \"开始时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                end_time: {\n                    type: \"object\",\n                    description: \"结束时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.search\",\n        description: \"搜索日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"搜索开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"搜索结束时间\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历访问控制工具\n */ const CALENDAR_ACL_TOOLS = [\n    {\n        name: \"calendar.v4.calendarAcl.create\",\n        description: \"创建日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                role: {\n                    type: \"string\",\n                    enum: [\n                        \"unknown\",\n                        \"free_busy_reader\",\n                        \"reader\",\n                        \"writer\",\n                        \"owner\"\n                    ],\n                    description: \"访问权限角色\"\n                },\n                scope: {\n                    type: \"object\",\n                    properties: {\n                        type: {\n                            type: \"string\",\n                            enum: [\n                                \"user\"\n                            ],\n                            description: \"权限生效范围类型\"\n                        },\n                        user_id: {\n                            type: \"string\",\n                            description: \"用户ID\"\n                        }\n                    },\n                    required: [\n                        \"type\"\n                    ]\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"role\",\n                \"scope\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.delete\",\n        description: \"删除日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                acl_id: {\n                    type: \"string\",\n                    description: \"访问控制ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"acl_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.list\",\n        description: \"获取日历访问控制列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最小值10\",\n                    minimum: 10\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.subscription\",\n        description: \"订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.unsubscription\",\n        description: \"取消订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 扩展的日历管理工具\n */ const EXTENDED_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendar.create\",\n        description: \"创建共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色（RGB int32值）\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.delete\",\n        description: \"删除共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.patch\",\n        description: \"更新日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.primary\",\n        description: \"获取主日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.search\",\n        description: \"搜索日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 200\n                }\n            },\n            required: [\n                \"query\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscribe\",\n        description: \"订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscription\",\n        description: \"订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscribe\",\n        description: \"取消订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscription\",\n        description: \"取消订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件参与者工具\n */ const CALENDAR_EVENT_ATTENDEE_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventAttendee.batchDelete\",\n        description: \"批量删除日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_ids: {\n                    type: \"array\",\n                    items: {\n                        type: \"string\"\n                    },\n                    description: \"参与人ID列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_ids\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.chatMembersBatchCreate\",\n        description: \"批量添加群成员为日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                chat_id: {\n                    type: \"string\",\n                    description: \"群聊ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.create\",\n        description: \"添加日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendees: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            type: {\n                                type: \"string\",\n                                enum: [\n                                    \"user\",\n                                    \"chat\",\n                                    \"resource\",\n                                    \"third_party\"\n                                ],\n                                description: \"参与人类型\"\n                            },\n                            attendee_id: {\n                                type: \"string\",\n                                description: \"参与人ID\"\n                            },\n                            rsvp_status: {\n                                type: \"string\",\n                                enum: [\n                                    \"needs_action\",\n                                    \"accept\",\n                                    \"tentative\",\n                                    \"decline\",\n                                    \"removed\"\n                                ],\n                                description: \"参与状态\"\n                            },\n                            is_optional: {\n                                type: \"boolean\",\n                                description: \"是否为可选参与人\"\n                            },\n                            display_name: {\n                                type: \"string\",\n                                description: \"参与人名称\"\n                            }\n                        },\n                        required: [\n                            \"type\",\n                            \"attendee_id\"\n                        ]\n                    },\n                    description: \"参与人列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendees\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.list\",\n        description: \"获取日程参与人列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendeeChatMember.list\",\n        description: \"获取日程参与人群成员列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_id: {\n                    type: \"string\",\n                    description: \"参与人ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件扩展工具\n */ const CALENDAR_EVENT_EXTENDED_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"获取日程实例视图\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"结束时间\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"日程公开范围\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与人权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"日程占用的忙闲状态\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"地点纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"地点经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                reminders: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            minutes: {\n                                type: \"number\",\n                                description: \"提前多少分钟提醒\"\n                            }\n                        }\n                    },\n                    description: \"日程提醒列表\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则\"\n                },\n                status: {\n                    type: \"string\",\n                    enum: [\n                        \"tentative\",\n                        \"confirmed\",\n                        \"cancelled\"\n                    ],\n                    description: \"日程状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instances\",\n        description: \"获取重复日程的实例\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    }\n];\n/**\n * 会议聊天工具\n */ const CALENDAR_EVENT_MEETING_CHAT_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.create\",\n        description: \"创建会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.delete\",\n        description: \"删除会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.patch\",\n        description: \"更新会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingMinute.create\",\n        description: \"创建会议纪要\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                content: {\n                    type: \"string\",\n                    description: \"会议纪要内容\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"content\"\n            ]\n        }\n    }\n];\n/**\n * 其他日历工具\n */ const OTHER_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.reply\",\n        description: \"回复日程邀请\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                rsvp_status: {\n                    type: \"string\",\n                    enum: [\n                        \"accept\",\n                        \"tentative\",\n                        \"decline\"\n                    ],\n                    description: \"回复状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"rsvp_status\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.subscription\",\n        description: \"订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.unsubscription\",\n        description: \"取消订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.create\",\n        description: \"创建Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                admin_account: {\n                    type: \"string\",\n                    description: \"管理员账号\"\n                },\n                exchange_account: {\n                    type: \"string\",\n                    description: \"Exchange账号\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"admin_account\",\n                \"exchange_account\",\n                \"user_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.delete\",\n        description: \"删除Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.get\",\n        description: \"获取Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.freebusy.list\",\n        description: \"查询忙闲信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                time_min: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                time_max: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                room_id: {\n                    type: \"string\",\n                    description: \"会议室ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"time_min\",\n                \"time_max\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.setting.generateCaldavConf\",\n        description: \"生成CalDAV配置\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                device_name: {\n                    type: \"string\",\n                    description: \"设备名称\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.create\",\n        description: \"创建请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                timezone: {\n                    type: \"string\",\n                    description: \"时区\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"请假开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"请假结束时间\"\n                },\n                title: {\n                    type: \"string\",\n                    description: \"请假标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"请假描述\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"user_id\",\n                \"timezone\",\n                \"start_time\",\n                \"end_time\",\n                \"title\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.delete\",\n        description: \"删除请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                timeoff_event_id: {\n                    type: \"string\",\n                    description: \"请假日程ID\"\n                }\n            },\n            required: [\n                \"timeoff_event_id\"\n            ]\n        }\n    }\n];\n/**\n * 核心日历工具（9个精选工具）\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return ALL_FEISHU_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数（简化版本）\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/readable-stream","vendor-chunks/winston","vendor-chunks/color","vendor-chunks/async","vendor-chunks/logform","vendor-chunks/safe-stable-stringify","vendor-chunks/@colors","vendor-chunks/fecha","vendor-chunks/winston-transport","vendor-chunks/string_decoder","vendor-chunks/@dabh","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/stack-trace","vendor-chunks/triple-beam","vendor-chunks/ms","vendor-chunks/kuler","vendor-chunks/safe-buffer","vendor-chunks/one-time","vendor-chunks/inherits","vendor-chunks/fn.name","vendor-chunks/enabled","vendor-chunks/colorspace","vendor-chunks/is-stream","vendor-chunks/simple-swizzle","vendor-chunks/text-hex","vendor-chunks/is-arrayish","vendor-chunks/util-deprecate","vendor-chunks/@larksuiteoapi","vendor-chunks/ws","vendor-chunks/protobufjs","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/@protobufjs","vendor-chunks/qs","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/object-inspect","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/supports-color","vendor-chunks/side-channel","vendor-chunks/side-channel-weakmap","vendor-chunks/side-channel-map","vendor-chunks/side-channel-list","vendor-chunks/mime-types","vendor-chunks/lodash.pickby","vendor-chunks/lodash.merge","vendor-chunks/lodash.identity","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream","vendor-chunks/call-bound"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&page=%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmcp%2Ftools%2Fcall%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();