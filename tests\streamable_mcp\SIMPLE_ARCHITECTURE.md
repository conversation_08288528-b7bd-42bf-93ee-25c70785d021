# 飞书MCP Streamable 简化架构图

## 🎯 核心架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                        用户层                                    │
├─────────────────────────────────────────────────────────────────┤
│  test_calendar_view.py  │  自定义客户端脚本  │  其他应用        │
└─────────────────────────┼─────────────────────┼─────────────────┘
                          │                     │
                          ▼                     ▼
┌─────────────────────────────────────────────────────────────────┐
│                        MCP客户端层                              │
├─────────────────────────────────────────────────────────────────┤
│              StreamableMCPClient (HTTP客户端)                   │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   连接管理      │  │   请求构建      │  │   响应解析      │  │
│  │  aiohttp.Session│  │  JSON-RPC 2.0   │  │  MCP格式处理    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                        MCP服务器层                              │
├─────────────────────────────────────────────────────────────────┤
│                    FastAPI MCP服务器                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   /mcp/initialize│  │  /mcp/tools/list│  │ /mcp/tools/call │  │
│  │   MCP初始化     │  │   工具列表      │  │   工具调用      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
│                          │                                     │
│                          ▼                                     │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    MCP工具集合                              │ │
│  │  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │ │
│  │  │list_calendars│ │get_calendar_│ │create_calendar│ │get_today│ │ │
│  │  │  日历列表   │ │  events     │ │  _event     │ │ _events │ │ │
│  │  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      飞书集成层                                 │
├─────────────────────────────────────────────────────────────────┤
│                  FeishuCalendarLark                             │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   API客户端     │  │   令牌管理      │  │   错误处理      │  │
│  │   HTTP请求      │  │   访问令牌      │  │   异常处理      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      外部服务层                                 │
├─────────────────────────────────────────────────────────────────┤
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   飞书开放平台   │  │   日历API       │  │   用户认证API   │  │
│  │   https://open. │  │   /calendar/v4/ │  │   /auth/v3/     │  │
│  │   feishu.cn     │  │                 │  │                 │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
                          │
                          ▼
┌─────────────────────────────────────────────────────────────────┐
│                      数据存储层                                 │
├─────────────────────────────────────────────────────────────────┤
│                    Supabase数据库                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   用户令牌表     │  │   日历缓存表     │  │   日志记录表     │  │
│  │   user_tokens   │  │   calendar_cache│  │   api_logs      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 🔄 数据流向

```
用户请求 → MCP客户端 → HTTP请求 → MCP服务器 → 飞书API → 数据库
    ↑                                                           ↓
    ←─────────────── JSON响应 ←─────────── 数据转换 ←─────────── 令牌
```

## 🛠️ 核心组件说明

### 1. **MCP客户端** (`StreamableMCPClient`)
- **作用**: 提供HTTP模式的MCP客户端功能
- **特点**: 异步HTTP连接、自动重试、错误处理
- **协议**: JSON-RPC 2.0 over HTTP

### 2. **MCP服务器** (`feishu_mcp_server_real.py`)
- **作用**: 提供MCP协议服务，集成飞书日历API
- **特点**: FastAPI框架、异步处理、工具分发
- **端点**: `/mcp/initialize`, `/mcp/tools/list`, `/mcp/tools/call`

### 3. **飞书集成** (`FeishuCalendarLark`)
- **作用**: 封装飞书日历API调用
- **特点**: OAuth认证、令牌管理、API封装
- **功能**: 日历CRUD操作

### 4. **测试脚本** (`test_calendar_view.py`)
- **作用**: 演示如何使用MCP服务
- **特点**: 完整的日历查看示例
- **用途**: 开发和测试

## 📊 工具映射关系

| MCP工具名称 | 飞书API | 功能描述 |
|------------|---------|----------|
| `list_calendars` | `GET /calendar/v4/calendar/list` | 获取用户日历列表 |
| `get_calendar_events` | `GET /calendar/v4/calendarEvent/search` | 获取日历事件 |
| `create_calendar_event` | `POST /calendar/v4/calendarEvent/create` | 创建日历事件 |
| `get_today_events` | 组合API调用 | 获取今天的事件 |

## 🔐 认证流程

```
1. 用户授权 → 2. 获取授权码 → 3. 交换访问令牌 → 4. 存储令牌 → 5. API调用
```

## 🎯 使用场景

1. **日历查看**: 通过MCP协议查看飞书日历
2. **事件管理**: 创建、查询、更新日历事件
3. **集成开发**: 其他应用通过MCP协议集成飞书日历
4. **AI助手**: 为AI助手提供日历操作能力

## 💡 技术优势

- **标准化**: 遵循MCP协议标准
- **异步**: 全异步架构，高性能
- **可扩展**: 易于添加新的工具和功能
- **安全**: OAuth认证，令牌管理
- **易用**: 简单的HTTP接口 