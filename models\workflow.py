"""
工作流相关数据模型
"""

from typing import Any, Dict, List, Optional

from .base import BaseModel


class Command(BaseModel):
    """工作流命令模型"""

    update: Optional[Dict[str, Any]] = None
    goto: Optional[str] = None


class WorkflowState(BaseModel):
    """工作流状态模型"""

    user_input: str = ""
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    processed_text: Optional[str] = None
    intent: Optional[str] = None
    confidence: Optional[float] = None
    context: Dict[str, Any] = {}
    data: Dict[str, Any] = {}
    message: Optional[str] = None
    error: Optional[str] = None


class IntentResult(BaseModel):
    """意图识别结果模型"""

    intent: str
    confidence: float
    entities: Dict[str, Any] = {}


class CalendarInfo(BaseModel):
    """日历信息提取结果"""

    action: str  # create, update, delete, query
    title: Optional[str] = None
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    start_time: Optional[str] = None
    end_time: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    attendees: List[str] = []
    reminders: List[int] = []
    is_all_day: bool = False
