# Manual Tests Directory

这个目录包含手动测试和临时测试脚本。

## 文件说明

- `test_agents_cli.py` - 代理CLI测试
- `test_api_endpoints.py` - API端点测试
- `test_confirmation_debug.py` - 确认功能调试测试
- `test_feishu_api_integration.py` - 飞书API集成测试
- `test_feishu_integration.py` - 飞书集成测试
- `test_final_integration.py` - 最终集成测试
- `test_human_collaboration.py` - 人机协作测试
- `test_intent_recognition.py` - 意图识别测试

## 使用说明

这些是开发过程中的临时测试文件，主要用于：
1. 手动验证功能
2. 调试特定问题
3. 集成测试验证

正式的测试应该使用 `tests/` 目录下的结构化测试。
