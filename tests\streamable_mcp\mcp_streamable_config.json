{"mcpServers": {"feishu-calendar-streamable": {"command": "python", "args": ["feishu_mcp_server_streamable.py"], "env": {"PYTHONPATH": "."}, "transport": {"type": "http", "url": "http://localhost:8000"}}}, "httpServer": {"host": "0.0.0.0", "port": 8000, "cors": {"allowOrigins": ["*"], "allowCredentials": true, "allowMethods": ["*"], "allowHeaders": ["*"]}}, "websocket": {"enabled": true, "path": "/ws", "maxConnections": 100}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s"}, "security": {"enableAuth": false, "apiKey": null, "rateLimit": {"enabled": false, "requestsPerMinute": 60}}, "features": {"healthCheck": true, "metrics": false, "swagger": true, "realtime": true}}