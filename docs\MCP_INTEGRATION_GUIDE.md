# 飞书MCP服务集成指南

## 📋 概述

本文档介绍如何将飞书MCP服务集成到智能日历助手项目中，实现自然语言输入到飞书API调用的无缝转换。

## 🎯 集成目标

- **自然语言处理**：用户可以用自然语言描述日历操作需求
- **智能意图识别**：系统自动识别用户意图并提取操作参数
- **MCP工具调用**：将处理结果转换为飞书MCP工具调用
- **实时操作执行**：直接操作用户的飞书日历数据

## 🏗️ 架构设计

### 整体流程
```
用户自然语言输入 → 意图识别 → 参数提取 → MCP工具调用 → 飞书API执行 → 结果反馈
```

### 核心组件

1. **FeishuMCPAdapter** (`core/mcp/feishu_mcp_adapter.py`)
   - 管理MCP服务连接
   - 提供高级API接口
   - 处理工具调用和错误

2. **Enhanced Calendar Processor** (`core/workflow/nodes.py`)
   - 集成MCP调用的日历处理器
   - 支持自然语言到操作参数的转换
   - 提供智能错误处理和用户引导

3. **MCP Integration Tests** (`tests/test_mcp_integration.py`)
   - 完整的测试覆盖
   - 模拟和真实环境测试
   - 端到端工作流验证

## 🚀 快速开始

### 1. 启动飞书MCP服务

使用提供的命令启动MCP服务：

```bash
npx -y @larksuiteoapi/lark-mcp mcp \
  -a cli_a76a68f612bf900c \
  -s EVumG3wCHsDBeJRfpbmJkfRhzCns73jC \
  --oauth \
  --token-mode user_access_token \
  -t calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.list,calendar.v4.calendarEvent.create,calendar.v4.calendarEvent.search
```

### 2. 配置环境变量

确保在 `.env` 文件中配置了正确的飞书应用信息：

```env
FEISHU_CLIENT_ID=cli_a76a68f612bf900c
FEISHU_CLIENT_SECRET=EVumG3wCHsDBeJRfpbmJkfRhzCns73jC
```

### 3. 测试MCP集成

```python
from core.mcp.feishu_mcp_adapter import test_mcp_integration
import asyncio

# 运行集成测试
asyncio.run(test_mcp_integration())
```

## 💬 使用示例

### 创建日历事件

**用户输入：**
```
"明天下午3点安排一个项目进度会议，地点在会议室A"
```

**系统处理流程：**
1. 意图识别：`calendar` (置信度: 0.95)
2. 操作提取：`create`
3. 参数解析：
   ```json
   {
     "action": "create",
     "title": "项目进度会议",
     "start_time": "2025-07-16T15:00:00",
     "end_time": "2025-07-16T16:00:00",
     "location": "会议室A"
   }
   ```
4. MCP调用：`calendar.v4.calendarEvent.create`
5. 结果反馈：`✅ 事件创建成功`

### 查询日程安排

**用户输入：**
```
"查看今天的日程安排"
```

**系统处理流程：**
1. 意图识别：`calendar` (置信度: 0.92)
2. 操作提取：`query`
3. MCP调用：`calendar.v4.calendar.list`
4. 结果展示：显示今日所有日程

### 搜索相关事件

**用户输入：**
```
"搜索本周所有项目相关的会议"
```

**系统处理流程：**
1. 意图识别：`calendar` (置信度: 0.88)
2. 操作提取：`search`
3. 关键词：`项目`
4. MCP调用：`calendar.v4.calendarEvent.search`
5. 结果展示：匹配的事件列表

## 🔧 技术实现细节

### MCP适配器核心功能

```python
class FeishuMCPAdapter:
    async def start_mcp_service(self):
        """启动MCP服务并建立连接"""
        
    async def create_calendar_event(self, event_data):
        """创建日历事件"""
        
    async def list_calendars(self):
        """获取日历列表"""
        
    async def search_calendar_events(self, query):
        """搜索日历事件"""
```

### 自然语言处理增强

```python
async def calendar_processor_node(state):
    """增强的日历处理器，支持MCP集成"""
    
    # 1. LLM处理自然语言
    # 2. 提取操作参数
    # 3. 执行MCP调用
    # 4. 返回处理结果
```

### 操作参数提取

```python
async def _extract_calendar_operation(llm_response, user_input):
    """从LLM响应和用户输入中提取操作信息"""
    
    # 支持JSON格式解析
    # 支持关键词分析
    # 返回标准化操作参数
```

## 🧪 测试策略

### 单元测试
- MCP适配器功能测试
- 操作参数提取测试
- 错误处理测试

### 集成测试
- 端到端工作流测试
- 真实MCP服务连接测试
- 多场景自然语言处理测试

### 测试运行

```bash
# 运行所有MCP相关测试
pytest tests/test_mcp_integration.py -v

# 运行集成测试（需要真实环境）
pytest tests/test_mcp_integration.py::TestMCPIntegrationE2E -v -m integration
```

## 🔍 支持的操作类型

| 操作类型 | 自然语言示例 | MCP工具 | 说明 |
|---------|-------------|---------|------|
| **创建事件** | "明天3点安排会议" | `calendarEvent.create` | 创建新的日历事件 |
| **查询日历** | "查看今天的安排" | `calendar.list` | 获取日历和事件列表 |
| **搜索事件** | "搜索项目会议" | `calendarEvent.search` | 按关键词搜索事件 |
| **更新事件** | "把会议改到4点" | `calendarEvent.update` | 修改现有事件（待实现） |
| **删除事件** | "取消明天的会议" | `calendarEvent.delete` | 删除指定事件（待实现） |

## ⚠️ 注意事项

### 权限要求
- 需要用户授权访问飞书日历
- MCP服务需要正确的OAuth配置
- 确保应用有足够的API权限

### 错误处理
- 网络连接异常
- MCP服务启动失败
- 飞书API调用限制
- 用户输入信息不完整

### 性能考虑
- MCP服务启动时间（约3秒）
- 并发请求处理
- 长时间运行的连接管理

## 🔮 未来扩展

### 计划功能
1. **智能时间解析**：支持更复杂的时间表达
2. **冲突检测**：自动检测时间冲突并提供建议
3. **批量操作**：支持批量创建、修改、删除事件
4. **智能提醒**：基于用户习惯的智能提醒设置
5. **多日历管理**：支持多个日历的统一管理

### 技术优化
1. **连接池管理**：优化MCP连接的生命周期
2. **缓存机制**：缓存常用的日历数据
3. **异步处理**：提高并发处理能力
4. **错误重试**：智能的错误重试机制

## 📚 相关文档

- [飞书开放平台文档](https://open.feishu.cn/document/)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [项目PRD文档](./PRD.md)
- [技术架构文档](./TECH_ARCHITECTURE.md)

---

*本文档将随着功能开发和用户反馈持续更新。*
