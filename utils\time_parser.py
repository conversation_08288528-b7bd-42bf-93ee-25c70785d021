"""
时间解析工具
提供自然语言时间解析功能
"""

import logging
import re
from datetime import datetime, timedelta
from typing import Any, Dict, Optional, Tuple

logger = logging.getLogger(__name__)


class TimeParser:
    """时间解析器"""

    @staticmethod
    def parse_relative_time(
        text: str, base_time: Optional[datetime] = None
    ) -> Optional[datetime]:
        """
        解析相对时间表达

        Args:
            text: 时间文本
            base_time: 基准时间，默认为当前时间

        Returns:
            解析后的时间，如果解析失败返回None
        """
        if base_time is None:
            base_time = datetime.now()

        text = text.lower().strip()

        # 今天
        if "今天" in text or "今日" in text:
            return base_time.replace(hour=0, minute=0, second=0, microsecond=0)

        # 明天
        if "明天" in text or "明日" in text:
            return base_time.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + timedelta(days=1)

        # 后天
        if "后天" in text:
            return base_time.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + timedelta(days=2)

        # 昨天
        if "昨天" in text or "昨日" in text:
            return base_time.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) - timedelta(days=1)

        # 下周
        if "下周" in text:
            days_ahead = 7 - base_time.weekday()
            return base_time.replace(
                hour=0, minute=0, second=0, microsecond=0
            ) + timedelta(days=days_ahead)

        # 下个月
        if "下个月" in text or "下月" in text:
            if base_time.month == 12:
                return base_time.replace(
                    year=base_time.year + 1,
                    month=1,
                    day=1,
                    hour=0,
                    minute=0,
                    second=0,
                    microsecond=0,
                )
            else:
                return base_time.replace(
                    month=base_time.month + 1,
                    day=1,
                    hour=0,
                    minute=0,
                    second=0,
                    microsecond=0,
                )

        return None

    @staticmethod
    def parse_time_of_day(text: str) -> Optional[Tuple[int, int]]:
        """
        解析一天中的时间

        Args:
            text: 时间文本

        Returns:
            (小时, 分钟) 元组，如果解析失败返回None
        """
        text = text.lower().strip()

        # 匹配 "下午3点"、"上午9点半"、"晚上8点30分" 等格式
        time_patterns = [
            r"(?:上午|早上|早晨)(\d{1,2})(?:点|时)(?:(\d{1,2})分?)?",
            r"(?:下午|午后)(\d{1,2})(?:点|时)(?:(\d{1,2})分?)?",
            r"(?:晚上|夜里|夜晚)(\d{1,2})(?:点|时)(?:(\d{1,2})分?)?",
            r"(\d{1,2})(?:点|时)(?:(\d{1,2})分?)?",
            r"(\d{1,2}):(\d{1,2})",
        ]

        for pattern in time_patterns:
            match = re.search(pattern, text)
            if match:
                hour = int(match.group(1))
                minute = int(match.group(2)) if match.group(2) else 0

                # 处理上午/下午
                if "下午" in text or "午后" in text:
                    if hour < 12:
                        hour += 12
                elif "晚上" in text or "夜里" in text or "夜晚" in text:
                    if hour < 12:
                        hour += 12

                # 处理"半"
                if "半" in text:
                    minute = 30

                # 验证时间有效性
                if 0 <= hour <= 23 and 0 <= minute <= 59:
                    return (hour, minute)

        return None

    @staticmethod
    def parse_duration(text: str) -> Optional[int]:
        """
        解析时长（分钟）

        Args:
            text: 时长文本

        Returns:
            时长（分钟），如果解析失败返回None
        """
        text = text.lower().strip()

        # 匹配各种时长格式
        duration_patterns = [
            r"(\d+)小时",
            r"(\d+)个小时",
            r"(\d+)分钟",
            r"(\d+)分",
            r"(\d+)h",
            r"(\d+)m",
        ]

        total_minutes = 0

        for pattern in duration_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                value = int(match)
                if "小时" in pattern or "h" in pattern:
                    total_minutes += value * 60
                else:  # 分钟
                    total_minutes += value

        return total_minutes if total_minutes > 0 else None

    @staticmethod
    def parse_recurring_pattern(text: str) -> Optional[Dict[str, Any]]:
        """
        解析重复模式

        Args:
            text: 文本内容

        Returns:
            重复模式字典，如果解析失败返回None
        """
        text = text.lower().strip()

        patterns = {
            "daily": ["每天", "每日", "天天"],
            "weekly": ["每周", "每星期", "周周"],
            "monthly": ["每月", "每个月", "月月"],
            "yearly": ["每年", "年年"],
            "weekdays": ["工作日", "平日"],
            "weekends": ["周末", "双休日"],
        }

        for pattern_type, keywords in patterns.items():
            if any(keyword in text for keyword in keywords):
                return {
                    "type": pattern_type,
                    "keywords_found": [kw for kw in keywords if kw in text],
                }

        return None

    @staticmethod
    def parse_time_range(
        text: str, base_time: Optional[datetime] = None
    ) -> Optional[Tuple[datetime, datetime]]:
        """
        解析时间范围

        Args:
            text: 时间文本
            base_time: 基准时间

        Returns:
            (开始时间, 结束时间) 元组，如果解析失败返回None
        """
        if base_time is None:
            base_time = datetime.now()

        text = text.lower().strip()

        # 匹配 "从9点到11点"、"9:00-11:00"、"上午9点到下午3点" 等格式
        range_patterns = [
            r"从\s*(\d{1,2})(?:点|时)\s*到\s*(\d{1,2})(?:点|时)",
            r"(\d{1,2}):(\d{1,2})\s*[-到]\s*(\d{1,2}):(\d{1,2})",
            r"(?:上午|早上)\s*(\d{1,2})(?:点|时)\s*到\s*(?:下午|午后)\s*(\d{1,2})(?:点|时)",
        ]

        for pattern in range_patterns:
            match = re.search(pattern, text)
            if match:
                groups = match.groups()
                if len(groups) == 2:  # 简单的小时范围
                    start_hour = int(groups[0])
                    end_hour = int(groups[1])

                    start_time = base_time.replace(
                        hour=start_hour, minute=0, second=0, microsecond=0
                    )
                    end_time = base_time.replace(
                        hour=end_hour, minute=0, second=0, microsecond=0
                    )

                    return (start_time, end_time)
                elif len(groups) == 4:  # 详细的时分范围
                    start_hour, start_min, end_hour, end_min = map(int, groups)

                    start_time = base_time.replace(
                        hour=start_hour, minute=start_min, second=0, microsecond=0
                    )
                    end_time = base_time.replace(
                        hour=end_hour, minute=end_min, second=0, microsecond=0
                    )

                    return (start_time, end_time)

        return None

    @staticmethod
    def parse_weekday(
        text: str, base_time: Optional[datetime] = None
    ) -> Optional[datetime]:
        """
        解析星期几

        Args:
            text: 文本内容
            base_time: 基准时间

        Returns:
            对应的日期时间，如果解析失败返回None
        """
        if base_time is None:
            base_time = datetime.now()

        text = text.lower().strip()

        weekdays = {
            "周一": 0,
            "星期一": 0,
            "礼拜一": 0,
            "周二": 1,
            "星期二": 1,
            "礼拜二": 1,
            "周三": 2,
            "星期三": 2,
            "礼拜三": 2,
            "周四": 3,
            "星期四": 3,
            "礼拜四": 3,
            "周五": 4,
            "星期五": 4,
            "礼拜五": 4,
            "周六": 5,
            "星期六": 5,
            "礼拜六": 5,
            "周日": 6,
            "星期日": 6,
            "礼拜日": 6,
            "周天": 6,
        }

        for weekday_name, weekday_num in weekdays.items():
            if weekday_name in text:
                # 计算到下一个该星期几的天数
                days_ahead = weekday_num - base_time.weekday()
                if days_ahead <= 0:  # 如果是今天或已过，则指向下周
                    days_ahead += 7

                target_date = base_time + timedelta(days=days_ahead)
                return target_date.replace(hour=0, minute=0, second=0, microsecond=0)

        return None
