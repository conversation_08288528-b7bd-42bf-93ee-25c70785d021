/**
 * CalMCP Dashboard
 * Next.js 主页面，显示 MCP 服务状态和工具信息
 */

'use client';

import { useState, useEffect } from 'react';
import type { MCPTool } from '@/types/mcp';

interface HealthStatus {
  status: string;
  timestamp: string;
  version: string;
  service: string;
  tools: {
    count: number;
    available: string[];
  };
  environment: {
    nodeEnv: string;
    hasFeishuConfig: boolean;
  };
}

export default function Dashboard() {
  const [health, setHealth] = useState<HealthStatus | null>(null);
  const [tools, setTools] = useState<MCPTool[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取健康状态
      const healthResponse = await fetch('/api/health');
      if (!healthResponse.ok) {
        throw new Error('Failed to fetch health status');
      }
      const healthData = await healthResponse.json();
      setHealth(healthData);

      // 获取工具列表
      const toolsResponse = await fetch('/api/mcp/tools');
      if (!toolsResponse.ok) {
        throw new Error('Failed to fetch tools');
      }
      const toolsData = await toolsResponse.json();
      setTools(toolsData.data.tools);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading CalMCP Dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-xl mb-4">❌ Error</div>
          <p className="text-gray-600">{error}</p>
          <button
            onClick={fetchData}
            className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">CalMCP Dashboard</h1>
          <p className="text-gray-600 mb-4">Feishu Calendar MCP Service with Streamable Support</p>

          {/* Quick Actions */}
          <div className="flex justify-center space-x-4 flex-wrap gap-2">
            <a
              href="/auth"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              🔑 获取用户令牌
            </a>
            <a
              href="/tools"
              className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
            >
              🛠️ 查看所有工具
            </a>
            <a
              href="/api/mcp/tools"
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
            >
              🔧 查看 API
            </a>
          </div>
        </div>

        {/* Status Card */}
        {health && (
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-2xl font-semibold text-gray-900">Service Status</h2>
              <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                health.status === 'ok' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-red-100 text-red-800'
              }`}>
                {health.status === 'ok' ? '🟢 Online' : '🔴 Offline'}
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500">Version</p>
                <p className="text-lg font-medium">{health.version}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Environment</p>
                <p className="text-lg font-medium">{health.environment.nodeEnv}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Feishu Config</p>
                <p className="text-lg font-medium">
                  {health.environment.hasFeishuConfig ? '✅ Configured' : '❌ Missing'}
                </p>
              </div>
            </div>
            
            <div className="mt-4">
              <p className="text-sm text-gray-500">Last Updated</p>
              <p className="text-lg font-medium">{new Date(health.timestamp).toLocaleString()}</p>
            </div>
          </div>
        )}

        {/* Tools Grid */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-semibold text-gray-900">Available Tools</h2>
            <span className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
              {tools.length} tools
            </span>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tools.map((tool) => (
              <div key={tool.name} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <h3 className="text-lg font-medium text-gray-900 mb-2">{tool.name}</h3>
                <p className="text-gray-600 text-sm mb-3">{tool.description}</p>
                
                <div className="text-xs text-gray-500">
                  <p className="mb-1">Required fields:</p>
                  <div className="flex flex-wrap gap-1">
                    {tool.inputSchema.required?.map((field) => (
                      <span key={field} className="bg-gray-100 px-2 py-1 rounded">
                        {field}
                      </span>
                    )) || <span className="text-gray-400">None</span>}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* API Endpoints */}
        <div className="bg-white rounded-lg shadow-md p-6 mt-8">
          <h2 className="text-2xl font-semibold text-gray-900 mb-6">API Endpoints</h2>
          
          <div className="space-y-4">
            <div className="border-l-4 border-green-500 pl-4">
              <h3 className="font-medium text-gray-900">GET /api/health</h3>
              <p className="text-gray-600 text-sm">Service health check</p>
            </div>
            
            <div className="border-l-4 border-blue-500 pl-4">
              <h3 className="font-medium text-gray-900">GET /api/mcp/tools</h3>
              <p className="text-gray-600 text-sm">List all available MCP tools</p>
            </div>
            
            <div className="border-l-4 border-purple-500 pl-4">
              <h3 className="font-medium text-gray-900">POST /api/mcp/tools/call</h3>
              <p className="text-gray-600 text-sm">Call MCP tool (standard mode)</p>
            </div>
            
            <div className="border-l-4 border-orange-500 pl-4">
              <h3 className="font-medium text-gray-900">POST /api/mcp/tools/stream</h3>
              <p className="text-gray-600 text-sm">Call MCP tool (streamable mode)</p>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8 text-gray-500 text-sm">
          <p>CalMCP - Feishu Calendar MCP Service</p>
          <p>Built with Next.js and TypeScript</p>
        </div>
      </div>
    </div>
  );
}
