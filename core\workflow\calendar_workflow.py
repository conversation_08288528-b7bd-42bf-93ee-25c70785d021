"""
智能日历助手多代理工作流引擎
基于PDeerFlow设计思路，集成飞书API
"""

import logging
from datetime import datetime
from typing import Any, Dict, Optional

from models.conversation import ConversationContext

from ..agents import CoordinatorAgent, ExecutorAgent, PlannerAgent
from ..agents.base import AgentState, Command

logger = logging.getLogger(__name__)


class CalendarWorkflowEngine:
    """
    智能日历助手工作流引擎

    负责协调多个代理的工作流程：
    1. CoordinatorAgent: 意图识别和路由
    2. PlannerAgent: 任务规划和实体提取
    3. ExecutorAgent: 飞书API调用和执行
    """

    def __init__(self):
        """初始化工作流引擎"""
        self.coordinator = CoordinatorAgent()
        self.planner = PlannerAgent()
        self.executor = ExecutorAgent()

        # 定义工作流图
        self.workflow_graph = {
            "coordinator": ["planner", "chat_handler", "__end__"],
            "planner": ["executor", "confirmation", "__end__"],
            "executor": ["__end__"],
            "confirmation": ["planner", "__end__"],
            "chat_handler": ["__end__"],
        }

    async def run_workflow(
        self, user_input: str, user_id: str, context: ConversationContext
    ) -> Dict[str, Any]:
        """
        运行多代理工作流

        Args:
            user_input: 用户输入
            user_id: 用户ID
            context: 会话上下文

        Returns:
            工作流执行结果
        """
        try:
            # 初始化代理状态
            state = AgentState(
                user_input=user_input,
                processed_text=user_input,
                user_id=user_id,
                session_id=context.session_id,
                messages=[{"role": "user", "content": user_input}],
                context={"conversation_context": context},
            )

            # 开始工作流执行
            current_node = "coordinator"
            max_iterations = 10  # 防止无限循环
            iteration = 0

            while current_node != "__end__" and iteration < max_iterations:
                iteration += 1
                logger.info(f"工作流第{iteration}步: 执行节点 {current_node}")

                # 执行当前节点
                command = await self._execute_node(current_node, state)

                # 更新状态
                if command.update:
                    for key, value in command.update.items():
                        setattr(state, key, value)

                # 确定下一个节点
                current_node = command.goto or "__end__"

                logger.info(f"下一个节点: {current_node}")

            # 提取最终结果
            final_message = self._extract_final_message(state)

            return {
                "success": True,
                "message": final_message,
                "intent": state.intent or "calendar",
                "confidence": state.confidence or 0.8,
                "data": {
                    "last_operation": state.last_operation,
                    "execution_result": state.execution_result,
                },
                "state": state,
            }

        except Exception as e:
            logger.error(f"工作流执行失败: {e}")
            return {
                "success": False,
                "message": f"处理请求时发生错误：{str(e)}",
                "intent": "error",
                "confidence": 0.0,
            }

    async def _execute_node(self, node_name: str, state: AgentState) -> Command:
        """
        执行指定节点

        Args:
            node_name: 节点名称
            state: 当前状态

        Returns:
            执行命令
        """
        if node_name == "coordinator":
            return await self.coordinator.process(state)
        elif node_name == "planner":
            return await self.planner.process(state)
        elif node_name == "executor":
            return await self.executor.process(state)
        elif node_name == "chat_handler":
            return await self._handle_chat(state)
        elif node_name == "confirmation":
            return await self._handle_confirmation(state)
        else:
            logger.warning(f"未知节点: {node_name}")
            return Command(goto="__end__")

    async def _handle_chat(self, state: AgentState) -> Command:
        """
        处理聊天意图

        Args:
            state: 当前状态

        Returns:
            执行命令
        """
        try:
            # 简单的聊天响应逻辑
            user_input = state.user_input

            if "你好" in user_input or "hello" in user_input.lower():
                response = "你好！我是您的智能日历助手，可以帮您管理日程、安排会议等。有什么我可以帮您的吗？"
            elif "功能" in user_input or "能做什么" in user_input:
                response = """我可以帮您：
• 📅 创建日历事件（如"明天下午3点安排会议"）
• 🔍 查询日程安排（如"查看今天的日程"）
• ✏️ 修改已有事件
• 🗑️ 删除不需要的事件

请用自然语言告诉我您的需求！"""
            else:
                response = '我是您的智能日历助手。如果您需要管理日程，请直接告诉我，比如"明天下午3点安排会议"。'

            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": response,
                            "name": "chat_handler",
                        }
                    ]
                },
                goto="__end__",
            )

        except Exception as e:
            logger.error(f"处理聊天失败: {e}")
            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "抱歉，我遇到了一些问题。请稍后再试。",
                            "name": "chat_handler",
                        }
                    ]
                },
                goto="__end__",
            )

    async def _handle_confirmation(self, state: AgentState) -> Command:
        """
        处理确认流程

        Args:
            state: 当前状态

        Returns:
            执行命令
        """
        try:
            # 这里应该集成确认服务的逻辑
            # 暂时返回到规划器重新处理
            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "请确认您的操作...",
                            "name": "confirmation",
                        }
                    ]
                },
                goto="planner",
            )

        except Exception as e:
            logger.error(f"处理确认失败: {e}")
            return Command(goto="__end__")

    def _extract_final_message(self, state: AgentState) -> str:
        """
        提取最终响应消息

        Args:
            state: 最终状态

        Returns:
            最终消息
        """
        if state.messages:
            # 获取最后一条助手消息
            for message in reversed(state.messages):
                if message.get("role") == "assistant":
                    return message.get("content", "操作完成")

        return "操作完成"

    def get_workflow_status(self, state: AgentState) -> Dict[str, Any]:
        """
        获取工作流状态信息

        Args:
            state: 当前状态

        Returns:
            状态信息
        """
        return {
            "intent": state.intent,
            "confidence": state.confidence,
            "last_operation": state.last_operation,
            "pending_confirmation": state.pending_confirmation,
            "execution_result": state.execution_result,
            "message_count": len(state.messages),
        }
