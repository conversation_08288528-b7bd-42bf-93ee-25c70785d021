# Feishu App Configuration
FEISHU_APP_ID=cli_a76a68f612bf900c
FEISHU_APP_SECRET=EVumG3wCHsDBeJRfpbmJkfRhzCns73jC

# Feishu User Access Token (required for personal calendar access)
FEISHU_USER_ACCESS_TOKEN=u-your_user_access_token_here

# MCP Server Configuration
MCP_SERVER_PORT=3002
MCP_SERVER_HOST=localhost

# Next.js Configuration
NEXT_PUBLIC_API_URL=http://localhost:3000
NODE_ENV=development

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=./logs/mcp-server.log

# Security Configuration
JWT_SECRET=your_jwt_secret_here
API_KEY=your_api_key_here

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Streamable Configuration
STREAM_TIMEOUT_MS=30000
STREAM_BUFFER_SIZE=1024
