# 飞书MCP服务器使用指南

## 概述

本项目提供了一个基于现有飞书日历API的MCP (Model Context Protocol) 服务器，使用streamable模式。该服务器可以让AI助手通过MCP协议直接操作飞书日历。

## 特性

- ✅ **Streamable模式**: 支持MCP协议的stdio传输模式
- ✅ **完整的日历操作**: 支持日历列表、事件CRUD操作
- ✅ **基于现有API**: 复用项目中已有的飞书日历API集成
- ✅ **类型安全**: 使用Pydantic模型确保数据类型安全
- ✅ **错误处理**: 完善的错误处理和日志记录

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

在 `.env` 文件中设置：

```env
FEISHU_CLIENT_ID=cli_a76a68f612bf900c
FEISHU_CLIENT_SECRET=EVumG3wCHsDBeJRfpbmJkfRhzCns73jC
```

### 3. 启动服务器

#### 方式一：使用批处理文件 (Windows)

```cmd
start_feishu_mcp.bat
```

#### 方式二：使用PowerShell脚本

```powershell
.\start_feishu_mcp.ps1
```

#### 方式三：直接运行Python脚本

```bash
python start_feishu_mcp_server.py
```

## MCP客户端配置

### Claude Desktop配置

在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "feishu-calendar": {
      "command": "python",
      "args": ["D:/Code/feishu-coze-plugin/start_feishu_mcp_server.py"],
      "cwd": "D:/Code/feishu-coze-plugin",
      "env": {
        "FEISHU_CLIENT_ID": "cli_a76a68f612bf900c",
        "FEISHU_CLIENT_SECRET": "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC"
      }
    }
  }
}
```

### 其他MCP客户端

对于其他支持MCP协议的客户端，使用以下配置：

- **传输模式**: stdio
- **命令**: `python start_feishu_mcp_server.py`
- **工作目录**: 项目根目录

## 可用工具

### 1. list_calendars
获取用户的日历列表

### 2. get_calendar_events
获取指定日历的事件列表

### 3. create_calendar_event
创建新的日历事件

### 4. update_calendar_event
更新现有的日历事件

### 5. delete_calendar_event
删除日历事件

### 6. get_today_events
获取今天的所有事件

详细的工具参数和响应格式请参考 [FEISHU_MCP_TOOLS.md](./FEISHU_MCP_TOOLS.md)

## 使用示例

### 在Claude中使用

启动服务器后，在Claude中可以这样使用：

```
请帮我查看今天的日程安排
```

```
请帮我创建一个明天下午2点的会议，标题是"项目评审"，地点在会议室A
```

```
请帮我获取所有日历列表
```

### 通过MCP客户端直接调用

```json
{
  "jsonrpc": "2.0",
  "id": 1,
  "method": "tools/call",
  "params": {
    "name": "get_today_events",
    "arguments": {
      "user_id": "user123"
    }
  }
}
```

## 故障排除

### 常见问题

1. **服务器启动失败**
   - 检查Python环境是否正确
   - 确认已安装所有依赖：`pip install -r requirements.txt`
   - 检查环境变量是否正确设置

2. **MCP连接失败**
   - 确认服务器正在运行
   - 检查客户端配置中的路径是否正确
   - 查看服务器日志输出

3. **飞书API调用失败**
   - 检查飞书应用配置
   - 确认用户已授权
   - 检查网络连接

### 日志查看

服务器会输出详细的日志信息到stderr，包括：
- 工具调用记录
- 错误信息
- 调试信息

### 调试模式

设置环境变量启用调试模式：

```bash
export PYTHONPATH=.
export LOG_LEVEL=DEBUG
python start_feishu_mcp_server.py
```

## 开发指南

### 添加新工具

1. 在 `FeishuMCPServer._setup_tools()` 中添加工具定义
2. 实现对应的处理方法 `_handle_xxx()`
3. 更新工具文档

### 修改现有工具

1. 修改工具的 `inputSchema` 定义
2. 更新处理方法的逻辑
3. 测试工具功能

### 测试

```bash
# 运行单元测试
pytest tests/test_mcp_integration.py

# 手动测试工具
python -c "
import asyncio
from core.mcp.feishu_mcp_server import FeishuMCPServer
server = FeishuMCPServer()
# 测试代码
"
```

## 架构说明

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   MCP Client    │◄──►│  MCP Server      │◄──►│  Feishu API     │
│   (Claude等)    │    │  (本项目)        │    │  (现有集成)     │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

- **MCP Client**: 支持MCP协议的AI助手或应用
- **MCP Server**: 本项目实现的服务器，转换MCP调用为飞书API调用
- **Feishu API**: 项目中已有的飞书日历API集成

## 相关文档

- [MCP协议规范](https://modelcontextprotocol.io/)
- [飞书开放平台文档](https://open.feishu.cn/document/)
- [项目API文档](./API.md)
- [工具定义文档](./FEISHU_MCP_TOOLS.md)
