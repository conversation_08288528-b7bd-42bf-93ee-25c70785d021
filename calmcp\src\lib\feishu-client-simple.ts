/**
 * Simplified Feishu API Client
 * 基于官方 @larksuiteoapi/node-sdk 的简化版本
 */

import * as lark from '@larksuiteoapi/node-sdk';

export interface SimpleApiResponse {
  code: number;
  msg: string;
  data: any;
}

export class SimpleFeishuClient {
  private client: lark.Client;
  private userAccessToken?: string;

  constructor(appId: string, appSecret: string, userAccessToken?: string) {
    this.client = new lark.Client({
      appId,
      appSecret,
      appType: lark.AppType.SelfBuild,
      domain: lark.Domain.Feishu
    });

    // 从环境变量获取用户访问令牌
    this.userAccessToken = userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN;

    console.log('Feishu client initialized:', {
      appId,
      hasAppSecret: !!appSecret,
      hasUserToken: !!this.userAccessToken,
      userTokenPrefix: this.userAccessToken ? this.userAccessToken.substring(0, 10) + '...' : 'none'
    });
  }

  setUserAccessToken(token: string) {
    this.userAccessToken = token;
  }

  /**
   * 获取日历列表
   */
  async getCalendars(pageSize: number = 50, pageToken?: string): Promise<SimpleApiResponse> {
    try {
      // 构建请求选项，确保 page_size 至少为 50
      const validPageSize = Math.max(pageSize, 50);
      const requestOptions: any = {
        params: {
          page_size: validPageSize
        }
      };

      // 如果有分页令牌，添加它
      if (pageToken) {
        requestOptions.params.page_token = pageToken;
      }

      console.log('Calendar list request options:', requestOptions);

      let response;
      if (this.userAccessToken) {
        console.log('Using user access token:', this.userAccessToken.substring(0, 10) + '...');
        // 使用官方推荐的方式传递用户访问令牌
        response = await this.client.calendar.v4.calendar.list(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        console.log('No user access token, using app access token');
        response = await this.client.calendar.v4.calendar.list(requestOptions);
      }

      console.log('Calendar list response:', response);

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error: any) {
      console.error('获取日历列表失败:', error);

      // 提取更详细的错误信息
      let errorMsg = `获取日历列表失败: ${error.message || error}`;
      if (error.response?.data) {
        errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;
      }

      return {
        code: -1,
        msg: errorMsg,
        data: null
      };
    }
  }

  /**
   * 创建日历事件
   */
  async createEvent(calendarId: string, eventData: any): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId
        },
        data: eventData
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.create(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.create(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('创建日历事件失败:', error);
      return {
        code: -1,
        msg: `创建日历事件失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 搜索日历事件
   */
  async searchEvents(calendarId: string, searchParams: any): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId
        },
        data: {
          query: searchParams.query || '',
          filter: {
            start_time: searchParams.start_time,
            end_time: searchParams.end_time
          }
        }
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.search(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.search(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('搜索日历事件失败:', error);
      return {
        code: -1,
        msg: `搜索日历事件失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 更新日历事件
   */
  async updateEvent(calendarId: string, eventId: string, updateData: any): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId,
          event_id: eventId
        },
        data: updateData
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.patch(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.patch(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('更新日历事件失败:', error);
      return {
        code: -1,
        msg: `更新日历事件失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 删除日历事件
   */
  async deleteEvent(calendarId: string, eventId: string): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId,
          event_id: eventId
        }
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.delete(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.delete(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data || {}
      };
    } catch (error) {
      console.error('删除日历事件失败:', error);
      return {
        code: -1,
        msg: `删除日历事件失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 获取单个日历事件详情
   */
  async getEvent(calendarId: string, eventId: string): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId,
          event_id: eventId
        }
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.get(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.get(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('获取日历事件详情失败:', error);
      return {
        code: -1,
        msg: `获取日历事件详情失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 获取日历事件列表
   */
  async getEvents(calendarId: string, params: any = {}): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId
        },
        params
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendarEvent.list(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendarEvent.list(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('获取日历事件列表失败:', error);
      return {
        code: -1,
        msg: `获取日历事件列表失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 通用 API 调用方法
   */
  async callApi(method: string, params: any = {}): Promise<SimpleApiResponse> {
    try {
      console.log(`🔧 调用 API 方法: ${method}`, params);

      // 支持简化工具名称
      switch (method) {
        case 'calendar_list':
          return await this.getCalendars(params.page_size, params.page_token);

        case 'calendar_event_create':
          return await this.createEvent(params.calendar_id, params);

        case 'calendar_event_search':
          return await this.searchEvents(params.calendar_id, params);

        case 'calendar_event_update':
          return await this.updateEvent(params.calendar_id, params.event_id, params);

        case 'calendar_event_delete':
          return await this.deleteEvent(params.calendar_id, params.event_id);

        case 'calendar_event_get':
          return await this.getEvent(params.calendar_id, params.event_id);

        case 'calendar_event_list':
          return await this.getEvents(params.calendar_id, params);
      }

      // 支持官方工具名称（calendar.v4.xxx）
      switch (method) {
        case 'calendar.v4.calendar.list':
          return await this.getCalendars(params.page_size, params.page_token);

        case 'calendar.v4.calendar.get':
          return await this.getCalendar(params.calendar_id);

        case 'calendar.v4.calendar.delete':
          return await this.deleteCalendar(params.calendar_id);

        case 'calendar.v4.calendarEvent.list':
          return await this.getEvents(params.calendar_id, params);

        case 'calendar.v4.calendarEvent.create':
          return await this.createEvent(params.calendar_id, params);

        case 'calendar.v4.calendarEvent.search':
          return await this.searchEvents(params.calendar_id, params);

        default:
          console.warn(`⚠️  未知的 API 方法: ${method}`);
          return {
            code: -1,
            msg: `Unknown method: ${method}`,
            data: null
          };
      }
    } catch (error) {
      console.error(`❌ API 调用失败 (${method}):`, error);
      return {
        code: -1,
        msg: `API 调用失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 获取单个日历信息
   */
  async getCalendar(calendarId: string): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId
        }
      };

      let response;
      if (this.userAccessToken) {
        response = await this.client.calendar.v4.calendar.get(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        response = await this.client.calendar.v4.calendar.get(requestOptions);
      }

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };
    } catch (error) {
      console.error('获取日历信息失败:', error);
      return {
        code: -1,
        msg: `获取日历信息失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 删除日历
   */
  async deleteCalendar(calendarId: string): Promise<SimpleApiResponse> {
    try {
      const requestOptions = {
        path: {
          calendar_id: calendarId
        }
      };

      console.log('删除日历请求:', { calendarId, requestOptions });

      let response;
      if (this.userAccessToken) {
        console.log('使用用户访问令牌删除日历:', this.userAccessToken.substring(0, 10) + '...');
        response = await this.client.calendar.v4.calendar.delete(
          requestOptions,
          lark.withUserAccessToken(this.userAccessToken)
        );
      } else {
        console.log('使用应用访问令牌删除日历');
        response = await this.client.calendar.v4.calendar.delete(requestOptions);
      }

      console.log('删除日历响应:', response);

      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data || {}
      };
    } catch (error: any) {
      console.error('删除日历失败:', error);

      // 提取更详细的错误信息
      let errorMsg = `删除日历失败: ${error.message || error}`;
      if (error.response?.data) {
        errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;
      }

      return {
        code: -1,
        msg: errorMsg,
        data: null
      };
    }
  }
}
