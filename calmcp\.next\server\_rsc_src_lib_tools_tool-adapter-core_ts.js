"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_tools_tool-adapter-core_ts";
exports.ids = ["_rsc_src_lib_tools_tool-adapter-core_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/tools/tool-adapter-core.ts":
/*!********************************************!*\
  !*** ./src/lib/tools/tool-adapter-core.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器 - 核心版本\n * 只保留项目必需的9个核心日历工具\n */ /**\n * 核心日历工具集 - 7个精选工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    // 📝 日程管理 (核心功能)\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"查看日程视图(含重复日程展开)\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间，Unix 时间戳，单位为秒\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间，Unix 时间戳，单位为秒\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取单个日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则，RRULE 格式\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与者权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    }\n];\n/**\n * 导出核心工具集 (兼容原有接口)\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return CORE_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter-core.ts\n");

/***/ })

};
;