"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_tools_tool-adapter-core_ts";
exports.ids = ["_rsc_src_lib_tools_tool-adapter-core_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/tools/tool-adapter-core.ts":
/*!********************************************!*\
  !*** ./src/lib/tools/tool-adapter-core.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器 - 核心版本\n * 只保留项目必需的9个核心日历工具\n */ /**\n * 核心日历工具集 - 7个精选工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情。注意：必须先调用calendar.list获取日历ID，不能直接使用日历名称\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    // 📝 日程管理 (核心功能)\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"查看指定日历中指定时间范围的日程视图(含重复日程展开)。注意：必须先调用calendar.list获取日历ID\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间，Unix 时间戳，单位为秒\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间，Unix 时间戳，单位为秒\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取单个日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则，RRULE 格式\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与者权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    }\n];\n/**\n * 导出核心工具集 (兼容原有接口)\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return CORE_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter-core.ts\n");

/***/ })

};
;