```markdown
# 智能秘书系统完整技术方案（Cursor / Augment 版）
> **一句话定位**：一句话并行完成**闲聊、日历、任务、文案、财经**五大场景；  
> **技术亮点**：**Cursor / Augment 全链路提示**，从目录到代码片段皆可一键生成。

---

## 目录
1. 业务整体架构  
2. 技术整体架构  
3. 双层标签规范（domain & shape）  
4. Cursor / Augment 构建清单  
5. 核心代码（含 LLM → MCP 智能编排）  
6. 运行、测试、回退  
7. 版本记录  

---

## 1. 业务整体架构
| 场景 | 一级域 | 示例用户输入 | 系统输出 |
|---|---|---|---|
| 闲聊 | chat | “讲个笑话” | 笑话文本 |
| 日历 | calendar | “下周三提醒我体检” | 日程卡片 |
| 任务 | task | “明天下午写完产品方案” | 待办卡片 |
| 文案 | copy | “写小红书推广文案” | 文案正文 |
| 财经 | finance | “今天 A 股怎么样” | 日报正文 |

### 1.1 多意图并发流程
```
用户一句话
   ↓
多意图提取器（LLM Function Calling）
   ↓
调度器 Dispatcher（asyncio.gather）
   ├── 子图1 → SubResult
   ├── 子图2 → SubResult
   └── ...
   ↓
结果聚合器 → 一次性回包
```

---

## 2. 技术整体架构
```
┌────────────────────────┐
│  Client (Web/WeChat)   │  HTTP/WebSocket
└──────────┬─────────────┘
           │
┌──────────┴─────────────┐
│  FastAPI Gateway       │  session + auth
└──────────┬─────────────┘
           │
┌──────────┴─────────────┐
│  Orchestrator          │  LangGraph（外层）
│  ├─ extract_intents    │
│  ├─ dispatch           │
│  └─ summarize          │
└──────────┬─────────────┘
           │ MCP over HTTP
┌──────────┴─────────────┐
│  Next.js MCP Server    │
│  ├─ /mcp/tools         │
│  └─ /mcp/call          │
└────────────────────────┘
```

---

## 3. 双层标签规范
| 维度 | 可枚举值 | 示例 |
|---|---|---|
| **domain** | finance, calendar, task, copy, chat, personal, gov | 国标一级分类 |
| **shape**  | info, task, event, form, poll, live | 前端交互范式 |

---

## 4. Cursor / Augment 构建清单
| 步骤 | 提示（直接复制到 Cursor 聊天框） | 生成文件 |
|---|---|---|
| 1 | `新建 FastAPI 项目，根目录 secretay，含 main.py、requirements.txt，依赖 langgraph/langchain_openai/httpx/pydantic` | `main.py, requirements.txt` |
| 2 | `在 secretay/config 目录创建 intent_schema.py，定义 Intent, DOMAINS, SHAPES` | `secretay/config/intent_schema.py` |
| 3 | `在 secretay/mcp_client.py 创建 McpClient 类，支持 preload & call` | `secretay/mcp_client.py` |
| 4 | `在 secretay/orchestrator/nodes.py 实现 extract_intents, dispatch, summarize 节点` | `secretay/orchestrator/nodes.py` |
| 5 | `在 secretay/orchestrator/graph.py 实现外层 LangGraph` | `secretay/orchestrator/graph.py` |
| 6 | `在 secretay/subgraphs/calendar/graph.py 创建 LangGraph 子图，含 parse_time_node & create_event_node，使用 McpClient` | `secretay/subgraphs/calendar/graph.py` |
| 7 | `在 secretay/subgraphs/*/graph.py 依次创建 task、finance、copy、chat 子图` | 4 个文件 |
| 8 | `在根目录创建 .cursorrules，内容见下方` | `.cursorrules` |

---

## 5. 核心代码（可直接拷）

### 5.1 `.cursorrules`（Cursor 专用）
```
# 项目规则
- 所有代码使用 Python 3.11
- 统一异步 async/await
- LLM Function Calling 统一使用 langchain_openai.ChatOpenAI
- 所有子图返回 SubResult 结构
- MCP Server 端口默认 3000
```

### 5.2 双层标签模型
```python
# config/intent_schema.py
from pydantic import BaseModel, Field
from typing import Literal

DOMAINS = Literal["finance","calendar","task","copy","chat","personal","gov"]
SHAPES  = Literal["info","task","event","form","poll","live"]

class Intent(BaseModel):
    domain: DOMAINS
    shape:  SHAPES
    content: str
    metadata: dict = {}
```

### 5.3 MCP 客户端
```python
# mcp_client.py
import httpx

class McpClient:
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base = base_url.rstrip("/")
        self._tools = None

    async def preload(self):
        if self._tools is None:
            self._tools = await self.list_tools()
        return self._tools

    async def list_tools(self):
        async with httpx.AsyncClient(timeout=10) as c:
            return (await c.get(f"{self.base}/mcp/tools")).json()

    async def call(self, tool: str, arguments: dict):
        async with httpx.AsyncClient(timeout=30) as c:
            return (await c.post(
                f"{self.base}/mcp/call",
                json={"tool": tool, "arguments": arguments}
            )).json()
```

### 5.4 日历子图（LLM 自动选工具示例）
```python
# subgraphs/calendar/graph.py
from langchain_openai import ChatOpenAI
from langchain.agents import create_openai_tools_agent, AgentExecutor
from langchain.prompts import ChatPromptTemplate
from mcp_client import McpClient

mcp = McpClient()

class ExtractEvent(BaseModel):
    title: str
    start: str
    end: str

async def preload_tools():
    tools = await mcp.preload()
    return [convert_mcp_schema_to_langchain(t) for t in tools]

prompt = ChatPromptTemplate.from_messages([
    ("system", "你是日历助手，请使用工具完成用户请求"),
    ("human", "{content}")
])

async def calendar_subgraph(state: dict):
    tools = await preload_tools()
    agent = create_openai_tools_agent(
        llm=ChatOpenAI(model="gpt-4o-mini"),
        tools=tools,
        prompt=prompt
    )
    executor = AgentExecutor(agent=agent, tools=tools, verbose=True)
    result = await executor.ainvoke({"content": state["content"]})
    return {
        "domain": "calendar",
        "shape": "event",
        "status": "ok",
        "data": result["output"],
        "ui_hint": "日程已创建"
    }
```

### 5.5 外层 Orchestrator
```python
# orchestrator/graph.py
from langgraph.graph import StateGraph, START, END
from .nodes import extract_intents, dispatch, summarize

class State(TypedDict):
    query: str
    intents: list
    results: list
    answer: str

workflow = StateGraph(State)
workflow.add_node("extract", extract_intents)
workflow.add_node("dispatch", dispatch)
workflow.add_node("summarize", summarize)
workflow.add_edge(START, "extract")
workflow.add_edge("extract", "dispatch")
workflow.add_edge("dispatch", "summarize")
workflow.add_edge("summarize", END)

app = workflow.compile()
```

### 5.6 FastAPI 入口
```python
# main.py
from fastapi import FastAPI
from orchestrator.graph import app as orc_app

api = FastAPI(title="Secretary")

@api.post("/chat")
async def chat(query: str):
    final = await orc_app.ainvoke({"query": query})
    return {"reply": final["answer"]}
```

---

## 6. 运行、测试、回退
```bash
# 1. MCP Server
cd mcp-server && npm run dev

# 2. 秘书服务
pip install -r requirements.txt
uvicorn main:api --reload --port 8000

# 3. 单测
curl -X POST http://localhost:8000/chat \
  -H "Content-Type: application/json" \
  -d '{"query":"明天下午写完产品方案，后天9点发布会，再给我今天财经日报"}'
```

---

## 7. 版本记录
| 版本 | 日期 | 说明 |
|---|---|---|
| v1.0 | 2024-07-21 | 首版，含 Cursor/Augment 全链路提示 |
```