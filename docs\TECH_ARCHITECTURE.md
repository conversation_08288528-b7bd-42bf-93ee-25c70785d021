# 智能日历助手 - 技术架构文档

## 🏗️ 系统架构概览

### 整体架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    用户交互层                                │
├─────────────────┬─────────────────┬─────────────────────────┤
│   CLI Interface │   Web Interface │   API Interface         │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────────┐
│                    服务层 (Services)                        │
├─────────────────┬─────────────────┬─────────────────────────┤
│  ChatService    │ConfirmationSvc  │   CalendarService       │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────────┐
│                 核心引擎层 (Core Engine)                     │
├─────────────────┬─────────────────┬─────────────────────────┤
│  Multi-Agent    │  Conversation   │   Time Parser           │
│   System        │   Manager       │   Engine                │
└─────────────────┴─────────────────┴─────────────────────────┘
                           │
┌─────────────────────────────────────────────────────────────┐
│                   AI & 工具层                               │
├─────────────────┬─────────────────┬─────────────────────────┤
│   LLM Client    │   Tool Binding  │   External APIs         │
│  (OpenAI/Claude)│    System       │   (Feishu/Calendar)     │
└─────────────────┴─────────────────┴─────────────────────────┘
```

## 🤖 多代理系统架构

### 代理协作流程
```
用户输入 → CoordinatorAgent → PlannerAgent → ExecutorAgent → 结果输出
    ↑                                                           │
    └─────────────── 人机协作反馈循环 ←─────────────────────────┘
```

### 代理职责分工

#### 1. CoordinatorAgent (协调器)
```python
class CoordinatorAgent:
    """
    职责：
    - 意图识别和分类
    - 路由决策
    - 上下文管理
    """
    
    async def process(self, state: AgentState) -> AgentCommand:
        # 1. 意图识别
        intent = await self.classify_intent(state.user_input)
        
        # 2. 路由决策
        if intent == "calendar":
            return AgentCommand(goto="planner")
        elif intent == "chat":
            return AgentCommand(goto="chat_handler")
        
        # 3. 更新状态
        state.intent = intent
        return AgentCommand(update={"intent": intent})
```

#### 2. PlannerAgent (规划器)
```python
class PlannerAgent:
    """
    职责：
    - 任务规划
    - 实体提取
    - 确认机制
    """
    
    async def process(self, state: AgentState) -> AgentCommand:
        # 1. 实体提取
        entities = await self.extract_entities(state.user_input)
        
        # 2. 生成计划
        plan = await self.generate_plan(entities)
        
        # 3. 确认检查
        if self.needs_confirmation(plan):
            return AgentCommand(
                goto="confirmation",
                update={"pending_plan": plan}
            )
        
        return AgentCommand(goto="executor")
```

#### 3. ExecutorAgent (执行器)
```python
class ExecutorAgent:
    """
    职责：
    - 具体操作执行
    - API调用
    - 结果处理
    """
    
    async def process(self, state: AgentState) -> AgentCommand:
        # 1. 执行操作
        result = await self.execute_operation(state.calendar_plan)
        
        # 2. 处理结果
        if result.success:
            return AgentCommand(
                goto="END",
                update={"result": result}
            )
        
        return AgentCommand(goto="error_handler")
```

## 💬 会话管理系统

### 状态机设计
```
┌─────────────┐    用户输入    ┌──────────────────┐
│   NORMAL    │ ──────────→   │ WAITING_CONFIRM  │
│             │               │                  │
└─────────────┘               └──────────────────┘
       ↑                              │
       │ 确认/取消                     │ 修改建议
       │                              ↓
┌─────────────┐               ┌──────────────────┐
│    END      │               │ WAITING_SUPPLEMENT│
│             │               │                  │
└─────────────┘               └──────────────────┘
```

### 会话上下文模型
```python
class ConversationContext(BaseModel):
    # 基本信息
    user_id: str
    session_id: str
    state: ConversationState
    
    # 意图上下文
    current_intent: Optional[str]
    intent_confidence: Optional[float]
    original_request: Optional[str]
    
    # 待处理操作
    pending_operation: Optional[Dict[str, Any]]
    extracted_entities: Optional[Dict[str, Any]]
    
    # 确认相关
    confirmation_message: Optional[str]
    confirmation_data: Optional[Dict[str, Any]]
    
    # 对话历史
    message_history: List[Dict[str, Any]]
```

## 🕐 时间解析引擎

### 解析器架构
```python
class TimeParser:
    """
    多层次时间解析系统
    """
    
    def parse(self, text: str) -> TimeExtraction:
        # 1. 相对时间解析
        relative = self.parse_relative_time(text)
        
        # 2. 具体时间解析
        specific = self.parse_time_of_day(text)
        
        # 3. 重复模式解析
        recurring = self.parse_recurring_pattern(text)
        
        # 4. 时间范围解析
        range_time = self.parse_time_range(text)
        
        return TimeExtraction(
            relative_time=relative,
            specific_time=specific,
            recurring_pattern=recurring,
            time_range=range_time
        )
```

### 支持的时间表达
```python
# 相对时间
RELATIVE_PATTERNS = {
    "今天": timedelta(days=0),
    "明天": timedelta(days=1),
    "后天": timedelta(days=2),
    "下周": timedelta(weeks=1),
    "下个月": relativedelta(months=1)
}

# 具体时间
TIME_PATTERNS = [
    r"(\d{1,2})点(?:(\d{1,2})分)?",      # 8点30分
    r"(\d{1,2}):(\d{2})",                # 8:30
    r"(上午|下午|晚上)(\d{1,2})点",       # 下午3点
]

# 重复模式
RECURRING_PATTERNS = {
    "每天": "daily",
    "每周": "weekly", 
    "每月": "monthly",
    "工作日": "weekdays",
    "周末": "weekends"
}
```

## 🤝 人机协作机制

### 确认服务架构
```python
class ConfirmationService:
    """
    人机协作确认服务
    """
    
    async def process_user_feedback(
        self, 
        user_input: str, 
        context: ConversationContext
    ) -> Dict[str, Any]:
        
        # 1. 分类用户反馈
        feedback_type = self._classify_feedback(user_input, context)
        
        # 2. 根据反馈类型处理
        if feedback_type == UserFeedbackType.CONFIRM:
            return await self._handle_confirmation(context)
        elif feedback_type == UserFeedbackType.MODIFY:
            return await self._handle_modification(user_input, context)
        elif feedback_type == UserFeedbackType.REJECT:
            return await self._handle_rejection(context)
        
        return await self._handle_unclear_feedback(user_input, context)
```

### 反馈分类算法
```python
def _classify_feedback(self, user_input: str, context: ConversationContext) -> UserFeedbackType:
    """
    智能反馈分类
    """
    user_input_lower = user_input.lower().strip()
    
    # 确认关键词
    if any(keyword in user_input_lower for keyword in CONFIRM_KEYWORDS):
        return UserFeedbackType.CONFIRM
    
    # 拒绝关键词  
    if any(keyword in user_input_lower for keyword in REJECT_KEYWORDS):
        return UserFeedbackType.REJECT
    
    # 修改关键词
    if any(keyword in user_input_lower for keyword in MODIFY_KEYWORDS):
        return UserFeedbackType.MODIFY
    
    # 上下文相关判断
    if context.state == ConversationState.WAITING_SUPPLEMENT:
        return UserFeedbackType.SUPPLEMENT
    
    return UserFeedbackType.MODIFY  # 默认为修改
```

## 🔧 工具绑定系统

### 工具注册机制
```python
class ToolRegistry:
    """
    工具注册表
    """
    
    def __init__(self):
        self.tools = {}
    
    def register_tool(self, name: str, tool: BaseTool):
        """注册工具"""
        self.tools[name] = tool
    
    def get_tool(self, name: str) -> BaseTool:
        """获取工具"""
        return self.tools.get(name)

# 注册时间解析工具
tool_registry.register_tool("parse_time", TimeParsingTool())
tool_registry.register_tool("extract_entities", EntityExtractionTool())
```

### 工具调用流程
```python
class BaseTool:
    """
    工具基类
    """
    
    def __init__(self, name: str, description: str):
        self.name = name
        self.description = description
    
    async def invoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        """工具调用接口"""
        raise NotImplementedError

class TimeParsingTool(BaseTool):
    """
    时间解析工具
    """
    
    async def invoke(self, input_data: Dict[str, Any]) -> Dict[str, Any]:
        text = input_data.get("text", "")
        parser = TimeParser()
        result = parser.parse(text)
        return result.dict()
```

## 📊 数据流架构

### 请求处理流程
```
1. 用户输入 → ChatService.process_message()
2. 获取会话上下文 → ConversationManager.get_or_create_conversation()
3. 检查状态 → should_skip_intent_classification()
4. 意图识别/反馈处理 → CoordinatorAgent/ConfirmationService
5. 实体提取 → PlannerAgent.extract_entities()
6. 时间解析 → TimeParser.parse()
7. 确认检查 → needs_confirmation()
8. 操作执行 → ExecutorAgent.execute()
9. 状态更新 → ConversationContext.update()
10. 响应返回 → ChatResponse
```

### 数据模型关系
```python
# 核心数据模型
ChatRequest → ConversationContext → AgentState → CalendarPlan → ChatResponse
     ↓              ↓                    ↓           ↓            ↓
  用户输入      会话状态管理         代理处理      计划生成      响应输出
```

## 🔒 安全与性能

### 安全机制
- **输入验证**: Pydantic模型验证
- **状态隔离**: 用户会话独立管理
- **错误处理**: 完善的异常捕获和降级机制

### 性能优化
- **异步处理**: 全异步架构
- **缓存机制**: 会话状态缓存
- **连接池**: 数据库连接复用
- **负载均衡**: 支持水平扩展

## 🧪 测试架构

### 测试分层
```
单元测试 (Unit Tests)
├── TimeParser 测试
├── Agent 测试  
├── Service 测试
└── Model 测试

集成测试 (Integration Tests)
├── API 端到端测试
├── 多代理协作测试
└── 人机协作流程测试

性能测试 (Performance Tests)
├── 并发测试
├── 响应时间测试
└── 内存使用测试
```

### 测试工具
- **单元测试**: pytest
- **异步测试**: pytest-asyncio
- **API测试**: httpx
- **性能测试**: locust

---

## 📚 扩展阅读

- [PRD产品需求文档](./PRD.md)
- [API接口文档](./API.md)
- [开发指南](./DEVELOPMENT.md)
- [部署指南](./DEPLOYMENT.md)
