name: Test Cases Check

on:
  push:
    branches: [ 'main' ]
  pull_request:
    branches: [ '*' ]

permissions:
  contents: read

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Install the latest version of uv
      uses: astral-sh/setup-uv@v5
      with:
        version: "latest"

    - name: Install dependencies
      run: |
        uv venv --python 3.12
        uv pip install -e ".[dev]"
        uv pip install -e ".[test]"

    - name: Run test cases with coverage
      run: |
        source .venv/bin/activate
        TAVILY_API_KEY=mock-key make coverage

    - name: Generate HTML Coverage Report
      run: |
        source .venv/bin/activate
        python -m coverage html -d coverage_html

    - name: Upload Coverage Report
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: coverage_html/
        
    - name: Display Coverage Summary
      run: |
        source .venv/bin/activate
        python -m coverage report