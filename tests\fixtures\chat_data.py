"""
聊天测试数据
"""

from datetime import datetime, timedelta

from core.ai.intent_classifier import IntentType
from models.workflow import IntentResult

# 意图识别测试数据
INTENT_TEST_CASES = {
    "calendar_create": [
        "明天下午3点安排一个会议",
        "帮我预定下周一上午10点的会议室",
        "安排一个项目讨论会议",
        "下周三下午2点开会",
        "预约明天的培训",
    ],
    "calendar_query": [
        "今天有什么安排？",
        "查看本周的日程",
        "我明天几点有会议？",
        "下周的安排是什么？",
        "显示我的日历",
    ],
    "calendar_update": [
        "把明天的会议改到后天",
        "将下午3点的会议推迟到4点",
        "修改会议时间",
        "更改会议地点",
        "会议改期",
    ],
    "calendar_delete": [
        "取消今天下午的会议",
        "删除明天的培训",
        "不要这个会议了",
        "取消所有安排",
        "删除重复会议",
    ],
    "chat_greeting": [
        "你好",
        "hi",
        "早上好",
        "晚上好",
        "hello",
    ],
    "chat_thanks": [
        "谢谢",
        "感谢你的帮助",
        "太好了，谢谢",
        "非常感谢",
        "thanks",
    ],
    "chat_help": [
        "你能做什么？",
        "帮助",
        "怎么使用？",
        "有什么功能？",
        "使用说明",
    ],
    "confirmation_yes": [
        "是的",
        "好的",
        "确认",
        "同意",
        "可以",
        "没问题",
        "OK",
    ],
    "confirmation_no": [
        "不是",
        "取消",
        "不要",
        "不同意",
        "不可以",
        "算了",
    ],
}

# 时间解析测试数据
TIME_PARSE_TEST_CASES = {
    "absolute_times": [
        {
            "text": "2025年7月15日下午3点",
            "expected": datetime(2025, 7, 15, 15, 0),
        },
        {
            "text": "明天上午9点",
            "expected": None,  # 相对时间，需要基准时间
        },
        {
            "text": "下周一下午2点",
            "expected": None,  # 相对时间
        },
    ],
    "relative_times": [
        "明天",
        "后天",
        "下周",
        "下个月",
        "1小时后",
        "30分钟后",
    ],
    "durations": [
        "1小时",
        "30分钟",
        "2小时30分钟",
        "半小时",
        "一整天",
    ],
    "recurring_patterns": [
        "每周一",
        "每天上午9点",
        "每月15号",
        "每周五下午",
    ],
}

# 多轮对话测试场景
MULTI_TURN_SCENARIOS = {
    "calendar_creation_complete": [
        {
            "user": "明天下午安排一个会议",
            "expected_intent": IntentType.CALENDAR_CREATE,
            "expected_response_contains": ["会议主题", "标题"],
        },
        {
            "user": "项目进度讨论",
            "expected_intent": IntentType.CALENDAR_CREATE_SUPPLEMENT,
            "expected_response_contains": ["时长", "持续时间"],
        },
        {
            "user": "2小时",
            "expected_intent": IntentType.CALENDAR_CREATE_SUPPLEMENT,
            "expected_response_contains": ["参会者", "邀请"],
        },
        {
            "user": "张三和李四",
            "expected_intent": IntentType.CALENDAR_CREATE_SUPPLEMENT,
            "expected_response_contains": ["确认创建"],
        },
        {
            "user": "确认",
            "expected_intent": IntentType.CONFIRMATION_YES,
            "expected_response_contains": ["已创建", "成功"],
        },
    ],
    "calendar_creation_interrupted": [
        {
            "user": "明天安排会议",
            "expected_intent": IntentType.CALENDAR_CREATE,
        },
        {
            "user": "今天天气怎么样？",
            "expected_intent": IntentType.CHAT_GENERAL,
        },
        {
            "user": "继续刚才的会议安排",
            "expected_intent": IntentType.CALENDAR_CREATE,
        },
    ],
    "multi_intent_handling": [
        {
            "user": "帮我查看今天的安排，然后安排明天的会议",
            "expected_intent": IntentType.MULTI_INTENT,
            "expected_response_contains": ["先", "首先"],
        },
        {
            "user": "先查看今天的安排",
            "expected_intent": IntentType.CALENDAR_QUERY,
        },
    ],
}

# 冲突检测测试数据
CONFLICT_TEST_CASES = {
    "time_conflicts": [
        {
            "description": "完全重叠",
            "new_event": {
                "start": datetime(2025, 7, 15, 14, 30),
                "end": datetime(2025, 7, 15, 15, 30),
            },
            "existing_event": {
                "start": datetime(2025, 7, 15, 14, 0),
                "end": datetime(2025, 7, 15, 16, 0),
            },
            "expected_conflict": True,
        },
        {
            "description": "开始时间冲突",
            "new_event": {
                "start": datetime(2025, 7, 15, 13, 30),
                "end": datetime(2025, 7, 15, 14, 30),
            },
            "existing_event": {
                "start": datetime(2025, 7, 15, 14, 0),
                "end": datetime(2025, 7, 15, 16, 0),
            },
            "expected_conflict": True,
        },
        {
            "description": "无冲突",
            "new_event": {
                "start": datetime(2025, 7, 15, 12, 0),
                "end": datetime(2025, 7, 15, 13, 0),
            },
            "existing_event": {
                "start": datetime(2025, 7, 15, 14, 0),
                "end": datetime(2025, 7, 15, 16, 0),
            },
            "expected_conflict": False,
        },
    ],
    "location_conflicts": [
        {
            "description": "同一会议室冲突",
            "new_event": {
                "location": "会议室A",
                "start": datetime(2025, 7, 15, 14, 0),
                "end": datetime(2025, 7, 15, 15, 0),
            },
            "existing_event": {
                "location": "会议室A",
                "start": datetime(2025, 7, 15, 14, 30),
                "end": datetime(2025, 7, 15, 15, 30),
            },
            "expected_conflict": True,
        },
    ],
}

# 模拟API响应数据
MOCK_API_RESPONSES = {
    "feishu_auth_success": {
        "code": 0,
        "msg": "success",
        "data": {
            "access_token": "mock_access_token",
            "refresh_token": "mock_refresh_token",
            "expires_in": 7200,
        },
    },
    "feishu_user_info": {
        "code": 0,
        "msg": "success",
        "data": {
            "open_id": "mock_user_id",
            "name": "测试用户",
            "avatar_url": "https://example.com/avatar.jpg",
        },
    },
    "feishu_calendars": {
        "code": 0,
        "msg": "success",
        "data": {
            "calendar_list": [
                {
                    "calendar_id": "mock_calendar_id",
                    "summary": "测试日历",
                    "type": "primary",
                    "role": "owner",
                }
            ]
        },
    },
    "feishu_events": {
        "code": 0,
        "msg": "success",
        "data": {
            "items": [
                {
                    "event_id": "mock_event_id",
                    "summary": "测试会议",
                    "start_time": {"iso_format": "2025-07-15T14:00:00+08:00"},
                    "end_time": {"iso_format": "2025-07-15T15:00:00+08:00"},
                    "status": "confirmed",
                }
            ]
        },
    },
}

# 用户偏好测试数据
USER_PREFERENCES = {
    "default_user": {
        "default_meeting_duration": "1小时",
        "preferred_meeting_time": "下午",
        "default_location": "会议室A",
        "timezone": "Asia/Shanghai",
        "reminder_minutes": 15,
    },
    "power_user": {
        "default_meeting_duration": "30分钟",
        "preferred_meeting_time": "上午",
        "default_location": "线上会议",
        "timezone": "Asia/Shanghai",
        "reminder_minutes": 5,
        "auto_accept_invites": True,
    },
}

# 错误场景测试数据
ERROR_SCENARIOS = {
    "invalid_times": [
        "25点30分",  # 无效时间
        "2025年13月32日",  # 无效日期
        "明天昨天",  # 矛盾时间
    ],
    "ambiguous_inputs": [
        "下午",  # 缺少具体时间
        "会议",  # 缺少时间信息
        "明天",  # 缺少具体时间
    ],
    "network_errors": [
        "connection_timeout",
        "server_error",
        "rate_limit_exceeded",
    ],
}
