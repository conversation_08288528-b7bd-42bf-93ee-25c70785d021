"""
真实业务场景测试
测试实际的用户业务流程和场景
"""

import pytest
import os
import asyncio
from datetime import datetime, timedelta

from integrations.feishu import get_feishu_client
from integrations.storage import save_token, get_token


class TestRealUserScenarios:
    """真实用户场景测试"""
    
    @pytest.mark.network
    @pytest.mark.skipif(
        not os.getenv("TEST_ACCESS_TOKEN"),
        reason="需要TEST_ACCESS_TOKEN环境变量进行真实API测试"
    )
    def test_user_authentication_flow(self):
        """测试用户认证流程"""
        client = get_feishu_client()
        access_token = os.getenv("TEST_ACCESS_TOKEN")
        
        # 模拟用户登录后获取用户信息
        try:
            result = client.get_user_info(access_token)
            
            # 验证API调用结构
            assert isinstance(result, dict)
            assert "code" in result
            
            if result.get("code") == 0:
                user_data = result.get("data", {})
                assert "open_id" in user_data or "user_id" in user_data
                print(f"✅ 用户认证成功，用户信息: {user_data}")
            else:
                print(f"⚠️  用户认证返回错误: {result.get('msg', '未知错误')}")
                
        except Exception as e:
            print(f"⚠️  用户认证测试异常: {str(e)}")
    
    @pytest.mark.network
    @pytest.mark.skipif(
        not os.getenv("TEST_ACCESS_TOKEN") or not os.getenv("TEST_USER_ID"),
        reason="需要TEST_ACCESS_TOKEN和TEST_USER_ID环境变量"
    )
    def test_calendar_discovery_scenario(self):
        """测试日历发现场景 - 用户查看自己的日历列表"""
        client = get_feishu_client()
        test_user_id = os.getenv("TEST_USER_ID")
        
        # 模拟用户查看日历列表
        result = client.get_calendars(test_user_id)
        
        # 验证API调用结构
        assert isinstance(result, dict)
        assert "code" in result
        
        if result.get("code") == 0:
            data = result.get("data", {})
            calendar_list = data.get("calendar_list", [])
            
            print(f"✅ 发现 {len(calendar_list)} 个日历")
            
            # 验证日历数据结构
            for i, calendar in enumerate(calendar_list[:3]):  # 只检查前3个
                assert "calendar_id" in calendar
                assert "summary" in calendar
                print(f"   日历 {i+1}: {calendar.get('summary', '未命名')}")
                
        else:
            print(f"⚠️  日历发现返回错误: {result.get('msg', '未知错误')}")
    
    @pytest.mark.integration
    def test_event_creation_workflow(self, test_user_id):
        """测试事件创建工作流"""
        # 模拟用户创建事件的完整流程
        
        # 1. 准备事件数据
        event_data = {
            "summary": f"测试会议_{int(datetime.now().timestamp())}",
            "description": "这是一个通过API创建的测试会议",
            "start_time": {
                "timestamp": str(int(datetime.now().timestamp()))
            },
            "end_time": {
                "timestamp": str(int((datetime.now() + timedelta(hours=1)).timestamp()))
            },
            "location": "会议室A",
            "attendees": []
        }
        
        # 2. 验证事件数据结构
        required_fields = ["summary", "start_time", "end_time"]
        for field in required_fields:
            assert field in event_data, f"事件数据缺少必需字段: {field}"
        
        # 3. 验证时间格式
        assert "timestamp" in event_data["start_time"]
        assert "timestamp" in event_data["end_time"]
        
        # 4. 验证时间逻辑
        start_timestamp = int(event_data["start_time"]["timestamp"])
        end_timestamp = int(event_data["end_time"]["timestamp"])
        assert end_timestamp > start_timestamp, "结束时间应该晚于开始时间"
        
        print(f"✅ 事件创建工作流验证通过: {event_data['summary']}")
    
    @pytest.mark.integration
    def test_daily_task_management_scenario(self):
        """测试日常任务管理场景"""
        # 模拟用户管理日常任务的场景
        
        # 1. 创建今日任务
        today_tasks = [
            {
                "summary": "晨会",
                "start_time": "09:00",
                "duration": 30,
                "priority": "high"
            },
            {
                "summary": "项目评审",
                "start_time": "14:00", 
                "duration": 60,
                "priority": "medium"
            },
            {
                "summary": "周报撰写",
                "start_time": "17:00",
                "duration": 30,
                "priority": "low"
            }
        ]
        
        # 2. 验证任务数据结构
        for task in today_tasks:
            required_fields = ["summary", "start_time", "duration"]
            for field in required_fields:
                assert field in task, f"任务数据缺少必需字段: {field}"
        
        # 3. 验证时间冲突检测逻辑
        def time_to_minutes(time_str):
            """将时间字符串转换为分钟数"""
            hours, minutes = map(int, time_str.split(':'))
            return hours * 60 + minutes
        
        # 检查任务时间冲突
        for i, task1 in enumerate(today_tasks):
            start1 = time_to_minutes(task1["start_time"])
            end1 = start1 + task1["duration"]
            
            for j, task2 in enumerate(today_tasks[i+1:], i+1):
                start2 = time_to_minutes(task2["start_time"])
                end2 = start2 + task2["duration"]
                
                # 检查时间冲突
                has_conflict = not (end1 <= start2 or end2 <= start1)
                assert not has_conflict, f"任务时间冲突: {task1['summary']} 与 {task2['summary']}"
        
        print(f"✅ 日常任务管理场景验证通过: {len(today_tasks)} 个任务")
    
    @pytest.mark.integration
    def test_batch_event_processing_scenario(self):
        """测试批量事件处理场景"""
        # 模拟用户批量处理事件的场景
        
        # 1. 准备批量事件数据
        batch_events = []
        base_time = datetime.now()
        
        for i in range(5):
            event = {
                "summary": f"批量事件_{i+1}",
                "description": f"这是第{i+1}个批量创建的事件",
                "start_time": {
                    "timestamp": str(int((base_time + timedelta(days=i)).timestamp()))
                },
                "end_time": {
                    "timestamp": str(int((base_time + timedelta(days=i, hours=1)).timestamp()))
                }
            }
            batch_events.append(event)
        
        # 2. 验证批量数据结构
        assert len(batch_events) == 5
        
        for i, event in enumerate(batch_events):
            assert "summary" in event
            assert "start_time" in event
            assert "end_time" in event
            assert f"批量事件_{i+1}" == event["summary"]
        
        # 3. 验证批量处理逻辑
        successful_events = []
        failed_events = []
        
        for event in batch_events:
            try:
                # 模拟事件验证
                if event["summary"] and event["start_time"] and event["end_time"]:
                    successful_events.append(event)
                else:
                    failed_events.append(event)
            except Exception as e:
                failed_events.append({"event": event, "error": str(e)})
        
        # 4. 验证批量处理结果
        assert len(successful_events) == 5
        assert len(failed_events) == 0
        
        print(f"✅ 批量事件处理场景验证通过: {len(successful_events)} 个成功，{len(failed_events)} 个失败")


class TestWorkflowIntegrationScenarios:
    """工作流集成场景测试"""
    
    @pytest.mark.integration
    def test_intent_recognition_workflow(self):
        """测试意图识别工作流"""
        # 模拟用户输入和意图识别流程
        
        test_inputs = [
            {
                "user_input": "帮我创建一个明天下午2点的会议",
                "expected_intent": "event_create",
                "expected_entities": ["时间", "事件类型"]
            },
            {
                "user_input": "查看我下周的日程安排",
                "expected_intent": "event_query",
                "expected_entities": ["时间范围"]
            },
            {
                "user_input": "取消今天晚上的晚餐约会",
                "expected_intent": "event_delete",
                "expected_entities": ["时间", "事件类型"]
            },
            {
                "user_input": "把明天的会议改到后天",
                "expected_intent": "event_update",
                "expected_entities": ["时间", "新时间"]
            }
        ]
        
        # 验证意图识别逻辑结构
        for test_case in test_inputs:
            user_input = test_case["user_input"]
            expected_intent = test_case["expected_intent"]
            
            # 模拟意图识别过程
            assert len(user_input) > 0, "用户输入不能为空"
            assert expected_intent in ["event_create", "event_query", "event_update", "event_delete", "calendar_create", "calendar_query"], "意图类型应该在预定义范围内"
            
            # 验证实体提取逻辑
            expected_entities = test_case["expected_entities"]
            assert len(expected_entities) > 0, "应该提取到相关实体"
            
        print(f"✅ 意图识别工作流验证通过: {len(test_inputs)} 个测试用例")
    
    @pytest.mark.integration
    def test_multi_agent_coordination_workflow(self):
        """测试多代理协调工作流"""
        # 模拟多代理协作处理用户请求
        
        # 1. 协调器代理 - 接收用户请求
        user_request = {
            "user_id": "test_user_123",
            "message": "帮我安排下周一到周五每天上午9点的站会",
            "context": {
                "current_time": datetime.now().isoformat(),
                "user_timezone": "Asia/Shanghai"
            }
        }
        
        # 验证请求结构
        assert "user_id" in user_request
        assert "message" in user_request
        assert "context" in user_request
        
        # 2. 规划器代理 - 分析和规划任务
        planning_result = {
            "intent": "batch_event_create",
            "entities": {
                "event_type": "站会",
                "frequency": "每天",
                "time": "上午9点",
                "duration": "工作日",
                "date_range": "下周一到周五"
            },
            "action_plan": [
                "解析时间范围",
                "生成重复事件",
                "检查时间冲突",
                "批量创建事件"
            ]
        }
        
        # 验证规划结果
        assert "intent" in planning_result
        assert "entities" in planning_result
        assert "action_plan" in planning_result
        assert len(planning_result["action_plan"]) > 0
        
        # 3. 执行器代理 - 执行具体操作
        execution_steps = []
        for step in planning_result["action_plan"]:
            execution_step = {
                "step": step,
                "status": "pending",
                "result": None
            }
            
            # 模拟执行过程
            if step == "解析时间范围":
                execution_step["status"] = "completed"
                execution_step["result"] = "2024-01-15 到 2024-01-19"
            elif step == "生成重复事件":
                execution_step["status"] = "completed"
                execution_step["result"] = "5个事件"
            elif step == "检查时间冲突":
                execution_step["status"] = "completed"
                execution_step["result"] = "无冲突"
            elif step == "批量创建事件":
                execution_step["status"] = "completed"
                execution_step["result"] = "5个事件创建成功"
            
            execution_steps.append(execution_step)
        
        # 验证执行结果
        assert len(execution_steps) == len(planning_result["action_plan"])
        
        completed_steps = [step for step in execution_steps if step["status"] == "completed"]
        assert len(completed_steps) == len(execution_steps), "所有步骤都应该完成"
        
        print(f"✅ 多代理协调工作流验证通过: {len(execution_steps)} 个执行步骤")
    
    @pytest.mark.integration
    def test_error_recovery_workflow(self):
        """测试错误恢复工作流"""
        # 模拟错误情况和恢复机制
        
        error_scenarios = [
            {
                "scenario": "网络连接失败",
                "error_type": "NetworkError",
                "recovery_strategy": "重试机制",
                "max_retries": 3
            },
            {
                "scenario": "API限流",
                "error_type": "RateLimitError", 
                "recovery_strategy": "延迟重试",
                "delay_seconds": 60
            },
            {
                "scenario": "权限不足",
                "error_type": "PermissionError",
                "recovery_strategy": "用户重新授权",
                "action_required": "redirect_to_auth"
            },
            {
                "scenario": "数据格式错误",
                "error_type": "ValidationError",
                "recovery_strategy": "数据修正",
                "correction_needed": True
            }
        ]
        
        # 验证错误恢复逻辑
        for scenario in error_scenarios:
            assert "scenario" in scenario
            assert "error_type" in scenario
            assert "recovery_strategy" in scenario
            
            # 验证恢复策略的合理性
            recovery_strategy = scenario["recovery_strategy"]
            assert recovery_strategy in [
                "重试机制", "延迟重试", "用户重新授权", "数据修正", "降级处理"
            ], f"未知的恢复策略: {recovery_strategy}"
            
            # 模拟错误恢复过程
            recovery_result = {
                "scenario": scenario["scenario"],
                "recovery_attempted": True,
                "recovery_successful": True,  # 模拟成功恢复
                "recovery_time": 2.5  # 模拟恢复时间
            }
            
            assert recovery_result["recovery_attempted"]
            assert recovery_result["recovery_successful"]
        
        print(f"✅ 错误恢复工作流验证通过: {len(error_scenarios)} 个错误场景")


class TestPerformanceScenarios:
    """性能场景测试"""
    
    @pytest.mark.performance
    def test_large_calendar_list_performance(self):
        """测试大量日历列表性能"""
        import time
        
        # 模拟大量日历数据
        large_calendar_list = []
        for i in range(100):
            calendar = {
                "calendar_id": f"calendar_{i}",
                "summary": f"日历_{i}",
                "description": f"这是第{i}个日历",
                "type": "primary" if i == 0 else "shared",
                "role": "owner" if i < 10 else "reader"
            }
            large_calendar_list.append(calendar)
        
        # 测试处理性能
        start_time = time.time()
        
        # 模拟日历列表处理
        processed_calendars = []
        for calendar in large_calendar_list:
            # 模拟数据处理逻辑
            processed_calendar = {
                "id": calendar["calendar_id"],
                "name": calendar["summary"],
                "type": calendar["type"],
                "editable": calendar["role"] == "owner"
            }
            processed_calendars.append(processed_calendar)
        
        processing_time = time.time() - start_time
        
        # 验证性能要求
        assert processing_time < 1.0, f"处理100个日历耗时过长: {processing_time:.3f}秒"
        assert len(processed_calendars) == 100
        
        print(f"✅ 大量日历列表性能测试通过: {processing_time:.3f}秒处理{len(processed_calendars)}个日历")
    
    @pytest.mark.performance
    def test_concurrent_api_calls_simulation(self):
        """测试并发API调用模拟"""
        import time
        import threading
        
        # 模拟并发API调用
        api_call_results = []
        api_call_lock = threading.Lock()
        
        def simulate_api_call(call_id):
            """模拟API调用"""
            start_time = time.time()
            
            # 模拟API处理时间
            time.sleep(0.1)  # 模拟100ms的API响应时间
            
            end_time = time.time()
            duration = end_time - start_time
            
            with api_call_lock:
                api_call_results.append({
                    "call_id": call_id,
                    "duration": duration,
                    "success": True
                })
        
        # 创建并发线程
        threads = []
        concurrent_calls = 10
        
        start_time = time.time()
        
        for i in range(concurrent_calls):
            thread = threading.Thread(target=simulate_api_call, args=(i,))
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        total_time = time.time() - start_time
        
        # 验证并发性能
        assert len(api_call_results) == concurrent_calls
        assert total_time < 1.0, f"10个并发API调用耗时过长: {total_time:.3f}秒"
        
        # 验证所有调用都成功
        successful_calls = [result for result in api_call_results if result["success"]]
        assert len(successful_calls) == concurrent_calls
        
        print(f"✅ 并发API调用性能测试通过: {total_time:.3f}秒完成{concurrent_calls}个并发调用")


# 运行真实业务场景测试的便捷函数
def run_business_scenario_tests():
    """运行真实业务场景测试"""
    import subprocess
    import sys
    
    print("🚀 运行真实业务场景测试")
    print("="*60)
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_real_business_scenarios.py", 
        "-v", "--tb=short"
    ])
    
    return result.returncode == 0


if __name__ == "__main__":
    success = run_business_scenario_tests()
    exit(0 if success else 1)
