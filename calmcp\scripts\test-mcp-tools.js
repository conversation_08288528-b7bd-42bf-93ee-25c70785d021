#!/usr/bin/env node
/**
 * 测试 MCP 工具调用功能
 */

const http = require('http');

async function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(url, options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(10000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

async function testMCPTools() {
  console.log('🔧 测试 MCP 工具调用功能...\n');
  
  try {
    // 1. 测试获取工具列表
    console.log('📋 1. 测试获取 MCP 工具列表...');
    const toolsResponse = await makeRequest('http://localhost:3000/api/mcp/tools');
    
    if (toolsResponse.status === 200) {
      console.log('✅ MCP 工具列表获取成功');
      console.log(`📊 可用工具数量: ${toolsResponse.data.tools?.length || 0}`);
      
      // 显示前几个工具
      if (toolsResponse.data.tools && toolsResponse.data.tools.length > 0) {
        console.log('📋 前 5 个工具:');
        toolsResponse.data.tools.slice(0, 5).forEach((tool, index) => {
          console.log(`  ${index + 1}. ${tool.name} - ${tool.description}`);
        });
      }
    } else {
      console.log('❌ MCP 工具列表获取失败:', toolsResponse.status);
      console.log('响应:', toolsResponse.data);
    }

    // 2. 测试工具调用 - 获取日历列表
    console.log('\n📋 2. 测试工具调用 - calendar_list...');
    const callResponse = await makeRequest(
      'http://localhost:3000/api/mcp/tools/call',
      'POST',
      {
        name: 'calendar_list',
        arguments: {
          page_size: 10
        }
      }
    );
    
    if (callResponse.status === 200) {
      console.log('✅ 工具调用成功');
      console.log('📊 响应数据:', JSON.stringify(callResponse.data, null, 2));
    } else {
      console.log('❌ 工具调用失败:', callResponse.status);
      console.log('响应:', callResponse.data);
    }

    // 3. 测试参数验证 - 缺少必需参数
    console.log('\n📋 3. 测试参数验证 - 缺少必需参数...');
    const validationResponse = await makeRequest(
      'http://localhost:3000/api/mcp/tools/call',
      'POST',
      {
        name: 'calendar_event_create',
        arguments: {
          summary: '测试事件'
          // 缺少 calendar_id, start_time, end_time
        }
      }
    );
    
    if (validationResponse.status === 400) {
      console.log('✅ 参数验证正常工作 - 正确拒绝了无效请求');
      console.log('📊 错误信息:', validationResponse.data.error);
    } else {
      console.log('❌ 参数验证可能有问题:', validationResponse.status);
      console.log('响应:', validationResponse.data);
    }

    // 4. 测试不存在的工具
    console.log('\n📋 4. 测试不存在的工具...');
    const notFoundResponse = await makeRequest(
      'http://localhost:3000/api/mcp/tools/call',
      'POST',
      {
        name: 'non_existent_tool',
        arguments: {}
      }
    );
    
    if (notFoundResponse.status === 404) {
      console.log('✅ 工具不存在检查正常工作');
      console.log('📊 错误信息:', notFoundResponse.data.error);
    } else {
      console.log('❌ 工具不存在检查可能有问题:', notFoundResponse.status);
      console.log('响应:', notFoundResponse.data);
    }

    // 5. 测试官方工具调用
    console.log('\n📋 5. 测试官方工具调用 - calendar.v4.calendar.list...');
    const officialToolResponse = await makeRequest(
      'http://localhost:3000/api/mcp/tools/call',
      'POST',
      {
        name: 'calendar.v4.calendar.list',
        arguments: {
          page_size: 10
        }
      }
    );
    
    if (officialToolResponse.status === 200) {
      console.log('✅ 官方工具调用成功');
      console.log('📊 响应数据:', JSON.stringify(officialToolResponse.data, null, 2));
    } else {
      console.log('❌ 官方工具调用失败:', officialToolResponse.status);
      console.log('响应:', officialToolResponse.data);
    }

    // 总结
    console.log('\n' + '='.repeat(60));
    console.log('🎉 MCP 工具调用测试完成!');
    console.log('='.repeat(60));
    
    console.log('\n📊 测试结果总结:');
    console.log(`✅ 工具列表获取: ${toolsResponse.status === 200 ? '成功' : '失败'}`);
    console.log(`${callResponse.status === 200 ? '✅' : '❌'} 简化工具调用: ${callResponse.status === 200 ? '成功' : '失败'}`);
    console.log(`✅ 参数验证: ${validationResponse.status === 400 ? '正常' : '异常'}`);
    console.log(`✅ 工具不存在检查: ${notFoundResponse.status === 404 ? '正常' : '异常'}`);
    console.log(`${officialToolResponse.status === 200 ? '✅' : '❌'} 官方工具调用: ${officialToolResponse.status === 200 ? '成功' : '失败'}`);
    
    console.log('\n🔧 下一步建议:');
    console.log('1. 配置飞书应用凭据进行真实 API 调用测试');
    console.log('2. 测试更多复杂的工具调用场景');
    console.log('3. 验证错误处理和重试机制');
    console.log('4. 进行性能和并发测试');

  } catch (error) {
    console.log('❌ 测试过程中出现错误:', error.message);
    console.log('\n💡 请确保:');
    console.log('1. Next.js 服务器正在运行');
    console.log('2. MCP 服务正常工作');
    console.log('3. 所有 API 端点可访问');
  }
}

// 运行测试
testMCPTools();
