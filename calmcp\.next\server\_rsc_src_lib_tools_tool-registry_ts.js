"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_lib_tools_tool-registry_ts";
exports.ids = ["_rsc_src_lib_tools_tool-registry_ts"];
exports.modules = {

/***/ "(rsc)/./src/lib/tools/tool-adapter.ts":
/*!***************************************!*\
  !*** ./src/lib/tools/tool-adapter.ts ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CALENDAR_ACL_TOOLS: () => (/* binding */ CALENDAR_ACL_TOOLS),\n/* harmony export */   CALENDAR_EVENT_ATTENDEE_TOOLS: () => (/* binding */ CALENDAR_EVENT_ATTENDEE_TOOLS),\n/* harmony export */   CALENDAR_EVENT_EXTENDED_TOOLS: () => (/* binding */ CALENDAR_EVENT_EXTENDED_TOOLS),\n/* harmony export */   CALENDAR_EVENT_MEETING_CHAT_TOOLS: () => (/* binding */ CALENDAR_EVENT_MEETING_CHAT_TOOLS),\n/* harmony export */   COMMON_CALENDAR_TOOLS: () => (/* binding */ COMMON_CALENDAR_TOOLS),\n/* harmony export */   EXTENDED_CALENDAR_TOOLS: () => (/* binding */ EXTENDED_CALENDAR_TOOLS),\n/* harmony export */   OTHER_CALENDAR_TOOLS: () => (/* binding */ OTHER_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器\n * 手动定义常用的飞书日历工具，避免复杂的 Zod 转换\n */ /**\n * 手动定义的常用飞书日历工具\n * 基于官方 API 文档创建，避免 Zod 转换复杂性\n */ /**\n * 常用的日历工具（简化版）\n */ const COMMON_CALENDAR_TOOLS = [\n    // 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最小值50\",\n                    minimum: 50,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.list\",\n        description: \"获取日历事件列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间（时间戳）\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间（时间戳）\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    description: \"开始时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                end_time: {\n                    type: \"object\",\n                    description: \"结束时间\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.search\",\n        description: \"搜索日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"搜索开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"搜索结束时间\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历访问控制工具\n */ const CALENDAR_ACL_TOOLS = [\n    {\n        name: \"calendar.v4.calendarAcl.create\",\n        description: \"创建日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                role: {\n                    type: \"string\",\n                    enum: [\n                        \"unknown\",\n                        \"free_busy_reader\",\n                        \"reader\",\n                        \"writer\",\n                        \"owner\"\n                    ],\n                    description: \"访问权限角色\"\n                },\n                scope: {\n                    type: \"object\",\n                    properties: {\n                        type: {\n                            type: \"string\",\n                            enum: [\n                                \"user\"\n                            ],\n                            description: \"权限生效范围类型\"\n                        },\n                        user_id: {\n                            type: \"string\",\n                            description: \"用户ID\"\n                        }\n                    },\n                    required: [\n                        \"type\"\n                    ]\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"role\",\n                \"scope\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.delete\",\n        description: \"删除日历访问控制\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                acl_id: {\n                    type: \"string\",\n                    description: \"访问控制ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"acl_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.list\",\n        description: \"获取日历访问控制列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最小值10\",\n                    minimum: 10\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.subscription\",\n        description: \"订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarAcl.unsubscription\",\n        description: \"取消订阅日历访问控制变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 扩展的日历管理工具\n */ const EXTENDED_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendar.create\",\n        description: \"创建共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色（RGB int32值）\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.delete\",\n        description: \"删除共享日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.patch\",\n        description: \"更新日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日历标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日历描述\"\n                },\n                permissions: {\n                    type: \"string\",\n                    enum: [\n                        \"private\",\n                        \"show_only_free_busy\",\n                        \"public\"\n                    ],\n                    description: \"日历公开范围\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日历颜色\"\n                },\n                summary_alias: {\n                    type: \"string\",\n                    description: \"日历备注名\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.primary\",\n        description: \"获取主日历信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.search\",\n        description: \"搜索日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 200\n                }\n            },\n            required: [\n                \"query\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscribe\",\n        description: \"订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.subscription\",\n        description: \"订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscribe\",\n        description: \"取消订阅日历\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.unsubscription\",\n        description: \"取消订阅日历变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件参与者工具\n */ const CALENDAR_EVENT_ATTENDEE_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventAttendee.batchDelete\",\n        description: \"批量删除日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_ids: {\n                    type: \"array\",\n                    items: {\n                        type: \"string\"\n                    },\n                    description: \"参与人ID列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_ids\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.chatMembersBatchCreate\",\n        description: \"批量添加群成员为日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                chat_id: {\n                    type: \"string\",\n                    description: \"群聊ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.create\",\n        description: \"添加日程参与人\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendees: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            type: {\n                                type: \"string\",\n                                enum: [\n                                    \"user\",\n                                    \"chat\",\n                                    \"resource\",\n                                    \"third_party\"\n                                ],\n                                description: \"参与人类型\"\n                            },\n                            attendee_id: {\n                                type: \"string\",\n                                description: \"参与人ID\"\n                            },\n                            rsvp_status: {\n                                type: \"string\",\n                                enum: [\n                                    \"needs_action\",\n                                    \"accept\",\n                                    \"tentative\",\n                                    \"decline\",\n                                    \"removed\"\n                                ],\n                                description: \"参与状态\"\n                            },\n                            is_optional: {\n                                type: \"boolean\",\n                                description: \"是否为可选参与人\"\n                            },\n                            display_name: {\n                                type: \"string\",\n                                description: \"参与人名称\"\n                            }\n                        },\n                        required: [\n                            \"type\",\n                            \"attendee_id\"\n                        ]\n                    },\n                    description: \"参与人列表\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendees\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendee.list\",\n        description: \"获取日程参与人列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventAttendeeChatMember.list\",\n        description: \"获取日程参与人群成员列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                attendee_id: {\n                    type: \"string\",\n                    description: \"参与人ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"attendee_id\"\n            ]\n        }\n    }\n];\n/**\n * 日历事件扩展工具\n */ const CALENDAR_EVENT_EXTENDED_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"获取日程实例视图\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"秒级时间戳\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    description: \"结束时间\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"日程公开范围\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与人权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"日程占用的忙闲状态\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"地点纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"地点经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                reminders: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            minutes: {\n                                type: \"number\",\n                                description: \"提前多少分钟提醒\"\n                            }\n                        }\n                    },\n                    description: \"日程提醒列表\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则\"\n                },\n                status: {\n                    type: \"string\",\n                    enum: [\n                        \"tentative\",\n                        \"confirmed\",\n                        \"cancelled\"\n                    ],\n                    description: \"日程状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.instances\",\n        description: \"获取重复日程的实例\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小\",\n                    minimum: 1,\n                    maximum: 500\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    }\n];\n/**\n * 会议聊天工具\n */ const CALENDAR_EVENT_MEETING_CHAT_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.create\",\n        description: \"创建会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.delete\",\n        description: \"删除会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingChat.patch\",\n        description: \"更新会议群聊\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                meeting_chat_id: {\n                    type: \"string\",\n                    description: \"会议群聊ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"meeting_chat_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEventMeetingMinute.create\",\n        description: \"创建会议纪要\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                content: {\n                    type: \"string\",\n                    description: \"会议纪要内容\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"content\"\n            ]\n        }\n    }\n];\n/**\n * 其他日历工具\n */ const OTHER_CALENDAR_TOOLS = [\n    {\n        name: \"calendar.v4.calendarEvent.reply\",\n        description: \"回复日程邀请\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程ID\"\n                },\n                rsvp_status: {\n                    type: \"string\",\n                    enum: [\n                        \"accept\",\n                        \"tentative\",\n                        \"decline\"\n                    ],\n                    description: \"回复状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\",\n                \"rsvp_status\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.subscription\",\n        description: \"订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.unsubscription\",\n        description: \"取消订阅日程变更事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.create\",\n        description: \"创建Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                admin_account: {\n                    type: \"string\",\n                    description: \"管理员账号\"\n                },\n                exchange_account: {\n                    type: \"string\",\n                    description: \"Exchange账号\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"admin_account\",\n                \"exchange_account\",\n                \"user_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.delete\",\n        description: \"删除Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.exchangeBinding.get\",\n        description: \"获取Exchange绑定\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                exchange_binding_id: {\n                    type: \"string\",\n                    description: \"Exchange绑定ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"exchange_binding_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.freebusy.list\",\n        description: \"查询忙闲信息\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                time_min: {\n                    type: \"string\",\n                    description: \"查询开始时间\"\n                },\n                time_max: {\n                    type: \"string\",\n                    description: \"查询结束时间\"\n                },\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                room_id: {\n                    type: \"string\",\n                    description: \"会议室ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"time_min\",\n                \"time_max\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.setting.generateCaldavConf\",\n        description: \"生成CalDAV配置\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                device_name: {\n                    type: \"string\",\n                    description: \"设备名称\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.create\",\n        description: \"创建请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                user_id: {\n                    type: \"string\",\n                    description: \"用户ID\"\n                },\n                timezone: {\n                    type: \"string\",\n                    description: \"时区\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"请假开始时间\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"请假结束时间\"\n                },\n                title: {\n                    type: \"string\",\n                    description: \"请假标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"请假描述\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"user_id\",\n                \"timezone\",\n                \"start_time\",\n                \"end_time\",\n                \"title\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.timeoffEvent.delete\",\n        description: \"删除请假日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                timeoff_event_id: {\n                    type: \"string\",\n                    description: \"请假日程ID\"\n                }\n            },\n            required: [\n                \"timeoff_event_id\"\n            ]\n        }\n    }\n];\n/**\n * 完整的日历工具（包含所有 43 个工具）\n */ const ALL_FEISHU_CALENDAR_TOOLS = [\n    ...COMMON_CALENDAR_TOOLS,\n    ...CALENDAR_ACL_TOOLS,\n    ...EXTENDED_CALENDAR_TOOLS,\n    ...CALENDAR_EVENT_ATTENDEE_TOOLS,\n    ...CALENDAR_EVENT_EXTENDED_TOOLS,\n    ...CALENDAR_EVENT_MEETING_CHAT_TOOLS,\n    ...OTHER_CALENDAR_TOOLS\n];\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return ALL_FEISHU_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数（简化版本）\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-registry.ts":
/*!****************************************!*\
  !*** ./src/lib/tools/tool-registry.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TOOL_CATEGORIES: () => (/* binding */ TOOL_CATEGORIES),\n/* harmony export */   getCommonTools: () => (/* binding */ getCommonTools),\n/* harmony export */   getToolCategories: () => (/* binding */ getToolCategories),\n/* harmony export */   getToolStats: () => (/* binding */ getToolStats),\n/* harmony export */   getToolsByCategory: () => (/* binding */ getToolsByCategory),\n/* harmony export */   searchTools: () => (/* binding */ searchTools),\n/* harmony export */   validateToolCompleteness: () => (/* binding */ validateToolCompleteness)\n/* harmony export */ });\n/* harmony import */ var _tool_adapter__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./tool-adapter */ \"(rsc)/./src/lib/tools/tool-adapter.ts\");\n/**\n * 飞书日历工具注册表\n * 提供所有可用工具的统计和分类信息\n */ \n/**\n * 工具分类\n */ const TOOL_CATEGORIES = {\n    // 日历管理 (9个工具)\n    CALENDAR_MANAGEMENT: [\n        \"calendar.v4.calendar.create\",\n        \"calendar.v4.calendar.delete\",\n        \"calendar.v4.calendar.get\",\n        \"calendar.v4.calendar.list\",\n        \"calendar.v4.calendar.patch\",\n        \"calendar.v4.calendar.primary\",\n        \"calendar.v4.calendar.search\",\n        \"calendar.v4.calendar.subscribe\",\n        \"calendar.v4.calendar.unsubscribe\"\n    ],\n    // 日历访问控制 (5个工具)\n    CALENDAR_ACL: [\n        \"calendar.v4.calendarAcl.create\",\n        \"calendar.v4.calendarAcl.delete\",\n        \"calendar.v4.calendarAcl.list\",\n        \"calendar.v4.calendarAcl.subscription\",\n        \"calendar.v4.calendarAcl.unsubscription\"\n    ],\n    // 日历事件管理 (11个工具)\n    CALENDAR_EVENT: [\n        \"calendar.v4.calendarEvent.create\",\n        \"calendar.v4.calendarEvent.delete\",\n        \"calendar.v4.calendarEvent.get\",\n        \"calendar.v4.calendarEvent.instanceView\",\n        \"calendar.v4.calendarEvent.instances\",\n        \"calendar.v4.calendarEvent.list\",\n        \"calendar.v4.calendarEvent.patch\",\n        \"calendar.v4.calendarEvent.reply\",\n        \"calendar.v4.calendarEvent.search\",\n        \"calendar.v4.calendarEvent.subscription\",\n        \"calendar.v4.calendarEvent.unsubscription\"\n    ],\n    // 日程参与者管理 (5个工具)\n    EVENT_ATTENDEE: [\n        \"calendar.v4.calendarEventAttendee.batchDelete\",\n        \"calendar.v4.calendarEventAttendee.chatMembersBatchCreate\",\n        \"calendar.v4.calendarEventAttendee.create\",\n        \"calendar.v4.calendarEventAttendee.list\",\n        \"calendar.v4.calendarEventAttendeeChatMember.list\"\n    ],\n    // 会议聊天 (4个工具)\n    MEETING_CHAT: [\n        \"calendar.v4.calendarEventMeetingChat.create\",\n        \"calendar.v4.calendarEventMeetingChat.delete\",\n        \"calendar.v4.calendarEventMeetingChat.patch\",\n        \"calendar.v4.calendarEventMeetingMinute.create\"\n    ],\n    // Exchange集成 (3个工具)\n    EXCHANGE_BINDING: [\n        \"calendar.v4.exchangeBinding.create\",\n        \"calendar.v4.exchangeBinding.delete\",\n        \"calendar.v4.exchangeBinding.get\"\n    ],\n    // 其他工具 (8个工具)\n    OTHERS: [\n        \"calendar.v4.calendar.subscription\",\n        \"calendar.v4.calendar.unsubscription\",\n        \"calendar.v4.freebusy.list\",\n        \"calendar.v4.setting.generateCaldavConf\",\n        \"calendar.v4.timeoffEvent.create\",\n        \"calendar.v4.timeoffEvent.delete\"\n    ]\n};\n/**\n * 获取工具分类信息\n */ function getToolCategories() {\n    const categories = Object.entries(TOOL_CATEGORIES).map(([category, tools])=>({\n            category,\n            count: tools.length,\n            tools\n        }));\n    const totalTools = categories.reduce((sum, cat)=>sum + cat.count, 0);\n    return {\n        categories,\n        totalTools,\n        summary: {\n            \"日历管理\": TOOL_CATEGORIES.CALENDAR_MANAGEMENT.length,\n            \"访问控制\": TOOL_CATEGORIES.CALENDAR_ACL.length,\n            \"事件管理\": TOOL_CATEGORIES.CALENDAR_EVENT.length,\n            \"参与者管理\": TOOL_CATEGORIES.EVENT_ATTENDEE.length,\n            \"会议聊天\": TOOL_CATEGORIES.MEETING_CHAT.length,\n            \"Exchange集成\": TOOL_CATEGORIES.EXCHANGE_BINDING.length,\n            \"其他工具\": TOOL_CATEGORIES.OTHERS.length\n        }\n    };\n}\n/**\n * 根据分类获取工具\n */ function getToolsByCategory(category) {\n    const toolNames = TOOL_CATEGORIES[category];\n    return _tool_adapter__WEBPACK_IMPORTED_MODULE_0__.ALL_FEISHU_CALENDAR_TOOLS.filter((tool)=>toolNames.includes(tool.name));\n}\n/**\n * 搜索工具\n */ function searchTools(keyword) {\n    const lowerKeyword = keyword.toLowerCase();\n    return _tool_adapter__WEBPACK_IMPORTED_MODULE_0__.ALL_FEISHU_CALENDAR_TOOLS.filter((tool)=>tool.name.toLowerCase().includes(lowerKeyword) || tool.description.toLowerCase().includes(lowerKeyword));\n}\n/**\n * 获取常用工具（推荐使用）\n */ function getCommonTools() {\n    const commonToolNames = [\n        \"calendar.v4.calendar.list\",\n        \"calendar.v4.calendar.get\",\n        \"calendar.v4.calendarEvent.create\",\n        \"calendar.v4.calendarEvent.list\",\n        \"calendar.v4.calendarEvent.get\",\n        \"calendar.v4.calendarEvent.patch\",\n        \"calendar.v4.calendarEvent.delete\",\n        \"calendar.v4.calendarEvent.search\",\n        \"calendar.v4.calendarEventAttendee.create\",\n        \"calendar.v4.calendarEventAttendee.list\"\n    ];\n    return _tool_adapter__WEBPACK_IMPORTED_MODULE_0__.ALL_FEISHU_CALENDAR_TOOLS.filter((tool)=>commonToolNames.includes(tool.name));\n}\n/**\n * 工具使用统计\n */ function getToolStats() {\n    const categories = getToolCategories();\n    return {\n        totalTools: _tool_adapter__WEBPACK_IMPORTED_MODULE_0__.ALL_FEISHU_CALENDAR_TOOLS.length,\n        expectedTotal: 43,\n        isComplete: _tool_adapter__WEBPACK_IMPORTED_MODULE_0__.ALL_FEISHU_CALENDAR_TOOLS.length >= 43,\n        categories: categories.summary,\n        commonToolsCount: getCommonTools().length\n    };\n}\n/**\n * 验证工具完整性\n */ function validateToolCompleteness() {\n    const stats = getToolStats();\n    const allCategoryTools = Object.values(TOOL_CATEGORIES).flat();\n    const uniqueTools = new Set(allCategoryTools);\n    return {\n        isComplete: stats.isComplete,\n        totalFound: stats.totalTools,\n        expectedTotal: stats.expectedTotal,\n        categorizedTools: uniqueTools.size,\n        uncategorizedTools: stats.totalTools - uniqueTools.size,\n        duplicateInCategories: allCategoryTools.length - uniqueTools.size\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-registry.ts\n");

/***/ })

};
;