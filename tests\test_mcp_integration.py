#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP集成测试
验证飞书MCP服务与自然语言处理的集成
"""

import asyncio
import pytest
import logging
from unittest.mock import AsyncMock, patch

from core.mcp.feishu_mcp_adapter import FeishuMCPAdapter, get_feishu_mcp_adapter
from core.workflow.nodes import calendar_processor_node, _extract_calendar_operation, _execute_calendar_mcp_operation
from core.workflow.engine import WorkflowEngine

logger = logging.getLogger(__name__)


class TestMCPIntegration:
    """MCP集成测试类"""
    
    @pytest.fixture
    def mock_mcp_adapter(self):
        """模拟MCP适配器"""
        adapter = AsyncMock(spec=FeishuMCPAdapter)
        adapter.is_connected = True
        adapter.get_available_tools.return_value = [
            "calendar.v4.calendar.list",
            "calendar.v4.calendar.create", 
            "calendar.v4.calendarEvent.create",
            "calendar.v4.calendarEvent.search"
        ]
        return adapter
    
    @pytest.mark.asyncio
    async def test_mcp_adapter_initialization(self):
        """测试MCP适配器初始化"""
        adapter = FeishuMCPAdapter()
        
        # 检查初始状态
        assert adapter.app_id is not None
        assert adapter.app_secret is not None
        assert adapter.is_connected is False
        assert len(adapter.tools_cache) == 0
    
    @pytest.mark.asyncio
    async def test_calendar_operation_extraction(self):
        """测试日历操作信息提取"""
        
        # 测试创建事件
        create_input = "明天下午3点安排一个项目会议"
        operation = await _extract_calendar_operation("", create_input)
        assert operation.get("action") == "create"
        
        # 测试查询事件
        query_input = "查看今天的安排"
        operation = await _extract_calendar_operation("", query_input)
        assert operation.get("action") == "query"
        
        # 测试搜索事件
        search_input = "搜索项目相关的会议"
        operation = await _extract_calendar_operation("", search_input)
        assert operation.get("action") == "search"
        
        # 测试删除事件
        delete_input = "取消明天的会议"
        operation = await _extract_calendar_operation("", delete_input)
        assert operation.get("action") == "delete"
    
    @pytest.mark.asyncio
    @patch('core.workflow.nodes.get_feishu_mcp_adapter')
    async def test_mcp_create_event_operation(self, mock_get_adapter):
        """测试MCP创建事件操作"""
        # 设置模拟适配器
        mock_adapter = AsyncMock()
        mock_adapter.create_calendar_event.return_value = {
            "success": True,
            "event_id": "test_event_123",
            "message": "事件创建成功"
        }
        mock_get_adapter.return_value = mock_adapter
        
        # 测试操作信息
        operation_info = {
            "action": "create",
            "title": "测试会议",
            "description": "MCP集成测试",
            "start_time": "2025-07-16T15:00:00",
            "end_time": "2025-07-16T16:00:00",
            "location": "会议室A"
        }
        
        # 执行操作
        result = await _execute_calendar_mcp_operation(operation_info)
        
        # 验证结果
        assert result["success"] is True
        assert result["event_id"] == "test_event_123"
        assert "成功" in result["message"]
        
        # 验证MCP适配器被正确调用
        mock_adapter.create_calendar_event.assert_called_once()
        call_args = mock_adapter.create_calendar_event.call_args[0][0]
        assert call_args["title"] == "测试会议"
        assert call_args["start_time"] == "2025-07-16T15:00:00"
    
    @pytest.mark.asyncio
    @patch('core.workflow.nodes.get_feishu_mcp_adapter')
    async def test_mcp_list_calendars_operation(self, mock_get_adapter):
        """测试MCP获取日历列表操作"""
        # 设置模拟适配器
        mock_adapter = AsyncMock()
        mock_adapter.list_calendars.return_value = {
            "success": True,
            "calendars": [
                {"calendar_id": "cal1", "summary": "工作日历"},
                {"calendar_id": "cal2", "summary": "个人日历"}
            ],
            "message": "获取日历列表成功"
        }
        mock_get_adapter.return_value = mock_adapter
        
        # 测试操作信息
        operation_info = {
            "action": "query"
        }
        
        # 执行操作
        result = await _execute_calendar_mcp_operation(operation_info)
        
        # 验证结果
        assert result["success"] is True
        assert len(result["calendars"]) == 2
        assert result["calendars"][0]["summary"] == "工作日历"
        
        # 验证MCP适配器被正确调用
        mock_adapter.list_calendars.assert_called_once()
    
    @pytest.mark.asyncio
    @patch('core.workflow.nodes.get_feishu_mcp_adapter')
    async def test_mcp_search_events_operation(self, mock_get_adapter):
        """测试MCP搜索事件操作"""
        # 设置模拟适配器
        mock_adapter = AsyncMock()
        mock_adapter.search_calendar_events.return_value = {
            "success": True,
            "events": [
                {"event_id": "event1", "summary": "项目会议", "start_time": "2025-07-16T15:00:00"},
                {"event_id": "event2", "summary": "项目评审", "start_time": "2025-07-17T10:00:00"}
            ],
            "message": "搜索事件成功"
        }
        mock_get_adapter.return_value = mock_adapter
        
        # 测试操作信息
        operation_info = {
            "action": "search",
            "title": "项目",
            "description": "搜索项目相关的会议"
        }
        
        # 执行操作
        result = await _execute_calendar_mcp_operation(operation_info)
        
        # 验证结果
        assert result["success"] is True
        assert len(result["events"]) == 2
        assert "项目会议" in result["events"][0]["summary"]
        
        # 验证MCP适配器被正确调用
        mock_adapter.search_calendar_events.assert_called_once_with("项目")
    
    @pytest.mark.asyncio
    @patch('core.workflow.nodes.get_llm')
    @patch('core.workflow.nodes.get_feishu_mcp_adapter')
    async def test_calendar_processor_node_with_mcp(self, mock_get_adapter, mock_get_llm):
        """测试集成MCP的日历处理器节点"""
        # 设置模拟LLM响应
        mock_llm = AsyncMock()
        mock_llm.invoke.return_value.content = '''
        根据您的请求，我将为您创建一个项目会议。
        
        {
            "action": "create",
            "title": "项目进度会议",
            "start_time": "2025-07-16T15:00:00",
            "end_time": "2025-07-16T16:00:00",
            "location": "会议室A",
            "description": "讨论项目进度和下一步计划"
        }
        '''
        mock_get_llm.return_value = mock_llm
        
        # 设置模拟MCP适配器
        mock_adapter = AsyncMock()
        mock_adapter.create_calendar_event.return_value = {
            "success": True,
            "event_id": "created_event_456",
            "message": "事件创建成功"
        }
        mock_get_adapter.return_value = mock_adapter
        
        # 测试状态
        state = {
            "processed_text": "明天下午3点安排项目进度会议",
            "intent": "calendar",
            "confidence": 0.9
        }
        
        # 执行节点处理
        result = await calendar_processor_node(state)
        
        # 验证结果
        assert result["intent"] == "calendar"
        assert result["data"]["processed"] is True
        assert "mcp_result" in result["data"]
        assert result["data"]["mcp_result"]["success"] is True
        assert "✅" in result["message"]  # 成功标识
        
        # 验证LLM和MCP适配器被调用
        mock_llm.invoke.assert_called_once()
        mock_adapter.create_calendar_event.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_error_handling_in_mcp_operations(self):
        """测试MCP操作中的错误处理"""
        
        # 测试缺少必要信息的情况
        operation_info = {
            "action": "create",
            "title": "测试会议",
            # 缺少start_time
        }
        
        result = await _execute_calendar_mcp_operation(operation_info)
        
        # 验证错误处理
        assert result["success"] is False
        assert "时间" in result["message"]
        assert result["need_more_info"] is True
    
    @pytest.mark.asyncio
    async def test_natural_language_to_mcp_workflow(self):
        """测试自然语言到MCP的完整工作流"""
        
        # 模拟自然语言输入
        natural_inputs = [
            "明天下午3点安排一个项目会议",
            "查看今天的日程安排", 
            "搜索本周的项目相关会议",
            "取消明天上午的培训"
        ]
        
        expected_actions = ["create", "query", "search", "delete"]
        
        for i, input_text in enumerate(natural_inputs):
            # 提取操作信息
            operation = await _extract_calendar_operation("", input_text)
            
            # 验证操作类型正确识别
            assert operation.get("action") == expected_actions[i], f"输入 '{input_text}' 的操作类型识别错误"
    
    def test_mcp_adapter_singleton(self):
        """测试MCP适配器单例模式"""
        adapter1 = get_feishu_mcp_adapter()
        adapter2 = get_feishu_mcp_adapter()
        
        # 验证返回同一个实例
        assert adapter1 is adapter2


class TestMCPIntegrationE2E:
    """MCP集成端到端测试"""
    
    @pytest.mark.asyncio
    @pytest.mark.integration
    async def test_real_mcp_service_connection(self):
        """测试真实MCP服务连接（需要真实环境）"""
        # 这个测试需要真实的飞书MCP服务运行
        # 在CI/CD中可以跳过，在本地开发时手动运行
        
        try:
            adapter = FeishuMCPAdapter()
            await adapter.start_mcp_service()
            
            # 测试获取工具列表
            tools = adapter.get_available_tools()
            assert len(tools) > 0
            
            # 测试获取日历列表
            result = await adapter.list_calendars()
            assert "success" in result
            
            await adapter.close()
            
        except Exception as e:
            # 如果没有真实环境，跳过测试
            pytest.skip(f"跳过真实MCP服务测试: {e}")


if __name__ == "__main__":
    # 运行特定测试
    asyncio.run(TestMCPIntegration().test_calendar_operation_extraction())
    print("✅ MCP集成测试通过")
