"""
日历相关数据模型
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from .base import BaseModel


class TimeInfo(BaseModel):
    """时间信息模型"""

    date_time: Optional[str] = None  # ISO格式时间字符串
    timestamp: Optional[str] = None  # 时间戳
    timezone: Optional[str] = "Asia/Shanghai"


class Location(BaseModel):
    """地点信息模型"""

    name: str
    address: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None


class Reminder(BaseModel):
    """提醒设置模型"""

    minutes: int  # 提前多少分钟提醒


class Attendee(BaseModel):
    """参与者模型"""

    user_id: Optional[str] = None
    email: Optional[str] = None
    name: Optional[str] = None
    status: Optional[str] = (
        "needs_action"  # needs_action, accepted, declined, tentative
    )


class CalendarEvent(BaseModel):
    """日历事件模型"""

    id: Optional[str] = None
    calendar_id: Optional[str] = None
    summary: str
    description: Optional[str] = None
    start_time: TimeInfo
    end_time: TimeInfo
    location: Optional[Location] = None
    reminders: List[Reminder] = []
    attendees: List[Attendee] = []
    is_all_day: bool = False
    status: str = "confirmed"  # confirmed, tentative, cancelled
    recurrence: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class Calendar(BaseModel):
    """日历模型"""

    id: Optional[str] = None
    name: str
    description: Optional[str] = None
    color: str = "#1976d2"
    is_default: bool = False
    permissions: str = "private"  # private, show_only_free_busy, public
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
