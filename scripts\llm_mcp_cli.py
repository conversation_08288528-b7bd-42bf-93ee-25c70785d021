"""
LLM+本地MCP交互命令行工具
结合硅基流动LLM和本地calmcp MCP服务，提供自然语言日历操作界面
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

import asyncio
import json
import logging
import requests
from datetime import datetime
from typing import Dict, List, Any, Optional

from core.ai.llm_client import LLMClient
from models.chat import LLMResponse, ToolCall

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class LocalMCPClient:
    """本地MCP客户端 - 连接到calmcp服务"""

    def __init__(self, base_url: str = "http://localhost:3000"):
        """初始化本地MCP客户端"""
        self.base_url = base_url
        self.tools_cache = []
        self.is_connected = False

    async def connect(self):
        """连接到本地MCP服务"""
        try:
            # 获取工具列表
            response = requests.get(f"{self.base_url}/api/tools")
            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.tools_cache = data.get("tools", [])
                    self.is_connected = True
                    logger.info(f"✅ 成功连接到本地MCP服务，加载了 {len(self.tools_cache)} 个工具")
                    return True

            raise Exception(f"连接失败: {response.status_code}")

        except Exception as e:
            logger.error(f"连接本地MCP服务失败: {str(e)}")
            raise

    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            url = f"{self.base_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }

            response = requests.post(url, json=payload)
            if response.status_code == 200:
                return response.json()
            else:
                return {"error": f"API调用失败: {response.status_code}"}

        except Exception as e:
            return {"error": f"工具调用失败: {str(e)}"}

    async def close(self):
        """关闭连接"""
        self.is_connected = False
        logger.info("本地MCP客户端连接已关闭")


class LLMMCPChatInterface:
    """LLM+本地MCP聊天界面"""

    def __init__(self):
        """初始化聊天界面"""
        self.llm_client = LLMClient()
        self.mcp_client = LocalMCPClient()
        self.conversation_history = []
        self.is_mcp_connected = False
        
    async def start(self):
        """启动服务"""
        try:
            print("🚀 正在启动飞书智能日历助手...")
            print("📡 连接本地MCP服务...")

            # 连接到本地MCP服务
            await self.mcp_client.connect()
            self.is_mcp_connected = True

            print("✅ 本地MCP服务连接成功！")
            print("🤖 LLM客户端已就绪！")
            print("\n" + "="*60)
            print("🎉 飞书智能日历助手已启动！")
            print("💡 您可以用自然语言操作您的飞书日历，例如：")
            print("   • '查看我今天的日程'")
            print("   • '明天下午2点安排一个会议'")
            print("   • '删除今天的某个会议'")
            print("   • '查看我的所有日历'")
            print("   • '搜索包含项目的会议'")
            print("\n💬 输入 'help' 查看更多命令，输入 'quit' 退出")
            print("="*60)

        except Exception as e:
            logger.error(f"启动失败: {str(e)}")
            print(f"❌ 启动失败: {str(e)}")
            raise
    
    async def stop(self):
        """停止服务"""
        try:
            if self.is_mcp_connected:
                await self.mcp_client.close()
                print("🔌 MCP服务已断开")
        except Exception as e:
            logger.error(f"停止服务时出错: {str(e)}")
    
    def _prepare_mcp_tools_for_llm(self) -> List[Dict[str, Any]]:
        """将MCP工具转换为LLM可用的工具格式"""
        if not self.mcp_client.tools_cache:
            return []

        llm_tools = []
        for tool in self.mcp_client.tools_cache:
            # 注意：LLM客户端的_prepare_tools方法会再次包装，所以这里只需要function部分
            llm_tool = {
                "name": tool["name"],
                "description": tool["description"],
                "parameters": tool.get("inputSchema", {})
            }
            llm_tools.append(llm_tool)

        return llm_tools
    
    async def _execute_mcp_tool(self, tool_call: ToolCall) -> Dict[str, Any]:
        """执行MCP工具调用"""
        try:
            print(f"🔧 执行工具: {tool_call.function.name}")
            print(f"📝 参数: {tool_call.function.arguments}")

            # 直接调用本地MCP服务
            result = await self.mcp_client.call_tool(
                tool_call.function.name,
                tool_call.function.arguments
            )

            print(f"✅ 工具执行成功")
            return result

        except Exception as e:
            error_msg = f"工具执行失败: {str(e)}"
            logger.error(error_msg)
            print(f"❌ {error_msg}")
            return {"error": error_msg}
    
    async def process_message(self, user_input: str) -> str:
        """处理用户消息"""
        try:
            # 添加用户消息到对话历史
            self.conversation_history.append({
                "role": "user",
                "content": user_input
            })
            
            # 准备系统提示
            system_prompt = {
                "role": "system",
                "content": """你是一个飞书日历智能助手。你可以帮助用户管理飞书日历和日程。

可用的工具包括：
- calendar.v4.calendar.list: 获取用户的日历列表
- calendar.v4.calendar.create: 创建新日历
- calendar.v4.calendar.search: 搜索日历
- calendarEvent.list: 获取日历事件列表
- calendar.v4.calendarEvent.create: 创建新的日历事件
- calendar.v4.calendarEvent.search: 搜索日历事件

请根据用户的自然语言请求，选择合适的工具来完成任务。
如果需要调用工具，请使用function calling。
如果不需要调用工具，请直接回复用户。

注意：
1. 创建事件时需要指定calendar_id，请先获取日历列表
2. 时间格式使用ISO 8601格式，如：2024-01-15T14:00:00+08:00
3. 搜索时可以使用关键词匹配
4. 回复要友好、简洁、有用"""
            }
            
            # 准备消息列表
            messages = [system_prompt] + self.conversation_history[-10:]  # 保留最近10轮对话
            
            # 准备工具列表
            tools = self._prepare_mcp_tools_for_llm()
            
            # 调用LLM
            print("🤔 正在思考...")
            response = self.llm_client.invoke(messages, tools)
            
            # 处理响应
            if response.tool_calls:
                # 有工具调用
                tool_results = []
                for tool_call in response.tool_calls:
                    result = await self._execute_mcp_tool(tool_call)
                    tool_results.append({
                        "tool_call_id": tool_call.id,
                        "result": result
                    })
                
                # 将工具调用结果添加到对话历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response.content,
                    "tool_calls": [tc.to_dict() for tc in response.tool_calls]
                })
                
                for tool_result in tool_results:
                    self.conversation_history.append({
                        "role": "tool",
                        "content": json.dumps(tool_result["result"], ensure_ascii=False),
                        "tool_call_id": tool_result["tool_call_id"]
                    })
                
                # 再次调用LLM获取最终回复
                messages = [system_prompt] + self.conversation_history[-15:]
                final_response = self.llm_client.invoke(messages)
                
                # 添加最终回复到对话历史
                self.conversation_history.append({
                    "role": "assistant",
                    "content": final_response.content
                })
                
                return final_response.content
            else:
                # 没有工具调用，直接回复
                self.conversation_history.append({
                    "role": "assistant",
                    "content": response.content
                })
                
                return response.content
                
        except Exception as e:
            error_msg = f"处理消息时出错: {str(e)}"
            logger.error(error_msg)
            return f"❌ {error_msg}"
    
    def print_help(self):
        """打印帮助信息"""
        help_text = """
🆘 飞书智能日历助手 - 帮助信息

📋 基本命令：
  help          - 显示此帮助信息
  quit/exit/q   - 退出程序
  clear         - 清屏
  history       - 显示对话历史

🗓️ 日历操作示例：
  查看我的日历列表
  查看我今天的日程
  明天下午2点安排一个会议，主题是项目讨论
  搜索包含"会议"的日程
  创建一个新的日历叫"个人日程"
  删除今天下午3点的会议

💡 使用技巧：
  • 可以使用自然语言描述您的需求
  • 支持相对时间表达，如"明天"、"下周一"等
  • 可以指定具体的时间和日期
  • 支持搜索和筛选功能

🔧 技术信息：
  • LLM模型：硅基流动 Qwen/Qwen3-14B
  • MCP服务：飞书日历工具集
  • 支持的操作：查看、创建、搜索日历和事件
        """
        print(help_text)


async def llm_mcp_interactive_cli():
    """LLM+MCP交互式命令行界面"""
    chat_interface = LLMMCPChatInterface()
    
    try:
        # 启动服务
        await chat_interface.start()
        
        # 主循环
        while True:
            try:
                # 获取用户输入
                user_input = input("\n🤖 您: ").strip()
                
                if not user_input:
                    continue
                
                # 处理特殊命令
                if user_input.lower() in ["quit", "exit", "q"]:
                    print("👋 感谢使用飞书智能日历助手，再见！")
                    break
                elif user_input.lower() == "help":
                    chat_interface.print_help()
                    continue
                elif user_input.lower() == "clear":
                    os.system("cls" if os.name == "nt" else "clear")
                    continue
                elif user_input.lower() == "history":
                    print("\n📜 对话历史：")
                    for i, msg in enumerate(chat_interface.conversation_history[-10:], 1):
                        role = "👤 用户" if msg["role"] == "user" else "🤖 助手"
                        print(f"{i}. {role}: {msg['content'][:100]}...")
                    continue
                
                # 处理用户消息
                response = await chat_interface.process_message(user_input)
                print(f"\n🤖 助手: {response}")
                
            except KeyboardInterrupt:
                print("\n\n👋 检测到中断信号，正在退出...")
                break
            except Exception as e:
                logger.error(f"处理用户输入时出错: {str(e)}")
                print(f"\n❌ 出现错误: {str(e)}")
                print("💡 请重试或输入 'help' 查看帮助")
    
    finally:
        # 清理资源
        await chat_interface.stop()


async def mcp_interactive_cli():
    """纯MCP交互式命令行界面（用于调试）"""
    mcp_client = LocalMCPClient()

    try:
        print("🚀 启动MCP调试界面...")
        await mcp_client.connect()

        print("✅ 本地MCP服务已连接")
        print("🔧 可用工具:")
        for tool in mcp_client.tools_cache:
            print(f"  • {tool['name']}: {tool['description']}")

        print("\n💡 输入工具名称和参数（JSON格式），或输入 'quit' 退出")
        print("📝 示例: calendar.v4.calendar.list {}")

        while True:
            try:
                user_input = input("\n🔧 MCP> ").strip()

                if user_input.lower() in ["quit", "exit", "q"]:
                    break

                if not user_input:
                    continue

                # 解析输入（简单格式：tool_name {"param": "value"}）
                parts = user_input.split(" ", 1)
                tool_name = parts[0]

                if len(parts) > 1:
                    try:
                        arguments = json.loads(parts[1])
                    except json.JSONDecodeError:
                        print("❌ 参数格式错误，请使用JSON格式")
                        continue
                else:
                    arguments = {}

                # 调用工具
                result = await mcp_client.call_tool(tool_name, arguments)
                print(f"✅ 结果: {json.dumps(result, ensure_ascii=False, indent=2)}")

            except KeyboardInterrupt:
                print("\n👋 退出中...")
                break
            except Exception as e:
                print(f"❌ 错误: {str(e)}")

    finally:
        await mcp_client.close()


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="LLM+MCP交互工具")
    parser.add_argument("--mode", choices=["llm", "mcp"], default="llm", 
                       help="选择模式：llm（智能对话）或 mcp（调试模式）")
    
    args = parser.parse_args()
    
    if args.mode == "llm":
        asyncio.run(llm_mcp_interactive_cli())
    else:
        asyncio.run(mcp_interactive_cli())
