"""
意图识别器
基于LLM的智能意图识别
"""

import json
import logging
from enum import Enum
from typing import Any, Dict

from models.workflow import IntentResult
from utils.prompts import INTENT_CLASSIFIER_PROMPT

from .llm_client import get_llm

logger = logging.getLogger(__name__)


class IntentType(Enum):
    """意图类型枚举"""
    CALENDAR_CREATE = "calendar_create"
    CALENDAR_QUERY = "calendar_query"
    CALENDAR_UPDATE = "calendar_update"
    CALENDAR_DELETE = "calendar_delete"
    EVENT_CREATE = "event_create"
    EVENT_QUERY = "event_query"
    EVENT_UPDATE = "event_update"
    EVENT_DELETE = "event_delete"
    CHAT = "chat"
    HELP = "help"
    UNKNOWN = "unknown"


class IntentClassifier:
    """意图识别器"""

    def __init__(self):
        """初始化意图识别器"""
        self.llm = get_llm()

    async def classify(self, text: str, context: Dict[str, Any] = None) -> IntentResult:
        """
        识别文本意图

        Args:
            text: 输入文本
            context: 上下文信息

        Returns:
            意图识别结果
        """
        try:
            # 准备消息
            messages = [
                {"role": "system", "content": INTENT_CLASSIFIER_PROMPT},
                {"role": "user", "content": text},
            ]

            # 调用LLM
            response = self.llm.invoke(messages)

            # 解析响应
            content = response.content.strip()
            if content.startswith("```json"):
                content = content[7:]
            if content.endswith("```"):
                content = content[:-3]
            content = content.strip()

            try:
                result = json.loads(content)
                return IntentResult(
                    intent=result.get("intent", "chat"),
                    confidence=result.get("confidence", 0.8),
                    entities=result.get("entities", {}),
                )
            except json.JSONDecodeError:
                # 降级处理
                return self._fallback_classification(text)

        except Exception as e:
            logger.error(f"意图识别失败: {str(e)}")
            return self._fallback_classification(text)

    def _fallback_classification(self, text: str) -> IntentResult:
        """
        降级意图识别

        Args:
            text: 输入文本

        Returns:
            意图识别结果
        """
        text_lower = text.lower()

        # 基于关键词的简单分类
        if any(
            word in text_lower
            for word in ["日程", "会议", "安排", "提醒", "约会", "日历", "查询"]
        ):
            return IntentResult(intent="calendar", confidence=0.7)
        elif any(word in text_lower for word in ["日记", "记录", "笔记"]):
            return IntentResult(intent="journal", confidence=0.7)
        elif any(word in text_lower for word in ["写", "创作", "诗", "故事"]):
            return IntentResult(intent="media", confidence=0.7)
        else:
            return IntentResult(intent="chat", confidence=0.8)
