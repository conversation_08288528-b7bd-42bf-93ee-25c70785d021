# 飞书官方 MCP 工具集成指南

## 概述

本项目已集成飞书官方 MCP 开源项目中的日历工具定义，提供完整的飞书日历 v4 API 支持。

## 文件结构

```
src/lib/tools/
├── feishu-calendar-v4.ts    # 飞书官方工具定义（43个工具）
├── calendar-v4-tools.ts     # 重新导出官方工具
├── tool-adapter.ts          # 工具适配器
└── mcp-tools.ts            # 当前项目的工具定义
```

## 集成方案

### 1. 直接使用 TypeScript

**优势**：
- ✅ 无需编译，Next.js 原生支持 TypeScript
- ✅ 类型安全，完整的类型检查
- ✅ 开发体验好，IDE 智能提示
- ✅ 热重载支持

**当前项目配置**：
- Next.js 13+ 原生支持 TypeScript
- 已配置 `tsconfig.json`
- 构建时自动编译

### 2. 工具定义对比

#### 官方工具 vs 当前工具

| 特性 | 官方工具 | 当前工具 |
|------|----------|----------|
| 工具数量 | 43个完整工具 | 7个简化工具 |
| 参数验证 | Zod 完整验证 | 基础验证 |
| 类型安全 | 完整 TypeScript | 基础类型 |
| 文档完整性 | 官方文档 | 简化描述 |
| 维护性 | 官方维护 | 手动维护 |

#### 建议的迁移策略

1. **渐进式迁移**：
   - 保留当前简化工具用于基础功能
   - 逐步引入官方工具用于高级功能

2. **混合使用**：
   - 常用操作：使用简化工具（快速响应）
   - 复杂操作：使用官方工具（功能完整）

## 使用方法

### 1. 导入工具定义

```typescript
// 导入官方工具
import * as FeishuTools from '@/lib/tools/feishu-calendar-v4';

// 导入适配器
import { COMMON_CALENDAR_TOOLS, validateToolArguments } from '@/lib/tools/tool-adapter';
```

### 2. 工具调用示例

```typescript
// 使用官方工具定义
const calendarListTool = FeishuTools.calendarV4CalendarList;

// 参数验证
const args = { page_size: 50 };
const validation = validateToolArguments('calendar.v4.calendar.list', args);

if (validation.valid) {
  // 调用 API
  const result = await feishuClient.callTool(calendarListTool, args);
}
```

### 3. 在 MCP 服务中使用

```typescript
// 更新 mcp-tools.ts
import { COMMON_CALENDAR_TOOLS } from './tools/tool-adapter';

export const ALL_TOOLS = [
  ...COMMON_CALENDAR_TOOLS,  // 官方工具
  // ...其他自定义工具
];
```

## 工具列表

### 核心工具（推荐优先使用）

1. **日历管理**
   - `calendar.v4.calendar.list` - 获取日历列表
   - `calendar.v4.calendar.get` - 获取单个日历
   - `calendar.v4.calendar.create` - 创建日历

2. **日程管理**
   - `calendar.v4.calendarEvent.list` - 获取日程列表
   - `calendar.v4.calendarEvent.get` - 获取单个日程
   - `calendar.v4.calendarEvent.create` - 创建日程
   - `calendar.v4.calendarEvent.patch` - 更新日程
   - `calendar.v4.calendarEvent.delete` - 删除日程
   - `calendar.v4.calendarEvent.search` - 搜索日程

### 高级工具

3. **访问控制**
   - `calendar.v4.calendarAcl.*` - 日历权限管理

4. **参与人管理**
   - `calendar.v4.calendarEventAttendee.*` - 参与人管理

5. **会议功能**
   - `calendar.v4.calendarEventMeetingChat.*` - 会议群管理
   - `calendar.v4.calendarEventMeetingMinute.*` - 会议纪要

## 参数说明

### 通用参数

- `useUAT`: 是否使用用户访问令牌（推荐设为 true）
- `user_id_type`: 用户 ID 类型（open_id, union_id, user_id）
- `page_size`: 分页大小（注意：日历列表最小值为 50）

### 认证说明

官方工具支持两种认证方式：
- `tenant`: 应用访问令牌（访问应用级资源）
- `user`: 用户访问令牌（访问个人资源，推荐）

## 迁移建议

### 短期（当前）
1. 保持现有简化工具不变
2. 在新功能中使用官方工具
3. 测试官方工具的稳定性

### 中期（1-2周）
1. 逐步替换核心工具
2. 添加参数验证
3. 完善错误处理

### 长期（1个月）
1. 完全迁移到官方工具
2. 移除自定义工具定义
3. 利用官方工具的高级功能

## 注意事项

1. **参数验证**：官方工具使用 Zod 进行严格验证
2. **错误处理**：需要处理 Zod 验证错误
3. **类型安全**：充分利用 TypeScript 类型系统
4. **文档参考**：参考飞书官方 API 文档

## 测试

```bash
# 测试官方工具集成
npm run test:tools

# 测试特定工具
npm run test:calendar-list
```
