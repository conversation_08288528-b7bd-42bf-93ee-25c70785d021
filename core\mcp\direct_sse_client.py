#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接SSE MCP客户端
绕过langchain_mcp_adapters，直接与您的飞书MCP服务通信
"""

import asyncio
import json
import logging
import httpx
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class DirectSSEMCPClient:
    """直接SSE MCP客户端 - 直接与飞书MCP服务通信"""
    
    def __init__(self, sse_url: str = "http://localhost:3000/sse"):
        self.sse_url = sse_url
        self.is_connected = False
        self.tools_cache = []
        self.session = None
        self.request_id = 0
        
    async def start_mcp_service(self):
        """连接到MCP服务"""
        try:
            logger.info(f"直接连接到飞书MCP SSE服务: {self.sse_url}")
            
            # 创建HTTP会话
            self.session = httpx.AsyncClient(timeout=30.0)
            
            # 测试连接并获取工具列表
            await self._initialize_connection()
            
            self.is_connected = True
            logger.info(f"✅ 成功连接到飞书MCP服务")
            
        except Exception as e:
            logger.error(f"连接MCP服务失败: {str(e)}")
            raise
    
    async def _initialize_connection(self):
        """初始化MCP连接"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "feishu-calendar-assistant",
                        "version": "1.0.0"
                    }
                }
            }
            
            # 通过POST发送JSON-RPC请求到SSE端点
            response = await self._send_json_rpc_request(init_request)
            logger.info("MCP连接初始化成功")
            
            # 获取工具列表
            await self._load_tools()
            
        except Exception as e:
            logger.error(f"MCP连接初始化失败: {str(e)}")
            raise
    
    async def _send_json_rpc_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送JSON-RPC请求到SSE端点"""
        try:
            # 使用提供的token
            token = "u-cxDYYm6pd9QFhdaltgUW9HhkjylB1kaXW200glCw03nZ"
            headers = {
                "Authorization": f"Bearer {token}",
                "Content-Type": "application/json",
                "Accept": "application/json"
            }

            # 尝试POST请求到SSE端点
            response = await self.session.post(
                self.sse_url,
                json=request,
                headers=headers
            )

            logger.info(f"请求: {request}")
            logger.info(f"响应状态: {response.status_code}")
            logger.info(f"响应内容: {response.text}")

            if response.status_code == 200:
                return response.json()
            else:
                # 如果SSE端点不工作，尝试其他可能的端点
                for endpoint in ["/mcp", "/rpc", "/jsonrpc", ""]:
                    try:
                        url = self.sse_url.replace("/sse", endpoint) if endpoint else self.sse_url.replace("/sse", "")
                        resp = await self.session.post(url, json=request, headers=headers)
                        logger.info(f"尝试 {url}: {resp.status_code}")
                        if resp.status_code == 200:
                            logger.info(f"成功连接到 {url}")
                            return resp.json()
                    except Exception as e:
                        logger.debug(f"端点 {endpoint} 失败: {e}")

                raise Exception(f"所有端点都失败，最后状态码: {response.status_code}")

        except Exception as e:
            logger.error(f"发送JSON-RPC请求失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """加载可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self._send_json_rpc_request(request)
            tools = response.get("result", {}).get("tools", [])
            
            # 转换为简单的工具信息
            for tool in tools:
                self.tools_cache.append({
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                    "schema": tool.get("inputSchema", {})
                })
            
            logger.info(f"加载了 {len(self.tools_cache)} 个工具: {[t['name'] for t in self.tools_cache]}")
            
        except Exception as e:
            logger.error(f"加载工具失败: {str(e)}")
            # 不抛出异常，继续运行
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """调用MCP工具"""
        try:
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_json_rpc_request(request)
            result = response.get("result", {})
            
            logger.info(f"工具调用成功: {tool_name}")
            return result
            
        except Exception as e:
            logger.error(f"工具调用失败 {tool_name}: {str(e)}")
            raise
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            # 查找日历列表工具
            calendar_tools = [t for t in self.tools_cache if "calendar.list" in t["name"] or "calendar_list" in t["name"]]
            
            if not calendar_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到获取日历列表的工具。可用工具: {available_tools}"
                }
            
            tool_name = calendar_tools[0]["name"]
            logger.info(f"调用工具 {tool_name}")
            
            # 调用工具
            result = await self.call_tool(tool_name, {})
            
            logger.info(f"工具调用结果: {result}")
            
            calendars = result.get("data", {}).get("calendar_list", [])
            
            return {
                "success": True,
                "calendars": calendars,
                "message": f"获取到 {len(calendars)} 个日历",
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"获取日历列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历列表失败: {str(e)}"
            }
    
    async def close(self):
        """关闭连接"""
        if self.session:
            await self.session.aclose()
        self.is_connected = False
        logger.info("MCP客户端连接已关闭")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool["name"] for tool in self.tools_cache]


# 全局客户端实例
_direct_sse_client: Optional[DirectSSEMCPClient] = None

def get_direct_sse_client() -> DirectSSEMCPClient:
    """获取直接SSE MCP客户端实例"""
    global _direct_sse_client
    if _direct_sse_client is None:
        _direct_sse_client = DirectSSEMCPClient()
    return _direct_sse_client


async def test_direct_sse_client():
    """测试直接SSE MCP客户端"""
    client = get_direct_sse_client()
    
    try:
        # 启动服务
        await client.start_mcp_service()
        
        # 显示可用工具
        tools = client.get_available_tools()
        print(f"可用工具: {tools}")
        
        # 测试获取日历列表
        calendars_result = await client.list_calendars()
        print(f"日历列表: {calendars_result}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
    finally:
        await client.close()


if __name__ == "__main__":
    asyncio.run(test_direct_sse_client())
