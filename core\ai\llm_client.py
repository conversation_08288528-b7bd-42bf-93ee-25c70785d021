"""
LLM客户端
统一的大语言模型调用接口
"""

import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))

import json
import logging
from typing import Any, Dict, List, Optional

import requests

from config import LLM_MAX_TOKENS, LLM_MODEL, LLM_TEMPERATURE, SILICONFLOW_API_KEY
from models.chat import LLMResponse, ToolCall

# 配置日志
logger = logging.getLogger(__name__)

# 硅基流动API端点
API_URL = "https://api.siliconflow.cn/v1/chat/completions"


class LLMClient:
    """硅基流动LLM客户端"""

    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """
        初始化LLM客户端

        Args:
            api_key: 硅基流动API密钥，默认使用配置文件中的密钥
            model: 使用的模型，默认使用配置文件中的模型
        """
        self.api_key = api_key or SILICONFLOW_API_KEY
        self.model = model or LLM_MODEL
        self.headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.api_key}",
        }

    def invoke(
        self,
        messages: List[Dict[str, str]],
        tools: Optional[List[Dict[str, Any]]] = None,
    ) -> LLMResponse:
        """
        调用LLM API

        Args:
            messages: 消息列表
            tools: 工具列表（可选）

        Returns:
            LLM响应
        """
        # 准备请求数据
        payload = {
            "model": self.model,
            "messages": messages,
            "temperature": LLM_TEMPERATURE,
            "max_tokens": LLM_MAX_TOKENS,
        }

        # 添加工具（如果有）
        if tools:
            payload["tools"] = self._prepare_tools(tools)

        # 记录请求信息
        logger.debug("=== LLM API Request ===")
        logger.debug(f"URL: {API_URL}")
        logger.debug(f"Model: {self.model}")
        logger.debug(f"Messages: {json.dumps(messages, indent=2, ensure_ascii=False)}")

        try:
            # 发送请求
            response = requests.post(API_URL, headers=self.headers, json=payload)

            # 记录响应信息
            logger.debug("=== LLM API Response ===")
            logger.debug(f"Status Code: {response.status_code}")

            response.raise_for_status()

            # 解析响应
            response_data = response.json()
            choice = response_data["choices"][0]
            message = choice["message"]

            # 提取工具调用
            tool_calls = []
            if "tool_calls" in message and message["tool_calls"]:
                for tool_call in message["tool_calls"]:
                    tool_calls.append(
                        ToolCall(
                            name=tool_call["function"]["name"],
                            args=json.loads(tool_call["function"]["arguments"]),
                            id=tool_call.get("id"),
                        )
                    )

            return LLMResponse(
                content=message.get("content", ""), tool_calls=tool_calls
            )

        except requests.exceptions.RequestException as e:
            logger.error(f"LLM API请求失败: {str(e)}")
            return LLMResponse(content=f"LLM服务暂时不可用: {str(e)}", tool_calls=[])
        except Exception as e:
            logger.error(f"LLM API调用异常: {str(e)}")
            return LLMResponse(
                content=f"处理LLM响应时发生错误: {str(e)}", tool_calls=[]
            )

    def _prepare_tools(self, tools: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        准备工具定义

        Args:
            tools: 工具列表

        Returns:
            格式化的工具定义
        """
        formatted_tools = []
        for tool in tools:
            formatted_tools.append({"type": "function", "function": tool})
        return formatted_tools


# 创建默认LLM客户端实例
_default_llm_client = None


def get_llm() -> LLMClient:
    """
    获取默认LLM客户端实例

    Returns:
        LLM客户端实例
    """
    global _default_llm_client
    if _default_llm_client is None:
        _default_llm_client = LLMClient()
    return _default_llm_client


if __name__ == "__main__":
    """测试LLM连接和使用"""
    import sys
    import os
    sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../..")))
    
    print("=" * 50)
    print("🧪 LLM连接测试")
    print("=" * 50)
    
    try:
        # 获取LLM客户端
        llm = get_llm()
        print(f"✅ LLM客户端初始化成功")
        print(f"📋 模型: {llm.model}")
        print(f"🔑 API Key: {llm.api_key[:10]}...")
        
        # 测试简单对话
        print("\n🔄 测试简单对话...")
        messages = [{"role": "user", "content": "你好，请简单回复一下"}]
        response = llm.invoke(messages)
        print(f"✅ LLM响应: {response.content}")
        
        # 测试工具调用
        print("\n🔄 测试工具调用...")
        tools = [
            {
                "name": "test_tool",
                "description": "测试工具",
                "inputSchema": {
                    "type": "object",
                    "properties": {
                        "param": {"type": "string", "description": "测试参数"}
                    },
                    "required": []
                }
            }
        ]
        messages = [{"role": "user", "content": "请调用test_tool工具，参数是hello"}]
        response = llm.invoke(messages, tools=tools)
        print(f"✅ LLM响应: {response.content}")
        if response.tool_calls:
            for tool_call in response.tool_calls:
                print(f"🛠️  工具调用: {tool_call.name}, 参数: {tool_call.args}")
        else:
            print("⚠️  没有工具调用")
            
        print("\n🎉 LLM测试完成，一切正常！")
        
    except Exception as e:
        print(f"❌ LLM测试失败: {e}")
        import traceback
        traceback.print_exc()
