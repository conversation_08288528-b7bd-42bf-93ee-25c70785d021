#!/usr/bin/env python3
"""
测试删除功能
"""

import requests
import json
import sys
import os

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


def test_delete_function():
    """测试删除功能"""
    print("🧪 测试删除功能")
    print("="*60)
    
    # 首先获取一个测试日历
    print("1️⃣ 获取测试日历...")
    
    try:
        response = requests.post(
            "http://localhost:3000/api/mcp/tools/call",
            json={
                "name": "calendar.v4.calendar.list",
                "arguments": {"page_size": 100}
            },
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                content = result.get('result', {}).get('content', [])
                if content:
                    feishu_response = json.loads(content[0].get('text', '{}'))
                    if feishu_response.get('code') == 0:
                        calendars = feishu_response.get('data', {}).get('calendar_list', [])
                        
                        # 找到一个测试日历
                        test_calendar = None
                        for cal in calendars:
                            name = cal.get('summary', '')
                            if '更新后的测试日历' in name:
                                test_calendar = cal
                                break
                        
                        if test_calendar:
                            calendar_id = test_calendar.get('calendar_id')
                            calendar_name = test_calendar.get('summary', '未命名')
                            
                            print(f"✅ 找到测试日历: {calendar_name}")
                            print(f"   ID: {calendar_id}")
                            
                            # 测试删除
                            print(f"\n2️⃣ 测试删除日历...")
                            delete_response = requests.post(
                                "http://localhost:3000/api/mcp/tools/call",
                                json={
                                    "name": "calendar.v4.calendar.delete",
                                    "arguments": {"calendar_id": calendar_id}
                                },
                                timeout=10
                            )
                            
                            print(f"🌐 删除请求状态码: {delete_response.status_code}")
                            
                            if delete_response.status_code == 200:
                                delete_result = delete_response.json()
                                print(f"📄 删除响应: {json.dumps(delete_result, indent=2, ensure_ascii=False)}")
                                
                                if delete_result.get('success'):
                                    print("✅ 删除方法调用成功！")
                                    
                                    # 解析删除结果
                                    delete_content = delete_result.get('result', {}).get('content', [])
                                    if delete_content:
                                        delete_feishu_response = json.loads(delete_content[0].get('text', '{}'))
                                        delete_code = delete_feishu_response.get('code', -1)
                                        delete_msg = delete_feishu_response.get('msg', '无消息')
                                        
                                        print(f"   飞书删除代码: {delete_code}")
                                        print(f"   飞书删除消息: {delete_msg}")
                                        
                                        if delete_code == 0:
                                            print("🎉 日历删除成功！")
                                            
                                            # 验证删除：再次获取日历列表
                                            print(f"\n3️⃣ 验证删除结果...")
                                            verify_response = requests.post(
                                                "http://localhost:3000/api/mcp/tools/call",
                                                json={
                                                    "name": "calendar.v4.calendar.list",
                                                    "arguments": {"page_size": 100}
                                                },
                                                timeout=10
                                            )
                                            
                                            if verify_response.status_code == 200:
                                                verify_result = verify_response.json()
                                                if verify_result.get('success'):
                                                    verify_content = verify_result.get('result', {}).get('content', [])
                                                    if verify_content:
                                                        verify_feishu_response = json.loads(verify_content[0].get('text', '{}'))
                                                        if verify_feishu_response.get('code') == 0:
                                                            verify_calendars = verify_feishu_response.get('data', {}).get('calendar_list', [])
                                                            
                                                            # 检查删除的日历是否还存在
                                                            still_exists = any(cal.get('calendar_id') == calendar_id for cal in verify_calendars)
                                                            
                                                            if not still_exists:
                                                                print(f"✅ 验证成功：日历 '{calendar_name}' 已被删除")
                                                                print(f"   当前日历总数: {len(verify_calendars)}")
                                                            else:
                                                                print(f"⚠️  日历 '{calendar_name}' 仍然存在")
                                                        else:
                                                            print(f"❌ 验证失败: {verify_feishu_response.get('msg')}")
                                                else:
                                                    print(f"❌ 验证调用失败: {verify_result.get('error', '未知错误')}")
                                            else:
                                                print(f"❌ 验证请求失败: {verify_response.status_code}")
                                        else:
                                            print(f"❌ 飞书API删除失败: {delete_msg}")
                                else:
                                    print(f"❌ 删除方法调用失败: {delete_result.get('error', '未知错误')}")
                            else:
                                print(f"❌ 删除请求失败: {delete_response.status_code}")
                                print(f"   响应内容: {delete_response.text}")
                        else:
                            print("⚠️  没有找到'更新后的测试日历'")
                            print("📋 可用的日历:")
                            for i, cal in enumerate(calendars[:10], 1):
                                name = cal.get('summary', '未命名')
                                cal_type = cal.get('type', 'unknown')
                                print(f"  {i}. {name} ({cal_type})")
                    else:
                        print(f"❌ 获取日历列表失败: {feishu_response.get('msg')}")
                else:
                    print("❌ 日历列表响应内容为空")
            else:
                print(f"❌ 日历列表调用失败: {result.get('error', '未知错误')}")
        else:
            print(f"❌ 日历列表请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")


if __name__ == "__main__":
    test_delete_function()
