# 智能日历助手 - 业务测试场景文档

## 📋 文档概述

本文档基于项目PRD需求，设计了完整的业务测试场景流程，覆盖意图识别、聊天、日历、日记、媒体四大核心功能模块，确保系统的稳定性、准确性和用户体验。

## 🎯 测试目标

- **功能完整性**：验证所有业务功能正常工作
- **意图识别准确性**：确保意图识别准确率 > 95%
- **人机协作流畅性**：验证多轮对话和确认机制
- **数据一致性**：确保数据操作的准确性和完整性
- **异常处理能力**：验证系统的容错和恢复能力
- **性能指标**：验证响应时间和并发处理能力

## 📊 测试场景分类

### 1. 基础功能测试
- 单一功能模块测试
- 基本CRUD操作验证
- API接口正确性测试

### 2. 集成测试
- 模块间交互测试
- 端到端业务流程测试
- 第三方服务集成测试

### 3. 用户体验测试
- 多轮对话流程测试
- 人机协作确认机制测试
- 上下文感知能力测试

### 4. 压力和边界测试
- 并发用户处理测试
- 大数据量处理测试
- 异常输入处理测试

## 🔍 测试执行原则

### 执行顺序
1. **前置条件验证** → 环境准备和依赖检查
2. **基础功能测试** → 核心功能单独验证
3. **集成流程测试** → 端到端业务流程
4. **异常场景测试** → 边界和错误处理
5. **性能压力测试** → 并发和大数据量测试

### 数据策略
- **真实数据优先**：使用真实的飞书环境和数据
- **隔离测试环境**：确保测试不影响生产数据
- **数据清理机制**：测试后自动清理测试数据

### 验证标准
- **功能正确性**：输出结果符合预期
- **性能指标**：响应时间 < 2秒
- **错误处理**：优雅处理异常情况
- **用户体验**：交互流程自然流畅

---

## 🧠 第一部分：意图识别测试场景

### 1.1 基础意图识别测试

#### 测试目标
验证系统能够准确识别用户的基本意图类型

#### 测试场景

**场景1.1.1：单一意图识别**
```yaml
测试用例:
  - 输入: "明天下午3点安排一个会议"
    预期意图: calendar
    预期操作: create
    置信度要求: > 0.9
    
  - 输入: "你好，今天天气怎么样？"
    预期意图: chat
    预期操作: general
    置信度要求: > 0.9
    
  - 输入: "记录今天的工作总结"
    预期意图: journal
    预期操作: create
    置信度要求: > 0.9
    
  - 输入: "帮我生成一个产品介绍视频"
    预期意图: media
    预期操作: create
    置信度要求: > 0.9

验证标准:
  - 意图识别准确率 = 100%
  - 平均置信度 > 0.9
  - 响应时间 < 1秒
```

**场景1.1.2：边界意图识别**
```yaml
测试用例:
  - 输入: "会议"  # 模糊输入
    预期行为: 要求用户补充信息
    
  - 输入: "明天"  # 不完整时间
    预期行为: 要求用户补充具体时间
    
  - 输入: ""  # 空输入
    预期行为: 提示用户输入有效内容
    
  - 输入: "asdfghjkl"  # 无意义输入
    预期意图: chat
    预期行为: 礼貌回应无法理解

验证标准:
  - 系统不崩溃
  - 给出合理的引导提示
  - 降级到chat意图处理
```

### 1.2 上下文感知测试

#### 测试目标
验证系统在多轮对话中保持上下文，避免重复意图识别

#### 测试场景

**场景1.2.1：确认状态上下文保持**
```yaml
对话流程:
  轮次1:
    用户: "明天下午安排会议"
    系统: 识别为calendar_create，要求补充会议主题
    
  轮次2:
    用户: "项目进度讨论"
    预期行为: 不重新进行意图识别，直接作为会议主题处理
    验证点: conversation_state = waiting_supplement
    
  轮次3:
    用户: "确认"
    预期行为: 识别为confirmation_yes，执行创建操作
    验证点: 成功创建日历事件

验证标准:
  - 第2轮不触发意图识别
  - 上下文状态正确维护
  - 最终成功完成业务操作
```

**场景1.2.2：意图切换处理**
```yaml
对话流程:
  轮次1:
    用户: "明天安排会议"
    系统: 进入calendar_create流程
    
  轮次2:
    用户: "今天天气怎么样？"
    预期行为: 识别为新的chat意图，暂停日历流程
    
  轮次3:
    用户: "继续刚才的会议安排"
    预期行为: 恢复calendar_create流程
    验证点: 能够恢复之前的上下文

验证标准:
  - 正确处理意图切换
  - 能够恢复中断的流程
  - 上下文数据不丢失
```

### 1.3 多意图处理测试

#### 测试目标
验证系统处理包含多个意图的复杂输入

#### 测试场景

**场景1.3.1：复合意图识别**
```yaml
测试用例:
  - 输入: "帮我查看今天的安排，然后安排明天的会议"
    预期行为: 
      - 识别为multi_intent
      - 分解为: calendar_query + calendar_create
      - 按优先级顺序执行
    
  - 输入: "取消今天的会议，改到明天同一时间"
    预期行为:
      - 识别为: calendar_delete + calendar_create
      - 先执行删除，再执行创建

验证标准:
  - 正确识别复合意图
  - 按逻辑顺序执行操作
  - 每个子操作都成功完成
```

---

## 📅 第二部分：日历业务测试场景

### 2.1 授权流程测试

#### 测试目标
验证飞书OAuth授权流程的完整性和安全性

#### 测试场景

**场景2.1.1：完整授权流程**
```yaml
测试步骤:
  1. 发起授权请求
     - 访问: /auth/login
     - 验证: 正确跳转到飞书授权页面
     
  2. 用户授权确认
     - 在飞书页面完成授权
     - 验证: 获得authorization_code
     
  3. 授权码换取令牌
     - 调用: /auth/callback
     - 验证: 成功获取access_token和refresh_token
     
  4. 获取用户信息
     - 调用: /auth/user_info
     - 验证: 成功获取用户基本信息

验证标准:
  - 每个步骤都成功完成
  - 令牌格式正确且有效
  - 用户信息完整准确
  - 安全性：令牌正确存储和传输
```

**场景2.1.2：令牌刷新机制**
```yaml
测试步骤:
  1. 模拟令牌过期
     - 使用过期的access_token调用API
     - 验证: 返回401未授权错误
     
  2. 自动刷新令牌
     - 系统自动使用refresh_token刷新
     - 验证: 获得新的access_token
     
  3. 重试原始请求
     - 使用新令牌重新调用API
     - 验证: 请求成功完成

验证标准:
  - 自动检测令牌过期
  - 成功刷新令牌
  - 用户无感知的令牌更新
```

### 2.2 日历CRUD操作测试

#### 测试目标
验证日历的创建、读取、更新、删除操作

#### 测试场景

**场景2.2.1：获取日历列表**
```yaml
测试步骤:
  1. 调用获取日历列表API
     - 请求: GET /auth/callback/calendars/
     - 参数: user_id=test_user
     
  2. 验证响应数据
     - 检查: 返回状态码200
     - 检查: 日历列表不为空
     - 检查: 包含主日历(primary)
     
  3. 验证日历信息完整性
     - 检查: calendar_id存在
     - 检查: summary不为空
     - 检查: 权限信息正确

验证标准:
  - API调用成功
  - 数据格式正确
  - 包含必要的日历信息
```

**场景2.2.2：日历权限验证**
```yaml
测试用例:
  - 主日历访问权限
    操作: 读取、创建事件
    预期: 成功
    
  - 共享日历访问权限
    操作: 读取事件
    预期: 成功
    操作: 创建事件
    预期: 根据权限决定
    
  - 无权限日历
    操作: 任何操作
    预期: 返回权限错误

验证标准:
  - 权限控制正确
  - 错误信息清晰
  - 不会泄露无权限数据
```

### 2.3 日程CRUD操作测试

#### 测试目标
验证日程事件的完整生命周期管理

#### 测试场景

**场景2.3.1：创建日程事件**
```yaml
测试步骤:
  1. 准备事件数据
     - 标题: "项目进度会议"
     - 时间: 明天14:00-15:00
     - 地点: "会议室A"
     - 参与者: ["张三", "李四"]
     
  2. 调用创建API
     - 请求: POST /auth/callback/calendars/{calendar_id}/events
     - 验证: 返回201创建成功
     
  3. 验证创建结果
     - 检查: 返回event_id
     - 检查: 事件信息正确
     - 检查: 时间格式正确

验证标准:
  - 事件创建成功
  - 所有字段正确保存
  - 时间处理准确
```

**场景2.3.2：查询日程事件**
```yaml
测试步骤:
  1. 查询今日事件
     - 请求: GET /auth/callback/events/today
     - 验证: 返回今日所有事件

  2. 查询指定日期范围
     - 请求: GET /auth/callback/events/range
     - 参数: start_date, end_date
     - 验证: 返回范围内事件

  3. 查询特定事件
     - 请求: GET /auth/callback/events/{event_id}
     - 验证: 返回事件详细信息

验证标准:
  - 查询结果准确
  - 时间过滤正确
  - 事件信息完整
```

**场景2.3.3：更新日程事件**
```yaml
测试步骤:
  1. 修改事件时间
     - 原时间: 14:00-15:00
     - 新时间: 15:00-16:00
     - 验证: 时间更新成功

  2. 修改事件地点
     - 原地点: "会议室A"
     - 新地点: "会议室B"
     - 验证: 地点更新成功

  3. 添加参与者
     - 原参与者: ["张三", "李四"]
     - 新参与者: ["张三", "李四", "王五"]
     - 验证: 参与者列表更新

验证标准:
  - 更新操作成功
  - 只修改指定字段
  - 其他字段保持不变
```

**场景2.3.4：删除日程事件**
```yaml
测试步骤:
  1. 删除单个事件
     - 请求: DELETE /auth/callback/events/{event_id}
     - 验证: 事件被成功删除

  2. 删除重复事件
     - 选择: 删除单次/删除所有
     - 验证: 按选择正确删除

  3. 验证删除结果
     - 查询已删除事件
     - 验证: 返回404不存在

验证标准:
  - 删除操作成功
  - 数据一致性保持
  - 相关数据正确清理
```

### 2.4 冲突检测测试

#### 测试目标
验证系统能够准确检测和处理时间冲突

#### 测试场景

**场景2.4.1：时间冲突检测**
```yaml
测试步骤:
  1. 创建基础事件
     - 时间: 2025-07-15 14:00-16:00
     - 标题: "项目会议"

  2. 尝试创建冲突事件
     - 时间: 2025-07-15 15:00-17:00
     - 标题: "客户会议"
     - 预期: 检测到时间冲突

  3. 冲突处理选项
     - 选项1: 取消创建
     - 选项2: 修改时间
     - 选项3: 强制创建
     - 验证: 按用户选择正确处理

验证标准:
  - 准确检测时间冲突
  - 提供清晰的冲突信息
  - 支持多种处理方式
```

**场景2.4.2：地点冲突检测**
```yaml
测试用例:
  - 同一会议室时间重叠
    事件1: 会议室A, 14:00-15:00
    事件2: 会议室A, 14:30-15:30
    预期: 检测到地点冲突

  - 不同会议室同时间
    事件1: 会议室A, 14:00-15:00
    事件2: 会议室B, 14:00-15:00
    预期: 无冲突

  - 线上会议无地点冲突
    事件1: 线上会议, 14:00-15:00
    事件2: 线上会议, 14:00-15:00
    预期: 无冲突

验证标准:
  - 地点冲突检测准确
  - 区分物理和虚拟地点
  - 冲突提示信息清晰
```

### 2.5 批量操作测试

#### 测试目标
验证系统处理批量日历操作的能力

#### 测试场景

**场景2.5.1：批量创建事件**
```yaml
测试步骤:
  1. 准备批量数据
     - 创建10个不同时间的会议
     - 包含不同类型: 会议、培训、面试

  2. 执行批量创建
     - 调用批量创建API
     - 验证: 所有事件创建成功

  3. 验证创建结果
     - 检查: 所有事件都存在
     - 检查: 事件信息正确
     - 检查: 无重复创建

验证标准:
  - 批量操作成功率100%
  - 操作时间合理
  - 数据一致性保持
```

**场景2.5.2：批量删除事件**
```yaml
测试步骤:
  1. 选择删除条件
     - 按时间范围: 删除本周所有事件
     - 按关键词: 删除包含"测试"的事件
     - 按类型: 删除所有培训事件

  2. 执行批量删除
     - 提供确认机制
     - 显示将要删除的事件列表
     - 用户确认后执行删除

  3. 验证删除结果
     - 检查: 符合条件的事件被删除
     - 检查: 不符合条件的事件保留
     - 检查: 无误删除

验证标准:
  - 删除条件准确匹配
  - 提供安全确认机制
  - 删除操作可撤销
```

---

## 💬 第三部分：聊天业务测试场景

### 3.1 基础对话测试

#### 测试目标
验证聊天功能的基本对话能力

#### 测试场景

**场景3.1.1：问候和礼貌用语**
```yaml
测试用例:
  - 输入: "你好"
    预期回复: 友好的问候回应

  - 输入: "谢谢"
    预期回复: 礼貌的回应

  - 输入: "再见"
    预期回复: 礼貌的告别

验证标准:
  - 回复内容自然友好
  - 符合中文表达习惯
  - 响应时间 < 1秒
```

**场景3.1.2：功能介绍和帮助**
```yaml
测试用例:
  - 输入: "你能做什么？"
    预期回复: 详细的功能介绍

  - 输入: "怎么使用？"
    预期回复: 使用指南和示例

  - 输入: "帮助"
    预期回复: 帮助菜单和常用命令

验证标准:
  - 功能介绍准确完整
  - 提供具体使用示例
  - 信息组织清晰易懂
```

### 3.2 多轮对话测试

#### 测试目标
验证系统在多轮对话中的上下文管理能力

#### 测试场景

**场景3.2.1：上下文保持**
```yaml
对话流程:
  轮次1:
    用户: "我想了解一下日历功能"
    系统: 介绍日历功能

  轮次2:
    用户: "具体怎么创建事件？"
    预期: 基于上一轮的日历话题，详细介绍创建事件的方法

  轮次3:
    用户: "那查询呢？"
    预期: 继续日历话题，介绍查询功能

验证标准:
  - 正确维护对话上下文
  - 回答与上下文相关
  - 话题连贯性好
```

**场景3.2.2：话题切换**
```yaml
对话流程:
  轮次1:
    用户: "介绍一下日历功能"
    系统: 介绍日历功能

  轮次2:
    用户: "今天天气怎么样？"
    预期: 识别为新话题，回应天气相关内容

  轮次3:
    用户: "回到刚才的日历话题"
    预期: 能够恢复之前的日历讨论

验证标准:
  - 正确识别话题切换
  - 能够恢复之前的话题
  - 话题管理灵活自然
```

### 3.3 错误处理测试

#### 测试目标
验证聊天功能的错误处理和恢复能力

#### 测试场景

**场景3.3.1：无法理解的输入**
```yaml
测试用例:
  - 输入: "asdfghjkl"
    预期: 礼貌地表示无法理解，提供帮助建议

  - 输入: "123456"
    预期: 询问用户意图，提供功能选项

  - 输入: "？？？"
    预期: 提供帮助信息和使用指南

验证标准:
  - 不返回错误信息
  - 提供有用的引导
  - 保持友好的交互体验
```

---

## 📝 第四部分：日记业务测试场景

### 4.1 日记创建测试

#### 测试目标
验证日记记录功能的完整性

#### 测试场景

**场景4.1.1：基础日记创建**
```yaml
测试步骤:
  1. 创建简单日记
     - 输入: "记录今天的工作总结"
     - 内容: "今天完成了项目需求分析"
     - 验证: 成功创建日记条目

  2. 创建带标签的日记
     - 输入: "记录工作日记，标签：项目管理"
     - 验证: 正确识别标签

  3. 创建带情感的日记
     - 输入: "今天心情很好，项目进展顺利"
     - 验证: 识别情感倾向

验证标准:
  - 日记内容正确保存
  - 标签和分类准确
  - 时间戳自动添加
```

### 4.2 日记查询测试

#### 测试目标
验证日记检索和查询功能

#### 测试场景

**场景4.2.1：按时间查询**
```yaml
测试用例:
  - 查询今天的日记
  - 查询本周的日记
  - 查询指定日期的日记
  - 查询日期范围内的日记

验证标准:
  - 时间过滤准确
  - 结果按时间排序
  - 支持多种时间格式
```

---

## 🎬 第五部分：媒体业务测试场景

### 5.1 媒体创作测试

#### 测试目标
验证媒体内容创作功能

#### 测试场景

**场景5.1.1：文本内容生成**
```yaml
测试用例:
  - 输入: "生成一份产品介绍文档"
    预期: 创建结构化的产品介绍

  - 输入: "写一篇技术博客"
    预期: 生成技术相关的文章内容

验证标准:
  - 内容质量高
  - 结构清晰合理
  - 符合指定类型要求
```

---

## 🔄 第六部分：特殊测试场景

### 6.1 多场景组合测试

#### 测试目标
验证多个功能模块的组合使用

#### 测试场景

**场景6.1.1：日历+聊天组合**
```yaml
测试流程:
  1. 用户询问今天安排 (聊天)
  2. 系统查询并返回日程 (日历)
  3. 用户要求修改某个会议 (日历)
  4. 系统确认修改并执行 (日历+聊天)

验证标准:
  - 功能切换自然
  - 数据传递正确
  - 用户体验流畅
```

### 6.2 并发处理测试

#### 测试目标
验证系统的并发处理能力

#### 测试场景

**场景6.2.1：多用户并发**
```yaml
测试设置:
  - 并发用户数: 50
  - 测试时长: 5分钟
  - 操作类型: 混合(聊天、日历、日记)

验证标准:
  - 所有请求正确处理
  - 响应时间稳定
  - 无数据冲突
  - 系统稳定运行
```

### 6.3 异常恢复测试

#### 测试目标
验证系统的异常处理和恢复能力

#### 测试场景

**场景6.3.1：网络异常恢复**
```yaml
测试步骤:
  1. 正常操作中断网络连接
  2. 系统检测到网络异常
  3. 恢复网络连接
  4. 系统自动重试失败的操作

验证标准:
  - 正确检测网络异常
  - 提供用户友好的错误提示
  - 自动重试机制有效
  - 数据不丢失
```

---

## 📋 测试执行计划

### 执行顺序

1. **环境准备** (30分钟)
   - 部署测试环境
   - 配置测试数据
   - 验证依赖服务

2. **基础功能测试** (2小时)
   - 意图识别测试
   - 各模块基础功能测试

3. **集成流程测试** (3小时)
   - 端到端业务流程测试
   - 多轮对话测试

4. **异常场景测试** (1小时)
   - 边界条件测试
   - 错误处理测试

5. **性能压力测试** (1小时)
   - 并发测试
   - 大数据量测试

6. **测试报告** (30分钟)
   - 结果汇总
   - 问题分析
   - 改进建议

### 成功标准

- **功能测试通过率**: > 95%
- **性能指标达标率**: > 90%
- **用户体验评分**: > 4.5/5
- **系统稳定性**: 无崩溃，无数据丢失

---

## 📊 测试报告模板

### 测试结果汇总
```yaml
测试概况:
  总测试用例数: XXX
  通过用例数: XXX
  失败用例数: XXX
  通过率: XX%

性能指标:
  平均响应时间: X.X秒
  并发处理能力: XX用户
  系统稳定性: XX%

问题分析:
  - 高优先级问题: X个
  - 中优先级问题: X个
  - 低优先级问题: X个

改进建议:
  - 功能优化建议
  - 性能优化建议
  - 用户体验改进建议
```

---

*本文档将根据测试执行情况和项目发展持续更新和完善。*
```
