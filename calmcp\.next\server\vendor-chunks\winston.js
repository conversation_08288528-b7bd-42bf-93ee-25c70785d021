"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/winston";
exports.ids = ["vendor-chunks/winston"];
exports.modules = {

/***/ "(rsc)/./node_modules/winston/lib/winston.js":
/*!*********************************************!*\
  !*** ./node_modules/winston/lib/winston.js ***!
  \*********************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * winston.js: Top-level include defining Winston.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst logform = __webpack_require__(/*! logform */ \"(rsc)/./node_modules/logform/index.js\");\nconst { warn } = __webpack_require__(/*! ./winston/common */ \"(rsc)/./node_modules/winston/lib/winston/common.js\");\n\n/**\n * Expose version. Use `require` method for `webpack` support.\n * @type {string}\n */\nexports.version = __webpack_require__(/*! ../package.json */ \"(rsc)/./node_modules/winston/package.json\").version;\n/**\n * Include transports defined by default by winston\n * @type {Array}\n */\nexports.transports = __webpack_require__(/*! ./winston/transports */ \"(rsc)/./node_modules/winston/lib/winston/transports/index.js\");\n/**\n * Expose utility methods\n * @type {Object}\n */\nexports.config = __webpack_require__(/*! ./winston/config */ \"(rsc)/./node_modules/winston/lib/winston/config/index.js\");\n/**\n * Hoist format-related functionality from logform.\n * @type {Object}\n */\nexports.addColors = logform.levels;\n/**\n * Hoist format-related functionality from logform.\n * @type {Object}\n */\nexports.format = logform.format;\n/**\n * Expose core Logging-related prototypes.\n * @type {function}\n */\nexports.createLogger = __webpack_require__(/*! ./winston/create-logger */ \"(rsc)/./node_modules/winston/lib/winston/create-logger.js\");\n/**\n * Expose core Logging-related prototypes.\n * @type {function}\n */\nexports.Logger = __webpack_require__(/*! ./winston/logger */ \"(rsc)/./node_modules/winston/lib/winston/logger.js\");\n/**\n * Expose core Logging-related prototypes.\n * @type {Object}\n */\nexports.ExceptionHandler = __webpack_require__(/*! ./winston/exception-handler */ \"(rsc)/./node_modules/winston/lib/winston/exception-handler.js\");\n/**\n * Expose core Logging-related prototypes.\n * @type {Object}\n */\nexports.RejectionHandler = __webpack_require__(/*! ./winston/rejection-handler */ \"(rsc)/./node_modules/winston/lib/winston/rejection-handler.js\");\n/**\n * Expose core Logging-related prototypes.\n * @type {Container}\n */\nexports.Container = __webpack_require__(/*! ./winston/container */ \"(rsc)/./node_modules/winston/lib/winston/container.js\");\n/**\n * Expose core Logging-related prototypes.\n * @type {Object}\n */\nexports.Transport = __webpack_require__(/*! winston-transport */ \"(rsc)/./node_modules/winston-transport/index.js\");\n/**\n * We create and expose a default `Container` to `winston.loggers` so that the\n * programmer may manage multiple `winston.Logger` instances without any\n * additional overhead.\n * @example\n *   // some-file1.js\n *   const logger = require('winston').loggers.get('something');\n *\n *   // some-file2.js\n *   const logger = require('winston').loggers.get('something');\n */\nexports.loggers = new exports.Container();\n\n/**\n * We create and expose a 'defaultLogger' so that the programmer may do the\n * following without the need to create an instance of winston.Logger directly:\n * @example\n *   const winston = require('winston');\n *   winston.log('info', 'some message');\n *   winston.error('some error');\n */\nconst defaultLogger = exports.createLogger();\n\n// Pass through the target methods onto `winston.\nObject.keys(exports.config.npm.levels)\n  .concat([\n    'log',\n    'query',\n    'stream',\n    'add',\n    'remove',\n    'clear',\n    'profile',\n    'startTimer',\n    'handleExceptions',\n    'unhandleExceptions',\n    'handleRejections',\n    'unhandleRejections',\n    'configure',\n    'child'\n  ])\n  .forEach(\n    method => (exports[method] = (...args) => defaultLogger[method](...args))\n  );\n\n/**\n * Define getter / setter for the default logger level which need to be exposed\n * by winston.\n * @type {string}\n */\nObject.defineProperty(exports, \"level\", ({\n  get() {\n    return defaultLogger.level;\n  },\n  set(val) {\n    defaultLogger.level = val;\n  }\n}));\n\n/**\n * Define getter for `exceptions` which replaces `handleExceptions` and\n * `unhandleExceptions`.\n * @type {Object}\n */\nObject.defineProperty(exports, \"exceptions\", ({\n  get() {\n    return defaultLogger.exceptions;\n  }\n}));\n\n/**\n * Define getter for `rejections` which replaces `handleRejections` and\n * `unhandleRejections`.\n * @type {Object}\n */\nObject.defineProperty(exports, \"rejections\", ({\n  get() {\n    return defaultLogger.rejections;\n  }\n}));\n\n/**\n * Define getters / setters for appropriate properties of the default logger\n * which need to be exposed by winston.\n * @type {Logger}\n */\n['exitOnError'].forEach(prop => {\n  Object.defineProperty(exports, prop, {\n    get() {\n      return defaultLogger[prop];\n    },\n    set(val) {\n      defaultLogger[prop] = val;\n    }\n  });\n});\n\n/**\n * The default transports and exceptionHandlers for the default winston logger.\n * @type {Object}\n */\nObject.defineProperty(exports, \"default\", ({\n  get() {\n    return {\n      exceptionHandlers: defaultLogger.exceptionHandlers,\n      rejectionHandlers: defaultLogger.rejectionHandlers,\n      transports: defaultLogger.transports\n    };\n  }\n}));\n\n// Have friendlier breakage notices for properties that were exposed by default\n// on winston < 3.0.\nwarn.deprecated(exports, 'setLevels');\nwarn.forFunctions(exports, 'useFormat', ['cli']);\nwarn.forProperties(exports, 'useFormat', ['padLevels', 'stripColors']);\nwarn.forFunctions(exports, 'deprecated', [\n  'addRewriter',\n  'addFilter',\n  'clone',\n  'extend'\n]);\nwarn.forProperties(exports, 'deprecated', ['emitErrs', 'levelLength']);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/common.js":
/*!****************************************************!*\
  !*** ./node_modules/winston/lib/winston/common.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * common.js: Internal helper and utility functions for winston.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst { format } = __webpack_require__(/*! util */ \"util\");\n\n/**\n * Set of simple deprecation notices and a way to expose them for a set of\n * properties.\n * @type {Object}\n * @private\n */\nexports.warn = {\n  deprecated(prop) {\n    return () => {\n      throw new Error(format('{ %s } was removed in winston@3.0.0.', prop));\n    };\n  },\n  useFormat(prop) {\n    return () => {\n      throw new Error([\n        format('{ %s } was removed in winston@3.0.0.', prop),\n        'Use a custom winston.format = winston.format(function) instead.'\n      ].join('\\n'));\n    };\n  },\n  forFunctions(obj, type, props) {\n    props.forEach(prop => {\n      obj[prop] = exports.warn[type](prop);\n    });\n  },\n  forProperties(obj, type, props) {\n    props.forEach(prop => {\n      const notice = exports.warn[type](prop);\n      Object.defineProperty(obj, prop, {\n        get: notice,\n        set: notice\n      });\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/common.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/config/index.js":
/*!**********************************************************!*\
  !*** ./node_modules/winston/lib/winston/config/index.js ***!
  \**********************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * index.js: Default settings for all levels that winston knows about.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst logform = __webpack_require__(/*! logform */ \"(rsc)/./node_modules/logform/index.js\");\nconst { configs } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\n\n/**\n * Export config set for the CLI.\n * @type {Object}\n */\nexports.cli = logform.levels(configs.cli);\n\n/**\n * Export config set for npm.\n * @type {Object}\n */\nexports.npm = logform.levels(configs.npm);\n\n/**\n * Export config set for the syslog.\n * @type {Object}\n */\nexports.syslog = logform.levels(configs.syslog);\n\n/**\n * Hoist addColors from logform where it was refactored into in winston@3.\n * @type {Object}\n */\nexports.addColors = logform.levels;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvd2luc3Rvbi9saWIvd2luc3Rvbi9jb25maWcvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVhOztBQUViLGdCQUFnQixtQkFBTyxDQUFDLHNEQUFTO0FBQ2pDLFFBQVEsVUFBVSxFQUFFLG1CQUFPLENBQUMsOERBQWE7O0FBRXpDO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxXQUFXOztBQUVYO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxXQUFXOztBQUVYO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxjQUFjOztBQUVkO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jYWxtY3AvLi9ub2RlX21vZHVsZXMvd2luc3Rvbi9saWIvd2luc3Rvbi9jb25maWcvaW5kZXguanM/MThlNyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIGluZGV4LmpzOiBEZWZhdWx0IHNldHRpbmdzIGZvciBhbGwgbGV2ZWxzIHRoYXQgd2luc3RvbiBrbm93cyBhYm91dC5cbiAqXG4gKiAoQykgMjAxMCBDaGFybGllIFJvYmJpbnNcbiAqIE1JVCBMSUNFTkNFXG4gKi9cblxuJ3VzZSBzdHJpY3QnO1xuXG5jb25zdCBsb2dmb3JtID0gcmVxdWlyZSgnbG9nZm9ybScpO1xuY29uc3QgeyBjb25maWdzIH0gPSByZXF1aXJlKCd0cmlwbGUtYmVhbScpO1xuXG4vKipcbiAqIEV4cG9ydCBjb25maWcgc2V0IGZvciB0aGUgQ0xJLlxuICogQHR5cGUge09iamVjdH1cbiAqL1xuZXhwb3J0cy5jbGkgPSBsb2dmb3JtLmxldmVscyhjb25maWdzLmNsaSk7XG5cbi8qKlxuICogRXhwb3J0IGNvbmZpZyBzZXQgZm9yIG5wbS5cbiAqIEB0eXBlIHtPYmplY3R9XG4gKi9cbmV4cG9ydHMubnBtID0gbG9nZm9ybS5sZXZlbHMoY29uZmlncy5ucG0pO1xuXG4vKipcbiAqIEV4cG9ydCBjb25maWcgc2V0IGZvciB0aGUgc3lzbG9nLlxuICogQHR5cGUge09iamVjdH1cbiAqL1xuZXhwb3J0cy5zeXNsb2cgPSBsb2dmb3JtLmxldmVscyhjb25maWdzLnN5c2xvZyk7XG5cbi8qKlxuICogSG9pc3QgYWRkQ29sb3JzIGZyb20gbG9nZm9ybSB3aGVyZSBpdCB3YXMgcmVmYWN0b3JlZCBpbnRvIGluIHdpbnN0b25AMy5cbiAqIEB0eXBlIHtPYmplY3R9XG4gKi9cbmV4cG9ydHMuYWRkQ29sb3JzID0gbG9nZm9ybS5sZXZlbHM7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/config/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/container.js":
/*!*******************************************************!*\
  !*** ./node_modules/winston/lib/winston/container.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * container.js: Inversion of control container for winston logger instances.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst createLogger = __webpack_require__(/*! ./create-logger */ \"(rsc)/./node_modules/winston/lib/winston/create-logger.js\");\n\n/**\n * Inversion of control container for winston logger instances.\n * @type {Container}\n */\nmodule.exports = class Container {\n  /**\n   * Constructor function for the Container object responsible for managing a\n   * set of `winston.Logger` instances based on string ids.\n   * @param {!Object} [options={}] - Default pass-thru options for Loggers.\n   */\n  constructor(options = {}) {\n    this.loggers = new Map();\n    this.options = options;\n  }\n\n  /**\n   * Retrieves a `winston.Logger` instance for the specified `id`. If an\n   * instance does not exist, one is created.\n   * @param {!string} id - The id of the Logger to get.\n   * @param {?Object} [options] - Options for the Logger instance.\n   * @returns {Logger} - A configured Logger instance with a specified id.\n   */\n  add(id, options) {\n    if (!this.loggers.has(id)) {\n      // Remark: Simple shallow clone for configuration options in case we pass\n      // in instantiated protoypal objects\n      options = Object.assign({}, options || this.options);\n      const existing = options.transports || this.options.transports;\n\n      // Remark: Make sure if we have an array of transports we slice it to\n      // make copies of those references.\n      if (existing) {\n        options.transports = Array.isArray(existing) ? existing.slice() : [existing];\n      } else {\n        options.transports = [];\n      }\n\n      const logger = createLogger(options);\n      logger.on('close', () => this._delete(id));\n      this.loggers.set(id, logger);\n    }\n\n    return this.loggers.get(id);\n  }\n\n  /**\n   * Retreives a `winston.Logger` instance for the specified `id`. If\n   * an instance does not exist, one is created.\n   * @param {!string} id - The id of the Logger to get.\n   * @param {?Object} [options] - Options for the Logger instance.\n   * @returns {Logger} - A configured Logger instance with a specified id.\n   */\n  get(id, options) {\n    return this.add(id, options);\n  }\n\n  /**\n   * Check if the container has a logger with the id.\n   * @param {?string} id - The id of the Logger instance to find.\n   * @returns {boolean} - Boolean value indicating if this instance has a\n   * logger with the specified `id`.\n   */\n  has(id) {\n    return !!this.loggers.has(id);\n  }\n\n  /**\n   * Closes a `Logger` instance with the specified `id` if it exists.\n   * If no `id` is supplied then all Loggers are closed.\n   * @param {?string} id - The id of the Logger instance to close.\n   * @returns {undefined}\n   */\n  close(id) {\n    if (id) {\n      return this._removeLogger(id);\n    }\n\n    this.loggers.forEach((val, key) => this._removeLogger(key));\n  }\n\n  /**\n   * Remove a logger based on the id.\n   * @param {!string} id - The id of the logger to remove.\n   * @returns {undefined}\n   * @private\n   */\n  _removeLogger(id) {\n    if (!this.loggers.has(id)) {\n      return;\n    }\n\n    const logger = this.loggers.get(id);\n    logger.close();\n    this._delete(id);\n  }\n\n  /**\n   * Deletes a `Logger` instance with the specified `id`.\n   * @param {!string} id - The id of the Logger instance to delete from\n   * container.\n   * @returns {undefined}\n   * @private\n   */\n  _delete(id) {\n    this.loggers.delete(id);\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/container.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/create-logger.js":
/*!***********************************************************!*\
  !*** ./node_modules/winston/lib/winston/create-logger.js ***!
  \***********************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * create-logger.js: Logger factory for winston logger instances.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst { LEVEL } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst config = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/winston/lib/winston/config/index.js\");\nconst Logger = __webpack_require__(/*! ./logger */ \"(rsc)/./node_modules/winston/lib/winston/logger.js\");\nconst debug = __webpack_require__(/*! @dabh/diagnostics */ \"(rsc)/./node_modules/@dabh/diagnostics/node/index.js\")('winston:create-logger');\n\nfunction isLevelEnabledFunctionName(level) {\n  return 'is' + level.charAt(0).toUpperCase() + level.slice(1) + 'Enabled';\n}\n\n/**\n * Create a new instance of a winston Logger. Creates a new\n * prototype for each instance.\n * @param {!Object} opts - Options for the created logger.\n * @returns {Logger} - A newly created logger instance.\n */\nmodule.exports = function (opts = {}) {\n  //\n  // Default levels: npm\n  //\n  opts.levels = opts.levels || config.npm.levels;\n\n  /**\n   * DerivedLogger to attach the logs level methods.\n   * @type {DerivedLogger}\n   * @extends {Logger}\n   */\n  class DerivedLogger extends Logger {\n    /**\n     * Create a new class derived logger for which the levels can be attached to\n     * the prototype of. This is a V8 optimization that is well know to increase\n     * performance of prototype functions.\n     * @param {!Object} options - Options for the created logger.\n     */\n    constructor(options) {\n      super(options);\n    }\n  }\n\n  const logger = new DerivedLogger(opts);\n\n  //\n  // Create the log level methods for the derived logger.\n  //\n  Object.keys(opts.levels).forEach(function (level) {\n    debug('Define prototype method for \"%s\"', level);\n    if (level === 'log') {\n      // eslint-disable-next-line no-console\n      console.warn('Level \"log\" not defined: conflicts with the method \"log\". Use a different level name.');\n      return;\n    }\n\n    //\n    // Define prototype methods for each log level e.g.:\n    // logger.log('info', msg) implies these methods are defined:\n    // - logger.info(msg)\n    // - logger.isInfoEnabled()\n    //\n    // Remark: to support logger.child this **MUST** be a function\n    // so it'll always be called on the instance instead of a fixed\n    // place in the prototype chain.\n    //\n    DerivedLogger.prototype[level] = function (...args) {\n      // Prefer any instance scope, but default to \"root\" logger\n      const self = this || logger;\n\n      // Optimize the hot-path which is the single object.\n      if (args.length === 1) {\n        const [msg] = args;\n        const info = msg && msg.message && msg || { message: msg };\n        info.level = info[LEVEL] = level;\n        self._addDefaultMeta(info);\n        self.write(info);\n        return (this || logger);\n      }\n\n      // When provided nothing assume the empty string\n      if (args.length === 0) {\n        self.log(level, '');\n        return self;\n      }\n\n      // Otherwise build argument list which could potentially conform to\n      // either:\n      // . v3 API: log(obj)\n      // 2. v1/v2 API: log(level, msg, ... [string interpolate], [{metadata}], [callback])\n      return self.log(level, ...args);\n    };\n\n    DerivedLogger.prototype[isLevelEnabledFunctionName(level)] = function () {\n      return (this || logger).isLevelEnabled(level);\n    };\n  });\n\n  return logger;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/create-logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/exception-handler.js":
/*!***************************************************************!*\
  !*** ./node_modules/winston/lib/winston/exception-handler.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * exception-handler.js: Object for handling uncaughtException events.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst os = __webpack_require__(/*! os */ \"os\");\nconst asyncForEach = __webpack_require__(/*! async/forEach */ \"(rsc)/./node_modules/async/forEach.js\");\nconst debug = __webpack_require__(/*! @dabh/diagnostics */ \"(rsc)/./node_modules/@dabh/diagnostics/node/index.js\")('winston:exception');\nconst once = __webpack_require__(/*! one-time */ \"(rsc)/./node_modules/one-time/index.js\");\nconst stackTrace = __webpack_require__(/*! stack-trace */ \"(rsc)/./node_modules/stack-trace/lib/stack-trace.js\");\nconst ExceptionStream = __webpack_require__(/*! ./exception-stream */ \"(rsc)/./node_modules/winston/lib/winston/exception-stream.js\");\n\n/**\n * Object for handling uncaughtException events.\n * @type {ExceptionHandler}\n */\nmodule.exports = class ExceptionHandler {\n  /**\n   * TODO: add contructor description\n   * @param {!Logger} logger - TODO: add param description\n   */\n  constructor(logger) {\n    if (!logger) {\n      throw new Error('Logger is required to handle exceptions');\n    }\n\n    this.logger = logger;\n    this.handlers = new Map();\n  }\n\n  /**\n   * Handles `uncaughtException` events for the current process by adding any\n   * handlers passed in.\n   * @returns {undefined}\n   */\n  handle(...args) {\n    args.forEach(arg => {\n      if (Array.isArray(arg)) {\n        return arg.forEach(handler => this._addHandler(handler));\n      }\n\n      this._addHandler(arg);\n    });\n\n    if (!this.catcher) {\n      this.catcher = this._uncaughtException.bind(this);\n      process.on('uncaughtException', this.catcher);\n    }\n  }\n\n  /**\n   * Removes any handlers to `uncaughtException` events for the current\n   * process. This does not modify the state of the `this.handlers` set.\n   * @returns {undefined}\n   */\n  unhandle() {\n    if (this.catcher) {\n      process.removeListener('uncaughtException', this.catcher);\n      this.catcher = false;\n\n      Array.from(this.handlers.values())\n        .forEach(wrapper => this.logger.unpipe(wrapper));\n    }\n  }\n\n  /**\n   * TODO: add method description\n   * @param {Error} err - Error to get information about.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getAllInfo(err) {\n    let message = null;\n    if (err) {\n      message = typeof err === 'string' ? err : err.message;\n    }\n\n    return {\n      error: err,\n      // TODO (indexzero): how do we configure this?\n      level: 'error',\n      message: [\n        `uncaughtException: ${(message || '(no error message)')}`,\n        err && err.stack || '  No stack trace'\n      ].join('\\n'),\n      stack: err && err.stack,\n      exception: true,\n      date: new Date().toString(),\n      process: this.getProcessInfo(),\n      os: this.getOsInfo(),\n      trace: this.getTrace(err)\n    };\n  }\n\n  /**\n   * Gets all relevant process information for the currently running process.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getProcessInfo() {\n    return {\n      pid: process.pid,\n      uid: process.getuid ? process.getuid() : null,\n      gid: process.getgid ? process.getgid() : null,\n      cwd: process.cwd(),\n      execPath: process.execPath,\n      version: process.version,\n      argv: process.argv,\n      memoryUsage: process.memoryUsage()\n    };\n  }\n\n  /**\n   * Gets all relevant OS information for the currently running process.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getOsInfo() {\n    return {\n      loadavg: os.loadavg(),\n      uptime: os.uptime()\n    };\n  }\n\n  /**\n   * Gets a stack trace for the specified error.\n   * @param {mixed} err - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getTrace(err) {\n    const trace = err ? stackTrace.parse(err) : stackTrace.get();\n    return trace.map(site => {\n      return {\n        column: site.getColumnNumber(),\n        file: site.getFileName(),\n        function: site.getFunctionName(),\n        line: site.getLineNumber(),\n        method: site.getMethodName(),\n        native: site.isNative()\n      };\n    });\n  }\n\n  /**\n   * Helper method to add a transport as an exception handler.\n   * @param {Transport} handler - The transport to add as an exception handler.\n   * @returns {void}\n   */\n  _addHandler(handler) {\n    if (!this.handlers.has(handler)) {\n      handler.handleExceptions = true;\n      const wrapper = new ExceptionStream(handler);\n      this.handlers.set(handler, wrapper);\n      this.logger.pipe(wrapper);\n    }\n  }\n\n  /**\n   * Logs all relevant information around the `err` and exits the current\n   * process.\n   * @param {Error} err - Error to handle\n   * @returns {mixed} - TODO: add return description.\n   * @private\n   */\n  _uncaughtException(err) {\n    const info = this.getAllInfo(err);\n    const handlers = this._getExceptionHandlers();\n    // Calculate if we should exit on this error\n    let doExit = typeof this.logger.exitOnError === 'function'\n      ? this.logger.exitOnError(err)\n      : this.logger.exitOnError;\n    let timeout;\n\n    if (!handlers.length && doExit) {\n      // eslint-disable-next-line no-console\n      console.warn('winston: exitOnError cannot be true with no exception handlers.');\n      // eslint-disable-next-line no-console\n      console.warn('winston: not exiting process.');\n      doExit = false;\n    }\n\n    function gracefulExit() {\n      debug('doExit', doExit);\n      debug('process._exiting', process._exiting);\n\n      if (doExit && !process._exiting) {\n        // Remark: Currently ignoring any exceptions from transports when\n        // catching uncaught exceptions.\n        if (timeout) {\n          clearTimeout(timeout);\n        }\n        // eslint-disable-next-line no-process-exit\n        process.exit(1);\n      }\n    }\n\n    if (!handlers || handlers.length === 0) {\n      return process.nextTick(gracefulExit);\n    }\n\n    // Log to all transports attempting to listen for when they are completed.\n    asyncForEach(handlers, (handler, next) => {\n      const done = once(next);\n      const transport = handler.transport || handler;\n\n      // Debug wrapping so that we can inspect what's going on under the covers.\n      function onDone(event) {\n        return () => {\n          debug(event);\n          done();\n        };\n      }\n\n      transport._ending = true;\n      transport.once('finish', onDone('finished'));\n      transport.once('error', onDone('error'));\n    }, () => doExit && gracefulExit());\n\n    this.logger.log(info);\n\n    // If exitOnError is true, then only allow the logging of exceptions to\n    // take up to `3000ms`.\n    if (doExit) {\n      timeout = setTimeout(gracefulExit, 3000);\n    }\n  }\n\n  /**\n   * Returns the list of transports and exceptionHandlers for this instance.\n   * @returns {Array} - List of transports and exceptionHandlers for this\n   * instance.\n   * @private\n   */\n  _getExceptionHandlers() {\n    // Remark (indexzero): since `logger.transports` returns all of the pipes\n    // from the _readableState of the stream we actually get the join of the\n    // explicit handlers and the implicit transports with\n    // `handleExceptions: true`\n    return this.logger.transports.filter(wrap => {\n      const transport = wrap.transport || wrap;\n      return transport.handleExceptions;\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/exception-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/exception-stream.js":
/*!**************************************************************!*\
  !*** ./node_modules/winston/lib/winston/exception-stream.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * exception-stream.js: TODO: add file header handler.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst { Writable } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\n\n/**\n * TODO: add class description.\n * @type {ExceptionStream}\n * @extends {Writable}\n */\nmodule.exports = class ExceptionStream extends Writable {\n  /**\n   * Constructor function for the ExceptionStream responsible for wrapping a\n   * TransportStream; only allowing writes of `info` objects with\n   * `info.exception` set to true.\n   * @param {!TransportStream} transport - Stream to filter to exceptions\n   */\n  constructor(transport) {\n    super({ objectMode: true });\n\n    if (!transport) {\n      throw new Error('ExceptionStream requires a TransportStream instance.');\n    }\n\n    // Remark (indexzero): we set `handleExceptions` here because it's the\n    // predicate checked in ExceptionHandler.prototype.__getExceptionHandlers\n    this.handleExceptions = true;\n    this.transport = transport;\n  }\n\n  /**\n   * Writes the info object to our transport instance if (and only if) the\n   * `exception` property is set on the info.\n   * @param {mixed} info - TODO: add param description.\n   * @param {mixed} enc - TODO: add param description.\n   * @param {mixed} callback - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   * @private\n   */\n  _write(info, enc, callback) {\n    if (info.exception) {\n      return this.transport.log(info, callback);\n    }\n\n    callback();\n    return true;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/exception-stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/logger.js":
/*!****************************************************!*\
  !*** ./node_modules/winston/lib/winston/logger.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * logger.js: TODO: add file header description.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst { Stream, Transform } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\nconst asyncForEach = __webpack_require__(/*! async/forEach */ \"(rsc)/./node_modules/async/forEach.js\");\nconst { LEVEL, SPLAT } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst isStream = __webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/is-stream/index.js\");\nconst ExceptionHandler = __webpack_require__(/*! ./exception-handler */ \"(rsc)/./node_modules/winston/lib/winston/exception-handler.js\");\nconst RejectionHandler = __webpack_require__(/*! ./rejection-handler */ \"(rsc)/./node_modules/winston/lib/winston/rejection-handler.js\");\nconst LegacyTransportStream = __webpack_require__(/*! winston-transport/legacy */ \"(rsc)/./node_modules/winston-transport/legacy.js\");\nconst Profiler = __webpack_require__(/*! ./profiler */ \"(rsc)/./node_modules/winston/lib/winston/profiler.js\");\nconst { warn } = __webpack_require__(/*! ./common */ \"(rsc)/./node_modules/winston/lib/winston/common.js\");\nconst config = __webpack_require__(/*! ./config */ \"(rsc)/./node_modules/winston/lib/winston/config/index.js\");\n\n/**\n * Captures the number of format (i.e. %s strings) in a given string.\n * Based on `util.format`, see Node.js source:\n * https://github.com/nodejs/node/blob/b1c8f15c5f169e021f7c46eb7b219de95fe97603/lib/util.js#L201-L230\n * @type {RegExp}\n */\nconst formatRegExp = /%[scdjifoO%]/g;\n\n/**\n * TODO: add class description.\n * @type {Logger}\n * @extends {Transform}\n */\nclass Logger extends Transform {\n  /**\n   * Constructor function for the Logger object responsible for persisting log\n   * messages and metadata to one or more transports.\n   * @param {!Object} options - foo\n   */\n  constructor(options) {\n    super({ objectMode: true });\n    this.configure(options);\n  }\n\n  child(defaultRequestMetadata) {\n    const logger = this;\n    return Object.create(logger, {\n      write: {\n        value: function (info) {\n          const infoClone = Object.assign(\n            {},\n            defaultRequestMetadata,\n            info\n          );\n\n          // Object.assign doesn't copy inherited Error\n          // properties so we have to do that explicitly\n          //\n          // Remark (indexzero): we should remove this\n          // since the errors format will handle this case.\n          //\n          if (info instanceof Error) {\n            infoClone.stack = info.stack;\n            infoClone.message = info.message;\n          }\n\n          logger.write(infoClone);\n        }\n      }\n    });\n  }\n\n  /**\n   * This will wholesale reconfigure this instance by:\n   * 1. Resetting all transports. Older transports will be removed implicitly.\n   * 2. Set all other options including levels, colors, rewriters, filters,\n   *    exceptionHandlers, etc.\n   * @param {!Object} options - TODO: add param description.\n   * @returns {undefined}\n   */\n  configure({\n    silent,\n    format,\n    defaultMeta,\n    levels,\n    level = 'info',\n    exitOnError = true,\n    transports,\n    colors,\n    emitErrs,\n    formatters,\n    padLevels,\n    rewriters,\n    stripColors,\n    exceptionHandlers,\n    rejectionHandlers\n  } = {}) {\n    // Reset transports if we already have them\n    if (this.transports.length) {\n      this.clear();\n    }\n\n    this.silent = silent;\n    this.format = format || this.format || __webpack_require__(/*! logform/json */ \"(rsc)/./node_modules/logform/json.js\")();\n\n    this.defaultMeta = defaultMeta || null;\n    // Hoist other options onto this instance.\n    this.levels = levels || this.levels || config.npm.levels;\n    this.level = level;\n    if (this.exceptions) {\n      this.exceptions.unhandle();\n    }\n    if (this.rejections) {\n      this.rejections.unhandle();\n    }\n    this.exceptions = new ExceptionHandler(this);\n    this.rejections = new RejectionHandler(this);\n    this.profilers = {};\n    this.exitOnError = exitOnError;\n\n    // Add all transports we have been provided.\n    if (transports) {\n      transports = Array.isArray(transports) ? transports : [transports];\n      transports.forEach(transport => this.add(transport));\n    }\n\n    if (\n      colors ||\n      emitErrs ||\n      formatters ||\n      padLevels ||\n      rewriters ||\n      stripColors\n    ) {\n      throw new Error(\n        [\n          '{ colors, emitErrs, formatters, padLevels, rewriters, stripColors } were removed in winston@3.0.0.',\n          'Use a custom winston.format(function) instead.',\n          'See: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md'\n        ].join('\\n')\n      );\n    }\n\n    if (exceptionHandlers) {\n      this.exceptions.handle(exceptionHandlers);\n    }\n    if (rejectionHandlers) {\n      this.rejections.handle(rejectionHandlers);\n    }\n  }\n\n  isLevelEnabled(level) {\n    const givenLevelValue = getLevelValue(this.levels, level);\n    if (givenLevelValue === null) {\n      return false;\n    }\n\n    const configuredLevelValue = getLevelValue(this.levels, this.level);\n    if (configuredLevelValue === null) {\n      return false;\n    }\n\n    if (!this.transports || this.transports.length === 0) {\n      return configuredLevelValue >= givenLevelValue;\n    }\n\n    const index = this.transports.findIndex(transport => {\n      let transportLevelValue = getLevelValue(this.levels, transport.level);\n      if (transportLevelValue === null) {\n        transportLevelValue = configuredLevelValue;\n      }\n      return transportLevelValue >= givenLevelValue;\n    });\n    return index !== -1;\n  }\n\n  /* eslint-disable valid-jsdoc */\n  /**\n   * Ensure backwards compatibility with a `log` method\n   * @param {mixed} level - Level the log message is written at.\n   * @param {mixed} msg - TODO: add param description.\n   * @param {mixed} meta - TODO: add param description.\n   * @returns {Logger} - TODO: add return description.\n   *\n   * @example\n   *    // Supports the existing API:\n   *    logger.log('info', 'Hello world', { custom: true });\n   *    logger.log('info', new Error('Yo, it\\'s on fire'));\n   *\n   *    // Requires winston.format.splat()\n   *    logger.log('info', '%s %d%%', 'A string', 50, { thisIsMeta: true });\n   *\n   *    // And the new API with a single JSON literal:\n   *    logger.log({ level: 'info', message: 'Hello world', custom: true });\n   *    logger.log({ level: 'info', message: new Error('Yo, it\\'s on fire') });\n   *\n   *    // Also requires winston.format.splat()\n   *    logger.log({\n   *      level: 'info',\n   *      message: '%s %d%%',\n   *      [SPLAT]: ['A string', 50],\n   *      meta: { thisIsMeta: true }\n   *    });\n   *\n   */\n  /* eslint-enable valid-jsdoc */\n  log(level, msg, ...splat) {\n    // eslint-disable-line max-params\n    // Optimize for the hotpath of logging JSON literals\n    if (arguments.length === 1) {\n      // Yo dawg, I heard you like levels ... seriously ...\n      // In this context the LHS `level` here is actually the `info` so read\n      // this as: info[LEVEL] = info.level;\n      level[LEVEL] = level.level;\n      this._addDefaultMeta(level);\n      this.write(level);\n      return this;\n    }\n\n    // Slightly less hotpath, but worth optimizing for.\n    if (arguments.length === 2) {\n      if (msg && typeof msg === 'object') {\n        msg[LEVEL] = msg.level = level;\n        this._addDefaultMeta(msg);\n        this.write(msg);\n        return this;\n      }\n\n      msg = { [LEVEL]: level, level, message: msg };\n      this._addDefaultMeta(msg);\n      this.write(msg);\n      return this;\n    }\n\n    const [meta] = splat;\n    if (typeof meta === 'object' && meta !== null) {\n      // Extract tokens, if none available default to empty array to\n      // ensure consistancy in expected results\n      const tokens = msg && msg.match && msg.match(formatRegExp);\n\n      if (!tokens) {\n        const info = Object.assign({}, this.defaultMeta, meta, {\n          [LEVEL]: level,\n          [SPLAT]: splat,\n          level,\n          message: msg\n        });\n\n        if (meta.message) info.message = `${info.message} ${meta.message}`;\n        if (meta.stack) info.stack = meta.stack;\n        if (meta.cause) info.cause = meta.cause;\n\n        this.write(info);\n        return this;\n      }\n    }\n\n    this.write(Object.assign({}, this.defaultMeta, {\n      [LEVEL]: level,\n      [SPLAT]: splat,\n      level,\n      message: msg\n    }));\n\n    return this;\n  }\n\n  /**\n   * Pushes data so that it can be picked up by all of our pipe targets.\n   * @param {mixed} info - TODO: add param description.\n   * @param {mixed} enc - TODO: add param description.\n   * @param {mixed} callback - Continues stream processing.\n   * @returns {undefined}\n   * @private\n   */\n  _transform(info, enc, callback) {\n    if (this.silent) {\n      return callback();\n    }\n\n    // [LEVEL] is only soft guaranteed to be set here since we are a proper\n    // stream. It is likely that `info` came in through `.log(info)` or\n    // `.info(info)`. If it is not defined, however, define it.\n    // This LEVEL symbol is provided by `triple-beam` and also used in:\n    // - logform\n    // - winston-transport\n    // - abstract-winston-transport\n    if (!info[LEVEL]) {\n      info[LEVEL] = info.level;\n    }\n\n    // Remark: really not sure what to do here, but this has been reported as\n    // very confusing by pre winston@2.0.0 users as quite confusing when using\n    // custom levels.\n    if (!this.levels[info[LEVEL]] && this.levels[info[LEVEL]] !== 0) {\n      // eslint-disable-next-line no-console\n      console.error('[winston] Unknown logger level: %s', info[LEVEL]);\n    }\n\n    // Remark: not sure if we should simply error here.\n    if (!this._readableState.pipes) {\n      // eslint-disable-next-line no-console\n      console.error(\n        '[winston] Attempt to write logs with no transports, which can increase memory usage: %j',\n        info\n      );\n    }\n\n    // Here we write to the `format` pipe-chain, which on `readable` above will\n    // push the formatted `info` Object onto the buffer for this instance. We trap\n    // (and re-throw) any errors generated by the user-provided format, but also\n    // guarantee that the streams callback is invoked so that we can continue flowing.\n    try {\n      this.push(this.format.transform(info, this.format.options));\n    } finally {\n      this._writableState.sync = false;\n      // eslint-disable-next-line callback-return\n      callback();\n    }\n  }\n\n  /**\n   * Delays the 'finish' event until all transport pipe targets have\n   * also emitted 'finish' or are already finished.\n   * @param {mixed} callback - Continues stream processing.\n   */\n  _final(callback) {\n    const transports = this.transports.slice();\n    asyncForEach(\n      transports,\n      (transport, next) => {\n        if (!transport || transport.finished) return setImmediate(next);\n        transport.once('finish', next);\n        transport.end();\n      },\n      callback\n    );\n  }\n\n  /**\n   * Adds the transport to this logger instance by piping to it.\n   * @param {mixed} transport - TODO: add param description.\n   * @returns {Logger} - TODO: add return description.\n   */\n  add(transport) {\n    // Support backwards compatibility with all existing `winston < 3.x.x`\n    // transports which meet one of two criteria:\n    // 1. They inherit from winston.Transport in  < 3.x.x which is NOT a stream.\n    // 2. They expose a log method which has a length greater than 2 (i.e. more then\n    //    just `log(info, callback)`.\n    const target =\n      !isStream(transport) || transport.log.length > 2\n        ? new LegacyTransportStream({ transport })\n        : transport;\n\n    if (!target._writableState || !target._writableState.objectMode) {\n      throw new Error(\n        'Transports must WritableStreams in objectMode. Set { objectMode: true }.'\n      );\n    }\n\n    // Listen for the `error` event and the `warn` event on the new Transport.\n    this._onEvent('error', target);\n    this._onEvent('warn', target);\n    this.pipe(target);\n\n    if (transport.handleExceptions) {\n      this.exceptions.handle();\n    }\n\n    if (transport.handleRejections) {\n      this.rejections.handle();\n    }\n\n    return this;\n  }\n\n  /**\n   * Removes the transport from this logger instance by unpiping from it.\n   * @param {mixed} transport - TODO: add param description.\n   * @returns {Logger} - TODO: add return description.\n   */\n  remove(transport) {\n    if (!transport) return this;\n    let target = transport;\n    if (!isStream(transport) || transport.log.length > 2) {\n      target = this.transports.filter(\n        match => match.transport === transport\n      )[0];\n    }\n\n    if (target) {\n      this.unpipe(target);\n    }\n    return this;\n  }\n\n  /**\n   * Removes all transports from this logger instance.\n   * @returns {Logger} - TODO: add return description.\n   */\n  clear() {\n    this.unpipe();\n    return this;\n  }\n\n  /**\n   * Cleans up resources (streams, event listeners) for all transports\n   * associated with this instance (if necessary).\n   * @returns {Logger} - TODO: add return description.\n   */\n  close() {\n    this.exceptions.unhandle();\n    this.rejections.unhandle();\n    this.clear();\n    this.emit('close');\n    return this;\n  }\n\n  /**\n   * Sets the `target` levels specified on this instance.\n   * @param {Object} Target levels to use on this instance.\n   */\n  setLevels() {\n    warn.deprecated('setLevels');\n  }\n\n  /**\n   * Queries the all transports for this instance with the specified `options`.\n   * This will aggregate each transport's results into one object containing\n   * a property per transport.\n   * @param {Object} options - Query options for this instance.\n   * @param {function} callback - Continuation to respond to when complete.\n   */\n  query(options, callback) {\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    options = options || {};\n    const results = {};\n    const queryObject = Object.assign({}, options.query || {});\n\n    // Helper function to query a single transport\n    function queryTransport(transport, next) {\n      if (options.query && typeof transport.formatQuery === 'function') {\n        options.query = transport.formatQuery(queryObject);\n      }\n\n      transport.query(options, (err, res) => {\n        if (err) {\n          return next(err);\n        }\n\n        if (typeof transport.formatResults === 'function') {\n          res = transport.formatResults(res, options.format);\n        }\n\n        next(null, res);\n      });\n    }\n\n    // Helper function to accumulate the results from `queryTransport` into\n    // the `results`.\n    function addResults(transport, next) {\n      queryTransport(transport, (err, result) => {\n        // queryTransport could potentially invoke the callback multiple times\n        // since Transport code can be unpredictable.\n        if (next) {\n          result = err || result;\n          if (result) {\n            results[transport.name] = result;\n          }\n\n          // eslint-disable-next-line callback-return\n          next();\n        }\n\n        next = null;\n      });\n    }\n\n    // Iterate over the transports in parallel setting the appropriate key in\n    // the `results`.\n    asyncForEach(\n      this.transports.filter(transport => !!transport.query),\n      addResults,\n      () => callback(null, results)\n    );\n  }\n\n  /**\n   * Returns a log stream for all transports. Options object is optional.\n   * @param{Object} options={} - Stream options for this instance.\n   * @returns {Stream} - TODO: add return description.\n   */\n  stream(options = {}) {\n    const out = new Stream();\n    const streams = [];\n\n    out._streams = streams;\n    out.destroy = () => {\n      let i = streams.length;\n      while (i--) {\n        streams[i].destroy();\n      }\n    };\n\n    // Create a list of all transports for this instance.\n    this.transports\n      .filter(transport => !!transport.stream)\n      .forEach(transport => {\n        const str = transport.stream(options);\n        if (!str) {\n          return;\n        }\n\n        streams.push(str);\n\n        str.on('log', log => {\n          log.transport = log.transport || [];\n          log.transport.push(transport.name);\n          out.emit('log', log);\n        });\n\n        str.on('error', err => {\n          err.transport = err.transport || [];\n          err.transport.push(transport.name);\n          out.emit('error', err);\n        });\n      });\n\n    return out;\n  }\n\n  /**\n   * Returns an object corresponding to a specific timing. When done is called\n   * the timer will finish and log the duration. e.g.:\n   * @returns {Profile} - TODO: add return description.\n   * @example\n   *    const timer = winston.startTimer()\n   *    setTimeout(() => {\n   *      timer.done({\n   *        message: 'Logging message'\n   *      });\n   *    }, 1000);\n   */\n  startTimer() {\n    return new Profiler(this);\n  }\n\n  /**\n   * Tracks the time inbetween subsequent calls to this method with the same\n   * `id` parameter. The second call to this method will log the difference in\n   * milliseconds along with the message.\n   * @param {string} id Unique id of the profiler\n   * @returns {Logger} - TODO: add return description.\n   */\n  profile(id, ...args) {\n    const time = Date.now();\n    if (this.profilers[id]) {\n      const timeEnd = this.profilers[id];\n      delete this.profilers[id];\n\n      // Attempt to be kind to users if they are still using older APIs.\n      if (typeof args[args.length - 2] === 'function') {\n        // eslint-disable-next-line no-console\n        console.warn(\n          'Callback function no longer supported as of winston@3.0.0'\n        );\n        args.pop();\n      }\n\n      // Set the duration property of the metadata\n      const info = typeof args[args.length - 1] === 'object' ? args.pop() : {};\n      info.level = info.level || 'info';\n      info.durationMs = time - timeEnd;\n      info.message = info.message || id;\n      return this.write(info);\n    }\n\n    this.profilers[id] = time;\n    return this;\n  }\n\n  /**\n   * Backwards compatibility to `exceptions.handle` in winston < 3.0.0.\n   * @returns {undefined}\n   * @deprecated\n   */\n  handleExceptions(...args) {\n    // eslint-disable-next-line no-console\n    console.warn(\n      'Deprecated: .handleExceptions() will be removed in winston@4. Use .exceptions.handle()'\n    );\n    this.exceptions.handle(...args);\n  }\n\n  /**\n   * Backwards compatibility to `exceptions.handle` in winston < 3.0.0.\n   * @returns {undefined}\n   * @deprecated\n   */\n  unhandleExceptions(...args) {\n    // eslint-disable-next-line no-console\n    console.warn(\n      'Deprecated: .unhandleExceptions() will be removed in winston@4. Use .exceptions.unhandle()'\n    );\n    this.exceptions.unhandle(...args);\n  }\n\n  /**\n   * Throw a more meaningful deprecation notice\n   * @throws {Error} - TODO: add throws description.\n   */\n  cli() {\n    throw new Error(\n      [\n        'Logger.cli() was removed in winston@3.0.0',\n        'Use a custom winston.formats.cli() instead.',\n        'See: https://github.com/winstonjs/winston/tree/master/UPGRADE-3.0.md'\n      ].join('\\n')\n    );\n  }\n\n  /**\n   * Bubbles the `event` that occured on the specified `transport` up\n   * from this instance.\n   * @param {string} event - The event that occured\n   * @param {Object} transport - Transport on which the event occured\n   * @private\n   */\n  _onEvent(event, transport) {\n    function transportEvent(err) {\n      // https://github.com/winstonjs/winston/issues/1364\n      if (event === 'error' && !this.transports.includes(transport)) {\n        this.add(transport);\n      }\n      this.emit(event, err, transport);\n    }\n\n    if (!transport['__winston' + event]) {\n      transport['__winston' + event] = transportEvent.bind(this);\n      transport.on(event, transport['__winston' + event]);\n    }\n  }\n\n  _addDefaultMeta(msg) {\n    if (this.defaultMeta) {\n      Object.assign(msg, this.defaultMeta);\n    }\n  }\n}\n\nfunction getLevelValue(levels, level) {\n  const value = levels[level];\n  if (!value && value !== 0) {\n    return null;\n  }\n  return value;\n}\n\n/**\n * Represents the current readableState pipe targets for this Logger instance.\n * @type {Array|Object}\n */\nObject.defineProperty(Logger.prototype, 'transports', {\n  configurable: false,\n  enumerable: true,\n  get() {\n    const { pipes } = this._readableState;\n    return !Array.isArray(pipes) ? [pipes].filter(Boolean) : pipes;\n  }\n});\n\nmodule.exports = Logger;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/logger.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/profiler.js":
/*!******************************************************!*\
  !*** ./node_modules/winston/lib/winston/profiler.js ***!
  \******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * profiler.js: TODO: add file header description.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n/**\n * TODO: add class description.\n * @type {Profiler}\n * @private\n */\nclass Profiler {\n  /**\n   * Constructor function for the Profiler instance used by\n   * `Logger.prototype.startTimer`. When done is called the timer will finish\n   * and log the duration.\n   * @param {!Logger} logger - TODO: add param description.\n   * @private\n   */\n  constructor(logger) {\n    const Logger = __webpack_require__(/*! ./logger */ \"(rsc)/./node_modules/winston/lib/winston/logger.js\");\n    if (typeof logger !== 'object' || Array.isArray(logger) || !(logger instanceof Logger)) {\n      throw new Error('Logger is required for profiling');\n    } else {\n      this.logger = logger;\n      this.start = Date.now();\n    }\n  }\n\n  /**\n   * Ends the current timer (i.e. Profiler) instance and logs the `msg` along\n   * with the duration since creation.\n   * @returns {mixed} - TODO: add return description.\n   * @private\n   */\n  done(...args) {\n    if (typeof args[args.length - 1] === 'function') {\n      // eslint-disable-next-line no-console\n      console.warn('Callback function no longer supported as of winston@3.0.0');\n      args.pop();\n    }\n\n    const info = typeof args[args.length - 1] === 'object' ? args.pop() : {};\n    info.level = info.level || 'info';\n    info.durationMs = (Date.now()) - this.start;\n\n    return this.logger.write(info);\n  }\n};\n\nmodule.exports = Profiler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/profiler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/rejection-handler.js":
/*!***************************************************************!*\
  !*** ./node_modules/winston/lib/winston/rejection-handler.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * exception-handler.js: Object for handling uncaughtException events.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst os = __webpack_require__(/*! os */ \"os\");\nconst asyncForEach = __webpack_require__(/*! async/forEach */ \"(rsc)/./node_modules/async/forEach.js\");\nconst debug = __webpack_require__(/*! @dabh/diagnostics */ \"(rsc)/./node_modules/@dabh/diagnostics/node/index.js\")('winston:rejection');\nconst once = __webpack_require__(/*! one-time */ \"(rsc)/./node_modules/one-time/index.js\");\nconst stackTrace = __webpack_require__(/*! stack-trace */ \"(rsc)/./node_modules/stack-trace/lib/stack-trace.js\");\nconst RejectionStream = __webpack_require__(/*! ./rejection-stream */ \"(rsc)/./node_modules/winston/lib/winston/rejection-stream.js\");\n\n/**\n * Object for handling unhandledRejection events.\n * @type {RejectionHandler}\n */\nmodule.exports = class RejectionHandler {\n  /**\n   * TODO: add contructor description\n   * @param {!Logger} logger - TODO: add param description\n   */\n  constructor(logger) {\n    if (!logger) {\n      throw new Error('Logger is required to handle rejections');\n    }\n\n    this.logger = logger;\n    this.handlers = new Map();\n  }\n\n  /**\n   * Handles `unhandledRejection` events for the current process by adding any\n   * handlers passed in.\n   * @returns {undefined}\n   */\n  handle(...args) {\n    args.forEach(arg => {\n      if (Array.isArray(arg)) {\n        return arg.forEach(handler => this._addHandler(handler));\n      }\n\n      this._addHandler(arg);\n    });\n\n    if (!this.catcher) {\n      this.catcher = this._unhandledRejection.bind(this);\n      process.on('unhandledRejection', this.catcher);\n    }\n  }\n\n  /**\n   * Removes any handlers to `unhandledRejection` events for the current\n   * process. This does not modify the state of the `this.handlers` set.\n   * @returns {undefined}\n   */\n  unhandle() {\n    if (this.catcher) {\n      process.removeListener('unhandledRejection', this.catcher);\n      this.catcher = false;\n\n      Array.from(this.handlers.values()).forEach(wrapper =>\n        this.logger.unpipe(wrapper)\n      );\n    }\n  }\n\n  /**\n   * TODO: add method description\n   * @param {Error} err - Error to get information about.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getAllInfo(err) {\n    let message = null;\n    if (err) {\n      message = typeof err === 'string' ? err : err.message;\n    }\n\n    return {\n      error: err,\n      // TODO (indexzero): how do we configure this?\n      level: 'error',\n      message: [\n        `unhandledRejection: ${message || '(no error message)'}`,\n        err && err.stack || '  No stack trace'\n      ].join('\\n'),\n      stack: err && err.stack,\n      rejection: true,\n      date: new Date().toString(),\n      process: this.getProcessInfo(),\n      os: this.getOsInfo(),\n      trace: this.getTrace(err)\n    };\n  }\n\n  /**\n   * Gets all relevant process information for the currently running process.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getProcessInfo() {\n    return {\n      pid: process.pid,\n      uid: process.getuid ? process.getuid() : null,\n      gid: process.getgid ? process.getgid() : null,\n      cwd: process.cwd(),\n      execPath: process.execPath,\n      version: process.version,\n      argv: process.argv,\n      memoryUsage: process.memoryUsage()\n    };\n  }\n\n  /**\n   * Gets all relevant OS information for the currently running process.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getOsInfo() {\n    return {\n      loadavg: os.loadavg(),\n      uptime: os.uptime()\n    };\n  }\n\n  /**\n   * Gets a stack trace for the specified error.\n   * @param {mixed} err - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   */\n  getTrace(err) {\n    const trace = err ? stackTrace.parse(err) : stackTrace.get();\n    return trace.map(site => {\n      return {\n        column: site.getColumnNumber(),\n        file: site.getFileName(),\n        function: site.getFunctionName(),\n        line: site.getLineNumber(),\n        method: site.getMethodName(),\n        native: site.isNative()\n      };\n    });\n  }\n\n  /**\n   * Helper method to add a transport as an exception handler.\n   * @param {Transport} handler - The transport to add as an exception handler.\n   * @returns {void}\n   */\n  _addHandler(handler) {\n    if (!this.handlers.has(handler)) {\n      handler.handleRejections = true;\n      const wrapper = new RejectionStream(handler);\n      this.handlers.set(handler, wrapper);\n      this.logger.pipe(wrapper);\n    }\n  }\n\n  /**\n   * Logs all relevant information around the `err` and exits the current\n   * process.\n   * @param {Error} err - Error to handle\n   * @returns {mixed} - TODO: add return description.\n   * @private\n   */\n  _unhandledRejection(err) {\n    const info = this.getAllInfo(err);\n    const handlers = this._getRejectionHandlers();\n    // Calculate if we should exit on this error\n    let doExit =\n      typeof this.logger.exitOnError === 'function'\n        ? this.logger.exitOnError(err)\n        : this.logger.exitOnError;\n    let timeout;\n\n    if (!handlers.length && doExit) {\n      // eslint-disable-next-line no-console\n      console.warn('winston: exitOnError cannot be true with no rejection handlers.');\n      // eslint-disable-next-line no-console\n      console.warn('winston: not exiting process.');\n      doExit = false;\n    }\n\n    function gracefulExit() {\n      debug('doExit', doExit);\n      debug('process._exiting', process._exiting);\n\n      if (doExit && !process._exiting) {\n        // Remark: Currently ignoring any rejections from transports when\n        // catching unhandled rejections.\n        if (timeout) {\n          clearTimeout(timeout);\n        }\n        // eslint-disable-next-line no-process-exit\n        process.exit(1);\n      }\n    }\n\n    if (!handlers || handlers.length === 0) {\n      return process.nextTick(gracefulExit);\n    }\n\n    // Log to all transports attempting to listen for when they are completed.\n    asyncForEach(\n      handlers,\n      (handler, next) => {\n        const done = once(next);\n        const transport = handler.transport || handler;\n\n        // Debug wrapping so that we can inspect what's going on under the covers.\n        function onDone(event) {\n          return () => {\n            debug(event);\n            done();\n          };\n        }\n\n        transport._ending = true;\n        transport.once('finish', onDone('finished'));\n        transport.once('error', onDone('error'));\n      },\n      () => doExit && gracefulExit()\n    );\n\n    this.logger.log(info);\n\n    // If exitOnError is true, then only allow the logging of exceptions to\n    // take up to `3000ms`.\n    if (doExit) {\n      timeout = setTimeout(gracefulExit, 3000);\n    }\n  }\n\n  /**\n   * Returns the list of transports and exceptionHandlers for this instance.\n   * @returns {Array} - List of transports and exceptionHandlers for this\n   * instance.\n   * @private\n   */\n  _getRejectionHandlers() {\n    // Remark (indexzero): since `logger.transports` returns all of the pipes\n    // from the _readableState of the stream we actually get the join of the\n    // explicit handlers and the implicit transports with\n    // `handleRejections: true`\n    return this.logger.transports.filter(wrap => {\n      const transport = wrap.transport || wrap;\n      return transport.handleRejections;\n    });\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/rejection-handler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/rejection-stream.js":
/*!**************************************************************!*\
  !*** ./node_modules/winston/lib/winston/rejection-stream.js ***!
  \**************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * rejection-stream.js: TODO: add file header handler.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst { Writable } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\n\n/**\n * TODO: add class description.\n * @type {RejectionStream}\n * @extends {Writable}\n */\nmodule.exports = class RejectionStream extends Writable {\n  /**\n   * Constructor function for the RejectionStream responsible for wrapping a\n   * TransportStream; only allowing writes of `info` objects with\n   * `info.rejection` set to true.\n   * @param {!TransportStream} transport - Stream to filter to rejections\n   */\n  constructor(transport) {\n    super({ objectMode: true });\n\n    if (!transport) {\n      throw new Error('RejectionStream requires a TransportStream instance.');\n    }\n\n    this.handleRejections = true;\n    this.transport = transport;\n  }\n\n  /**\n   * Writes the info object to our transport instance if (and only if) the\n   * `rejection` property is set on the info.\n   * @param {mixed} info - TODO: add param description.\n   * @param {mixed} enc - TODO: add param description.\n   * @param {mixed} callback - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   * @private\n   */\n  _write(info, enc, callback) {\n    if (info.rejection) {\n      return this.transport.log(info, callback);\n    }\n\n    callback();\n    return true;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/rejection-stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/tail-file.js":
/*!*******************************************************!*\
  !*** ./node_modules/winston/lib/winston/tail-file.js ***!
  \*******************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * tail-file.js: TODO: add file header description.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst { StringDecoder } = __webpack_require__(/*! string_decoder */ \"string_decoder\");\nconst { Stream } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\n\n/**\n * Simple no-op function.\n * @returns {undefined}\n */\nfunction noop() {}\n\n/**\n * TODO: add function description.\n * @param {Object} options - Options for tail.\n * @param {function} iter - Iterator function to execute on every line.\n* `tail -f` a file. Options must include file.\n * @returns {mixed} - TODO: add return description.\n */\nmodule.exports = (options, iter) => {\n  const buffer = Buffer.alloc(64 * 1024);\n  const decode = new StringDecoder('utf8');\n  const stream = new Stream();\n  let buff = '';\n  let pos = 0;\n  let row = 0;\n\n  if (options.start === -1) {\n    delete options.start;\n  }\n\n  stream.readable = true;\n  stream.destroy = () => {\n    stream.destroyed = true;\n    stream.emit('end');\n    stream.emit('close');\n  };\n\n  fs.open(options.file, 'a+', '0644', (err, fd) => {\n    if (err) {\n      if (!iter) {\n        stream.emit('error', err);\n      } else {\n        iter(err);\n      }\n      stream.destroy();\n      return;\n    }\n\n    (function read() {\n      if (stream.destroyed) {\n        fs.close(fd, noop);\n        return;\n      }\n\n      return fs.read(fd, buffer, 0, buffer.length, pos, (error, bytes) => {\n        if (error) {\n          if (!iter) {\n            stream.emit('error', error);\n          } else {\n            iter(error);\n          }\n          stream.destroy();\n          return;\n        }\n\n        if (!bytes) {\n          if (buff) {\n            // eslint-disable-next-line eqeqeq\n            if (options.start == null || row > options.start) {\n              if (!iter) {\n                stream.emit('line', buff);\n              } else {\n                iter(null, buff);\n              }\n            }\n            row++;\n            buff = '';\n          }\n          return setTimeout(read, 1000);\n        }\n\n        let data = decode.write(buffer.slice(0, bytes));\n        if (!iter) {\n          stream.emit('data', data);\n        }\n\n        data = (buff + data).split(/\\n+/);\n\n        const l = data.length - 1;\n        let i = 0;\n\n        for (; i < l; i++) {\n          // eslint-disable-next-line eqeqeq\n          if (options.start == null || row > options.start) {\n            if (!iter) {\n              stream.emit('line', data[i]);\n            } else {\n              iter(null, data[i]);\n            }\n          }\n          row++;\n        }\n\n        buff = data[l];\n        pos += bytes;\n        return read();\n      });\n    }());\n  });\n\n  if (!iter) {\n    return stream;\n  }\n\n  return stream.destroy;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/tail-file.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/transports/console.js":
/*!****************************************************************!*\
  !*** ./node_modules/winston/lib/winston/transports/console.js ***!
  \****************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint-disable no-console */\n/*\n * console.js: Transport for outputting to the console.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst os = __webpack_require__(/*! os */ \"os\");\nconst { LEVEL, MESSAGE } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst TransportStream = __webpack_require__(/*! winston-transport */ \"(rsc)/./node_modules/winston-transport/index.js\");\n\n/**\n * Transport for outputting to the console.\n * @type {Console}\n * @extends {TransportStream}\n */\nmodule.exports = class Console extends TransportStream {\n  /**\n   * Constructor function for the Console transport object responsible for\n   * persisting log messages and metadata to a terminal or TTY.\n   * @param {!Object} [options={}] - Options for this instance.\n   */\n  constructor(options = {}) {\n    super(options);\n\n    // Expose the name of this Transport on the prototype\n    this.name = options.name || 'console';\n    this.stderrLevels = this._stringArrayToSet(options.stderrLevels);\n    this.consoleWarnLevels = this._stringArrayToSet(options.consoleWarnLevels);\n    this.eol = typeof options.eol === 'string' ? options.eol : os.EOL;\n    this.forceConsole = options.forceConsole || false;\n\n    // Keep a reference to the log, warn, and error console methods\n    // in case they get redirected to this transport after the logger is\n    // instantiated. This prevents a circular reference issue.\n    this._consoleLog = console.log.bind(console);\n    this._consoleWarn = console.warn.bind(console);\n    this._consoleError = console.error.bind(console);\n\n    this.setMaxListeners(30);\n  }\n\n  /**\n   * Core logging method exposed to Winston.\n   * @param {Object} info - TODO: add param description.\n   * @param {Function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  log(info, callback) {\n    setImmediate(() => this.emit('logged', info));\n\n    // Remark: what if there is no raw...?\n    if (this.stderrLevels[info[LEVEL]]) {\n      if (console._stderr && !this.forceConsole) {\n        // Node.js maps `process.stderr` to `console._stderr`.\n        console._stderr.write(`${info[MESSAGE]}${this.eol}`);\n      } else {\n        // console.error adds a newline\n        this._consoleError(info[MESSAGE]);\n      }\n\n      if (callback) {\n        callback(); // eslint-disable-line callback-return\n      }\n      return;\n    } else if (this.consoleWarnLevels[info[LEVEL]]) {\n      if (console._stderr && !this.forceConsole) {\n        // Node.js maps `process.stderr` to `console._stderr`.\n        // in Node.js console.warn is an alias for console.error\n        console._stderr.write(`${info[MESSAGE]}${this.eol}`);\n      } else {\n        // console.warn adds a newline\n        this._consoleWarn(info[MESSAGE]);\n      }\n\n      if (callback) {\n        callback(); // eslint-disable-line callback-return\n      }\n      return;\n    }\n\n    if (console._stdout && !this.forceConsole) {\n      // Node.js maps `process.stdout` to `console._stdout`.\n      console._stdout.write(`${info[MESSAGE]}${this.eol}`);\n    } else {\n      // console.log adds a newline.\n      this._consoleLog(info[MESSAGE]);\n    }\n\n    if (callback) {\n      callback(); // eslint-disable-line callback-return\n    }\n  }\n\n  /**\n   * Returns a Set-like object with strArray's elements as keys (each with the\n   * value true).\n   * @param {Array} strArray - Array of Set-elements as strings.\n   * @param {?string} [errMsg] - Custom error message thrown on invalid input.\n   * @returns {Object} - TODO: add return description.\n   * @private\n   */\n  _stringArrayToSet(strArray, errMsg) {\n    if (!strArray) return {};\n\n    errMsg =\n      errMsg || 'Cannot make set from type other than Array of string elements';\n\n    if (!Array.isArray(strArray)) {\n      throw new Error(errMsg);\n    }\n\n    return strArray.reduce((set, el) => {\n      if (typeof el !== 'string') {\n        throw new Error(errMsg);\n      }\n      set[el] = true;\n\n      return set;\n    }, {});\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/transports/console.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/transports/file.js":
/*!*************************************************************!*\
  !*** ./node_modules/winston/lib/winston/transports/file.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint-disable complexity,max-statements */\n/**\n * file.js: Transport for outputting to a local log file.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst fs = __webpack_require__(/*! fs */ \"fs\");\nconst path = __webpack_require__(/*! path */ \"path\");\nconst asyncSeries = __webpack_require__(/*! async/series */ \"(rsc)/./node_modules/async/series.js\");\nconst zlib = __webpack_require__(/*! zlib */ \"zlib\");\nconst { MESSAGE } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst { Stream, PassThrough } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\nconst TransportStream = __webpack_require__(/*! winston-transport */ \"(rsc)/./node_modules/winston-transport/index.js\");\nconst debug = __webpack_require__(/*! @dabh/diagnostics */ \"(rsc)/./node_modules/@dabh/diagnostics/node/index.js\")('winston:file');\nconst os = __webpack_require__(/*! os */ \"os\");\nconst tailFile = __webpack_require__(/*! ../tail-file */ \"(rsc)/./node_modules/winston/lib/winston/tail-file.js\");\n\n/**\n * Transport for outputting to a local log file.\n * @type {File}\n * @extends {TransportStream}\n */\nmodule.exports = class File extends TransportStream {\n  /**\n   * Constructor function for the File transport object responsible for\n   * persisting log messages and metadata to one or more files.\n   * @param {Object} options - Options for this instance.\n   */\n  constructor(options = {}) {\n    super(options);\n\n    // Expose the name of this Transport on the prototype.\n    this.name = options.name || 'file';\n\n    // Helper function which throws an `Error` in the event that any of the\n    // rest of the arguments is present in `options`.\n    function throwIf(target, ...args) {\n      args.slice(1).forEach(name => {\n        if (options[name]) {\n          throw new Error(`Cannot set ${name} and ${target} together`);\n        }\n      });\n    }\n\n    // Setup the base stream that always gets piped to to handle buffering.\n    this._stream = new PassThrough();\n    this._stream.setMaxListeners(30);\n\n    // Bind this context for listener methods.\n    this._onError = this._onError.bind(this);\n\n    if (options.filename || options.dirname) {\n      throwIf('filename or dirname', 'stream');\n      this._basename = this.filename = options.filename\n        ? path.basename(options.filename)\n        : 'winston.log';\n\n      this.dirname = options.dirname || path.dirname(options.filename);\n      this.options = options.options || { flags: 'a' };\n    } else if (options.stream) {\n      // eslint-disable-next-line no-console\n      console.warn('options.stream will be removed in winston@4. Use winston.transports.Stream');\n      throwIf('stream', 'filename', 'maxsize');\n      this._dest = this._stream.pipe(this._setupStream(options.stream));\n      this.dirname = path.dirname(this._dest.path);\n      // We need to listen for drain events when write() returns false. This\n      // can make node mad at times.\n    } else {\n      throw new Error('Cannot log to file without filename or stream.');\n    }\n\n    this.maxsize = options.maxsize || null;\n    this.rotationFormat = options.rotationFormat || false;\n    this.zippedArchive = options.zippedArchive || false;\n    this.maxFiles = options.maxFiles || null;\n    this.eol = (typeof options.eol === 'string') ? options.eol : os.EOL;\n    this.tailable = options.tailable || false;\n    this.lazy = options.lazy || false;\n\n    // Internal state variables representing the number of files this instance\n    // has created and the current size (in bytes) of the current logfile.\n    this._size = 0;\n    this._pendingSize = 0;\n    this._created = 0;\n    this._drain = false;\n    this._opening = false;\n    this._ending = false;\n    this._fileExist = false;\n\n    if (this.dirname) this._createLogDirIfNotExist(this.dirname);\n    if (!this.lazy) this.open();\n  }\n\n  finishIfEnding() {\n    if (this._ending) {\n      if (this._opening) {\n        this.once('open', () => {\n          this._stream.once('finish', () => this.emit('finish'));\n          setImmediate(() => this._stream.end());\n        });\n      } else {\n        this._stream.once('finish', () => this.emit('finish'));\n        setImmediate(() => this._stream.end());\n      }\n    }\n  }\n\n  /**\n   * Core logging method exposed to Winston. Metadata is optional.\n   * @param {Object} info - TODO: add param description.\n   * @param {Function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  log(info, callback = () => { }) {\n    // Remark: (jcrugzz) What is necessary about this callback(null, true) now\n    // when thinking about 3.x? Should silent be handled in the base\n    // TransportStream _write method?\n    if (this.silent) {\n      callback();\n      return true;\n    }\n\n\n    // Output stream buffer is full and has asked us to wait for the drain event\n    if (this._drain) {\n      this._stream.once('drain', () => {\n        this._drain = false;\n        this.log(info, callback);\n      });\n      return;\n    }\n    if (this._rotate) {\n      this._stream.once('rotate', () => {\n        this._rotate = false;\n        this.log(info, callback);\n      });\n      return;\n    }\n    if (this.lazy) {\n      if (!this._fileExist) {\n        if (!this._opening) {\n          this.open();\n        }\n        this.once('open', () => {\n          this._fileExist = true;\n          this.log(info, callback);\n          return;\n        });\n        return;\n      }\n      if (this._needsNewFile(this._pendingSize)) {\n        this._dest.once('close', () => {\n          if (!this._opening) {\n            this.open();\n          }\n          this.once('open', () => {\n            this.log(info, callback);\n            return;\n          });\n          return;\n        });\n        return;\n      }\n    }\n\n    // Grab the raw string and append the expected EOL.\n    const output = `${info[MESSAGE]}${this.eol}`;\n    const bytes = Buffer.byteLength(output);\n\n    // After we have written to the PassThrough check to see if we need\n    // to rotate to the next file.\n    //\n    // Remark: This gets called too early and does not depict when data\n    // has been actually flushed to disk.\n    function logged() {\n      this._size += bytes;\n      this._pendingSize -= bytes;\n\n      debug('logged %s %s', this._size, output);\n      this.emit('logged', info);\n\n      // Do not attempt to rotate files while rotating\n      if (this._rotate) {\n        return;\n      }\n\n      // Do not attempt to rotate files while opening\n      if (this._opening) {\n        return;\n      }\n\n      // Check to see if we need to end the stream and create a new one.\n      if (!this._needsNewFile()) {\n        return;\n      }\n      if (this.lazy) {\n        this._endStream(() => {this.emit('fileclosed');});\n        return;\n      }\n\n      // End the current stream, ensure it flushes and create a new one.\n      // This could potentially be optimized to not run a stat call but its\n      // the safest way since we are supporting `maxFiles`.\n      this._rotate = true;\n      this._endStream(() => this._rotateFile());\n    }\n\n    // Keep track of the pending bytes being written while files are opening\n    // in order to properly rotate the PassThrough this._stream when the file\n    // eventually does open.\n    this._pendingSize += bytes;\n    if (this._opening\n      && !this.rotatedWhileOpening\n      && this._needsNewFile(this._size + this._pendingSize)) {\n      this.rotatedWhileOpening = true;\n    }\n\n    const written = this._stream.write(output, logged.bind(this));\n    if (!written) {\n      this._drain = true;\n      this._stream.once('drain', () => {\n        this._drain = false;\n        callback();\n      });\n    } else {\n      callback(); // eslint-disable-line callback-return\n    }\n\n    debug('written', written, this._drain);\n\n    this.finishIfEnding();\n\n    return written;\n  }\n\n  /**\n   * Query the transport. Options object is optional.\n   * @param {Object} options - Loggly-like query options for this instance.\n   * @param {function} callback - Continuation to respond to when complete.\n   * TODO: Refactor me.\n   */\n  query(options, callback) {\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    options = normalizeQuery(options);\n    const file = path.join(this.dirname, this.filename);\n    let buff = '';\n    let results = [];\n    let row = 0;\n\n    const stream = fs.createReadStream(file, {\n      encoding: 'utf8'\n    });\n\n    stream.on('error', err => {\n      if (stream.readable) {\n        stream.destroy();\n      }\n      if (!callback) {\n        return;\n      }\n\n      return err.code !== 'ENOENT' ? callback(err) : callback(null, results);\n    });\n\n    stream.on('data', data => {\n      data = (buff + data).split(/\\n+/);\n      const l = data.length - 1;\n      let i = 0;\n\n      for (; i < l; i++) {\n        if (!options.start || row >= options.start) {\n          add(data[i]);\n        }\n        row++;\n      }\n\n      buff = data[l];\n    });\n\n    stream.on('close', () => {\n      if (buff) {\n        add(buff, true);\n      }\n      if (options.order === 'desc') {\n        results = results.reverse();\n      }\n\n      // eslint-disable-next-line callback-return\n      if (callback) callback(null, results);\n    });\n\n    function add(buff, attempt) {\n      try {\n        const log = JSON.parse(buff);\n        if (check(log)) {\n          push(log);\n        }\n      } catch (e) {\n        if (!attempt) {\n          stream.emit('error', e);\n        }\n      }\n    }\n\n    function push(log) {\n      if (\n        options.rows &&\n        results.length >= options.rows &&\n        options.order !== 'desc'\n      ) {\n        if (stream.readable) {\n          stream.destroy();\n        }\n        return;\n      }\n\n      if (options.fields) {\n        log = options.fields.reduce((obj, key) => {\n          obj[key] = log[key];\n          return obj;\n        }, {});\n      }\n\n      if (options.order === 'desc') {\n        if (results.length >= options.rows) {\n          results.shift();\n        }\n      }\n      results.push(log);\n    }\n\n    function check(log) {\n      if (!log) {\n        return;\n      }\n\n      if (typeof log !== 'object') {\n        return;\n      }\n\n      const time = new Date(log.timestamp);\n      if (\n        (options.from && time < options.from) ||\n        (options.until && time > options.until) ||\n        (options.level && options.level !== log.level)\n      ) {\n        return;\n      }\n\n      return true;\n    }\n\n    function normalizeQuery(options) {\n      options = options || {};\n\n      // limit\n      options.rows = options.rows || options.limit || 10;\n\n      // starting row offset\n      options.start = options.start || 0;\n\n      // now\n      options.until = options.until || new Date();\n      if (typeof options.until !== 'object') {\n        options.until = new Date(options.until);\n      }\n\n      // now - 24\n      options.from = options.from || (options.until - (24 * 60 * 60 * 1000));\n      if (typeof options.from !== 'object') {\n        options.from = new Date(options.from);\n      }\n\n      // 'asc' or 'desc'\n      options.order = options.order || 'desc';\n\n      return options;\n    }\n  }\n\n  /**\n   * Returns a log stream for this transport. Options object is optional.\n   * @param {Object} options - Stream options for this instance.\n   * @returns {Stream} - TODO: add return description.\n   * TODO: Refactor me.\n   */\n  stream(options = {}) {\n    const file = path.join(this.dirname, this.filename);\n    const stream = new Stream();\n    const tail = {\n      file,\n      start: options.start\n    };\n\n    stream.destroy = tailFile(tail, (err, line) => {\n      if (err) {\n        return stream.emit('error', err);\n      }\n\n      try {\n        stream.emit('data', line);\n        line = JSON.parse(line);\n        stream.emit('log', line);\n      } catch (e) {\n        stream.emit('error', e);\n      }\n    });\n\n    return stream;\n  }\n\n  /**\n   * Checks to see the filesize of.\n   * @returns {undefined}\n   */\n  open() {\n    // If we do not have a filename then we were passed a stream and\n    // don't need to keep track of size.\n    if (!this.filename) return;\n    if (this._opening) return;\n\n    this._opening = true;\n\n    // Stat the target file to get the size and create the stream.\n    this.stat((err, size) => {\n      if (err) {\n        return this.emit('error', err);\n      }\n      debug('stat done: %s { size: %s }', this.filename, size);\n      this._size = size;\n      this._dest = this._createStream(this._stream);\n      this._opening = false;\n      this.once('open', () => {\n        if (!this._stream.emit('rotate')) {\n          this._rotate = false;\n        }\n      });\n    });\n  }\n\n  /**\n   * Stat the file and assess information in order to create the proper stream.\n   * @param {function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  stat(callback) {\n    const target = this._getFile();\n    const fullpath = path.join(this.dirname, target);\n\n    fs.stat(fullpath, (err, stat) => {\n      if (err && err.code === 'ENOENT') {\n        debug('ENOENT ok', fullpath);\n        // Update internally tracked filename with the new target name.\n        this.filename = target;\n        return callback(null, 0);\n      }\n\n      if (err) {\n        debug(`err ${err.code} ${fullpath}`);\n        return callback(err);\n      }\n\n      if (!stat || this._needsNewFile(stat.size)) {\n        // If `stats.size` is greater than the `maxsize` for this\n        // instance then try again.\n        return this._incFile(() => this.stat(callback));\n      }\n\n      // Once we have figured out what the filename is, set it\n      // and return the size.\n      this.filename = target;\n      callback(null, stat.size);\n    });\n  }\n\n  /**\n   * Closes the stream associated with this instance.\n   * @param {function} cb - TODO: add param description.\n   * @returns {undefined}\n   */\n  close(cb) {\n    if (!this._stream) {\n      return;\n    }\n\n    this._stream.end(() => {\n      if (cb) {\n        cb(); // eslint-disable-line callback-return\n      }\n      this.emit('flush');\n      this.emit('closed');\n    });\n  }\n\n  /**\n   * TODO: add method description.\n   * @param {number} size - TODO: add param description.\n   * @returns {undefined}\n   */\n  _needsNewFile(size) {\n    size = size || this._size;\n    return this.maxsize && size >= this.maxsize;\n  }\n\n  /**\n   * TODO: add method description.\n   * @param {Error} err - TODO: add param description.\n   * @returns {undefined}\n   */\n  _onError(err) {\n    this.emit('error', err);\n  }\n\n  /**\n   * TODO: add method description.\n   * @param {Stream} stream - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   */\n  _setupStream(stream) {\n    stream.on('error', this._onError);\n\n    return stream;\n  }\n\n  /**\n   * TODO: add method description.\n   * @param {Stream} stream - TODO: add param description.\n   * @returns {mixed} - TODO: add return description.\n   */\n  _cleanupStream(stream) {\n    stream.removeListener('error', this._onError);\n    stream.destroy();\n    return stream;\n  }\n\n  /**\n   * TODO: add method description.\n   */\n  _rotateFile() {\n    this._incFile(() => this.open());\n  }\n\n  /**\n   * Unpipe from the stream that has been marked as full and end it so it\n   * flushes to disk.\n   *\n   * @param {function} callback - Callback for when the current file has closed.\n   * @private\n   */\n  _endStream(callback = () => { }) {\n    if (this._dest) {\n      this._stream.unpipe(this._dest);\n      this._dest.end(() => {\n        this._cleanupStream(this._dest);\n        callback();\n      });\n    } else {\n      callback(); // eslint-disable-line callback-return\n    }\n  }\n\n  /**\n   * Returns the WritableStream for the active file on this instance. If we\n   * should gzip the file then a zlib stream is returned.\n   *\n   * @param {ReadableStream} source –PassThrough to pipe to the file when open.\n   * @returns {WritableStream} Stream that writes to disk for the active file.\n   */\n  _createStream(source) {\n    const fullpath = path.join(this.dirname, this.filename);\n\n    debug('create stream start', fullpath, this.options);\n    const dest = fs.createWriteStream(fullpath, this.options)\n      // TODO: What should we do with errors here?\n      .on('error', err => debug(err))\n      .on('close', () => debug('close', dest.path, dest.bytesWritten))\n      .on('open', () => {\n        debug('file open ok', fullpath);\n        this.emit('open', fullpath);\n        source.pipe(dest);\n\n        // If rotation occured during the open operation then we immediately\n        // start writing to a new PassThrough, begin opening the next file\n        // and cleanup the previous source and dest once the source has drained.\n        if (this.rotatedWhileOpening) {\n          this._stream = new PassThrough();\n          this._stream.setMaxListeners(30);\n          this._rotateFile();\n          this.rotatedWhileOpening = false;\n          this._cleanupStream(dest);\n          source.end();\n        }\n      });\n\n    debug('create stream ok', fullpath);\n    return dest;\n  }\n\n  /**\n   * TODO: add method description.\n   * @param {function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  _incFile(callback) {\n    debug('_incFile', this.filename);\n    const ext = path.extname(this._basename);\n    const basename = path.basename(this._basename, ext);\n    const tasks = [];\n\n    if (this.zippedArchive) {\n      tasks.push(\n        function (cb) {\n          const num = this._created > 0 && !this.tailable ? this._created : '';\n          this._compressFile(\n            path.join(this.dirname, `${basename}${num}${ext}`),\n            path.join(this.dirname, `${basename}${num}${ext}.gz`),\n            cb\n          );\n        }.bind(this)\n      );\n    }\n\n    tasks.push(\n      function (cb) {\n        if (!this.tailable) {\n          this._created += 1;\n          this._checkMaxFilesIncrementing(ext, basename, cb);\n        } else {\n          this._checkMaxFilesTailable(ext, basename, cb);\n        }\n      }.bind(this)\n    );\n\n    asyncSeries(tasks, callback);\n  }\n\n  /**\n   * Gets the next filename to use for this instance in the case that log\n   * filesizes are being capped.\n   * @returns {string} - TODO: add return description.\n   * @private\n   */\n  _getFile() {\n    const ext = path.extname(this._basename);\n    const basename = path.basename(this._basename, ext);\n    const isRotation = this.rotationFormat\n      ? this.rotationFormat()\n      : this._created;\n\n    // Caveat emptor (indexzero): rotationFormat() was broken by design When\n    // combined with max files because the set of files to unlink is never\n    // stored.\n    return !this.tailable && this._created\n      ? `${basename}${isRotation}${ext}`\n      : `${basename}${ext}`;\n  }\n\n  /**\n   * Increment the number of files created or checked by this instance.\n   * @param {mixed} ext - TODO: add param description.\n   * @param {mixed} basename - TODO: add param description.\n   * @param {mixed} callback - TODO: add param description.\n   * @returns {undefined}\n   * @private\n   */\n  _checkMaxFilesIncrementing(ext, basename, callback) {\n    // Check for maxFiles option and delete file.\n    if (!this.maxFiles || this._created < this.maxFiles) {\n      return setImmediate(callback);\n    }\n\n    const oldest = this._created - this.maxFiles;\n    const isOldest = oldest !== 0 ? oldest : '';\n    const isZipped = this.zippedArchive ? '.gz' : '';\n    const filePath = `${basename}${isOldest}${ext}${isZipped}`;\n    const target = path.join(this.dirname, filePath);\n\n    fs.unlink(target, callback);\n  }\n\n  /**\n   * Roll files forward based on integer, up to maxFiles. e.g. if base if\n   * file.log and it becomes oversized, roll to file1.log, and allow file.log\n   * to be re-used. If file is oversized again, roll file1.log to file2.log,\n   * roll file.log to file1.log, and so on.\n   * @param {mixed} ext - TODO: add param description.\n   * @param {mixed} basename - TODO: add param description.\n   * @param {mixed} callback - TODO: add param description.\n   * @returns {undefined}\n   * @private\n   */\n  _checkMaxFilesTailable(ext, basename, callback) {\n    const tasks = [];\n    if (!this.maxFiles) {\n      return;\n    }\n\n    // const isZipped = this.zippedArchive ? '.gz' : '';\n    const isZipped = this.zippedArchive ? '.gz' : '';\n    for (let x = this.maxFiles - 1; x > 1; x--) {\n      tasks.push(function (i, cb) {\n        let fileName = `${basename}${(i - 1)}${ext}${isZipped}`;\n        const tmppath = path.join(this.dirname, fileName);\n\n        fs.exists(tmppath, exists => {\n          if (!exists) {\n            return cb(null);\n          }\n\n          fileName = `${basename}${i}${ext}${isZipped}`;\n          fs.rename(tmppath, path.join(this.dirname, fileName), cb);\n        });\n      }.bind(this, x));\n    }\n\n    asyncSeries(tasks, () => {\n      fs.rename(\n        path.join(this.dirname, `${basename}${ext}${isZipped}`),\n        path.join(this.dirname, `${basename}1${ext}${isZipped}`),\n        callback\n      );\n    });\n  }\n\n  /**\n   * Compresses src to dest with gzip and unlinks src\n   * @param {string} src - path to source file.\n   * @param {string} dest - path to zipped destination file.\n   * @param {Function} callback - callback called after file has been compressed.\n   * @returns {undefined}\n   * @private\n   */\n  _compressFile(src, dest, callback) {\n    fs.access(src, fs.F_OK, (err) => {\n      if (err) {\n        return callback();\n      }\n      var gzip = zlib.createGzip();\n      var inp = fs.createReadStream(src);\n      var out = fs.createWriteStream(dest);\n      out.on('finish', () => {\n        fs.unlink(src, callback);\n      });\n      inp.pipe(gzip).pipe(out);\n    });\n  }\n\n  _createLogDirIfNotExist(dirPath) {\n    /* eslint-disable no-sync */\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n    /* eslint-enable no-sync */\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/transports/file.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/transports/http.js":
/*!*************************************************************!*\
  !*** ./node_modules/winston/lib/winston/transports/http.js ***!
  \*************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * http.js: Transport for outputting to a json-rpcserver.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst http = __webpack_require__(/*! http */ \"http\");\nconst https = __webpack_require__(/*! https */ \"https\");\nconst { Stream } = __webpack_require__(/*! readable-stream */ \"(rsc)/./node_modules/readable-stream/readable.js\");\nconst TransportStream = __webpack_require__(/*! winston-transport */ \"(rsc)/./node_modules/winston-transport/index.js\");\nconst { configure } = __webpack_require__(/*! safe-stable-stringify */ \"(rsc)/./node_modules/safe-stable-stringify/index.js\");\n\n/**\n * Transport for outputting to a json-rpc server.\n * @type {Stream}\n * @extends {TransportStream}\n */\nmodule.exports = class Http extends TransportStream {\n  /**\n   * Constructor function for the Http transport object responsible for\n   * persisting log messages and metadata to a terminal or TTY.\n   * @param {!Object} [options={}] - Options for this instance.\n   */\n  // eslint-disable-next-line max-statements\n  constructor(options = {}) {\n    super(options);\n\n    this.options = options;\n    this.name = options.name || 'http';\n    this.ssl = !!options.ssl;\n    this.host = options.host || 'localhost';\n    this.port = options.port;\n    this.auth = options.auth;\n    this.path = options.path || '';\n    this.maximumDepth = options.maximumDepth;\n    this.agent = options.agent;\n    this.headers = options.headers || {};\n    this.headers['content-type'] = 'application/json';\n    this.batch = options.batch || false;\n    this.batchInterval = options.batchInterval || 5000;\n    this.batchCount = options.batchCount || 10;\n    this.batchOptions = [];\n    this.batchTimeoutID = -1;\n    this.batchCallback = {};\n\n    if (!this.port) {\n      this.port = this.ssl ? 443 : 80;\n    }\n  }\n\n  /**\n   * Core logging method exposed to Winston.\n   * @param {Object} info - TODO: add param description.\n   * @param {function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  log(info, callback) {\n    this._request(info, null, null, (err, res) => {\n      if (res && res.statusCode !== 200) {\n        err = new Error(`Invalid HTTP Status Code: ${res.statusCode}`);\n      }\n\n      if (err) {\n        this.emit('warn', err);\n      } else {\n        this.emit('logged', info);\n      }\n    });\n\n    // Remark: (jcrugzz) Fire and forget here so requests dont cause buffering\n    // and block more requests from happening?\n    if (callback) {\n      setImmediate(callback);\n    }\n  }\n\n  /**\n   * Query the transport. Options object is optional.\n   * @param {Object} options -  Loggly-like query options for this instance.\n   * @param {function} callback - Continuation to respond to when complete.\n   * @returns {undefined}\n   */\n  query(options, callback) {\n    if (typeof options === 'function') {\n      callback = options;\n      options = {};\n    }\n\n    options = {\n      method: 'query',\n      params: this.normalizeQuery(options)\n    };\n\n    const auth = options.params.auth || null;\n    delete options.params.auth;\n\n    const path = options.params.path || null;\n    delete options.params.path;\n\n    this._request(options, auth, path, (err, res, body) => {\n      if (res && res.statusCode !== 200) {\n        err = new Error(`Invalid HTTP Status Code: ${res.statusCode}`);\n      }\n\n      if (err) {\n        return callback(err);\n      }\n\n      if (typeof body === 'string') {\n        try {\n          body = JSON.parse(body);\n        } catch (e) {\n          return callback(e);\n        }\n      }\n\n      callback(null, body);\n    });\n  }\n\n  /**\n   * Returns a log stream for this transport. Options object is optional.\n   * @param {Object} options - Stream options for this instance.\n   * @returns {Stream} - TODO: add return description\n   */\n  stream(options = {}) {\n    const stream = new Stream();\n    options = {\n      method: 'stream',\n      params: options\n    };\n\n    const path = options.params.path || null;\n    delete options.params.path;\n\n    const auth = options.params.auth || null;\n    delete options.params.auth;\n\n    let buff = '';\n    const req = this._request(options, auth, path);\n\n    stream.destroy = () => req.destroy();\n    req.on('data', data => {\n      data = (buff + data).split(/\\n+/);\n      const l = data.length - 1;\n\n      let i = 0;\n      for (; i < l; i++) {\n        try {\n          stream.emit('log', JSON.parse(data[i]));\n        } catch (e) {\n          stream.emit('error', e);\n        }\n      }\n\n      buff = data[l];\n    });\n    req.on('error', err => stream.emit('error', err));\n\n    return stream;\n  }\n\n  /**\n   * Make a request to a winstond server or any http server which can\n   * handle json-rpc.\n   * @param {function} options - Options to sent the request.\n   * @param {Object?} auth - authentication options\n   * @param {string} path - request path\n   * @param {function} callback - Continuation to respond to when complete.\n   */\n  _request(options, auth, path, callback) {\n    options = options || {};\n\n    auth = auth || this.auth;\n    path = path || this.path || '';\n\n    if (this.batch) {\n      this._doBatch(options, callback, auth, path);\n    } else {\n      this._doRequest(options, callback, auth, path);\n    }\n  }\n\n  /**\n   * Send or memorize the options according to batch configuration\n   * @param {function} options - Options to sent the request.\n   * @param {function} callback - Continuation to respond to when complete.\n   * @param {Object?} auth - authentication options\n   * @param {string} path - request path\n   */\n  _doBatch(options, callback, auth, path) {\n    this.batchOptions.push(options);\n    if (this.batchOptions.length === 1) {\n      // First message stored, it's time to start the timeout!\n      const me = this;\n      this.batchCallback = callback;\n      this.batchTimeoutID = setTimeout(function () {\n        // timeout is reached, send all messages to endpoint\n        me.batchTimeoutID = -1;\n        me._doBatchRequest(me.batchCallback, auth, path);\n      }, this.batchInterval);\n    }\n    if (this.batchOptions.length === this.batchCount) {\n      // max batch count is reached, send all messages to endpoint\n      this._doBatchRequest(this.batchCallback, auth, path);\n    }\n  }\n\n  /**\n   * Initiate a request with the memorized batch options, stop the batch timeout\n   * @param {function} callback - Continuation to respond to when complete.\n   * @param {Object?} auth - authentication options\n   * @param {string} path - request path\n   */\n  _doBatchRequest(callback, auth, path) {\n    if (this.batchTimeoutID > 0) {\n      clearTimeout(this.batchTimeoutID);\n      this.batchTimeoutID = -1;\n    }\n    const batchOptionsCopy = this.batchOptions.slice();\n    this.batchOptions = [];\n    this._doRequest(batchOptionsCopy, callback, auth, path);\n  }\n\n  /**\n   * Make a request to a winstond server or any http server which can\n   * handle json-rpc.\n   * @param {function} options - Options to sent the request.\n   * @param {function} callback - Continuation to respond to when complete.\n   * @param {Object?} auth - authentication options\n   * @param {string} path - request path\n   */\n  _doRequest(options, callback, auth, path) {\n    // Prepare options for outgoing HTTP request\n    const headers = Object.assign({}, this.headers);\n    if (auth && auth.bearer) {\n      headers.Authorization = `Bearer ${auth.bearer}`;\n    }\n    const req = (this.ssl ? https : http).request({\n      ...this.options,\n      method: 'POST',\n      host: this.host,\n      port: this.port,\n      path: `/${path.replace(/^\\//, '')}`,\n      headers: headers,\n      auth: (auth && auth.username && auth.password) ? (`${auth.username}:${auth.password}`) : '',\n      agent: this.agent\n    });\n\n    req.on('error', callback);\n    req.on('response', res => (\n      res.on('end', () => callback(null, res)).resume()\n    ));\n    const jsonStringify = configure({\n      ...(this.maximumDepth && { maximumDepth: this.maximumDepth })\n    });\n    req.end(Buffer.from(jsonStringify(options, this.options.replacer), 'utf8'));\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/transports/http.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/transports/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/winston/lib/winston/transports/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/**\n * transports.js: Set of all transports Winston knows about.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\n/**\n * TODO: add property description.\n * @type {Console}\n */\nObject.defineProperty(exports, \"Console\", ({\n  configurable: true,\n  enumerable: true,\n  get() {\n    return __webpack_require__(/*! ./console */ \"(rsc)/./node_modules/winston/lib/winston/transports/console.js\");\n  }\n}));\n\n/**\n * TODO: add property description.\n * @type {File}\n */\nObject.defineProperty(exports, \"File\", ({\n  configurable: true,\n  enumerable: true,\n  get() {\n    return __webpack_require__(/*! ./file */ \"(rsc)/./node_modules/winston/lib/winston/transports/file.js\");\n  }\n}));\n\n/**\n * TODO: add property description.\n * @type {Http}\n */\nObject.defineProperty(exports, \"Http\", ({\n  configurable: true,\n  enumerable: true,\n  get() {\n    return __webpack_require__(/*! ./http */ \"(rsc)/./node_modules/winston/lib/winston/transports/http.js\");\n  }\n}));\n\n/**\n * TODO: add property description.\n * @type {Stream}\n */\nObject.defineProperty(exports, \"Stream\", ({\n  configurable: true,\n  enumerable: true,\n  get() {\n    return __webpack_require__(/*! ./stream */ \"(rsc)/./node_modules/winston/lib/winston/transports/stream.js\");\n  }\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/transports/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/lib/winston/transports/stream.js":
/*!***************************************************************!*\
  !*** ./node_modules/winston/lib/winston/transports/stream.js ***!
  \***************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/**\n * stream.js: Transport for outputting to any arbitrary stream.\n *\n * (C) 2010 Charlie Robbins\n * MIT LICENCE\n */\n\n\n\nconst isStream = __webpack_require__(/*! is-stream */ \"(rsc)/./node_modules/is-stream/index.js\");\nconst { MESSAGE } = __webpack_require__(/*! triple-beam */ \"(rsc)/./node_modules/triple-beam/index.js\");\nconst os = __webpack_require__(/*! os */ \"os\");\nconst TransportStream = __webpack_require__(/*! winston-transport */ \"(rsc)/./node_modules/winston-transport/index.js\");\n\n/**\n * Transport for outputting to any arbitrary stream.\n * @type {Stream}\n * @extends {TransportStream}\n */\nmodule.exports = class Stream extends TransportStream {\n  /**\n   * Constructor function for the Console transport object responsible for\n   * persisting log messages and metadata to a terminal or TTY.\n   * @param {!Object} [options={}] - Options for this instance.\n   */\n  constructor(options = {}) {\n    super(options);\n\n    if (!options.stream || !isStream(options.stream)) {\n      throw new Error('options.stream is required.');\n    }\n\n    // We need to listen for drain events when write() returns false. This can\n    // make node mad at times.\n    this._stream = options.stream;\n    this._stream.setMaxListeners(Infinity);\n    this.isObjectMode = options.stream._writableState.objectMode;\n    this.eol = (typeof options.eol === 'string') ? options.eol : os.EOL;\n  }\n\n  /**\n   * Core logging method exposed to Winston.\n   * @param {Object} info - TODO: add param description.\n   * @param {Function} callback - TODO: add param description.\n   * @returns {undefined}\n   */\n  log(info, callback) {\n    setImmediate(() => this.emit('logged', info));\n    if (this.isObjectMode) {\n      this._stream.write(info);\n      if (callback) {\n        callback(); // eslint-disable-line callback-return\n      }\n      return;\n    }\n\n    this._stream.write(`${info[MESSAGE]}${this.eol}`);\n    if (callback) {\n      callback(); // eslint-disable-line callback-return\n    }\n    return;\n  }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/winston/lib/winston/transports/stream.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/winston/package.json":
/*!*******************************************!*\
  !*** ./node_modules/winston/package.json ***!
  \*******************************************/
/***/ ((module) => {

module.exports = /*#__PURE__*/JSON.parse('{"name":"winston","description":"A logger for just about everything.","version":"3.17.0","author":"Charlie Robbins <<EMAIL>>","maintainers":["David Hyde <<EMAIL>>"],"repository":{"type":"git","url":"https://github.com/winstonjs/winston.git"},"keywords":["winston","logger","logging","logs","sysadmin","bunyan","pino","loglevel","tools","json","stream"],"dependencies":{"@dabh/diagnostics":"^2.0.2","@colors/colors":"^1.6.0","async":"^3.2.3","is-stream":"^2.0.0","logform":"^2.7.0","one-time":"^1.0.0","readable-stream":"^3.4.0","safe-stable-stringify":"^2.3.1","stack-trace":"0.0.x","triple-beam":"^1.3.0","winston-transport":"^4.9.0"},"devDependencies":{"@babel/cli":"^7.23.9","@babel/core":"^7.24.0","@babel/preset-env":"^7.24.0","@dabh/eslint-config-populist":"^4.4.0","@types/node":"^20.11.24","abstract-winston-transport":"^0.5.1","assume":"^2.2.0","cross-spawn-async":"^2.2.5","eslint":"^8.57.0","hock":"^1.4.1","mocha":"^10.3.0","nyc":"^17.1.0","rimraf":"5.0.1","split2":"^4.1.0","std-mocks":"^2.0.0","through2":"^4.0.2","winston-compat":"^0.1.5"},"main":"./lib/winston.js","browser":"./dist/winston","types":"./index.d.ts","scripts":{"lint":"eslint lib/*.js lib/winston/*.js lib/winston/**/*.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist","test":"rimraf test/fixtures/logs/* && mocha","test:coverage":"nyc npm run test:unit","test:unit":"mocha test/unit","test:integration":"mocha test/integration","build":"rimraf dist && babel lib -d dist","prepublishOnly":"npm run build"},"engines":{"node":">= 12.0.0"},"license":"MIT"}');

/***/ })

};
;