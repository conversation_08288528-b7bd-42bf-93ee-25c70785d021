#!/usr/bin/env node
/**
 * 验证所有 43 个飞书日历工具是否正确集成
 */

const http = require('http');

async function makeRequest(url) {
  return new Promise((resolve, reject) => {
    const req = http.get(url, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        try {
          resolve(JSON.parse(data));
        } catch (e) {
          resolve(data);
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(5000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });
  });
}

async function verifyTools() {
  console.log('🔍 验证飞书日历工具集成...\n');
  
  try {
    // 1. 检查健康状态
    console.log('📋 1. 检查服务健康状态...');
    const health = await makeRequest('http://localhost:3000/api/health');
    if (health.status === 'ok') {
      console.log('✅ 服务健康状态正常');
    } else {
      console.log('❌ 服务健康状态异常:', health);
      return;
    }
    
    // 2. 获取工具信息
    console.log('\n📋 2. 获取工具信息...');
    const toolsData = await makeRequest('http://localhost:3000/api/tools');
    
    if (!toolsData.success) {
      console.log('❌ 获取工具信息失败:', toolsData.error);
      return;
    }
    
    const { tools, categories, stats, validation } = toolsData;
    
    // 3. 验证工具数量
    console.log('\n📋 3. 验证工具数量...');
    console.log(`📊 总工具数: ${tools.length}`);
    console.log(`📊 预期工具数: ${stats.expectedTotal}`);
    console.log(`📊 是否完整: ${validation.isComplete ? '✅' : '❌'}`);
    
    if (tools.length >= 43) {
      console.log('✅ 工具数量验证通过');
    } else {
      console.log('❌ 工具数量不足，缺少', 43 - tools.length, '个工具');
    }
    
    // 4. 验证工具分类
    console.log('\n📋 4. 验证工具分类...');
    console.log('📊 分类统计:');
    categories.forEach(cat => {
      console.log(`  - ${cat.category}: ${cat.count} 个工具`);
    });
    
    // 5. 验证关键工具
    console.log('\n📋 5. 验证关键工具...');
    const keyTools = [
      'calendar.v4.calendar.list',
      'calendar.v4.calendar.create',
      'calendar.v4.calendarEvent.create',
      'calendar.v4.calendarEvent.list',
      'calendar.v4.calendarEvent.search',
      'calendar.v4.calendarEventAttendee.create'
    ];
    
    const foundTools = tools.map(t => t.name);
    let missingTools = [];
    
    keyTools.forEach(toolName => {
      if (foundTools.includes(toolName)) {
        console.log(`✅ ${toolName}`);
      } else {
        console.log(`❌ ${toolName} - 缺失`);
        missingTools.push(toolName);
      }
    });
    
    // 6. 验证工具结构
    console.log('\n📋 6. 验证工具结构...');
    const sampleTool = tools[0];
    const requiredFields = ['name', 'description', 'inputSchema'];
    let structureValid = true;
    
    requiredFields.forEach(field => {
      if (sampleTool[field]) {
        console.log(`✅ ${field} 字段存在`);
      } else {
        console.log(`❌ ${field} 字段缺失`);
        structureValid = false;
      }
    });
    
    // 7. 总结
    console.log('\n' + '='.repeat(60));
    console.log('🎉 工具集成验证完成!');
    console.log('='.repeat(60));
    
    console.log('\n📊 验证结果:');
    console.log(`✅ 服务状态: 正常`);
    console.log(`${tools.length >= 43 ? '✅' : '❌'} 工具数量: ${tools.length}/${stats.expectedTotal}`);
    console.log(`${categories.length > 0 ? '✅' : '❌'} 工具分类: ${categories.length} 个分类`);
    console.log(`${missingTools.length === 0 ? '✅' : '❌'} 关键工具: ${keyTools.length - missingTools.length}/${keyTools.length} 个可用`);
    console.log(`${structureValid ? '✅' : '❌'} 工具结构: ${structureValid ? '正确' : '有问题'}`);
    
    if (missingTools.length > 0) {
      console.log('\n⚠️  缺失的关键工具:');
      missingTools.forEach(tool => console.log(`  - ${tool}`));
    }
    
    console.log('\n🔧 下一步建议:');
    console.log('1. 在浏览器中访问 http://localhost:3000/tools 查看完整工具列表');
    console.log('2. 测试具体的工具调用功能');
    console.log('3. 验证工具参数验证逻辑');
    console.log('4. 进行端到端集成测试');
    
  } catch (error) {
    console.log('❌ 验证过程中出现错误:', error.message);
    console.log('\n💡 请确保:');
    console.log('1. Next.js 服务器正在运行 (npm run dev)');
    console.log('2. 服务器监听在 http://localhost:3000');
    console.log('3. 所有依赖已正确安装');
  }
}

// 运行验证
verifyTools();
