# 飞书智能日历助手

[![CI/CD Pipeline](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/ci.yml/badge.svg)](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/ci.yml)
[![Test Suite](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/test.yml/badge.svg)](https://github.com/ifcheung2012/feishu-coze-plugin/actions/workflows/test.yml)
[![codecov](https://codecov.io/gh/ifcheung2012/feishu-coze-plugin/branch/main/graph/badge.svg)](https://codecov.io/gh/ifcheung2012/feishu-coze-plugin)
[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

一个基于FastAPI的飞书日历智能助手，支持自然语言交互和日历管理，具备完整的CI/CD流程和生产级质量保证。

## ✨ 功能特性

### 🤖 智能交互
- **自然语言理解**: 支持"明天下午3点安排会议"等自然语言表达
- **智能意图识别**: 自动识别日历操作、聊天、记录等不同意图
- **多轮对话**: 上下文感知的智能交互
- **交互式补全**: 信息不完整时自动询问补充

### 📅 日历管理
- **飞书OAuth2.0授权**: 安全的用户认证和授权
- **完整日历操作**: 创建、查询、修改、删除日历事件
- **实时数据同步**: 与飞书日历实时同步
- **多日历支持**: 支持个人和共享日历

### 🔧 系统特性
- **自动令牌刷新**: 智能的令牌管理和自动刷新
- **多存储支持**: Supabase/文件/内存存储
- **RESTful API**: 完整的API接口
- **生产级质量**: 完整的测试覆盖和错误处理

### 🧪 开发特性
- **完整测试套件**: 单元测试、集成测试、端到端测试
- **CI/CD流程**: 自动化测试、构建和部署
- **代码质量保证**: Linting、格式化、安全检查
- **文档完善**: API文档和开发指南
- **统一SDK**: 基于官方lark_oapi SDK的统一客户端
- **工厂模式**: 智能的客户端管理和实例化

## 🚀 最新改造 (2024年)

### 统一SDK架构
- **官方SDK集成**: 完全迁移到飞书官方 `lark_oapi` SDK
- **统一客户端**: 新的 `FeishuCalendarLark` 类集成所有功能
- **工厂模式**: `FeishuClientFactory` 提供智能的客户端管理
- **向后兼容**: 保持旧API的兼容性，平滑迁移

### 代码质量提升
- **日志优化**: 清理冗余调试日志，保留关键信息
- **错误处理**: 统一的异常体系和重试机制
- **代码组织**: 合理的模块分离和依赖管理
- **文档完善**: 详细的使用指南和API文档

### 测试体系建设
- **分层测试**: 单元测试、集成测试、端到端测试
- **自动化CI/CD**: GitHub Actions完整流水线
- **覆盖率监控**: 代码覆盖率报告和质量门禁
- **性能测试**: 基准测试和负载测试

### 开发体验优化
- **快速开始**: 一键式环境设置和测试运行
- **丰富示例**: 完整的代码示例和最佳实践
- **调试工具**: 便捷的调试脚本和日志配置
- **部署指南**: 详细的部署和运维文档

## 🏗️ 技术架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端/客户端    │    │   FastAPI应用    │    │   飞书开放平台   │
│                │────▶│                │────▶│                │
│  - Web界面      │    │  - API路由      │    │  - OAuth认证    │
│  - 移动应用     │    │  - 业务逻辑     │    │  - 日历API      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌─────────────────┐
                       │   Supabase DB   │
                       │                │
                       │  - 用户数据     │
                       │  - 令牌存储     │
                       └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Python 3.9+
- 飞书开放平台应用
- Supabase账户（推荐）

### 安装

```bash
git clone https://github.com/ifcheung2012/feishu-coze-plugin.git
cd feishu-coze-plugin
pip install -r requirements.txt
```

### 配置

创建 `.env` 文件并填写配置：

```env
# 飞书应用配置
FEISHU_CLIENT_ID=your_feishu_app_id
FEISHU_CLIENT_SECRET=your_feishu_app_secret
REDIRECT_URI=http://localhost:5000/auth/callback
APP_URL=http://localhost:5000

# 数据库配置
SUPABASE_URL=your_supabase_url
SUPABASE_KEY=your_supabase_key
STORAGE_TYPE=supabase

# AI配置
SILICONFLOW_API_KEY=your_ai_api_key
LLM_MODEL=Qwen/Qwen3-14B

# 系统配置
FEISHU_ENV=local
LOG_LEVEL=INFO
```

### 运行

```bash
python main.py
```

访问 http://localhost:5000

## 🧪 开发和测试

### 使用新的统一SDK

```python
from integrations.feishu import get_feishu_client

# 获取统一的飞书客户端
client = get_feishu_client()

# 认证流程
result = await client.exchange_code("authorization_code")
user_info = await client.get_user_info("access_token")

# 日历操作
calendars = client.get_calendars("user_id")
events = client.get_calendar_events("user_id", "calendar_id")

# 事件管理
event = await client.create_event(
    user_id="user_id",
    calendar_id="calendar_id",
    summary="会议标题",
    start_time={"timestamp": "1672531200"},
    end_time={"timestamp": "1672534800"}
)
```

### 安装开发依赖

```bash
pip install -r requirements-dev.txt
```

### 🧪 测试指南

#### 测试环境设置

1. **创建测试环境配置**：
```bash
# 复制测试环境配置模板
cp tests/test.env.example tests/test.env

# 编辑测试配置
# 设置必需的环境变量：FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
# 可选设置：TEST_ACCESS_TOKEN（用于真实API测试）
```

2. **安装测试依赖**：
```bash
pip install -r requirements-test.txt
```

#### 运行测试

**使用统一测试脚本（推荐）**：
```bash
# 运行基础功能测试（默认，推荐）
python tests/run_tests.py

# 运行基础功能测试，详细输出
python tests/run_tests.py --type basic -v

# 运行所有测试
python tests/run_tests.py --type all -v

# 生成覆盖率报告
python tests/run_tests.py --type basic --coverage
```

**直接使用pytest**：
```bash
# 运行所有测试
pytest

# 运行特定类型的测试
pytest tests/unit/          # 单元测试
pytest tests/integration/   # 集成测试
pytest tests/e2e/          # 端到端测试
pytest tests/performance/   # 性能测试

# 详细输出
pytest -v

# 生成覆盖率报告
pytest --cov=integrations --cov=api --cov=services --cov-report=html
```

#### 测试类型说明

| 测试类型 | 文件/目录 | 描述 | 运行时间 |
|---------|-----------|------|----------|
| **基础+业务功能测试** | `tests/test_basic_functionality.py` | 验证技术基础设施+核心业务功能 | 快速（< 30秒） |
| **单元测试** | `tests/unit/` | 测试单个函数和类的功能 | 快速（< 30秒） |
| **集成测试** | `tests/integration/` | 测试模块间的交互 | 中等（1-2分钟） |
| **端到端测试** | `tests/e2e/` | 测试完整的用户流程 | 较慢（2-5分钟） |
| **性能测试** | `tests/performance/` | 测试系统性能指标 | 较慢（3-10分钟） |

#### 测试特性

- **完整业务覆盖**: 验证认证、日历管理、事件管理、工作流引擎等核心业务功能
- **真实数据测试**: 使用真实的API客户端和存储系统，无Mock
- **分层测试架构**: 基础设施测试 + 业务功能测试双重保障
- **自动环境检查**: 自动验证必需的环境变量
- **智能跳过**: 缺少配置时自动跳过相关测试
- **详细报告**: 提供执行时间、通过率、错误信息
- **自动清理**: 测试完成后自动清理测试数据

#### 测试最佳实践

1. **开发时运行基础功能测试**：
```bash
python tests/run_tests.py
```

2. **提交前运行基础功能测试**：
```bash
python tests/run_tests.py --type basic --coverage
```

3. **部署前运行完整检查**：
```bash
python scripts/deployment_check.py
```

#### 故障排除

**常见问题**：

1. **环境变量缺失**：
```bash
# 检查环境变量
python tests/run_tests.py
# 会自动显示缺失的环境变量
```

2. **测试数据冲突**：
```bash
# 测试使用时间戳创建唯一数据，通常不会冲突
# 如有问题，可以清理测试环境后重试
```

3. **网络测试失败**：
```bash
# 设置环境变量启用网络测试
export ALLOW_NETWORK_TESTS=true
export TEST_ACCESS_TOKEN=your_real_token
```

### 代码质量检查

```bash
# 代码格式化
black .
isort .

# 代码检查
flake8 .
mypy .

# 安全检查
bandit -r .
safety check
```

### Git钩子

```bash
# 安装pre-commit钩子
pre-commit install

# 手动运行所有钩子
pre-commit run --all-files
```

## 📚 API文档

启动服务后访问：
- **Swagger UI**: http://localhost:5000/docs
- **ReDoc**: http://localhost:5000/redoc

### 主要端点

| 端点 | 方法 | 描述 |
|------|------|------|
| `/` | GET | 健康检查 |
| `/login` | GET | 开始OAuth认证 |
| `/auth/callback` | GET | OAuth回调 |
| `/auth/callback/calendars/` | GET | 获取日历列表 |
| `/auth/callback/events/today` | GET | 获取今日事件 |
| `/auth/callback/calendars/{id}/events` | POST | 创建事件 |
| `/auth/callback/calendars/{id}/events/{event_id}` | DELETE | 删除事件 |
| `/chat/` | POST | 智能聊天 |

## 🚀 部署

### 本地测试

```bash
# 运行部署前检查
python deployment_check.py

# 运行API端点测试
python test_api_endpoints.py
```

### GitHub Actions CI/CD

项目配置了完整的CI/CD流程：

1. **代码推送** → 自动触发测试
2. **测试通过** → 构建部署包
3. **主分支** → 自动部署到生产环境

### Render.com部署

1. 连接GitHub仓库
2. 设置环境变量（参考`.env.production`）
3. 部署服务

部署后的应用地址：https://feishu-coze-plugin.onrender.com

## 📁 项目结构

```
feishu-coze-plugin/
├── api/                    # API路由
│   ├── routers/           # 路由模块
│   └── dependencies.py   # 依赖注入
├── integrations/          # 第三方集成
│   ├── feishu/           # 飞书API客户端
│   │   ├── client_factory.py  # 客户端工厂
│   │   ├── calendar_client.py # 统一客户端
│   │   └── api_client.py      # 遗留API客户端
│   └── storage/          # 数据存储
├── services/             # 业务服务
├── tests/                # 测试套件
│   ├── unit/            # 单元测试
│   ├── integration/     # 集成测试
│   ├── e2e/             # 端到端测试
│   ├── performance/     # 性能测试
│   ├── examples/        # 测试示例
│   ├── conftest.py      # 测试配置
│   ├── run_tests.py     # 统一测试运行器
│   └── test.env.example # 测试环境配置模板
├── docs/                 # 项目文档
│   ├── feishu_sdk_usage.md      # SDK使用指南
│   ├── api_client_analysis.md   # API分析
│   ├── large_files_analysis.md  # 文件分析
│   └── project_refactoring_summary.md # 改造总结
├── scripts/              # 工具脚本
│   ├── run_tests.py     # 测试运行脚本（已移至tests/）
│   └── deployment_check.py # 部署检查脚本
├── .github/workflows/    # GitHub Actions
├── main.py              # 应用入口
├── config.py            # 配置管理
├── requirements.txt     # 生产依赖
├── requirements-dev.txt # 开发依赖
└── pytest.ini           # 测试配置
```

## 🔄 开发流程

1. **本地开发** → 编写代码和测试
2. **质量检查** → 运行linting和测试
3. **提交代码** → 触发CI/CD流程
4. **代码审查** → Pull Request审查
5. **合并部署** → 自动部署到生产环境

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启Pull Request

## 📚 文档

### 开发文档
- [测试指南](TESTING.md) - 完整的测试体系和使用指南
- [飞书SDK使用指南](docs/feishu_sdk_usage.md) - 详细的SDK使用说明和示例
- [API客户端分析](docs/api_client_analysis.md) - API功能详细分析
- [项目改造总结](docs/project_refactoring_summary.md) - 最新改造成果总结

### 快速链接
- [API文档](http://localhost:5000/docs) - 交互式API文档
- [测试覆盖率报告](htmlcov/index.html) - 代码覆盖率详情
- [GitHub Actions](https://github.com/ifcheung2012/feishu-coze-plugin/actions) - CI/CD流水线

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如果您遇到问题或有建议，请：

1. 查看 [Issues](https://github.com/ifcheung2012/feishu-coze-plugin/issues)
2. 创建新的Issue
3. 联系维护者

---

**🎉 感谢使用飞书智能日历助手！**
