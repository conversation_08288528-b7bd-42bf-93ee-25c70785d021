# 飞书智能日历助手使用指南

## 概述

飞书智能日历助手是一个基于硅基流动LLM和飞书MCP服务的智能日历管理工具。您可以使用自然语言来操作飞书日历和日程，无需记忆复杂的命令格式。

## 功能特性

### 🤖 智能对话
- 基于硅基流动Qwen/Qwen3-14B模型
- 支持自然语言理解和响应
- 智能意图识别和任务分解

### 📅 日历操作
- **查看日历**: 获取您的日历列表
- **查看日程**: 查看指定时间的日程安排
- **创建日程**: 创建新的日历事件
- **搜索日程**: 根据关键词搜索相关日程
- **创建日历**: 创建新的日历分类

### 🔧 MCP集成
- 直接连接飞书MCP服务
- 实时数据同步
- 支持用户访问令牌认证

## 快速开始

### 1. 启动助手

```bash
# 方式1: 使用启动脚本（推荐）
python start_llm_calendar_chat.py

# 方式2: 使用CLI工具
python scripts/cli.py --llm-mcp

# 方式3: 直接运行
python scripts/llm_mcp_cli.py
```

### 2. 等待服务启动

程序会自动：
1. 启动飞书MCP服务
2. 连接LLM客户端
3. 初始化工具列表

看到以下信息表示启动成功：
```
✅ MCP服务连接成功！
🤖 LLM客户端已就绪！
🎉 飞书智能日历助手已启动！
```

## 使用示例

### 基本查询

```
🤖 您: 查看我的日历列表
🤖 助手: 我来为您获取日历列表...
[显示您的所有日历]

🤖 您: 查看我今天的日程
🤖 助手: 正在查询您今天的日程安排...
[显示今天的所有日程]
```

### 创建日程

```
🤖 您: 明天下午2点安排一个项目讨论会议，持续1小时
🤖 助手: 我来为您创建这个会议...
[自动解析时间并创建日程]

🤖 您: 下周一上午9点到11点安排团队例会
🤖 助手: 正在为您安排团队例会...
[创建指定时间段的会议]
```

### 搜索功能

```
🤖 您: 搜索包含"项目"的会议
🤖 助手: 正在搜索相关会议...
[显示所有包含"项目"关键词的会议]

🤖 您: 查找本周的重要会议
🤖 助手: 让我为您查找本周的会议安排...
[显示本周的会议列表]
```

### 日历管理

```
🤖 您: 创建一个新的日历叫"个人事务"
🤖 助手: 我来为您创建新的日历...
[创建名为"个人事务"的新日历]
```

## 支持的命令

### 系统命令
- `help` - 显示帮助信息
- `quit`/`exit`/`q` - 退出程序
- `clear` - 清屏
- `history` - 显示对话历史

### 自然语言示例

#### 查看类
- "查看我的日历"
- "显示我今天的日程"
- "我明天有什么安排"
- "本周的会议有哪些"

#### 创建类
- "明天下午3点安排一个会议"
- "下周一上午开个项目讨论会"
- "创建一个新日历叫工作日程"
- "安排一个1小时的培训会议"

#### 搜索类
- "搜索包含项目的会议"
- "查找本月的重要会议"
- "找一下关于培训的日程"

## 技术架构

### LLM层
- **模型**: 硅基流动 Qwen/Qwen3-14B
- **功能**: 自然语言理解、意图识别、响应生成
- **工具调用**: 支持Function Calling

### MCP层
- **服务**: 飞书官方MCP服务
- **连接**: stdio模式
- **认证**: 用户访问令牌
- **工具**: 日历和事件CRUD操作

### 集成层
- **工具映射**: LLM工具调用到MCP方法的映射
- **错误处理**: 统一的错误处理和用户反馈
- **对话管理**: 多轮对话上下文维护

## 配置说明

### 环境变量

确保以下环境变量已正确配置：

```bash
# 硅基流动API配置
SILICONFLOW_API_KEY=your_api_key
LLM_MODEL=Qwen/Qwen3-14B
LLM_TEMPERATURE=0.7
LLM_MAX_TOKENS=2048

# 飞书应用配置（MCP服务使用）
FEISHU_CLIENT_ID=your_client_id
FEISHU_CLIENT_SECRET=your_client_secret
```

### MCP服务配置

MCP服务使用以下配置：
- **应用ID**: cli_a76a68f612bf900c
- **应用密钥**: EVumG3wCHsDBeJRfpbmJkfRhzCns73jC
- **认证模式**: OAuth用户访问令牌
- **支持工具**: 日历和事件相关操作

## 故障排除

### 常见问题

1. **MCP服务启动失败**
   - 检查Node.js环境是否正确安装
   - 确认网络连接正常
   - 验证飞书应用配置

2. **LLM调用失败**
   - 检查硅基流动API密钥
   - 确认网络连接
   - 查看API配额是否充足

3. **工具调用错误**
   - 检查飞书应用权限
   - 确认用户访问令牌有效
   - 验证日历访问权限

### 调试模式

使用调试模式进行问题诊断：

```bash
# MCP调试模式
python scripts/llm_mcp_cli.py --mode mcp

# 查看详细日志
export LOG_LEVEL=DEBUG
python start_llm_calendar_chat.py
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的日历和事件操作
- 集成硅基流动LLM和飞书MCP
- 提供自然语言交互界面

## 贡献指南

欢迎提交Issue和Pull Request来改进这个工具！

### 开发环境设置

1. 克隆项目
2. 安装依赖：`pip install -r requirements.txt`
3. 配置环境变量
4. 运行测试：`python -m pytest tests/`

### 代码结构

- `scripts/llm_mcp_cli.py` - 主要的聊天界面实现
- `core/ai/llm_client.py` - LLM客户端
- `core/mcp/working_mcp_client.py` - MCP客户端
- `start_llm_calendar_chat.py` - 启动脚本
