"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/colorspace";
exports.ids = ["vendor-chunks/colorspace"];
exports.modules = {

/***/ "(rsc)/./node_modules/colorspace/index.js":
/*!******************************************!*\
  !*** ./node_modules/colorspace/index.js ***!
  \******************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("\n\nvar color = __webpack_require__(/*! color */ \"(rsc)/./node_modules/color/index.js\")\n  , hex = __webpack_require__(/*! text-hex */ \"(rsc)/./node_modules/text-hex/index.js\");\n\n/**\n * Generate a color for a given name. But be reasonably smart about it by\n * understanding name spaces and coloring each namespace a bit lighter so they\n * still have the same base color as the root.\n *\n * @param {string} namespace The namespace\n * @param {string} [delimiter] The delimiter\n * @returns {string} color\n */\nmodule.exports = function colorspace(namespace, delimiter) {\n  var split = namespace.split(delimiter || ':');\n  var base = hex(split[0]);\n\n  if (!split.length) return base;\n\n  for (var i = 0, l = split.length - 1; i < l; i++) {\n    base = color(base)\n    .mix(color(hex(split[i + 1])))\n    .saturate(1)\n    .hex();\n  }\n\n  return base;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvY29sb3JzcGFjZS9pbmRleC5qcyIsIm1hcHBpbmdzIjoiQUFBYTs7QUFFYixZQUFZLG1CQUFPLENBQUMsa0RBQU87QUFDM0IsVUFBVSxtQkFBTyxDQUFDLHdEQUFVOztBQUU1QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxRQUFRO0FBQ25CLFdBQVcsUUFBUTtBQUNuQixhQUFhLFFBQVE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsd0NBQXdDLE9BQU87QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY2FsbWNwLy4vbm9kZV9tb2R1bGVzL2NvbG9yc3BhY2UvaW5kZXguanM/YzYzZiJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbnZhciBjb2xvciA9IHJlcXVpcmUoJ2NvbG9yJylcbiAgLCBoZXggPSByZXF1aXJlKCd0ZXh0LWhleCcpO1xuXG4vKipcbiAqIEdlbmVyYXRlIGEgY29sb3IgZm9yIGEgZ2l2ZW4gbmFtZS4gQnV0IGJlIHJlYXNvbmFibHkgc21hcnQgYWJvdXQgaXQgYnlcbiAqIHVuZGVyc3RhbmRpbmcgbmFtZSBzcGFjZXMgYW5kIGNvbG9yaW5nIGVhY2ggbmFtZXNwYWNlIGEgYml0IGxpZ2h0ZXIgc28gdGhleVxuICogc3RpbGwgaGF2ZSB0aGUgc2FtZSBiYXNlIGNvbG9yIGFzIHRoZSByb290LlxuICpcbiAqIEBwYXJhbSB7c3RyaW5nfSBuYW1lc3BhY2UgVGhlIG5hbWVzcGFjZVxuICogQHBhcmFtIHtzdHJpbmd9IFtkZWxpbWl0ZXJdIFRoZSBkZWxpbWl0ZXJcbiAqIEByZXR1cm5zIHtzdHJpbmd9IGNvbG9yXG4gKi9cbm1vZHVsZS5leHBvcnRzID0gZnVuY3Rpb24gY29sb3JzcGFjZShuYW1lc3BhY2UsIGRlbGltaXRlcikge1xuICB2YXIgc3BsaXQgPSBuYW1lc3BhY2Uuc3BsaXQoZGVsaW1pdGVyIHx8ICc6Jyk7XG4gIHZhciBiYXNlID0gaGV4KHNwbGl0WzBdKTtcblxuICBpZiAoIXNwbGl0Lmxlbmd0aCkgcmV0dXJuIGJhc2U7XG5cbiAgZm9yICh2YXIgaSA9IDAsIGwgPSBzcGxpdC5sZW5ndGggLSAxOyBpIDwgbDsgaSsrKSB7XG4gICAgYmFzZSA9IGNvbG9yKGJhc2UpXG4gICAgLm1peChjb2xvcihoZXgoc3BsaXRbaSArIDFdKSkpXG4gICAgLnNhdHVyYXRlKDEpXG4gICAgLmhleCgpO1xuICB9XG5cbiAgcmV0dXJuIGJhc2U7XG59O1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/colorspace/index.js\n");

/***/ })

};
;