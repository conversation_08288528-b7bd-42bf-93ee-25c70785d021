/**
 * MCP 工具适配器
 * 使用核心日历工具集，只保留项目必需的功能
 */

import { MCPTool } from '../../types/mcp';

/**
 * 核心日历工具集 - 只保留项目必需的功能
 * 基于 feishu-calendar-v4-core.ts 定义
 */

/**
 * 核心日历工具（精简版）- 9个核心工具
 */
export const CORE_CALENDAR_TOOLS: MCPTool[] = [
  // 📅 日历管理
  {
    name: 'calendar.v4.calendar.list',
    description: '获取日历列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '分页大小，最大值为 1000',
          minimum: 1,
          maximum: 1000
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        sync_token: {
          type: 'string',
          description: '同步标记，用于增量同步'
        }
      }
    }
  },
  {
    name: 'calendar.v4.calendar.get',
    description: '获取单个日历详情',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.list',
    description: '获取日历事件列表',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 1000
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        sync_token: {
          type: 'string',
          description: '同步标记'
        },
        start_time: {
          type: 'string',
          description: '开始时间（时间戳）'
        },
        end_time: {
          type: 'string',
          description: '结束时间（时间戳）'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.create',
    description: '创建日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        summary: {
          type: 'string',
          description: '事件标题'
        },
        description: {
          type: 'string',
          description: '事件描述'
        },
        start_time: {
          type: 'object',
          description: '开始时间',
          properties: {
            timestamp: {
              type: 'string',
              description: '时间戳'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp']
        },
        end_time: {
          type: 'object',
          description: '结束时间',
          properties: {
            timestamp: {
              type: 'string',
              description: '时间戳'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp']
        }
      },
      required: ['calendar_id', 'summary', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.search',
    description: '搜索日历事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        query: {
          type: 'string',
          description: '搜索关键词'
        },
        start_time: {
          type: 'string',
          description: '搜索开始时间'
        },
        end_time: {
          type: 'string',
          description: '搜索结束时间'
        }
      },
      required: ['calendar_id']
    }
  }
];

/**
 * 日历访问控制工具
 */
export const CALENDAR_ACL_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendarAcl.create',
    description: '创建日历访问控制',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        role: {
          type: 'string',
          enum: ['unknown', 'free_busy_reader', 'reader', 'writer', 'owner'],
          description: '访问权限角色'
        },
        scope: {
          type: 'object',
          properties: {
            type: {
              type: 'string',
              enum: ['user'],
              description: '权限生效范围类型'
            },
            user_id: {
              type: 'string',
              description: '用户ID'
            }
          },
          required: ['type']
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'role', 'scope']
    }
  },
  {
    name: 'calendar.v4.calendarAcl.delete',
    description: '删除日历访问控制',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        acl_id: {
          type: 'string',
          description: '访问控制ID'
        }
      },
      required: ['calendar_id', 'acl_id']
    }
  },
  {
    name: 'calendar.v4.calendarAcl.list',
    description: '获取日历访问控制列表',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小，最小值10',
          minimum: 10
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendarAcl.subscription',
    description: '订阅日历访问控制变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendarAcl.unsubscription',
    description: '取消订阅日历访问控制变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  }
];

/**
 * 扩展的日历管理工具
 */
export const EXTENDED_CALENDAR_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendar.create',
    description: '创建共享日历',
    inputSchema: {
      type: 'object',
      properties: {
        summary: {
          type: 'string',
          description: '日历标题'
        },
        description: {
          type: 'string',
          description: '日历描述'
        },
        permissions: {
          type: 'string',
          enum: ['private', 'show_only_free_busy', 'public'],
          description: '日历公开范围'
        },
        color: {
          type: 'number',
          description: '日历颜色（RGB int32值）'
        },
        summary_alias: {
          type: 'string',
          description: '日历备注名'
        }
      }
    }
  },
  {
    name: 'calendar.v4.calendar.delete',
    description: '删除共享日历',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendar.patch',
    description: '更新日历信息',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        summary: {
          type: 'string',
          description: '日历标题'
        },
        description: {
          type: 'string',
          description: '日历描述'
        },
        permissions: {
          type: 'string',
          enum: ['private', 'show_only_free_busy', 'public'],
          description: '日历公开范围'
        },
        color: {
          type: 'number',
          description: '日历颜色'
        },
        summary_alias: {
          type: 'string',
          description: '日历备注名'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendar.primary',
    description: '获取主日历信息',
    inputSchema: {
      type: 'object',
      properties: {
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      }
    }
  },
  {
    name: 'calendar.v4.calendar.search',
    description: '搜索日历',
    inputSchema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: '搜索关键词'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 200
        }
      },
      required: ['query']
    }
  },
  {
    name: 'calendar.v4.calendar.subscribe',
    description: '订阅日历',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendar.subscription',
    description: '订阅日历变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendar.unsubscribe',
    description: '取消订阅日历',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendar.unsubscription',
    description: '取消订阅日历变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  }
];

/**
 * 日历事件参与者工具
 */
export const CALENDAR_EVENT_ATTENDEE_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendarEventAttendee.batchDelete',
    description: '批量删除日程参与人',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        attendee_ids: {
          type: 'array',
          items: {
            type: 'string'
          },
          description: '参与人ID列表'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id', 'attendee_ids']
    }
  },
  {
    name: 'calendar.v4.calendarEventAttendee.chatMembersBatchCreate',
    description: '批量添加群成员为日程参与人',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        chat_id: {
          type: 'string',
          description: '群聊ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id', 'chat_id']
    }
  },
  {
    name: 'calendar.v4.calendarEventAttendee.create',
    description: '添加日程参与人',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        attendees: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              type: {
                type: 'string',
                enum: ['user', 'chat', 'resource', 'third_party'],
                description: '参与人类型'
              },
              attendee_id: {
                type: 'string',
                description: '参与人ID'
              },
              rsvp_status: {
                type: 'string',
                enum: ['needs_action', 'accept', 'tentative', 'decline', 'removed'],
                description: '参与状态'
              },
              is_optional: {
                type: 'boolean',
                description: '是否为可选参与人'
              },
              display_name: {
                type: 'string',
                description: '参与人名称'
              }
            },
            required: ['type', 'attendee_id']
          },
          description: '参与人列表'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id', 'attendees']
    }
  },
  {
    name: 'calendar.v4.calendarEventAttendee.list',
    description: '获取日程参与人列表',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 500
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEventAttendeeChatMember.list',
    description: '获取日程参与人群成员列表',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        attendee_id: {
          type: 'string',
          description: '参与人ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 500
        }
      },
      required: ['calendar_id', 'event_id', 'attendee_id']
    }
  }
];

/**
 * 日历事件扩展工具
 */
export const CALENDAR_EVENT_EXTENDED_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendarEvent.delete',
    description: '删除日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        need_notification: {
          type: 'boolean',
          description: '是否给日程参与人发送bot通知'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.get',
    description: '获取日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.instanceView',
    description: '获取日程实例视图',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        start_time: {
          type: 'string',
          description: '查询开始时间'
        },
        end_time: {
          type: 'string',
          description: '查询结束时间'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 500
        }
      },
      required: ['calendar_id', 'event_id', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.patch',
    description: '更新日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        summary: {
          type: 'string',
          description: '日程标题'
        },
        description: {
          type: 'string',
          description: '日程描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '秒级时间戳'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          description: '开始时间'
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '秒级时间戳'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          description: '结束时间'
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '日程公开范围'
        },
        attendee_ability: {
          type: 'string',
          enum: ['none', 'can_see_others', 'can_invite_others', 'can_modify_event'],
          description: '参与人权限'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '日程占用的忙闲状态'
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '地点地址'
            },
            latitude: {
              type: 'number',
              description: '地点纬度'
            },
            longitude: {
              type: 'number',
              description: '地点经度'
            }
          },
          description: '日程地点'
        },
        color: {
          type: 'number',
          description: '日程颜色'
        },
        reminders: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              minutes: {
                type: 'number',
                description: '提前多少分钟提醒'
              }
            }
          },
          description: '日程提醒列表'
        },
        recurrence: {
          type: 'string',
          description: '重复规则'
        },
        status: {
          type: 'string',
          enum: ['tentative', 'confirmed', 'cancelled'],
          description: '日程状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.instances',
    description: '获取重复日程的实例',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        start_time: {
          type: 'string',
          description: '查询开始时间'
        },
        end_time: {
          type: 'string',
          description: '查询结束时间'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        },
        page_token: {
          type: 'string',
          description: '分页标记'
        },
        page_size: {
          type: 'number',
          description: '分页大小',
          minimum: 1,
          maximum: 500
        }
      },
      required: ['calendar_id', 'event_id', 'start_time', 'end_time']
    }
  }
];

/**
 * 会议聊天工具
 */
export const CALENDAR_EVENT_MEETING_CHAT_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendarEventMeetingChat.create',
    description: '创建会议群聊',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEventMeetingChat.delete',
    description: '删除会议群聊',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        meeting_chat_id: {
          type: 'string',
          description: '会议群聊ID'
        }
      },
      required: ['calendar_id', 'event_id', 'meeting_chat_id']
    }
  },
  {
    name: 'calendar.v4.calendarEventMeetingChat.patch',
    description: '更新会议群聊',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        meeting_chat_id: {
          type: 'string',
          description: '会议群聊ID'
        }
      },
      required: ['calendar_id', 'event_id', 'meeting_chat_id']
    }
  },
  {
    name: 'calendar.v4.calendarEventMeetingMinute.create',
    description: '创建会议纪要',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        content: {
          type: 'string',
          description: '会议纪要内容'
        }
      },
      required: ['calendar_id', 'event_id', 'content']
    }
  }
];

/**
 * 其他日历工具
 */
export const OTHER_CALENDAR_TOOLS: MCPTool[] = [
  {
    name: 'calendar.v4.calendarEvent.reply',
    description: '回复日程邀请',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        },
        event_id: {
          type: 'string',
          description: '日程ID'
        },
        rsvp_status: {
          type: 'string',
          enum: ['accept', 'tentative', 'decline'],
          description: '回复状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id', 'rsvp_status']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.subscription',
    description: '订阅日程变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.unsubscription',
    description: '取消订阅日程变更事件',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历ID'
        }
      },
      required: ['calendar_id']
    }
  },
  {
    name: 'calendar.v4.exchangeBinding.create',
    description: '创建Exchange绑定',
    inputSchema: {
      type: 'object',
      properties: {
        admin_account: {
          type: 'string',
          description: '管理员账号'
        },
        exchange_account: {
          type: 'string',
          description: 'Exchange账号'
        },
        user_id: {
          type: 'string',
          description: '用户ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['admin_account', 'exchange_account', 'user_id']
    }
  },
  {
    name: 'calendar.v4.exchangeBinding.delete',
    description: '删除Exchange绑定',
    inputSchema: {
      type: 'object',
      properties: {
        exchange_binding_id: {
          type: 'string',
          description: 'Exchange绑定ID'
        }
      },
      required: ['exchange_binding_id']
    }
  },
  {
    name: 'calendar.v4.exchangeBinding.get',
    description: '获取Exchange绑定',
    inputSchema: {
      type: 'object',
      properties: {
        exchange_binding_id: {
          type: 'string',
          description: 'Exchange绑定ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['exchange_binding_id']
    }
  },
  {
    name: 'calendar.v4.freebusy.list',
    description: '查询忙闲信息',
    inputSchema: {
      type: 'object',
      properties: {
        time_min: {
          type: 'string',
          description: '查询开始时间'
        },
        time_max: {
          type: 'string',
          description: '查询结束时间'
        },
        user_id: {
          type: 'string',
          description: '用户ID'
        },
        room_id: {
          type: 'string',
          description: '会议室ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['time_min', 'time_max']
    }
  },
  {
    name: 'calendar.v4.setting.generateCaldavConf',
    description: '生成CalDAV配置',
    inputSchema: {
      type: 'object',
      properties: {
        device_name: {
          type: 'string',
          description: '设备名称'
        }
      }
    }
  },
  {
    name: 'calendar.v4.timeoffEvent.create',
    description: '创建请假日程',
    inputSchema: {
      type: 'object',
      properties: {
        user_id: {
          type: 'string',
          description: '用户ID'
        },
        timezone: {
          type: 'string',
          description: '时区'
        },
        start_time: {
          type: 'string',
          description: '请假开始时间'
        },
        end_time: {
          type: 'string',
          description: '请假结束时间'
        },
        title: {
          type: 'string',
          description: '请假标题'
        },
        description: {
          type: 'string',
          description: '请假描述'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['user_id', 'timezone', 'start_time', 'end_time', 'title']
    }
  },
  {
    name: 'calendar.v4.timeoffEvent.delete',
    description: '删除请假日程',
    inputSchema: {
      type: 'object',
      properties: {
        timeoff_event_id: {
          type: 'string',
          description: '请假日程ID'
        }
      },
      required: ['timeoff_event_id']
    }
  }
];

/**
 * 核心日历工具（9个精选工具）
 */
export const ALL_FEISHU_CALENDAR_TOOLS: MCPTool[] = CORE_CALENDAR_TOOLS;

/**
 * 根据工具名称获取工具定义
 */
export function getToolByName(name: string): MCPTool | undefined {
  return ALL_FEISHU_CALENDAR_TOOLS.find(tool => tool.name === name);
}

/**
 * 验证工具参数（简化版本）
 */
export function validateToolArguments(toolName: string, args: any): { valid: boolean; errors?: string[] } {
  const tool = getToolByName(toolName);
  if (!tool) {
    return { valid: false, errors: [`Unknown tool: ${toolName}`] };
  }

  // 简单的必需参数检查
  const required = tool.inputSchema.required || [];
  const missing = required.filter(field => !(field in args));

  if (missing.length > 0) {
    return {
      valid: false,
      errors: [`Missing required fields: ${missing.join(', ')}`]
    };
  }

  return { valid: true };
}
