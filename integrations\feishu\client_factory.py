"""
飞书客户端工厂
提供统一的客户端获取接口，支持不同场景下的客户端选择
"""

import logging
from typing import Optional

from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET

from .calendar_client import FeishuCalendarLark

logger = logging.getLogger(__name__)


class FeishuClientFactory:
    """飞书客户端工厂类"""
    
    _calendar_client_instance: Optional[FeishuCalendarLark] = None
    
    @classmethod
    def get_calendar_client(
        cls, 
        app_id: Optional[str] = None, 
        app_secret: Optional[str] = None,
        force_new: bool = False
    ) -> FeishuCalendarLark:
        """
        获取日历客户端实例
        
        Args:
            app_id: 应用ID，默认使用配置文件中的值
            app_secret: 应用密钥，默认使用配置文件中的值
            force_new: 是否强制创建新实例
            
        Returns:
            FeishuCalendarLark: 日历客户端实例
        """
        app_id = app_id or FEISHU_CLIENT_ID
        app_secret = app_secret or FEISHU_CLIENT_SECRET
        
        if not app_id or not app_secret:
            raise ValueError("飞书应用ID和密钥不能为空")
        
        # 如果需要强制创建新实例，或者还没有实例，或者配置发生变化
        if (force_new or 
            cls._calendar_client_instance is None or 
            cls._calendar_client_instance.app_id != app_id or 
            cls._calendar_client_instance.app_secret != app_secret):
            
            logger.info("创建新的飞书日历客户端实例")
            cls._calendar_client_instance = FeishuCalendarLark(app_id, app_secret)
        
        return cls._calendar_client_instance
    
    @classmethod
    def get_unified_client(
        cls,
        app_id: Optional[str] = None,
        app_secret: Optional[str] = None,
        force_new: bool = False
    ) -> FeishuCalendarLark:
        """
        获取统一的飞书客户端（推荐使用）
        
        这个方法返回的客户端集成了认证、日历、事件等所有功能
        
        Args:
            app_id: 应用ID，默认使用配置文件中的值
            app_secret: 应用密钥，默认使用配置文件中的值
            force_new: 是否强制创建新实例
            
        Returns:
            FeishuCalendarLark: 统一的飞书客户端实例
        """
        return cls.get_calendar_client(app_id, app_secret, force_new)
    
    @classmethod
    def reset_instances(cls):
        """重置所有客户端实例"""
        logger.info("重置所有飞书客户端实例")
        cls._calendar_client_instance = None


# 便捷函数，用于快速获取客户端实例
def get_feishu_client(
    app_id: Optional[str] = None,
    app_secret: Optional[str] = None,
    force_new: bool = False
) -> FeishuCalendarLark:
    """
    快速获取飞书客户端实例
    
    Args:
        app_id: 应用ID，默认使用配置文件中的值
        app_secret: 应用密钥，默认使用配置文件中的值
        force_new: 是否强制创建新实例
        
    Returns:
        FeishuCalendarLark: 飞书客户端实例
    """
    return FeishuClientFactory.get_unified_client(app_id, app_secret, force_new)


def get_calendar_client(
    app_id: Optional[str] = None,
    app_secret: Optional[str] = None,
    force_new: bool = False
) -> FeishuCalendarLark:
    """
    快速获取日历客户端实例（向后兼容）
    
    Args:
        app_id: 应用ID，默认使用配置文件中的值
        app_secret: 应用密钥，默认使用配置文件中的值
        force_new: 是否强制创建新实例
        
    Returns:
        FeishuCalendarLark: 日历客户端实例
    """
    return FeishuClientFactory.get_calendar_client(app_id, app_secret, force_new)


# 客户端类型枚举
class ClientType:
    """客户端类型常量"""
    CALENDAR = "calendar"
    UNIFIED = "unified"  # 推荐使用的统一客户端


class FeishuClientManager:
    """飞书客户端管理器，提供更高级的客户端管理功能"""
    
    def __init__(self):
        self._clients = {}
        self._default_config = {
            "app_id": FEISHU_CLIENT_ID,
            "app_secret": FEISHU_CLIENT_SECRET
        }
    
    def get_client(
        self,
        client_type: str = ClientType.UNIFIED,
        app_id: Optional[str] = None,
        app_secret: Optional[str] = None,
        instance_name: str = "default"
    ) -> FeishuCalendarLark:
        """
        获取指定类型的客户端实例
        
        Args:
            client_type: 客户端类型
            app_id: 应用ID
            app_secret: 应用密钥
            instance_name: 实例名称，用于管理多个实例
            
        Returns:
            FeishuCalendarLark: 客户端实例
        """
        app_id = app_id or self._default_config["app_id"]
        app_secret = app_secret or self._default_config["app_secret"]
        
        client_key = f"{client_type}_{instance_name}_{app_id}"
        
        if client_key not in self._clients:
            if client_type in [ClientType.CALENDAR, ClientType.UNIFIED]:
                self._clients[client_key] = FeishuCalendarLark(app_id, app_secret)
            else:
                raise ValueError(f"不支持的客户端类型: {client_type}")
        
        return self._clients[client_key]
    
    def remove_client(self, client_type: str, instance_name: str = "default"):
        """移除指定的客户端实例"""
        client_key = f"{client_type}_{instance_name}"
        keys_to_remove = [key for key in self._clients.keys() if key.startswith(client_key)]
        for key in keys_to_remove:
            del self._clients[key]
    
    def clear_all_clients(self):
        """清除所有客户端实例"""
        self._clients.clear()
    
    def get_client_count(self) -> int:
        """获取当前管理的客户端实例数量"""
        return len(self._clients)


# 全局客户端管理器实例
client_manager = FeishuClientManager()
