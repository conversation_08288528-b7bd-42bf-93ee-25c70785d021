#!/usr/bin/env python3
"""
获取所有飞书日历
包括分页数据，确保获取完整的日历列表
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional, List

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class AllCalendarsFetcher:
    """获取所有日历的工具"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        test_token = os.environ.get('TEST_ACCESS_TOKEN')
        test_user_id = os.environ.get('TEST_USER_ID')
        
        if test_token and test_user_id:
            self.user_id = test_user_id
            self.user_token = test_token
            print(f"✅ 已加载测试凭据")
            print(f"   用户 ID: {self.user_id}")
            print(f"   Token 预览: {test_token[:20]}...")
        else:
            print("❌ 未找到测试凭据")
    
    def call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            # 添加用户信息到参数
            if self.user_id:
                arguments['user_id'] = self.user_id
            if self.user_token and tool_name.startswith('calendar.v4.'):
                arguments['user_access_token'] = self.user_token
            
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            print(f"📤 调用工具: {tool_name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            print(f"🌐 HTTP 状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误: {response.text}")
                return None
            
            result = response.json()
            print(f"📊 响应成功: {result.get('success', 'unknown')}")
            
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def get_all_calendars_with_pagination(self, tool_name: str) -> List[Dict[str, Any]]:
        """获取所有日历（包括分页）"""
        all_calendars = []
        page_token = None
        page_num = 1
        
        print(f"\n🔄 使用工具 {tool_name} 获取所有日历（支持分页）")
        print("="*60)
        
        while True:
            print(f"\n📄 获取第 {page_num} 页...")
            
            # 构建参数
            arguments = {"page_size": 50}
            if page_token:
                arguments["page_token"] = page_token
            
            # 调用工具
            result = self.call_mcp_tool(tool_name, arguments)
            
            if not result or not result.get('success'):
                print(f"❌ 第 {page_num} 页获取失败")
                break
            
            # 解析响应
            content = result.get('result', {}).get('content', [])
            if not content:
                print(f"❌ 第 {page_num} 页响应内容为空")
                break
            
            try:
                feishu_response = json.loads(content[0].get('text', '{}'))
                
                if feishu_response.get('code') != 0:
                    print(f"❌ 飞书 API 错误: {feishu_response.get('msg')}")
                    break
                
                data = feishu_response.get('data', {})
                calendars = data.get('calendar_list', [])
                
                print(f"✅ 第 {page_num} 页获取到 {len(calendars)} 个日历")
                
                # 添加到总列表
                all_calendars.extend(calendars)
                
                # 检查是否有更多数据
                has_more = data.get('has_more', False)
                page_token = data.get('page_token', '')
                
                print(f"   有更多数据: {has_more}")
                print(f"   分页令牌: {'有' if page_token else '无'}")
                
                if not has_more or not page_token:
                    print(f"✅ 已获取所有数据，共 {page_num} 页")
                    break
                
                page_num += 1
                
            except json.JSONDecodeError as e:
                print(f"❌ 第 {page_num} 页响应解析失败: {e}")
                break
        
        return all_calendars
    
    def display_calendars(self, calendars: List[Dict[str, Any]], title: str):
        """显示日历列表"""
        print(f"\n📅 {title}")
        print("="*60)
        print(f"📊 总计: {len(calendars)} 个日历")
        
        if not calendars:
            print("❌ 没有找到任何日历")
            return
        
        for i, cal in enumerate(calendars, 1):
            print(f"\n  {i}. {cal.get('summary', '未命名日历')}")
            print(f"     📋 ID: {cal.get('calendar_id', 'N/A')}")
            print(f"     🏷️  类型: {cal.get('type', 'N/A')}")
            print(f"     🔒 权限: {cal.get('permissions', 'N/A')}")
            print(f"     👤 角色: {cal.get('role', 'N/A')}")
            print(f"     🎨 颜色: {cal.get('color', 'N/A')}")
            
            if cal.get('description'):
                print(f"     📝 描述: {cal.get('description')}")
            
            if cal.get('summary_alias'):
                print(f"     🔖 别名: {cal.get('summary_alias')}")
    
    def compare_tools(self):
        """比较不同工具的结果"""
        print("🚀 获取所有飞书日历 - 完整分页测试")
        print("="*60)
        
        if not self.user_token:
            print("❌ 没有有效的用户凭据，无法继续测试")
            return
        
        # 测试简化工具
        print("\n1️⃣ 测试简化工具 (calendar_list)")
        simple_calendars = self.get_all_calendars_with_pagination("calendar_list")
        
        # 测试官方工具
        print("\n2️⃣ 测试官方工具 (calendar.v4.calendar.list)")
        official_calendars = self.get_all_calendars_with_pagination("calendar.v4.calendar.list")
        
        # 显示结果
        self.display_calendars(simple_calendars, "简化工具获取的日历")
        self.display_calendars(official_calendars, "官方工具获取的日历")
        
        # 对比分析
        print(f"\n📊 对比分析")
        print("="*60)
        print(f"简化工具日历数量: {len(simple_calendars)}")
        print(f"官方工具日历数量: {len(official_calendars)}")
        
        # 找出差异
        simple_ids = {cal.get('calendar_id') for cal in simple_calendars}
        official_ids = {cal.get('calendar_id') for cal in official_calendars}
        
        only_in_simple = simple_ids - official_ids
        only_in_official = official_ids - simple_ids
        common = simple_ids & official_ids
        
        print(f"共同日历: {len(common)} 个")
        print(f"仅在简化工具中: {len(only_in_simple)} 个")
        print(f"仅在官方工具中: {len(only_in_official)} 个")
        
        if only_in_simple:
            print(f"\n🔍 仅在简化工具中的日历:")
            for cal in simple_calendars:
                if cal.get('calendar_id') in only_in_simple:
                    print(f"  - {cal.get('summary', '未命名')}")
        
        if only_in_official:
            print(f"\n🔍 仅在官方工具中的日历:")
            for cal in official_calendars:
                if cal.get('calendar_id') in only_in_official:
                    print(f"  - {cal.get('summary', '未命名')}")


def main():
    """主函数"""
    fetcher = AllCalendarsFetcher()
    fetcher.compare_tools()


if __name__ == "__main__":
    main()
