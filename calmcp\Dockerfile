# CalMCP Dockerfile
# 多阶段构建，支持 Next.js 和独立 MCP 服务器

FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

# Install dependencies based on the preferred package manager
COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

# Build Next.js app
RUN npm run build

# Build MCP server
RUN npm run mcp:build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

# Copy built application
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
COPY --from=builder /app/dist ./dist

# Create logs directory
RUN mkdir -p logs && chown nextjs:node<PERSON>s logs

USER nextjs

EXPOSE 3000
EXPOSE 3002

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

# Start script that runs both Next.js and MCP server
COPY --from=builder /app/start.sh ./
RUN chmod +x start.sh

CMD ["./start.sh"]
