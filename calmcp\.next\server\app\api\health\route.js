"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/health/route";
exports.ids = ["app/api/health/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "string_decoder":
/*!*********************************!*\
  !*** external "string_decoder" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("string_decoder");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tty");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Code_feishu_coze_plugin_calmcp_src_app_api_health_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/health/route.ts */ \"(rsc)/./src/app/api/health/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/health/route\",\n        pathname: \"/api/health\",\n        filename: \"route\",\n        bundlePath: \"app/api/health/route\"\n    },\n    resolvedPagePath: \"D:\\\\Code\\\\feishu-coze-plugin\\\\calmcp\\\\src\\\\app\\\\api\\\\health\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Code_feishu_coze_plugin_calmcp_src_app_api_health_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/health/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/health/route.ts":
/*!*************************************!*\
  !*** ./src/app/api/health/route.ts ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/mcp-tools */ \"(rsc)/./src/lib/mcp-tools.ts\");\n/* harmony import */ var _lib_logger__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/logger */ \"(rsc)/./src/lib/logger.ts\");\n/**\n * Next.js API Route for Health Check\n * GET /api/health - 健康检查\n */ \n\n\nasync function GET(request) {\n    try {\n        const health = {\n            status: \"ok\",\n            timestamp: new Date().toISOString(),\n            version: \"1.0.0\",\n            service: \"calmcp\",\n            tools: {\n                count: _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__.ALL_TOOLS.length,\n                available: _lib_mcp_tools__WEBPACK_IMPORTED_MODULE_1__.ALL_TOOLS.map((tool)=>tool.name)\n            },\n            environment: {\n                nodeEnv: \"development\",\n                hasFeishuConfig: !!( true && \"EVumG3wCHsDBeJRfpbmJkfRhzCns73jC\")\n            }\n        };\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.info(\"Health check requested\", {\n            userAgent: request.headers.get(\"user-agent\"),\n            ip: request.ip\n        });\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(health);\n    } catch (error) {\n        _lib_logger__WEBPACK_IMPORTED_MODULE_2__.logger.error(\"Health check failed\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            status: \"error\",\n            timestamp: new Date().toISOString(),\n            error: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/health/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/logger.ts":
/*!***************************!*\
  !*** ./src/lib/logger.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   logger: () => (/* binding */ logger)\n/* harmony export */ });\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! winston */ \"(rsc)/./node_modules/winston/lib/winston.js\");\n/* harmony import */ var winston__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(winston__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! path */ \"path\");\n/* harmony import */ var path__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(path__WEBPACK_IMPORTED_MODULE_1__);\n/**\n * Logger Utility\n * 统一的日志记录工具\n */ \n\nclass Logger {\n    constructor(){\n        const logLevel = process.env.LOG_LEVEL || \"info\";\n        const logFile = process.env.LOG_FILE || \"./logs/mcp-server.log\";\n        // 确保日志目录存在\n        const logDir = path__WEBPACK_IMPORTED_MODULE_1___default().dirname(logFile);\n        this.logger = winston__WEBPACK_IMPORTED_MODULE_0___default().createLogger({\n            level: logLevel,\n            format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.timestamp(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.errors({\n                stack: true\n            }), winston__WEBPACK_IMPORTED_MODULE_0___default().format.json()),\n            defaultMeta: {\n                service: \"calmcp\"\n            },\n            transports: [\n                // 错误日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: path__WEBPACK_IMPORTED_MODULE_1___default().join(logDir, \"error.log\"),\n                    level: \"error\",\n                    maxsize: 5242880,\n                    maxFiles: 5\n                }),\n                // 所有日志文件\n                new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).File({\n                    filename: logFile,\n                    maxsize: 5242880,\n                    maxFiles: 5\n                })\n            ]\n        });\n        // 开发环境下添加控制台输出\n        if (true) {\n            this.logger.add(new (winston__WEBPACK_IMPORTED_MODULE_0___default().transports).Console({\n                format: winston__WEBPACK_IMPORTED_MODULE_0___default().format.combine(winston__WEBPACK_IMPORTED_MODULE_0___default().format.colorize(), winston__WEBPACK_IMPORTED_MODULE_0___default().format.simple())\n            }));\n        }\n    }\n    formatMessage(message, context) {\n        if (!context) return message;\n        const contextStr = Object.entries(context).map(([key, value])=>`${key}=${value}`).join(\" \");\n        return `${message} [${contextStr}]`;\n    }\n    info(message, context) {\n        this.logger.info(this.formatMessage(message, context), context);\n    }\n    error(message, error, context) {\n        const logContext = {\n            ...context\n        };\n        if (error) {\n            logContext.error = error.message;\n            logContext.stack = error.stack;\n        }\n        this.logger.error(this.formatMessage(message, context), logContext);\n    }\n    warn(message, context) {\n        this.logger.warn(this.formatMessage(message, context), context);\n    }\n    debug(message, context) {\n        this.logger.debug(this.formatMessage(message, context), context);\n    }\n    // MCP 特定的日志方法\n    mcpRequest(toolName, args, context) {\n        this.info(`MCP tool called: ${toolName}`, {\n            ...context,\n            toolName,\n            args: JSON.stringify(args)\n        });\n    }\n    mcpResponse(toolName, success, duration, context) {\n        this.info(`MCP tool completed: ${toolName}`, {\n            ...context,\n            toolName,\n            success,\n            duration: `${duration}ms`\n        });\n    }\n    mcpError(toolName, error, context) {\n        this.error(`MCP tool failed: ${toolName}`, error, {\n            ...context,\n            toolName\n        });\n    }\n    streamStart(streamId, context) {\n        this.info(`Stream started: ${streamId}`, {\n            ...context,\n            streamId\n        });\n    }\n    streamEnd(streamId, itemCount, duration, context) {\n        this.info(`Stream completed: ${streamId}`, {\n            ...context,\n            streamId,\n            itemCount,\n            duration: `${duration}ms`\n        });\n    }\n    streamError(streamId, error, context) {\n        this.error(`Stream failed: ${streamId}`, error, {\n            ...context,\n            streamId\n        });\n    }\n    // API 请求日志\n    apiRequest(method, path, context) {\n        this.info(`API ${method} ${path}`, {\n            ...context,\n            method,\n            path\n        });\n    }\n    apiResponse(method, path, status, duration, context) {\n        this.info(`API ${method} ${path} ${status}`, {\n            ...context,\n            method,\n            path,\n            status,\n            duration: `${duration}ms`\n        });\n    }\n    // 飞书 API 日志\n    feishuRequest(api, context) {\n        this.info(`Feishu API called: ${api}`, {\n            ...context,\n            feishuApi: api\n        });\n    }\n    feishuResponse(api, code, duration, context) {\n        this.info(`Feishu API completed: ${api}`, {\n            ...context,\n            feishuApi: api,\n            feishuCode: code,\n            duration: `${duration}ms`\n        });\n    }\n    feishuError(api, error, context) {\n        this.error(`Feishu API failed: ${api}`, error, {\n            ...context,\n            feishuApi: api\n        });\n    }\n}\n// 导出单例实例\nconst logger = new Logger();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (logger);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/logger.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/mcp-tools.ts":
/*!******************************!*\
  !*** ./src/lib/mcp-tools.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_TOOLS: () => (/* binding */ ALL_TOOLS),\n/* harmony export */   CALENDAR_TOOLS: () => (/* binding */ CALENDAR_TOOLS),\n/* harmony export */   TOOL_STATS: () => (/* binding */ TOOL_STATS)\n/* harmony export */ });\n/**\r\n * MCP Tools Definition\r\n * 定义所有可用的 MCP 工具\r\n * 包含简化工具和飞书官方工具\r\n */ // 导入核心工具集\nlet FEISHU_CORE_TOOLS = [];\ntry {\n    // 导入核心工具适配器\n    const { ALL_FEISHU_CALENDAR_TOOLS } = __webpack_require__(/*! ./tools/tool-adapter-core */ \"(rsc)/./src/lib/tools/tool-adapter-core.ts\");\n    FEISHU_CORE_TOOLS = ALL_FEISHU_CALENDAR_TOOLS || [];\n    console.log(`✅ 成功加载 ${FEISHU_CORE_TOOLS.length} 个核心工具`);\n} catch (error) {\n    console.warn(\"⚠️  核心工具加载失败，使用简化工具:\", error);\n}\nconst CALENDAR_TOOLS = [\n    {\n        name: \"calendar_list\",\n        description: \"获取用户的日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的日历数量，最小值50，默认50\",\n                    minimum: 50,\n                    maximum: 200\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，用于获取下一页数据\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar_event_create\",\n        description: \"创建新的日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区，如 Asia/Shanghai\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区，如 Asia/Shanghai\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ]\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"详细地址\"\n                        }\n                    }\n                },\n                attendees: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            type: {\n                                type: \"string\",\n                                enum: [\n                                    \"user\",\n                                    \"chat\",\n                                    \"resource\"\n                                ],\n                                description: \"参与者类型\"\n                            },\n                            attendee_id: {\n                                type: \"string\",\n                                description: \"参与者ID\"\n                            },\n                            is_optional: {\n                                type: \"boolean\",\n                                description: \"是否为可选参与者\"\n                            }\n                        },\n                        required: [\n                            \"type\",\n                            \"attendee_id\"\n                        ]\n                    }\n                },\n                reminders: {\n                    type: \"array\",\n                    items: {\n                        type: \"object\",\n                        properties: {\n                            minutes: {\n                                type: \"number\",\n                                description: \"提前提醒的分钟数\"\n                            }\n                        },\n                        required: [\n                            \"minutes\"\n                        ]\n                    }\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"事件可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_search\",\n        description: \"搜索日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                query: {\n                    type: \"string\",\n                    description: \"搜索关键词\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"搜索开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"搜索结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的事件数量\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_update\",\n        description: \"更新日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"事件标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"事件描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间戳（秒）\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    }\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"详细地址\"\n                        }\n                    }\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_delete\",\n        description: \"删除日历事件\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_get\",\n        description: \"获取单个日历事件详情\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"事件ID\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar_event_list\",\n        description: \"获取日历事件列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历ID\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间戳（秒）\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间戳（秒）\"\n                },\n                page_size: {\n                    type: \"number\",\n                    description: \"每页返回的事件数量\",\n                    minimum: 1,\n                    maximum: 100\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    }\n];\n// 使用核心工具集（精简版）\nconst ALL_TOOLS = [\n    ...FEISHU_CORE_TOOLS // 核心工具（9个精选工具）\n];\n// 导出工具统计信息\nconst TOOL_STATS = {\n    simple: CALENDAR_TOOLS.length,\n    core: FEISHU_CORE_TOOLS.length,\n    total: ALL_TOOLS.length\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/mcp-tools.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/tools/tool-adapter-core.ts":
/*!********************************************!*\
  !*** ./src/lib/tools/tool-adapter-core.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ALL_FEISHU_CALENDAR_TOOLS: () => (/* binding */ ALL_FEISHU_CALENDAR_TOOLS),\n/* harmony export */   CORE_CALENDAR_TOOLS: () => (/* binding */ CORE_CALENDAR_TOOLS),\n/* harmony export */   getToolByName: () => (/* binding */ getToolByName),\n/* harmony export */   validateToolArguments: () => (/* binding */ validateToolArguments)\n/* harmony export */ });\n/**\n * MCP 工具适配器 - 核心版本\n * 只保留项目必需的9个核心日历工具\n */ /**\n * 核心日历工具集 - 7个精选工具\n */ const CORE_CALENDAR_TOOLS = [\n    // 📅 日历管理\n    {\n        name: \"calendar.v4.calendar.list\",\n        description: \"获取日历列表\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                page_size: {\n                    type: \"number\",\n                    description: \"分页大小，最大值为 1000\",\n                    minimum: 1,\n                    maximum: 1000\n                },\n                page_token: {\n                    type: \"string\",\n                    description: \"分页标记，第一次请求不填\"\n                },\n                sync_token: {\n                    type: \"string\",\n                    description: \"同步标记，用于增量同步\"\n                }\n            }\n        }\n    },\n    {\n        name: \"calendar.v4.calendar.get\",\n        description: \"获取单个日历详情。注意：必须先调用calendar.list获取日历ID，不能直接使用日历名称\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                }\n            },\n            required: [\n                \"calendar_id\"\n            ]\n        }\n    },\n    // 📝 日程管理 (核心功能)\n    {\n        name: \"calendar.v4.calendarEvent.instanceView\",\n        description: \"查看指定日历中指定时间范围的日程视图(含重复日程展开)。注意：必须先调用calendar.list获取日历ID\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取\"\n                },\n                start_time: {\n                    type: \"string\",\n                    description: \"开始时间，Unix 时间戳，单位为秒\"\n                },\n                end_time: {\n                    type: \"string\",\n                    description: \"结束时间，Unix 时间戳，单位为秒\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.get\",\n        description: \"获取单个日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.create\",\n        description: \"创建日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        },\n                        latitude: {\n                            type: \"number\",\n                            description: \"纬度\"\n                        },\n                        longitude: {\n                            type: \"number\",\n                            description: \"经度\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                recurrence: {\n                    type: \"string\",\n                    description: \"重复规则，RRULE 格式\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                attendee_ability: {\n                    type: \"string\",\n                    enum: [\n                        \"none\",\n                        \"can_see_others\",\n                        \"can_invite_others\",\n                        \"can_modify_event\"\n                    ],\n                    description: \"参与者权限\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"summary\",\n                \"start_time\",\n                \"end_time\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.patch\",\n        description: \"更新日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                summary: {\n                    type: \"string\",\n                    description: \"日程标题\"\n                },\n                description: {\n                    type: \"string\",\n                    description: \"日程描述\"\n                },\n                start_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"开始时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"开始时间\"\n                },\n                end_time: {\n                    type: \"object\",\n                    properties: {\n                        timestamp: {\n                            type: \"string\",\n                            description: \"结束时间，Unix 时间戳，单位为秒\"\n                        },\n                        timezone: {\n                            type: \"string\",\n                            description: \"时区\"\n                        }\n                    },\n                    required: [\n                        \"timestamp\"\n                    ],\n                    description: \"结束时间\"\n                },\n                location: {\n                    type: \"object\",\n                    properties: {\n                        name: {\n                            type: \"string\",\n                            description: \"地点名称\"\n                        },\n                        address: {\n                            type: \"string\",\n                            description: \"地点地址\"\n                        }\n                    },\n                    description: \"日程地点\"\n                },\n                color: {\n                    type: \"number\",\n                    description: \"日程颜色\"\n                },\n                visibility: {\n                    type: \"string\",\n                    enum: [\n                        \"default\",\n                        \"public\",\n                        \"private\"\n                    ],\n                    description: \"可见性\"\n                },\n                free_busy_status: {\n                    type: \"string\",\n                    enum: [\n                        \"busy\",\n                        \"free\"\n                    ],\n                    description: \"忙闲状态\"\n                },\n                user_id_type: {\n                    type: \"string\",\n                    enum: [\n                        \"open_id\",\n                        \"union_id\",\n                        \"user_id\"\n                    ],\n                    description: \"用户ID类型\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    },\n    {\n        name: \"calendar.v4.calendarEvent.delete\",\n        description: \"删除日程\",\n        inputSchema: {\n            type: \"object\",\n            properties: {\n                calendar_id: {\n                    type: \"string\",\n                    description: \"日历 ID\"\n                },\n                event_id: {\n                    type: \"string\",\n                    description: \"日程 ID\"\n                },\n                need_notification: {\n                    type: \"boolean\",\n                    description: \"是否给日程参与人发送bot通知\"\n                }\n            },\n            required: [\n                \"calendar_id\",\n                \"event_id\"\n            ]\n        }\n    }\n];\n/**\n * 导出核心工具集 (兼容原有接口)\n */ const ALL_FEISHU_CALENDAR_TOOLS = CORE_CALENDAR_TOOLS;\n/**\n * 根据工具名称获取工具定义\n */ function getToolByName(name) {\n    return CORE_CALENDAR_TOOLS.find((tool)=>tool.name === name);\n}\n/**\n * 验证工具参数\n */ function validateToolArguments(toolName, args) {\n    const tool = getToolByName(toolName);\n    if (!tool) {\n        return {\n            valid: false,\n            errors: [\n                `Unknown tool: ${toolName}`\n            ]\n        };\n    }\n    // 简单的必需参数检查\n    const required = tool.inputSchema.required || [];\n    const missing = required.filter((field)=>!(field in args));\n    if (missing.length > 0) {\n        return {\n            valid: false,\n            errors: [\n                `Missing required fields: ${missing.join(\", \")}`\n            ]\n        };\n    }\n    return {\n        valid: true\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/tools/tool-adapter-core.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/readable-stream","vendor-chunks/winston","vendor-chunks/color","vendor-chunks/async","vendor-chunks/logform","vendor-chunks/safe-stable-stringify","vendor-chunks/@colors","vendor-chunks/fecha","vendor-chunks/winston-transport","vendor-chunks/string_decoder","vendor-chunks/@dabh","vendor-chunks/color-string","vendor-chunks/color-name","vendor-chunks/stack-trace","vendor-chunks/triple-beam","vendor-chunks/ms","vendor-chunks/kuler","vendor-chunks/safe-buffer","vendor-chunks/one-time","vendor-chunks/inherits","vendor-chunks/fn.name","vendor-chunks/enabled","vendor-chunks/colorspace","vendor-chunks/is-stream","vendor-chunks/simple-swizzle","vendor-chunks/text-hex","vendor-chunks/is-arrayish","vendor-chunks/util-deprecate"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fhealth%2Froute&page=%2Fapi%2Fhealth%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fhealth%2Froute.ts&appDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CCode%5Cfeishu-coze-plugin%5Ccalmcp&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();