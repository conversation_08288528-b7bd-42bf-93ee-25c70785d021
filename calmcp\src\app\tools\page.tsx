'use client';

import { useState, useEffect } from 'react';

interface ToolInfo {
  name: string;
  description: string;
  category: string;
  inputSchema: any;
}

interface CategoryInfo {
  category: string;
  count: number;
  tools: string[];
}

export default function ToolsPage() {
  const [tools, setTools] = useState<ToolInfo[]>([]);
  const [categories, setCategories] = useState<CategoryInfo[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTools();
  }, []);

  const loadTools = async () => {
    try {
      const response = await fetch('/api/tools');
      const data = await response.json();
      setTools(data.tools || []);
      setCategories(data.categories || []);
    } catch (error) {
      console.error('加载工具失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredTools = tools.filter(tool => {
    const matchesCategory = selectedCategory === 'all' || 
      categories.find(cat => cat.category === selectedCategory)?.tools.includes(tool.name);
    const matchesSearch = tool.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tool.description.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">加载工具中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">飞书日历工具</h1>
          <p className="mt-2 text-gray-600">
            共 {tools.length} 个工具，涵盖日历管理、事件操作、参与者管理等功能
          </p>
        </div>

        {/* 搜索和筛选 */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <input
              type="text"
              placeholder="搜索工具名称或描述..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="sm:w-64">
            <select
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
              className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">所有分类</option>
              {categories.map(cat => (
                <option key={cat.category} value={cat.category}>
                  {cat.category} ({cat.count})
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* 分类统计 */}
        <div className="mb-8 grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4">
          {categories.map(cat => (
            <div
              key={cat.category}
              className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                selectedCategory === cat.category
                  ? 'bg-blue-50 border-blue-200'
                  : 'bg-white border-gray-200 hover:bg-gray-50'
              }`}
              onClick={() => setSelectedCategory(cat.category)}
            >
              <div className="text-2xl font-bold text-blue-600">{cat.count}</div>
              <div className="text-sm text-gray-600">{cat.category}</div>
            </div>
          ))}
        </div>

        {/* 工具列表 */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredTools.map(tool => (
            <div key={tool.name} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="mb-3">
                <h3 className="text-lg font-semibold text-gray-900 mb-1">
                  {tool.name}
                </h3>
                <span className="inline-block px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded">
                  {tool.category}
                </span>
              </div>
              
              <p className="text-gray-600 text-sm mb-4">
                {tool.description}
              </p>

              {/* 参数信息 */}
              {tool.inputSchema?.properties && (
                <div className="border-t pt-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">参数:</h4>
                  <div className="space-y-1">
                    {Object.entries(tool.inputSchema.properties).slice(0, 3).map(([key, prop]: [string, any]) => (
                      <div key={key} className="text-xs text-gray-500">
                        <span className="font-medium">{key}</span>
                        {prop.description && (
                          <span className="ml-1">- {prop.description}</span>
                        )}
                      </div>
                    ))}
                    {Object.keys(tool.inputSchema.properties).length > 3 && (
                      <div className="text-xs text-gray-400">
                        ...还有 {Object.keys(tool.inputSchema.properties).length - 3} 个参数
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {filteredTools.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-lg">没有找到匹配的工具</div>
            <p className="text-gray-500 mt-2">尝试调整搜索条件或选择其他分类</p>
          </div>
        )}
      </div>
    </div>
  );
}
