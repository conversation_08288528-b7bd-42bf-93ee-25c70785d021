#!/usr/bin/env python3
"""
测试多代理工作流的CLI脚本
"""

import asyncio
import logging

from models.chat import ChatRequest
from services.chat_service import ChatService

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_multi_agent_workflow():
    """测试多代理工作流"""
    print("🤖 测试多代理工作流")
    print("=" * 50)

    chat_service = ChatService()

    test_cases = ["明天下午3点安排产品评审会议", "你好，你能做什么？", "查看今天的日程"]

    for i, message in enumerate(test_cases, 1):
        print(f"\n{i}. 测试消息: {message}")
        print("-" * 30)

        request = ChatRequest(user_id="test_agents_user", message=message)

        try:
            # 使用多代理工作流
            response = await chat_service.process_message_with_agents(request)

            print(f"✅ 成功: {response.success}")
            print(f"🎯 意图: {response.intent}")
            print(f"📊 置信度: {response.confidence}")
            print(f"💬 响应: {response.message}")

            if response.data:
                print(f"📋 数据: {response.data}")

            if response.context:
                print(f"🔄 上下文: {response.context}")

        except Exception as e:
            print(f"❌ 错误: {e}")
            logger.error(f"测试失败: {e}", exc_info=True)


async def main():
    """主函数"""
    print("🚀 开始多代理工作流测试")
    print("=" * 60)

    try:
        await test_multi_agent_workflow()
        print("\n✅ 测试完成")

    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        logger.error(f"测试失败: {e}", exc_info=True)


if __name__ == "__main__":
    asyncio.run(main())
