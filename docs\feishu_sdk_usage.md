# 飞书SDK使用指南

## 概述

本项目提供了统一的飞书SDK客户端，集成了认证、日历操作、事件管理等所有功能。新的统一客户端基于官方`lark_oapi` SDK，提供了更好的稳定性和功能完整性。

## 快速开始

### 1. 获取客户端实例

```python
from integrations.feishu import get_feishu_client

# 获取默认配置的客户端实例
client = get_feishu_client()

# 或者使用自定义配置
client = get_feishu_client(
    app_id="your_app_id",
    app_secret="your_app_secret"
)
```

### 2. 基本认证流程

```python
# 1. 通过授权码获取访问令牌
result = await client.exchange_code("authorization_code")
if result.get("code") == 0:
    access_token = result["data"]["access_token"]
    refresh_token = result["data"]["refresh_token"]

# 2. 获取用户信息
user_info = await client.get_user_info(access_token)
if user_info.get("code") == 0:
    user_id = user_info["data"]["open_id"]

# 3. 刷新访问令牌
refreshed = await client.refresh_token(refresh_token)
```

## 客户端工厂模式

### FeishuClientFactory

```python
from integrations.feishu import FeishuClientFactory

# 获取日历客户端
calendar_client = FeishuClientFactory.get_calendar_client()

# 获取统一客户端（推荐）
unified_client = FeishuClientFactory.get_unified_client()

# 强制创建新实例
new_client = FeishuClientFactory.get_unified_client(force_new=True)

# 重置所有实例
FeishuClientFactory.reset_instances()
```

### 客户端管理器

```python
from integrations.feishu import client_manager, ClientType

# 获取默认客户端
client = client_manager.get_client(ClientType.UNIFIED)

# 获取命名实例
client = client_manager.get_client(
    client_type=ClientType.UNIFIED,
    instance_name="my_instance"
)

# 清理所有客户端
client_manager.clear_all_clients()
```

## 日历操作

### 获取日历列表

```python
# 获取用户的日历列表
result = client.get_calendars(user_id="user_open_id")

if result.get("code") == 0:
    calendars = result["data"]["calendar_list"]
    for calendar in calendars:
        print(f"日历: {calendar.get('summary')}")
```

### 获取日历详情

```python
# 获取特定日历的详情
result = client.get_calendar_detail(
    user_id="user_open_id",
    calendar_id="calendar_id"
)

if result.get("code") == 0:
    calendar = result["data"]["calendar"]
    print(f"日历名称: {calendar.get('summary')}")
```

## 事件管理

### 创建事件

```python
# 创建日历事件
result = client.create_event(
    user_id="user_open_id",
    calendar_id="calendar_id",
    summary="会议标题",
    start_time={"timestamp": "1672531200"},  # 2023-01-01 10:00:00
    end_time={"timestamp": "1672534800"},    # 2023-01-01 11:00:00
    description="会议描述",
    location={"name": "会议室A"},
    reminders=[{"minutes": 10}, {"minutes": 30}],
    attendees=[{"user_id": "attendee_open_id"}]
)

if result.get("code") == 0:
    event_id = result["data"]["event"]["event_id"]
    print(f"事件创建成功，ID: {event_id}")
```

### 获取事件列表

```python
# 获取日历事件列表
result = client.get_calendar_events(
    user_id="user_open_id",
    calendar_id="calendar_id",
    start_time="2023-01-01T00:00:00+08:00",
    end_time="2023-01-31T23:59:59+08:00",
    page_size=50
)

if result.get("code") == 0:
    events = result["data"]["items"]
    for event in events:
        print(f"事件: {event.get('summary')}")
```

### 更新事件

```python
# 更新事件信息
result = client.update_event(
    user_id="user_open_id",
    calendar_id="calendar_id",
    event_id="event_id",
    summary="更新后的标题",
    description="更新后的描述"
)
```

### 删除事件

```python
# 删除事件
result = client.delete_event(
    user_id="user_open_id",
    calendar_id="calendar_id",
    event_id="event_id"
)
```

## 工具函数

### 时间格式转换

```python
# 转换为时间戳
timestamp = client.convert_to_timestamp("2023-01-01T10:00:00+08:00")

# 时间戳转ISO格式
iso_time = client.timestamp_to_iso(1672531200)
```

## 错误处理

### 异常类型

```python
from integrations.feishu import (
    FeishuAPIError,
    FeishuNetworkError,
    FeishuAuthError
)

try:
    result = await client.get_user_info(access_token)
except FeishuAPIError as e:
    print(f"API错误: {e.code} - {e.message}")
except FeishuNetworkError as e:
    print(f"网络错误: {e}")
except FeishuAuthError as e:
    print(f"认证错误: {e}")
```

### 标准错误处理

```python
async def safe_api_call():
    try:
        result = await client.get_calendars(user_id)
        
        if result.get("code") != 0:
            logger.error(f"API调用失败: {result.get('msg')}")
            return None
            
        return result["data"]
        
    except Exception as e:
        logger.error(f"调用异常: {str(e)}")
        return None
```

## 最佳实践

### 1. 客户端实例管理

```python
# 推荐：使用工厂模式获取客户端
client = get_feishu_client()

# 避免：重复创建实例
# client1 = FeishuCalendarLark(app_id, app_secret)
# client2 = FeishuCalendarLark(app_id, app_secret)
```

### 2. 错误处理

```python
# 推荐：完整的错误处理
async def create_event_safely(user_id, calendar_id, event_data):
    try:
        client = get_feishu_client()
        result = await client.create_event(user_id, calendar_id, **event_data)
        
        if result.get("code") == 0:
            return {"success": True, "data": result["data"]}
        else:
            return {"success": False, "error": result.get("msg")}
            
    except Exception as e:
        logger.error(f"创建事件失败: {str(e)}")
        return {"success": False, "error": str(e)}
```

### 3. 配置管理

```python
# 推荐：使用环境变量或配置文件
import os

app_id = os.getenv("FEISHU_CLIENT_ID")
app_secret = os.getenv("FEISHU_CLIENT_SECRET")

client = get_feishu_client(app_id, app_secret)
```

### 4. 日志记录

```python
import logging

logger = logging.getLogger(__name__)

async def api_operation():
    logger.info("开始API操作")
    
    try:
        result = await client.some_operation()
        logger.info("API操作成功")
        return result
    except Exception as e:
        logger.error(f"API操作失败: {str(e)}")
        raise
```

## 迁移指南

### 从旧API客户端迁移

```python
# 旧方式
from integrations.feishu.api_client import exchange_code, get_user_info

result = await exchange_code(code)
user_info = await get_user_info(access_token)

# 新方式
from integrations.feishu import get_feishu_client

client = get_feishu_client()
result = await client.exchange_code(code)
user_info = await client.get_user_info(access_token)
```

### 向后兼容性

项目保持了向后兼容性，旧的函数式API仍然可用：

```python
# 这些导入仍然有效
from integrations.feishu import (
    exchange_code,
    get_user_info,
    get_calendars,
    create_event
)
```

但建议迁移到新的统一客户端以获得更好的功能和维护性。

## 配置选项

### 环境变量

```bash
# 必需的环境变量
FEISHU_CLIENT_ID=your_app_id
FEISHU_CLIENT_SECRET=your_app_secret
REDIRECT_URI=your_redirect_uri

# 可选的环境变量
MAX_RETRY_ATTEMPTS=3
TIMEZONE=Asia/Shanghai
```

### 配置文件

在`config.py`中设置默认配置：

```python
FEISHU_CLIENT_ID = os.getenv("FEISHU_CLIENT_ID")
FEISHU_CLIENT_SECRET = os.getenv("FEISHU_CLIENT_SECRET")
MAX_RETRY_ATTEMPTS = int(os.getenv("MAX_RETRY_ATTEMPTS", "3"))
```

## 常见问题

### Q: 如何处理token过期？

A: 使用`api/dependencies.py`中的`ensure_valid_token`函数，它会自动处理token刷新。

### Q: 如何调试API调用？

A: 设置日志级别为DEBUG：

```python
import logging
logging.getLogger("feishu_calendar_lark").setLevel(logging.DEBUG)
```

### Q: 如何处理网络超时？

A: 客户端内置了重试机制，可以通过`MAX_RETRY_ATTEMPTS`配置重试次数。
