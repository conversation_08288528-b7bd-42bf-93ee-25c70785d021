"""
测试日历操作集成
"""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from main import app


class TestCalendarOperations:
    """日历操作集成测试"""

    @patch("api.dependencies.ensure_valid_token")
    @patch("integrations.feishu.get_calendars")
    def test_get_calendars(self, mock_get_calendars, mock_ensure_token):
        """测试获取日历列表"""
        mock_ensure_token.return_value = "test_access_token"
        mock_get_calendars.return_value = {
            "code": 0,
            "data": {
                "calendar_list": [
                    {"calendar_id": "cal1", "summary": "工作日历", "type": "primary"},
                    {"calendar_id": "cal2", "summary": "个人日历", "type": "shared"},
                ]
            },
        }

        client = TestClient(app)
        response = client.get("/auth/callback/calendars/?user_id=test_user")

        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]["calendar_list"]) == 2
        assert data["data"]["calendar_list"][0]["summary"] == "工作日历"

    @patch("api.dependencies.ensure_valid_token")
    @patch("integrations.feishu.get_today_events")
    def test_get_today_events(self, mock_get_events, mock_ensure_token):
        """测试获取今日事件"""
        mock_ensure_token.return_value = "test_access_token"
        mock_get_events.return_value = {
            "code": 0,
            "data": {
                "items": [
                    {
                        "event_id": "event1",
                        "summary": "晨会",
                        "start_time": {"iso_format": "2025-07-13T09:00:00+08:00"},
                        "status": "confirmed",
                    },
                    {
                        "event_id": "event2",
                        "summary": "项目评审",
                        "start_time": {"iso_format": "2025-07-13T14:00:00+08:00"},
                        "status": "confirmed",
                    },
                ]
            },
        }

        client = TestClient(app)
        response = client.get(
            "/auth/callback/events/today?user_id=test_user&calendar_id=test_cal"
        )

        assert response.status_code == 200
        data = response.json()
        assert len(data["data"]["items"]) == 2
        assert data["data"]["items"][0]["summary"] == "晨会"

    @patch("api.dependencies.ensure_valid_token")
    @patch("integrations.feishu.create_event")
    def test_create_event(self, mock_create_event, mock_ensure_token):
        """测试创建事件"""
        mock_ensure_token.return_value = "test_access_token"
        mock_create_event.return_value = {
            "code": 0,
            "data": {"event": {"event_id": "new_event_123", "summary": "新会议"}},
        }

        event_data = {
            "summary": "新会议",
            "description": "这是一个新会议",
            "start_time": "2025-07-14T10:00:00+08:00",
            "end_time": "2025-07-14T11:00:00+08:00",
            "location": {"name": "会议室A"},
        }

        client = TestClient(app)
        response = client.post(
            "/auth/callback/calendars/test_cal/events?user_id=test_user",
            json=event_data,
        )

        assert response.status_code == 200
        data = response.json()
        assert data["data"]["event"]["event_id"] == "new_event_123"

    @patch("api.dependencies.ensure_valid_token")
    @patch("integrations.feishu.delete_event")
    def test_delete_event(self, mock_delete_event, mock_ensure_token):
        """测试删除事件"""
        mock_ensure_token.return_value = "test_access_token"
        mock_delete_event.return_value = {"code": 0, "msg": "success"}

        client = TestClient(app)
        response = client.delete(
            "/auth/callback/calendars/test_cal/events/test_event?user_id=test_user"
        )

        assert response.status_code == 200
        data = response.json()
        assert data["code"] == 0

    @patch("api.dependencies.ensure_valid_token")
    @patch("integrations.feishu.get_calendars")
    def test_api_error_handling(self, mock_get_calendars, mock_ensure_token):
        """测试API错误处理"""
        mock_ensure_token.return_value = "test_access_token"
        mock_get_calendars.return_value = {"code": 191001, "msg": "invalid calendar_id"}

        client = TestClient(app)
        response = client.get("/auth/callback/calendars/?user_id=test_user")

        assert response.status_code == 400
        assert "invalid calendar_id" in response.json()["detail"]
