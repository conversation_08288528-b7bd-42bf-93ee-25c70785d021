"""
聊天流程集成测试
"""

from unittest.mock import AsyncMock, patch

import pytest
from fastapi.testclient import TestClient

from main import app
from tests.fixtures.chat_data import INTENT_TEST_CASES, MULTI_TURN_SCENARIOS


class TestChatFlow:
    """聊天流程集成测试"""

    def setup_method(self):
        """测试前准备"""
        self.client = TestClient(app)

    def test_chat_api_basic(self):
        """测试基本聊天API"""
        chat_data = {
            "message": "你好",
            "user_id": "test_user",
            "context": {"timezone": "Asia/Shanghai"},
        }

        response = self.client.post("/chat/", json=chat_data)

        assert response.status_code == 200
        data = response.json()
        assert "intent" in data
        assert "message" in data
        assert data["intent"] == "chat"

    def test_intent_recognition_integration(self):
        """测试意图识别集成"""
        # 测试各种意图的识别
        for intent_type, messages in INTENT_TEST_CASES.items():
            for message in messages[:2]:  # 只测试前两个消息
                chat_data = {
                    "message": message,
                    "user_id": "test_user",
                    "context": {"timezone": "Asia/Shanghai"},
                }

                response = self.client.post("/chat/", json=chat_data)

                assert response.status_code == 200
                data = response.json()

                # 验证意图识别结果
                if intent_type.startswith("calendar"):
                    assert data["intent"] == "calendar"
                elif intent_type.startswith("chat"):
                    assert data["intent"] == "chat"

    @patch("integrations.feishu.api_client.get_calendars")
    @patch("integrations.feishu.api_client.get_user_info")
    def test_calendar_query_flow(self, mock_get_user_info, mock_get_calendars):
        """测试日历查询流程"""
        # 模拟API响应
        mock_get_user_info.return_value = {
            "code": 0,
            "data": {"open_id": "test_user", "name": "测试用户"},
        }
        mock_get_calendars.return_value = {
            "code": 0,
            "data": {"calendar_list": [{"calendar_id": "cal1", "summary": "工作日历"}]},
        }

        chat_data = {
            "message": "今天有什么安排？",
            "user_id": "test_user",
            "context": {"timezone": "Asia/Shanghai"},
        }

        response = self.client.post("/chat/", json=chat_data)

        assert response.status_code == 200
        data = response.json()
        assert data["intent"] == "calendar"
        assert "安排" in data["message"] or "日程" in data["message"]

    def test_multi_turn_conversation_session(self):
        """测试多轮对话会话管理"""
        user_id = "test_user_session"

        # 第一轮对话
        chat_data1 = {
            "message": "明天下午安排会议",
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        response1 = self.client.post("/chat/", json=chat_data1)
        assert response1.status_code == 200
        data1 = response1.json()
        assert data1["intent"] == "calendar"

        # 第二轮对话 - 应该在上下文中处理
        chat_data2 = {
            "message": "项目讨论",
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        response2 = self.client.post("/chat/", json=chat_data2)
        assert response2.status_code == 200
        data2 = response2.json()

        # 应该识别为补充信息
        assert "时长" in data2["message"] or "持续时间" in data2["message"]

    def test_conversation_context_isolation(self):
        """测试对话上下文隔离"""
        # 两个不同用户的对话应该互不影响
        user1_data = {
            "message": "明天安排会议",
            "user_id": "user1",
            "context": {"timezone": "Asia/Shanghai"},
        }

        user2_data = {
            "message": "今天有什么安排？",
            "user_id": "user2",
            "context": {"timezone": "Asia/Shanghai"},
        }

        # 用户1开始对话
        response1 = self.client.post("/chat/", json=user1_data)
        assert response1.status_code == 200

        # 用户2开始对话
        response2 = self.client.post("/chat/", json=user2_data)
        assert response2.status_code == 200

        # 用户1继续对话
        user1_continue = {
            "message": "项目讨论",
            "user_id": "user1",
            "context": {"timezone": "Asia/Shanghai"},
        }

        response3 = self.client.post("/chat/", json=user1_continue)
        assert response3.status_code == 200

        # 应该在用户1的上下文中处理
        data3 = response3.json()
        assert "时长" in data3["message"] or "持续时间" in data3["message"]

    def test_error_handling_in_chat_flow(self):
        """测试聊天流程中的错误处理"""
        # 测试无效输入
        invalid_data = {
            "message": "",  # 空消息
            "user_id": "test_user",
            "context": {"timezone": "Asia/Shanghai"},
        }

        response = self.client.post("/chat/", json=invalid_data)
        assert response.status_code == 200  # 应该优雅处理，不返回错误

        # 测试缺少必要字段
        incomplete_data = {
            "message": "测试消息",
            # 缺少user_id
        }

        response = self.client.post("/chat/", json=incomplete_data)
        assert response.status_code == 422  # 验证错误

    def test_chat_api_performance(self):
        """测试聊天API性能"""
        import time

        chat_data = {
            "message": "今天有什么安排？",
            "user_id": "test_user",
            "context": {"timezone": "Asia/Shanghai"},
        }

        start_time = time.time()
        response = self.client.post("/chat/", json=chat_data)
        end_time = time.time()

        assert response.status_code == 200
        # API响应时间应该在合理范围内（比如2秒）
        assert (end_time - start_time) < 2.0

    def test_concurrent_chat_requests(self):
        """测试并发聊天请求"""
        import threading
        import time

        results = []

        def make_request(user_id):
            chat_data = {
                "message": f"用户{user_id}的消息",
                "user_id": f"user_{user_id}",
                "context": {"timezone": "Asia/Shanghai"},
            }
            response = self.client.post("/chat/", json=chat_data)
            results.append(response.status_code)

        # 创建多个并发请求
        threads = []
        for i in range(5):
            thread = threading.Thread(target=make_request, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有请求完成
        for thread in threads:
            thread.join()

        # 所有请求都应该成功
        assert all(status == 200 for status in results)
        assert len(results) == 5

    @patch("services.calendar_service.CalendarService.create_event")
    def test_end_to_end_calendar_creation(self, mock_create_event):
        """测试端到端日历创建流程"""
        mock_create_event.return_value = {
            "event_id": "created_event_123",
            "summary": "项目讨论",
        }

        user_id = "test_user_e2e"

        # 模拟完整的多轮对话
        conversation_steps = [
            {
                "message": "明天下午3点安排会议",
                "expected_contains": ["会议主题", "标题"],
            },
            {
                "message": "项目讨论",
                "expected_contains": ["时长", "持续时间"],
            },
            {
                "message": "1小时",
                "expected_contains": ["参会者", "邀请"],
            },
            {
                "message": "不需要邀请其他人",
                "expected_contains": ["确认创建"],
            },
            {
                "message": "确认",
                "expected_contains": ["已创建", "成功"],
            },
        ]

        for step in conversation_steps:
            chat_data = {
                "message": step["message"],
                "user_id": user_id,
                "context": {"timezone": "Asia/Shanghai"},
            }

            response = self.client.post("/chat/", json=chat_data)
            assert response.status_code == 200

            data = response.json()
            # 检查响应是否包含预期内容
            assert any(
                keyword in data["message"] for keyword in step["expected_contains"]
            )

        # 验证最终创建了事件
        mock_create_event.assert_called_once()

    def test_conversation_recovery_after_error(self):
        """测试错误后的对话恢复"""
        user_id = "test_user_recovery"

        # 开始正常对话
        chat_data1 = {
            "message": "明天安排会议",
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        response1 = self.client.post("/chat/", json=chat_data1)
        assert response1.status_code == 200

        # 发送无效输入
        chat_data2 = {
            "message": "32点开会",  # 无效时间
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        response2 = self.client.post("/chat/", json=chat_data2)
        assert response2.status_code == 200
        data2 = response2.json()
        assert "无效" in data2["message"] or "重新" in data2["message"]

        # 发送正确输入，应该能恢复对话
        chat_data3 = {
            "message": "下午3点",
            "user_id": user_id,
            "context": {"timezone": "Asia/Shanghai"},
        }

        response3 = self.client.post("/chat/", json=chat_data3)
        assert response3.status_code == 200
        data3 = response3.json()

        # 应该继续正常的对话流程
        assert "会议主题" in data3["message"] or "标题" in data3["message"]
