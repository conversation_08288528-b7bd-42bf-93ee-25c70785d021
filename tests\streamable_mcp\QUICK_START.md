# MCP服务快速开始指南

## 🚀 如何查看你的日历

### 1. 启动MCP服务器

```bash
cd tests/streamable_mcp
python start_real_server.py
```

服务器将在 `http://localhost:5000` 启动。

### 2. 查看日历

使用测试脚本查看你的日历：

```bash
# 查看所有日历和第一个日历的事件
python test_calendar_view.py

# 查看今天的事件
python test_calendar_view.py today

# 查看特定日历的事件（需要提供日历ID）
python test_calendar_view.py <calendar_id>
```

### 3. 可用的MCP工具

服务器提供以下飞书日历工具：

- `list_calendars` - 获取用户的真实日历列表
- `get_calendar_events` - 获取指定日历的真实事件列表  
- `create_calendar_event` - 创建真实的日历事件
- `get_today_events` - 获取今天的所有真实事件

## ⚠️ 重要提示

### OAuth认证要求

**当前错误**: "未找到用户 me 的token数据"

**解决方案**: 需要先完成OAuth认证才能访问真实日历数据。

### 认证步骤

1. **确保飞书应用配置正确**
   - 应用ID: `cli_a76a68f612bf900c`
   - 应用密钥: 已在config.py中配置
   - 权限: 日历读取权限

2. **完成OAuth认证**
   - 运行OAuth认证流程
   - 获取用户访问令牌
   - 存储令牌到数据库

3. **使用真实用户ID**
   - 将测试脚本中的 `user_id = "me"` 替换为实际的用户ID
   - 或者使用OAuth认证后的用户ID

## 📁 文件说明

- `feishu_mcp_server_real.py` - 真实飞书API的MCP服务器
- `feishu_mcp_client_streamable.py` - HTTP模式的MCP客户端
- `test_calendar_view.py` - 测试脚本，用于查看日历
- `start_real_server.py` - 服务器启动脚本
- `USAGE_GUIDE.md` - 详细使用指南

## 🔧 故障排除

### 1. 服务器连接失败
- 确保服务器正在运行
- 检查端口8000是否被占用

### 2. 认证失败
- 完成OAuth认证流程
- 检查访问令牌是否有效
- 确认飞书应用权限配置

### 3. 无日历数据
- 确认飞书账号有日历
- 检查应用权限
- 验证用户ID是否正确

## 📞 下一步

要获取真实的日历数据，你需要：

1. 完成OAuth认证流程
2. 获取有效的用户访问令牌
3. 使用正确的用户ID调用MCP服务

当前系统已经可以正常连接，只需要完成认证即可查看你的真实日历数据！ 