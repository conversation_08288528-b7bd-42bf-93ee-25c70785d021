#!/usr/bin/env python3
"""
测试正确的参数格式
基于官方 Node.js SDK 的参数结构
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")


class CorrectParamsTester:
    """正确参数格式测试器"""
    
    def __init__(self, base_url: str = "http://localhost:3000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = None
        self.user_token = None
        
        # 加载用户凭据
        self._load_user_credentials()
    
    def _load_user_credentials(self):
        """加载用户凭据"""
        token_vars = [
            'FEISHU_USER_ACCESS_TOKEN',
            'TEST_ACCESS_TOKEN',
            'USER_ACCESS_TOKEN'
        ]
        
        for var in token_vars:
            token = os.environ.get(var)
            if token:
                self.user_token = token
                print(f"✅ 使用用户访问令牌: {var}")
                print(f"   Token 预览: {token[:20]}...")
                break
        
        user_id_vars = ['TEST_USER_ID', 'FEISHU_USER_ID', 'USER_ID']
        for var in user_id_vars:
            user_id = os.environ.get(var)
            if user_id:
                self.user_id = user_id
                print(f"   用户 ID: {user_id}")
                break
    
    def test_different_param_formats(self):
        """测试不同的参数格式"""
        print(f"\n🧪 测试不同的参数格式")
        print("="*60)
        
        # 测试格式1: 直接传递参数（当前方式）
        print(f"\n1️⃣ 测试格式1: 直接传递参数")
        result1 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "page_size": 10,
            "user_access_token": self.user_token,
            "user_id": self.user_id
        })
        self._analyze_result("直接传递参数", result1)
        
        # 测试格式2: 使用 params 包装（官方SDK方式）
        print(f"\n2️⃣ 测试格式2: 使用 params 包装")
        result2 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "params": {
                "page_size": 10
            },
            "user_access_token": self.user_token,
            "user_id": self.user_id
        })
        self._analyze_result("params 包装", result2)
        
        # 测试格式3: 空参数
        print(f"\n3️⃣ 测试格式3: 空参数")
        result3 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "user_access_token": self.user_token,
            "user_id": self.user_id
        })
        self._analyze_result("空参数", result3)
        
        # 测试格式4: 只有用户令牌
        print(f"\n4️⃣ 测试格式4: 只有用户令牌")
        result4 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "user_access_token": self.user_token
        })
        self._analyze_result("只有用户令牌", result4)
        
        # 测试格式5: 模拟官方SDK完整格式
        print(f"\n5️⃣ 测试格式5: 模拟官方SDK完整格式")
        result5 = self._call_mcp_tool("calendar.v4.calendar.list", {
            "params": {
                "page_size": 500
            },
            "useUAT": True,  # 使用用户访问令牌标志
            "user_access_token": self.user_token
        })
        self._analyze_result("官方SDK格式", result5)
    
    def _call_mcp_tool(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """调用 MCP 工具"""
        try:
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            print(f"📤 调用工具: {tool_name}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")
            
            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )
            
            print(f"🌐 HTTP 状态码: {response.status_code}")
            
            if response.status_code != 200:
                print(f"❌ HTTP 错误: {response.text}")
                return None
            
            result = response.json()
            return result
            
        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def _analyze_result(self, format_name: str, result: Dict[str, Any]):
        """分析结果"""
        print(f"📊 {format_name} 结果分析:")
        
        if not result:
            print(f"   ❌ 无响应")
            return
        
        success = result.get('success', False)
        print(f"   成功状态: {'✅' if success else '❌'}")
        
        if success:
            content = result.get('result', {}).get('content', [])
            if content:
                try:
                    text_content = content[0].get('text', '{}')
                    feishu_response = json.loads(text_content)
                    
                    code = feishu_response.get('code', -1)
                    msg = feishu_response.get('msg', '无消息')
                    
                    print(f"   飞书代码: {code}")
                    print(f"   飞书消息: {msg}")
                    
                    if code == 0:
                        calendar_list = feishu_response.get('data', {}).get('calendar_list', [])
                        print(f"   📅 日历数量: {len(calendar_list)}")
                        
                        if calendar_list:
                            print(f"   📋 前3个日历:")
                            for i, cal in enumerate(calendar_list[:3], 1):
                                print(f"     {i}. {cal.get('summary', '未命名')}")
                    else:
                        print(f"   ❌ 飞书API错误")
                        
                except json.JSONDecodeError as e:
                    print(f"   ❌ JSON解析失败: {e}")
                    print(f"   原始内容: {text_content}")
            else:
                print(f"   ❌ 响应内容为空")
        else:
            error = result.get('error', '未知错误')
            print(f"   错误信息: {error}")


def main():
    """主函数"""
    tester = CorrectParamsTester()
    
    if not tester.user_token:
        print("❌ 没有有效的用户访问令牌，无法测试")
        return
    
    tester.test_different_param_formats()


if __name__ == "__main__":
    main()
