"""
基础功能测试
验证核心功能是否正常工作
"""

import pytest
import os
from datetime import datetime, timedelta

from integrations.feishu import get_feishu_client, FeishuClientFactory
from integrations.storage import save_token, get_token, is_token_expired


class TestBasicFunctionality:
    """基础功能测试"""
    
    @pytest.mark.unit
    def test_environment_setup(self):
        """测试环境设置"""
        # 检查必需的环境变量
        assert os.getenv("FEISHU_CLIENT_ID") is not None
        assert os.getenv("FEISHU_CLIENT_SECRET") is not None
        
        # 检查测试环境标志
        testing_flag = os.getenv("TESTING")
        assert testing_flag in ["true", "1"], f"TESTING环境变量应该是'true'或'1'，实际值: {testing_flag}"
    
    @pytest.mark.unit
    def test_client_creation(self):
        """测试客户端创建"""
        # 基础客户端创建
        client = get_feishu_client()
        assert client is not None
        assert hasattr(client, 'app_id')
        assert hasattr(client, 'app_secret')
        
        # 客户端类型验证
        assert type(client).__name__ == "FeishuCalendarLark"
    
    @pytest.mark.unit
    def test_client_factory_singleton(self):
        """测试客户端工厂单例模式"""
        # 相同配置返回同一实例
        client1 = FeishuClientFactory.get_unified_client()
        client2 = FeishuClientFactory.get_unified_client()
        assert client1 is client2
        
        # 强制创建新实例
        client3 = FeishuClientFactory.get_unified_client(force_new=True)
        assert client3 is not client1
    
    @pytest.mark.integration
    def test_token_storage_basic(self, test_user_id, sample_token_data):
        """测试基础token存储功能"""
        # 保存token
        save_token(test_user_id, sample_token_data)
        
        # 获取token
        retrieved = get_token(test_user_id)
        assert retrieved is not None
        assert retrieved["access_token"] == sample_token_data["access_token"]
        assert retrieved["refresh_token"] == sample_token_data["refresh_token"]
    
    @pytest.mark.integration
    def test_token_expiry_check(self):
        """测试token过期检查"""
        # 未过期token
        future_time = int(datetime.now().timestamp()) + 3600
        assert not is_token_expired(future_time)
        
        # 过期token
        past_time = int(datetime.now().timestamp()) - 3600
        assert is_token_expired(past_time)
    
    @pytest.mark.integration
    def test_client_methods_exist(self):
        """测试客户端方法存在性"""
        client = get_feishu_client()
        
        # 检查认证相关方法
        assert hasattr(client, 'exchange_code')
        assert hasattr(client, 'refresh_token')
        assert hasattr(client, 'get_user_info')
        
        # 检查日历相关方法
        assert hasattr(client, 'get_calendars')
        assert hasattr(client, 'create_calendar')
        assert hasattr(client, 'update_calendar')
        assert hasattr(client, 'delete_calendar')
        
        # 检查时间转换方法
        assert hasattr(client, 'convert_to_timestamp')
        assert hasattr(client, 'timestamp_to_iso')
    
    @pytest.mark.integration
    def test_time_conversion(self):
        """测试时间转换功能"""
        client = get_feishu_client()
        
        # ISO格式转时间戳
        iso_time = "2023-01-01T10:00:00+08:00"
        timestamp = client.convert_to_timestamp(iso_time)
        assert timestamp is not None
        assert timestamp.isdigit()
        
        # datetime对象转时间戳
        dt = datetime(2023, 1, 1, 10, 0, 0)
        timestamp = client.convert_to_timestamp(dt)
        assert timestamp is not None
        
        # 时间戳转ISO
        iso_result = client.timestamp_to_iso(1672531200)
        assert iso_result is not None
        assert "2023" in str(iso_result)
    
    @pytest.mark.integration
    def test_api_call_structure(self, test_user_id):
        """测试API调用结构（不进行真实调用）"""
        client = get_feishu_client()
        
        # 测试日历获取调用结构
        result = client.get_calendars(test_user_id)
        
        # 验证返回结构
        assert isinstance(result, dict)
        assert "code" in result
        
        # 如果返回错误，应该有错误信息
        if result.get("code") != 0:
            assert "msg" in result or "message" in result
    
    @pytest.mark.integration
    def test_multiple_users_storage(self):
        """测试多用户存储"""
        # 创建多个测试用户
        users = [f"test_user_{i}_{int(datetime.now().timestamp())}" for i in range(3)]
        
        # 为每个用户保存不同的token
        for i, user_id in enumerate(users):
            token_data = {
                "access_token": f"token_{i}_{int(datetime.now().timestamp())}",
                "refresh_token": f"refresh_{i}_{int(datetime.now().timestamp())}",
                "access_token_expire": int(datetime.now().timestamp()) + 3600,
                "refresh_token_expire": int(datetime.now().timestamp()) + 86400
            }
            save_token(user_id, token_data)
        
        # 验证每个用户的token都正确保存
        for i, user_id in enumerate(users):
            retrieved = get_token(user_id)
            assert retrieved is not None
            assert f"token_{i}" in retrieved["access_token"]
            assert f"refresh_{i}" in retrieved["refresh_token"]
    
    @pytest.mark.integration
    def test_data_isolation(self):
        """测试数据隔离"""
        # 创建两个不同的用户
        user1 = f"user1_{int(datetime.now().timestamp())}"
        user2 = f"user2_{int(datetime.now().timestamp())}"
        
        # 为用户1保存token
        token1 = {
            "access_token": "token_user1",
            "refresh_token": "refresh_user1",
            "access_token_expire": int(datetime.now().timestamp()) + 3600,
            "refresh_token_expire": int(datetime.now().timestamp()) + 86400
        }
        save_token(user1, token1)
        
        # 为用户2保存不同的token
        token2 = {
            "access_token": "token_user2",
            "refresh_token": "refresh_user2",
            "access_token_expire": int(datetime.now().timestamp()) + 3600,
            "refresh_token_expire": int(datetime.now().timestamp()) + 86400
        }
        save_token(user2, token2)
        
        # 验证数据隔离
        retrieved1 = get_token(user1)
        retrieved2 = get_token(user2)
        
        assert retrieved1["access_token"] == "token_user1"
        assert retrieved2["access_token"] == "token_user2"
        assert retrieved1["access_token"] != retrieved2["access_token"]


class TestAPIStructure:
    """API结构测试"""
    
    @pytest.mark.integration
    def test_fastapi_app_creation(self):
        """测试FastAPI应用创建"""
        try:
            from main import app
            assert app is not None
            print("✅ FastAPI应用创建成功")
        except ImportError as e:
            pytest.fail(f"FastAPI应用导入失败: {e}")
    
    @pytest.mark.integration
    def test_health_endpoint(self):
        """测试健康检查端点"""
        try:
            from fastapi.testclient import TestClient
            from main import app
            
            client = TestClient(app)
            response = client.get("/health")
            
            assert response.status_code == 200
            data = response.json()
            assert "status" in data
            assert data["status"] == "healthy"
            print("✅ 健康检查端点正常")
        except Exception as e:
            pytest.fail(f"健康检查端点测试失败: {e}")
    
    @pytest.mark.integration
    def test_router_imports(self):
        """测试路由模块导入"""
        try:
            from api.routers import auth, calendar, health
            print("✅ 路由模块导入成功")
        except ImportError as e:
            pytest.fail(f"路由模块导入失败: {e}")


class TestBusinessFunctionality:
    """核心业务功能测试"""

    @pytest.mark.integration
    def test_oauth_authentication_flow(self):
        """测试OAuth认证流程结构"""
        client = get_feishu_client()

        # 测试授权码交换方法存在
        assert hasattr(client, 'exchange_code')
        assert callable(getattr(client, 'exchange_code'))

        # 测试用户信息获取方法存在
        assert hasattr(client, 'get_user_info')
        assert callable(getattr(client, 'get_user_info'))

        # 测试token刷新方法存在
        assert hasattr(client, 'refresh_token')
        assert callable(getattr(client, 'refresh_token'))

    @pytest.mark.integration
    def test_calendar_management_methods(self):
        """测试日历管理功能方法"""
        client = get_feishu_client()

        # 测试日历CRUD方法存在
        calendar_methods = [
            'get_calendars',      # 获取日历列表
            'create_calendar',    # 创建日历
            'update_calendar',    # 更新日历
            'delete_calendar',    # 删除日历
        ]

        for method_name in calendar_methods:
            assert hasattr(client, method_name), f"缺少日历管理方法: {method_name}"
            assert callable(getattr(client, method_name)), f"方法不可调用: {method_name}"

    @pytest.mark.integration
    def test_event_management_methods(self):
        """测试事件管理功能方法"""
        # 测试事件管理相关的API函数存在
        try:
            from integrations.feishu import (
                create_event,
                get_calendar_events,
                update_event,
                delete_event,
                get_event_detail
            )
            print("✅ 事件管理API函数导入成功")
        except ImportError as e:
            pytest.fail(f"事件管理API函数导入失败: {e}")

    @pytest.mark.integration
    def test_calendar_workflow_engine(self):
        """测试日历工作流引擎"""
        try:
            from core.workflow.calendar_workflow import CalendarWorkflowEngine

            # 创建工作流引擎实例
            engine = CalendarWorkflowEngine()

            # 验证工作流引擎的核心组件
            assert hasattr(engine, 'coordinator'), "缺少协调器代理"
            assert hasattr(engine, 'planner'), "缺少规划器代理"
            assert hasattr(engine, 'executor'), "缺少执行器代理"
            assert hasattr(engine, 'workflow_graph'), "缺少工作流图"

            # 验证工作流方法
            assert hasattr(engine, 'run_workflow'), "缺少工作流运行方法"
            assert callable(getattr(engine, 'run_workflow')), "工作流运行方法不可调用"

            print("✅ 日历工作流引擎结构验证通过")
        except ImportError as e:
            pytest.fail(f"日历工作流引擎导入失败: {e}")

    @pytest.mark.integration
    def test_calendar_api_call_structure(self, test_user_id):
        """测试日历API调用结构"""
        client = get_feishu_client()

        # 测试获取日历列表的调用结构
        result = client.get_calendars(test_user_id)

        # 验证返回结构
        assert isinstance(result, dict), "API返回应该是字典格式"
        assert "code" in result, "API返回应该包含code字段"

        # 验证错误处理结构
        if result.get("code") != 0:
            # 如果是错误响应，应该有错误信息
            assert "msg" in result or "message" in result, "错误响应应该包含错误信息"
            print(f"✅ 日历API错误处理结构正确: {result.get('msg', result.get('message'))}")
        else:
            # 如果是成功响应，应该有数据字段
            assert "data" in result, "成功响应应该包含data字段"
            print("✅ 日历API成功响应结构正确")

    @pytest.mark.integration
    def test_event_creation_structure(self, test_user_id):
        """测试事件创建结构"""
        try:
            from integrations.feishu import create_event

            # 准备测试事件数据
            test_calendar_id = "test_calendar_id"
            test_event_data = {
                "summary": f"测试事件_{int(datetime.now().timestamp())}",
                "start_time": {"timestamp": str(int(datetime.now().timestamp()))},
                "end_time": {"timestamp": str(int((datetime.now() + timedelta(hours=1)).timestamp()))},
                "description": "这是一个测试事件"
            }

            # 注意：这里不进行真实的API调用，只验证函数结构
            # 真实调用需要有效的access_token
            assert callable(create_event), "create_event函数应该可调用"
            print("✅ 事件创建函数结构验证通过")

        except ImportError as e:
            pytest.fail(f"事件创建函数导入失败: {e}")

    @pytest.mark.integration
    def test_batch_operations_structure(self):
        """测试批量操作结构"""
        try:
            from integrations.feishu import (
                batch_create_events,
                batch_update_events,
                batch_delete_events
            )

            # 验证批量操作函数存在且可调用
            batch_functions = [
                ('batch_create_events', batch_create_events),
                ('batch_update_events', batch_update_events),
                ('batch_delete_events', batch_delete_events)
            ]

            for func_name, func in batch_functions:
                assert callable(func), f"{func_name}函数应该可调用"

            print("✅ 批量操作函数结构验证通过")

        except ImportError as e:
            pytest.fail(f"批量操作函数导入失败: {e}")

    @pytest.mark.integration
    def test_task_management_structure(self):
        """测试任务管理结构"""
        try:
            from integrations.feishu import (
                create_daily_task,
                create_task_for_date,
                get_today_events,
                get_week_events
            )

            # 验证任务管理函数存在且可调用
            task_functions = [
                ('create_daily_task', create_daily_task),
                ('create_task_for_date', create_task_for_date),
                ('get_today_events', get_today_events),
                ('get_week_events', get_week_events)
            ]

            for func_name, func in task_functions:
                assert callable(func), f"{func_name}函数应该可调用"

            print("✅ 任务管理函数结构验证通过")

        except ImportError as e:
            pytest.fail(f"任务管理函数导入失败: {e}")


class TestAPIRouterStructure:
    """API路由结构测试"""

    @pytest.mark.integration
    def test_auth_router_structure(self):
        """测试认证路由结构"""
        try:
            from api.routers.auth import router

            # 验证路由对象存在
            assert router is not None, "认证路由对象应该存在"

            # 验证路由有正确的标签
            assert hasattr(router, 'tags'), "路由应该有标签"

            print("✅ 认证路由结构验证通过")

        except ImportError as e:
            pytest.fail(f"认证路由导入失败: {e}")

    @pytest.mark.integration
    def test_calendar_router_structure(self):
        """测试日历路由结构"""
        try:
            from api.routers.calendar import router

            # 验证路由对象存在
            assert router is not None, "日历路由对象应该存在"

            print("✅ 日历路由结构验证通过")

        except ImportError as e:
            pytest.fail(f"日历路由导入失败: {e}")

    @pytest.mark.integration
    def test_event_router_structure(self):
        """测试事件路由结构"""
        try:
            from api.routers.event import router

            # 验证路由对象存在
            assert router is not None, "事件路由对象应该存在"

            # 验证路由前缀
            assert hasattr(router, 'prefix'), "路由应该有前缀"

            print("✅ 事件路由结构验证通过")

        except ImportError as e:
            pytest.fail(f"事件路由导入失败: {e}")


class TestDataModelsStructure:
    """数据模型结构测试"""

    @pytest.mark.integration
    def test_event_models_structure(self):
        """测试事件数据模型结构"""
        try:
            from api.models.event import (
                EventCreate,
                EventUpdate,
                BatchEventCreate,
                DailyTaskCreate
            )

            # 验证事件模型类存在
            event_models = [
                ('EventCreate', EventCreate),
                ('EventUpdate', EventUpdate),
                ('BatchEventCreate', BatchEventCreate),
                ('DailyTaskCreate', DailyTaskCreate)
            ]

            for model_name, model_class in event_models:
                assert model_class is not None, f"{model_name}模型应该存在"

            print("✅ 事件数据模型结构验证通过")

        except ImportError as e:
            pytest.fail(f"事件数据模型导入失败: {e}")

    @pytest.mark.integration
    def test_calendar_models_structure(self):
        """测试日历数据模型结构"""
        try:
            from api.models.calendar import CalendarCreate

            # 验证日历模型类存在
            assert CalendarCreate is not None, "CalendarCreate模型应该存在"

            print("✅ 日历数据模型结构验证通过")

        except ImportError as e:
            pytest.fail(f"日历数据模型导入失败: {e}")


class TestErrorHandling:
    """错误处理测试"""

    @pytest.mark.integration
    def test_nonexistent_user_token(self):
        """测试不存在用户的token获取"""
        nonexistent_user = f"nonexistent_{int(datetime.now().timestamp())}"
        result = get_token(nonexistent_user)
        assert result is None

    @pytest.mark.integration
    def test_invalid_api_calls(self):
        """测试无效API调用"""
        client = get_feishu_client()

        # 使用无效用户ID调用API
        result = client.get_calendars("invalid_user_id")

        # 应该返回错误结构
        assert isinstance(result, dict)
        assert "code" in result
        # 无效用户ID应该返回错误
        assert result["code"] != 0

    @pytest.mark.integration
    def test_empty_parameters(self):
        """测试空参数处理"""
        client = get_feishu_client()

        # 测试空用户ID
        result = client.get_calendars("")
        assert isinstance(result, dict)
        assert "code" in result


# 运行基础测试的便捷函数
def run_basic_tests():
    """运行基础功能测试"""
    import subprocess
    import sys
    
    print("🚀 运行基础功能测试")
    print("="*50)
    
    result = subprocess.run([
        sys.executable, "-m", "pytest", 
        "tests/test_basic_functionality.py", 
        "-v", "--tb=short"
    ])
    
    return result.returncode == 0


if __name__ == "__main__":
    success = run_basic_tests()
    exit(0 if success else 1)
