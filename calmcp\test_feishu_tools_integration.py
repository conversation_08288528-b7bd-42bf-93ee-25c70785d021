#!/usr/bin/env python3
"""
测试飞书官方 MCP 工具集成
验证官方工具定义是否正确集成到项目中
使用 Supabase 中的用户 token 进行认证
"""

import requests
import json
import sys
import os
from typing import Dict, Any, Optional

# 添加项目根目录到 Python 路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 加载环境变量
try:
    from dotenv import load_dotenv
    # 尝试加载项目根目录的 .env.local 文件
    env_file = os.path.join(project_root, '.env.local')
    if os.path.exists(env_file):
        load_dotenv(env_file)
        print(f"✅ 已加载环境变量文件: {env_file}")
    else:
        print(f"⚠️  环境变量文件不存在: {env_file}")
except ImportError:
    print("⚠️  python-dotenv 未安装，跳过 .env 文件加载")

# 导入项目模块
try:
    from integrations.storage import get_all_tokens, get_token
    STORAGE_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  无法导入存储模块: {e}")
    STORAGE_AVAILABLE = False

class FeishuToolsIntegrationTest:
    def __init__(self, base_url: str = "http://localhost:3000", user_id: str = None):
        self.base_url = base_url
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json'
        })
        self.user_id = user_id
        self.user_token = None

        # 尝试加载用户 token
        self._load_user_token()

    def _load_user_token(self):
        """加载用户 token（优先使用环境变量，然后尝试 Supabase）"""
        # 首先尝试从环境变量获取测试凭据
        test_token = os.environ.get('TEST_ACCESS_TOKEN')
        test_user_id = os.environ.get('TEST_USER_ID')

        print(f"🔍 调试信息:")
        print(f"   TEST_ACCESS_TOKEN: {'存在' if test_token else '不存在'}")
        print(f"   TEST_USER_ID: {'存在' if test_user_id else '不存在'}")
        print(f"   当前 user_id: {self.user_id}")

        if test_token and test_user_id and not self.user_id:
            self.user_id = test_user_id
            self.user_token = test_token
            print(f"✅ 使用环境变量中的测试凭据")
            print(f"   用户 ID: {self.user_id}")
            print(f"   Token 预览: {test_token[:20]}...")
            return

        # 如果没有环境变量或指定了用户ID，尝试从 Supabase 获取
        if STORAGE_AVAILABLE:
            try:
                # 如果指定了用户ID或没有测试凭据，尝试从 Supabase 获取
                if self.user_id:
                    # 获取指定用户的 token
                    token_data = get_token(self.user_id)
                    if token_data:
                        self.user_token = token_data.get('access_token')
                        print(f"✅ 已从 Supabase 加载用户 {self.user_id} 的 token")
                    else:
                        print(f"⚠️  未找到用户 {self.user_id} 的 token")
                else:
                    # 获取所有用户的 token，选择第一个可用的
                    all_tokens = get_all_tokens()
                    if all_tokens:
                        # 选择第一个用户
                        first_user_id = list(all_tokens.keys())[0]
                        token_data = all_tokens[first_user_id]
                        self.user_id = first_user_id
                        self.user_token = token_data.get('access_token')
                        print(f"✅ 自动选择用户 {self.user_id}，已从 Supabase 加载 token")
                        print(f"📊 Supabase 中共有 {len(all_tokens)} 个用户的 token")
                    else:
                        print("⚠️  Supabase 中没有找到任何用户 token")
                        # 如果 Supabase 没有数据，回退到测试凭据
                        if test_token and test_user_id:
                            self.user_id = test_user_id
                            self.user_token = test_token
                            print(f"🔄 回退到环境变量中的测试凭据")
            except Exception as e:
                print(f"❌ 从 Supabase 加载用户 token 失败: {e}")
                # 如果 Supabase 失败，尝试使用环境变量
                if test_token and test_user_id:
                    self.user_id = test_user_id
                    self.user_token = test_token
                    print(f"🔄 回退到环境变量中的测试凭据")
                else:
                    print("❌ 没有可用的测试凭据")
        else:
            # 如果存储模块不可用，直接使用环境变量
            if test_token and test_user_id:
                self.user_id = test_user_id
                self.user_token = test_token
                print(f"✅ 存储模块不可用，使用环境变量中的测试凭据")
            else:
                print("❌ 存储模块不可用且没有测试凭据")

    def get_token_info(self) -> Dict[str, Any]:
        """获取当前 token 信息"""
        if not self.user_token:
            return {"has_token": False, "user_id": None}

        return {
            "has_token": True,
            "user_id": self.user_id,
            "token_preview": f"{self.user_token[:10]}..." if self.user_token else None
        }
    
    def health_check(self) -> bool:
        """检查服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/api/health", timeout=5)
            return response.status_code == 200
        except Exception as e:
            print(f"❌ 健康检查失败: {e}")
            return False
    
    def get_tools(self) -> Optional[Dict[str, Any]]:
        """获取可用工具列表"""
        try:
            response = self.session.get(f"{self.base_url}/api/mcp/tools")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            print(f"❌ 获取工具列表失败: {e}")
            return None
    
    def test_tool_availability(self) -> bool:
        """测试工具可用性"""
        print("🔧 测试工具可用性...")
        
        tools_info = self.get_tools()
        if not tools_info:
            return False
        
        tools = tools_info.get('data', {}).get('tools', [])
        print(f"✅ 找到 {len(tools)} 个可用工具")
        
        # 检查是否包含官方工具
        official_tools = [
            'calendar.v4.calendar.list',
            'calendar.v4.calendar.get', 
            'calendar.v4.calendarEvent.list',
            'calendar.v4.calendarEvent.create',
            'calendar.v4.calendarEvent.search'
        ]
        
        current_tool_names = [tool.get('name', '') for tool in tools]
        
        print("\n📋 检查官方工具集成状态:")
        official_tools_found = 0
        
        for tool_name in official_tools:
            if tool_name in current_tool_names:
                print(f"✅ {tool_name} - 已集成")
                official_tools_found += 1
            else:
                print(f"❌ {tool_name} - 未找到")
        
        # 检查当前简化工具
        simple_tools = [
            'calendar_list',
            'calendar_event_create',
            'calendar_event_search',
            'calendar_event_update',
            'calendar_event_delete',
            'calendar_event_get',
            'calendar_event_list'
        ]
        
        print("\n📋 检查当前简化工具:")
        simple_tools_found = 0
        
        for tool_name in simple_tools:
            if tool_name in current_tool_names:
                print(f"✅ {tool_name} - 可用")
                simple_tools_found += 1
            else:
                print(f"❌ {tool_name} - 未找到")
        
        print(f"\n📊 工具统计:")
        print(f"   - 官方工具: {official_tools_found}/{len(official_tools)}")
        print(f"   - 简化工具: {simple_tools_found}/{len(simple_tools)}")
        print(f"   - 总工具数: {len(tools)}")
        
        return len(tools) > 0
    
    def test_tool_call(self, tool_name: str, arguments: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """测试工具调用"""
        try:
            # 如果有用户 token，添加到参数中
            if self.user_token and self.user_id:
                # 对于需要用户认证的工具，添加用户信息
                if not arguments.get('user_id'):
                    arguments['user_id'] = self.user_id
                # 某些工具可能需要直接传递 token
                if tool_name.startswith('calendar.v4.'):
                    arguments['user_access_token'] = self.user_token

            payload = {
                "name": tool_name,
                "arguments": arguments
            }

            print(f"📤 调用工具: {tool_name}")
            print(f"� 使用用户: {self.user_id if self.user_id else '未指定'}")
            print(f"🔑 Token 状态: {'已加载' if self.user_token else '未加载'}")
            print(f"📋 参数: {json.dumps(arguments, indent=2, ensure_ascii=False)}")

            response = self.session.post(
                f"{self.base_url}/api/mcp/tools/call",
                json=payload,
                timeout=30
            )

            print(f"🌐 HTTP 状态码: {response.status_code}")

            if response.status_code == 401:
                print("❌ 认证失败 (401): 用户访问令牌可能无效或已过期")
                if not self.user_token:
                    print("💡 建议: 请确保 Supabase 中有有效的用户 token")
                return None
            elif response.status_code >= 400:
                print(f"❌ HTTP 错误 ({response.status_code}): {response.text}")
                return None

            response.raise_for_status()
            return response.json()

        except Exception as e:
            print(f"❌ 工具调用失败: {e}")
            return None
    
    def test_calendar_list_tools(self) -> None:
        """测试日历列表工具"""
        print("\n" + "="*60)
        print("📅 测试日历列表工具")
        print("="*60)
        
        # 测试简化工具
        print("\n1. 测试简化工具 (calendar_list)")
        result = self.test_tool_call("calendar_list", {"page_size": 50})
        
        if result:
            print(f"📊 响应状态: {'成功' if result.get('success') else '失败'}")
            if result.get('success'):
                self.analyze_calendar_response(result, "简化工具")
            else:
                print(f"❌ 错误: {result.get('error', '未知错误')}")
        
        # 测试官方工具（如果可用）
        print("\n2. 测试官方工具 (calendar.v4.calendar.list)")
        result = self.test_tool_call("calendar.v4.calendar.list", {
            "page_size": 50,
            "useUAT": True
        })
        
        if result:
            print(f"📊 响应状态: {'成功' if result.get('success') else '失败'}")
            if result.get('success'):
                self.analyze_calendar_response(result, "官方工具")
            else:
                print(f"❌ 错误: {result.get('error', '未知错误')}")
    
    def analyze_calendar_response(self, result: Dict[str, Any], tool_type: str) -> None:
        """分析日历响应"""
        try:
            content = result.get('result', {}).get('content', [])
            if content and len(content) > 0:
                feishu_response = json.loads(content[0].get('text', '{}'))
                
                print(f"\n🔍 {tool_type} 响应分析:")
                print(f"   飞书 API 代码: {feishu_response.get('code')}")
                print(f"   飞书 API 消息: {feishu_response.get('msg')}")
                
                if feishu_response.get('code') == 0:
                    data = feishu_response.get('data', {})
                    calendar_list = data.get('calendar_list', [])
                    
                    print(f"   📅 日历数量: {len(calendar_list)}")
                    print(f"   📄 有更多数据: {data.get('has_more', False)}")
                    print(f"   🔄 分页令牌: {'有' if data.get('page_token') else '无'}")
                    
                    if calendar_list:
                        print(f"\n   📋 所有日历:")
                        for i, cal in enumerate(calendar_list):
                            print(f"      {i+1}. {cal.get('summary', '未命名')}")
                            print(f"         类型: {cal.get('type', 'N/A')}")
                            print(f"         权限: {cal.get('permissions', 'N/A')}")
                            print(f"         ID: {cal.get('calendar_id', 'N/A')}")
                else:
                    print(f"   ❌ API 错误: {feishu_response.get('msg')}")
        except Exception as e:
            print(f"   ❌ 响应解析失败: {e}")
    
    def test_parameter_validation(self) -> None:
        """测试参数验证"""
        print("\n" + "="*60)
        print("🔍 测试参数验证")
        print("="*60)
        
        # 测试无效参数
        print("\n1. 测试无效 page_size (小于50)")
        result = self.test_tool_call("calendar_list", {"page_size": 10})
        
        if result:
            if result.get('success'):
                print("⚠️  工具接受了无效参数（可能有自动修正）")
            else:
                print("✅ 工具正确拒绝了无效参数")
        
        # 测试缺少必需参数
        print("\n2. 测试缺少必需参数")
        result = self.test_tool_call("calendar_event_get", {
            # 故意缺少 calendar_id 和 event_id 参数
        })
        
        if result:
            if result.get('success'):
                print("⚠️  工具接受了缺少参数的请求")
            else:
                print("✅ 工具正确拒绝了缺少参数的请求")
    
    def run_integration_tests(self) -> None:
        """运行完整的集成测试"""
        print("🚀 飞书官方 MCP 工具集成测试")
        print("=" * 60)

        # 显示 token 信息
        token_info = self.get_token_info()
        print(f"\n🔑 用户认证状态:")
        print(f"   Token 状态: {'✅ 已加载' if token_info['has_token'] else '❌ 未加载'}")
        if token_info['has_token']:
            print(f"   用户 ID: {token_info['user_id']}")
            print(f"   Token 预览: {token_info['token_preview']}")
        else:
            print("   ⚠️  没有可用的用户 token，某些测试可能失败")
            if not STORAGE_AVAILABLE:
                print("   💡 建议: 检查存储模块是否正确安装")

        # 1. 健康检查
        print("\n🏥 检查服务状态...")
        if not self.health_check():
            print("❌ CalMCP 服务未运行，请先启动服务:")
            print("   cd calmcp && npm run dev")
            sys.exit(1)

        print("✅ CalMCP 服务运行正常")
        
        # 2. 工具可用性测试
        if not self.test_tool_availability():
            print("❌ 工具可用性测试失败")
            return
        
        # 3. 日历列表工具测试
        self.test_calendar_list_tools()
        
        # 4. 参数验证测试
        self.test_parameter_validation()
        
        print("\n" + "="*60)
        print("🎉 集成测试完成!")
        print("="*60)
        
        print("\n📋 测试总结:")
        print("✅ 服务健康检查通过")
        print("✅ 工具列表获取成功")
        print("✅ 工具调用功能正常")
        print("✅ 参数验证测试完成")
        
        print("\n🔧 下一步建议:")
        print("1. 检查官方工具是否正确集成")
        print("2. 验证参数验证逻辑")
        print("3. 测试更多工具功能")
        print("4. 性能和稳定性测试")

def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='飞书 MCP 工具集成测试')
    parser.add_argument('--user-id', type=str, help='指定用户ID（可选，默认自动选择第一个可用用户）')
    parser.add_argument('--base-url', type=str, default='http://localhost:3000', help='MCP 服务基础URL')

    args = parser.parse_args()

    test = FeishuToolsIntegrationTest(base_url=args.base_url, user_id=args.user_id)
    test.run_integration_tests()

if __name__ == "__main__":
    main()
