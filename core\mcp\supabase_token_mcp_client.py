#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Supabase Token的MCP客户端
"""

import os
import json
import asyncio
import logging
import requests
from typing import Dict, List, Optional, Any

logger = logging.getLogger(__name__)

class SupabaseTokenMCPClient:
    """使用Supabase Token的MCP客户端"""
    
    def __init__(self, 
                 token_bridge_url: str = "http://localhost:3001",
                 mcp_service_url: str = "http://localhost:3000",
                 user_id: str = None):
        self.token_bridge_url = token_bridge_url
        self.mcp_service_url = mcp_service_url
        self.user_id = user_id or os.getenv("DEFAULT_USER_ID", "default_user")
        self.available_tools = []
        self.current_token = None
        
    async def initialize(self):
        """初始化客户端"""
        try:
            # 获取token
            await self._refresh_token()
            
            # 获取MCP工具列表
            await self._load_tools()
            
            logger.info(f"MCP客户端初始化成功，获取到 {len(self.available_tools)} 个工具")
            
        except Exception as e:
            logger.error(f"MCP客户端初始化失败: {str(e)}")
            raise
    
    async def _refresh_token(self):
        """从Token桥接服务获取token"""
        try:
            url = f"{self.token_bridge_url}/api/token/{self.user_id}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                token_data = response.json()
                self.current_token = token_data["access_token"]
                logger.info(f"成功获取用户 {self.user_id} 的token")
            else:
                logger.error(f"获取token失败: {response.status_code} - {response.text}")
                raise Exception(f"Token获取失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"从Token桥接服务获取token失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """获取MCP工具列表"""
        try:
            url = f"{self.mcp_service_url}/api/mcp/tools/list"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.available_tools = result.get("tools", [])
                    logger.info(f"成功获取 {len(self.available_tools)} 个MCP工具")
                else:
                    logger.error(f"获取工具列表失败: {result}")
            else:
                logger.error(f"获取工具列表失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"获取MCP工具列表失败: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if arguments is None:
                arguments = {}
            
            # 检查token是否需要刷新
            await self._ensure_valid_token()
            
            # 调用MCP工具
            url = f"{self.mcp_service_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            # 添加token到请求头（如果MCP服务支持）
            headers = {
                "Content-Type": "application/json"
            }
            if self.current_token:
                headers["Authorization"] = f"Bearer {self.current_token}"
            
            logger.info(f"调用MCP工具: {tool_name}, 参数: {arguments}")
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"工具调用成功: {tool_name}")
                return result
            else:
                logger.error(f"工具调用失败: {response.status_code} - {response.text}")
                return {"error": f"API调用失败: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"调用MCP工具失败: {str(e)}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    async def _ensure_valid_token(self):
        """确保token有效"""
        try:
            # 尝试刷新token
            url = f"{self.token_bridge_url}/api/token/refresh/{self.user_id}"
            response = requests.post(url, timeout=10)
            
            if response.status_code == 200:
                token_data = response.json()
                self.current_token = token_data["access_token"]
                logger.debug(f"Token刷新成功")
            else:
                logger.warning(f"Token刷新失败: {response.status_code}")
                
        except Exception as e:
            logger.warning(f"Token刷新检查失败: {str(e)}")
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return self.available_tools
    
    def format_tools_for_llm(self) -> List[Dict[str, Any]]:
        """格式化工具列表供LLM使用"""
        tools = []
        for tool in self.available_tools:
            tools.append({
                "type": "function",
                "function": {
                    "name": tool["name"],
                    "description": tool.get("description", ""),
                    "parameters": tool.get("inputSchema", {})
                }
            })
        return tools

# 全局客户端实例
_supabase_mcp_client: Optional[SupabaseTokenMCPClient] = None

def get_supabase_mcp_client(user_id: str = None) -> SupabaseTokenMCPClient:
    """获取Supabase Token MCP客户端实例"""
    global _supabase_mcp_client
    if _supabase_mcp_client is None:
        _supabase_mcp_client = SupabaseTokenMCPClient(user_id=user_id)
    return _supabase_mcp_client

async def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    client = SupabaseTokenMCPClient()
    await client.initialize()
    
    # 测试调用工具
    result = await client.call_tool("calendar.v4.calendar.list", {"page_size": 10})
    print(f"测试结果: {result}")

if __name__ == "__main__":
    asyncio.run(main())
