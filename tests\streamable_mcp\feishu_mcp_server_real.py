#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
飞书MCP服务器 - Streamable HTTP模式 (真实客户端版本)
强制使用真实的飞书日历API
"""

import json
import logging
import asyncio
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, List, Optional
from contextlib import asynccontextmanager
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect, Request
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
import uvicorn
from fastapi.responses import JSONResponse

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
import sys
sys.path.insert(0, str(project_root))

# 强制导入真实的飞书日历服务
try:
    from integrations.feishu import FeishuCalendarLark
    from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
    logger = logging.getLogger("feishu_mcp_real")
    logger.info("✅ 成功导入飞书日历客户端")
    logger.info(f"📋 飞书应用ID: {FEISHU_CLIENT_ID}")
except ImportError as e:
    logging.error(f"❌ 导入飞书客户端失败: {e}")
    raise ImportError(f"无法导入飞书客户端: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("feishu_mcp_real")

# 全局变量存储日历客户端
calendar_client = None

def get_calendar_client():
    """获取真实的飞书日历客户端实例"""
    global calendar_client
    if calendar_client is None:
        try:
            logger.info("🔗 创建真实飞书日历客户端...")
            logger.info(f"📋 使用配置: CLIENT_ID={FEISHU_CLIENT_ID}")
            calendar_client = FeishuCalendarLark(FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET)
            logger.info("✅ 飞书日历客户端创建成功")
        except Exception as e:
            logger.error(f"❌ 创建飞书日历客户端失败: {str(e)}")
            raise Exception(f"无法创建飞书日历客户端: {str(e)}")
    return calendar_client


# Pydantic模型定义
class MCPRequest(BaseModel):
    """MCP请求模型"""
    jsonrpc: str = "2.0"
    id: str
    method: str
    params: Optional[Dict[str, Any]] = None


class MCPResponse(BaseModel):
    """MCP响应模型"""
    jsonrpc: str = "2.0"
    id: str
    result: Optional[Dict[str, Any]] = None
    error: Optional[Dict[str, Any]] = None


class ToolCallRequest(BaseModel):
    """工具调用请求模型"""
    name: str
    arguments: Dict[str, Any] = Field(default_factory=dict)


class ToolCallResponse(BaseModel):
    """工具调用响应模型"""
    content: List[Dict[str, Any]]


# 创建FastAPI应用
app = FastAPI(
    title="飞书MCP服务器 - 真实客户端版本",
    description="基于HTTP的飞书日历MCP服务器 (使用真实API)",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 添加请求超时中间件
@app.middleware("http")
async def timeout_middleware(request, call_next):
    """添加请求超时处理"""
    import asyncio
    try:
        # 设置更长的超时时间，特别是对于真实API调用
        timeout = 120  # 2分钟超时
        response = await asyncio.wait_for(call_next(request), timeout=timeout)
        return response
    except asyncio.TimeoutError:
        logger.error(f"❌ 请求超时: {request.url}")
        return JSONResponse(
            status_code=408,
            content={
                "jsonrpc": "2.0",
                "id": "timeout",
                "error": {
                    "code": -32000,
                    "message": "请求超时，请稍后重试"
                }
            }
        )


# MCP工具定义
class MCPTools:
    """MCP工具集合 - 使用真实飞书API"""
    
    @staticmethod
    def list_calendars(user_id: str) -> Dict[str, Any]:
        """获取用户的真实日历列表"""
        try:
            logger.info(f"🛠️ 获取用户 {user_id} 的真实日历列表...")
            client = get_calendar_client()
            result = client.get_calendars(user_id)
            
            logger.info(f"📊 飞书API响应: {result}")
            
            if result.get("code") == 0:
                calendars = []
                for cal_data in result.get("data", {}).get("calendar_list", []):
                    calendars.append({
                        "id": cal_data.get("calendar_id"),
                        "name": cal_data.get("summary", ""),
                        "description": cal_data.get("description", ""),
                        "color": cal_data.get("color", "#1976d2"),
                        "permissions": cal_data.get("permissions", "private")
                    })
                
                logger.info(f"✅ 成功获取到 {len(calendars)} 个真实日历")
                return {
                    "success": True,
                    "calendars": calendars
                }
            else:
                error_msg = result.get("msg", "获取日历列表失败")
                logger.error(f"❌ 飞书API返回错误: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
            
        except Exception as e:
            logger.error(f"❌ 获取日历列表失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def get_calendar_events(user_id: str, calendar_id: str = "primary", 
                           start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """获取指定日历的真实事件列表"""
        try:
            logger.info(f"🛠️ 获取用户 {user_id} 日历 {calendar_id} 的真实事件...")
            client = get_calendar_client()
            result = client.get_calendar_events(
                user_id=user_id,
                calendar_id=calendar_id,
                start_time=start_time,
                end_time=end_time
            )
            
            logger.info(f"📊 飞书API响应: {result}")
            
            if result.get("code") == 0:
                events = []
                for event_data in result.get("data", {}).get("items", []):
                    events.append({
                        "id": event_data.get("event_id"),
                        "title": event_data.get("summary", ""),
                        "description": event_data.get("description", ""),
                        "start_time": event_data.get("start_time", {}).get("date_time"),
                        "end_time": event_data.get("end_time", {}).get("date_time"),
                        "location": event_data.get("location", {}).get("name", ""),
                        "attendees": [att.get("user_id", "") for att in event_data.get("attendees", [])]
                    })
                
                logger.info(f"✅ 成功获取到 {len(events)} 个真实事件")
                return {
                    "success": True,
                    "events": events
                }
            else:
                error_msg = result.get("msg", "获取日历事件失败")
                logger.error(f"❌ 飞书API返回错误: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
            
        except Exception as e:
            logger.error(f"❌ 获取日历事件失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def create_calendar_event(user_id: str, title: str, start_time: str, end_time: str,
                             calendar_id: str = "primary", description: str = "",
                             location: str = "", attendees: List[str] = None) -> Dict[str, Any]:
        """创建真实的日历事件"""
        try:
            logger.info(f"🛠️ 为用户 {user_id} 创建真实事件: {title}")
            client = get_calendar_client()
            
            # 准备事件数据
            event_data = {
                "summary": title,
                "start_time": {"date_time": start_time},
                "end_time": {"date_time": end_time},
                "description": description
            }
            
            if location:
                event_data["location"] = {"name": location}
            
            if attendees:
                event_data["attendees"] = [{"user_id": att} for att in attendees]
            
            result = client.create_event(
                user_id=user_id,
                calendar_id=calendar_id,
                **event_data
            )
            
            logger.info(f"📊 飞书API响应: {result}")
            
            if result.get("code") == 0:
                event_id = result.get("data", {}).get("event", {}).get("event_id")
                logger.info(f"✅ 成功创建真实事件: {event_id}")
                return {
                    "success": True,
                    "message": "事件创建成功",
                    "event_id": event_id
                }
            else:
                error_msg = result.get("msg", "创建事件失败")
                logger.error(f"❌ 飞书API返回错误: {error_msg}")
                return {
                    "success": False,
                    "error": error_msg
                }
            
        except Exception as e:
            logger.error(f"❌ 创建日历事件失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def get_today_events(user_id: str) -> Dict[str, Any]:
        """获取今天的所有真实事件"""
        try:
            logger.info(f"🛠️ 获取用户 {user_id} 今天的真实事件...")
            # 获取今天的日期范围
            today = datetime.now().date()
            start_time = f"{today}T00:00:00+08:00"
            end_time = f"{today}T23:59:59+08:00"
            
            # 调用获取事件的方法
            return MCPTools.get_calendar_events(user_id, "primary", start_time, end_time)
            
        except Exception as e:
            logger.error(f"❌ 获取今天事件失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }


# HTTP路由定义
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "飞书MCP服务器 - 真实客户端版本",
        "version": "1.0.0",
        "status": "running",
        "client_type": "real_feishu_api"
    }


@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy", 
        "timestamp": datetime.now().isoformat(),
        "client_type": "real_feishu_api"
    }


@app.post("/mcp/initialize")
async def initialize_mcp(request: MCPRequest):
    """MCP初始化"""
    return MCPResponse(
        id=request.id,
        result={
            "protocolVersion": "2024-11-05",
            "capabilities": {
                "tools": {}
            },
            "serverInfo": {
                "name": "feishu-calendar-real",
                "version": "1.0.0"
            }
        }
    )


@app.post("/mcp/tools/list")
async def list_tools(request: MCPRequest):
    """获取工具列表"""
    tools = [
        {
            "name": "list_calendars",
            "description": "获取用户的真实日历列表。Args: user_id (str) - 用户ID",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "用户ID"}
                },
                "required": ["user_id"]
            }
        },
        {
            "name": "get_calendar_events",
            "description": "获取指定日历的真实事件列表。Args: user_id (str) - 用户ID, calendar_id (str) - 日历ID, start_time (str) - 开始时间, end_time (str) - 结束时间",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "用户ID"},
                    "calendar_id": {"type": "string", "description": "日历ID", "default": "primary"},
                    "start_time": {"type": "string", "description": "开始时间 (ISO格式)"},
                    "end_time": {"type": "string", "description": "结束时间 (ISO格式)"}
                },
                "required": ["user_id"]
            }
        },
        {
            "name": "create_calendar_event",
            "description": "创建真实的日历事件。Args: user_id (str) - 用户ID, title (str) - 事件标题, start_time (str) - 开始时间, end_time (str) - 结束时间, calendar_id (str) - 日历ID, description (str) - 事件描述, location (str) - 事件地点, attendees (list) - 参与者列表",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "用户ID"},
                    "title": {"type": "string", "description": "事件标题"},
                    "start_time": {"type": "string", "description": "开始时间 (ISO格式)"},
                    "end_time": {"type": "string", "description": "结束时间 (ISO格式)"},
                    "calendar_id": {"type": "string", "description": "日历ID", "default": "primary"},
                    "description": {"type": "string", "description": "事件描述"},
                    "location": {"type": "string", "description": "事件地点"},
                    "attendees": {"type": "array", "items": {"type": "string"}, "description": "参与者列表"}
                },
                "required": ["user_id", "title", "start_time", "end_time"]
            }
        },
        {
            "name": "get_today_events",
            "description": "获取今天的所有真实事件。Args: user_id (str) - 用户ID",
            "inputSchema": {
                "type": "object",
                "properties": {
                    "user_id": {"type": "string", "description": "用户ID"}
                },
                "required": ["user_id"]
            }
        }
    ]
    
    return MCPResponse(
        id=request.id,
        result={"tools": tools}
    )


@app.post("/mcp/tools/call")
async def call_tool(request: MCPRequest):
    """调用MCP工具"""
    try:
        logger.info(f"🛠️ 收到工具调用请求: {request.method}")
        logger.info(f"📋 请求参数: {request.params}")
        
        if not request.params:
            raise HTTPException(status_code=400, detail="缺少参数")
        
        tool_name = request.params.get("name")
        arguments = request.params.get("arguments", {})
        
        if not tool_name:
            raise HTTPException(status_code=400, detail="缺少工具名称")
        
        logger.info(f"🔧 调用工具: {tool_name}")
        logger.info(f"📝 参数: {arguments}")
        
        # 根据工具名称调用相应的方法
        if tool_name == "list_calendars":
            user_id = arguments.get("user_id")
            if not user_id:
                raise HTTPException(status_code=400, detail="缺少user_id参数")
            
            logger.info(f"📅 开始获取用户 {user_id} 的日历列表...")
            result = MCPTools.list_calendars(user_id)
            
        elif tool_name == "get_calendar_events":
            user_id = arguments.get("user_id")
            calendar_id = arguments.get("calendar_id", "primary")
            start_time = arguments.get("start_time")
            end_time = arguments.get("end_time")
            
            if not user_id:
                raise HTTPException(status_code=400, detail="缺少user_id参数")
            
            logger.info(f"📅 开始获取用户 {user_id} 日历 {calendar_id} 的事件...")
            result = MCPTools.get_calendar_events(
                user_id=user_id,
                calendar_id=calendar_id,
                start_time=start_time,
                end_time=end_time
            )
            
        elif tool_name == "create_calendar_event":
            user_id = arguments.get("user_id")
            title = arguments.get("title")
            start_time = arguments.get("start_time")
            end_time = arguments.get("end_time")
            calendar_id = arguments.get("calendar_id", "primary")
            description = arguments.get("description", "")
            location = arguments.get("location", "")
            attendees = arguments.get("attendees", [])
            
            if not all([user_id, title, start_time, end_time]):
                raise HTTPException(status_code=400, detail="缺少必要参数")
            
            logger.info(f"📅 开始为用户 {user_id} 创建事件: {title}")
            result = MCPTools.create_calendar_event(
                user_id=user_id,
                title=title,
                start_time=start_time,
                end_time=end_time,
                calendar_id=calendar_id,
                description=description,
                location=location,
                attendees=attendees
            )
            
        elif tool_name == "get_today_events":
            user_id = arguments.get("user_id")
            if not user_id:
                raise HTTPException(status_code=400, detail="缺少user_id参数")
            
            logger.info(f"📅 开始获取用户 {user_id} 今天的事件...")
            result = MCPTools.get_today_events(user_id)
            
        else:
            raise HTTPException(status_code=400, detail=f"未知工具: {tool_name}")
        
        logger.info(f"✅ 工具调用完成: {tool_name}")
        logger.info(f"📊 结果: {result}")
        
        # 返回MCP格式的响应
        return MCPResponse(
            id=request.id,
            result={
                "content": [
                    {
                        "type": "text",
                        "text": json.dumps(result, ensure_ascii=False)
                    }
                ]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ 工具调用异常: {str(e)}")
        logger.error(f"📋 异常详情: {type(e).__name__}: {str(e)}")
        import traceback
        logger.error(f"🔍 堆栈跟踪: {traceback.format_exc()}")
        
        return MCPResponse(
            id=request.id,
            error={
                "code": -32000,
                "message": f"工具调用失败: {str(e)}"
            }
        )


@app.post("/mcp")
async def mcp_entry(request: Request):
    """MCP JSON-RPC 2.0 统一入口，兼容 Cursor 等客户端"""
    try:
        data = await request.json()
        method = data.get("method")
        # 构造 MCPRequest 对象
        mcp_req = MCPRequest(**data)
        if method == "initialize":
            return await initialize_mcp(mcp_req)
        elif method == "tools/list":
            return await list_tools(mcp_req)
        elif method == "tools/call":
            return await call_tool(mcp_req)
        else:
            return MCPResponse(
                id=mcp_req.id,
                error={
                    "code": -32601,
                    "message": f"未知方法: {method}"
                }
            )
    except Exception as e:
        return MCPResponse(
            id=data.get("id", "unknown"),
            error={
                "code": -32603,
                "message": f"服务器内部错误: {str(e)}"
            }
        )


def main():
    """主函数"""
    logger.info("🚀 启动飞书MCP服务器 (真实客户端版本)...")
    logger.info(f"飞书应用ID: {FEISHU_CLIENT_ID}")
    logger.info("📡 服务器将在 http://localhost:8000 启动")
    logger.info("🔗 使用真实飞书API")
    
    # 启动服务器
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8000,
        log_level="info"
    )


if __name__ == "__main__":
    main() 