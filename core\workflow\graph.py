"""
工作流图定义
基于PDeerFlow设计的状态图工作流
"""

from typing import Any, Dict, Literal

from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import END, START, StateGraph

from models.workflow import WorkflowState

from .nodes import (  # 保留原有的节点作为备用
    calendar_processor_node,
    chat_processor_node,
    coordinator_node,
    executor_node,
    human_feedback_node,
    intent_classifier_node,
    planner_node,
    text_input_node,
)


def create_workflow_graph():
    """
    创建工作流图

    Returns:
        编译后的工作流图
    """
    # 创建状态图
    workflow = StateGraph(WorkflowState)

    # 添加节点
    workflow.add_node("text_input", text_input_node)
    workflow.add_node("coordinator", coordinator_node)
    workflow.add_node("planner", planner_node)
    workflow.add_node("human_feedback", human_feedback_node)
    workflow.add_node("executor", executor_node)

    # 添加备用节点（兼容性）- 暂时注释掉不可达的节点
    # workflow.add_node("intent_classifier", intent_classifier_node)
    # workflow.add_node("calendar_processor", calendar_processor_node)
    # workflow.add_node("chat_processor", chat_processor_node)

    # 设置入口点
    workflow.set_entry_point("text_input")

    # 添加边
    workflow.add_edge("text_input", "coordinator")

    # 协调器的条件边
    workflow.add_conditional_edges(
        "coordinator",
        _route_from_coordinator,
        {"planner": "planner", "chat": END, "__end__": END},
    )

    # 规划器的条件边
    workflow.add_conditional_edges(
        "planner",
        _route_from_planner,
        {"human_feedback": "human_feedback", "executor": "executor", "__end__": END},
    )

    # 人机协作的条件边
    workflow.add_conditional_edges(
        "human_feedback",
        _route_from_human_feedback,
        {"planner": "planner", "executor": "executor", "__end__": END},
    )

    # 执行器直接结束
    workflow.add_edge("executor", END)

    # 备用路径（兼容性）- 暂时注释掉
    # workflow.add_conditional_edges(
    #     "intent_classifier",
    #     _route_from_intent_classifier,
    #     {
    #         "calendar": "calendar_processor",
    #         "chat": "chat_processor",
    #         "__end__": END
    #     }
    # )
    #
    # workflow.add_edge("calendar_processor", END)
    # workflow.add_edge("chat_processor", END)

    # 编译工作流（暂时不使用checkpointer避免版本兼容问题）
    app = workflow.compile()

    return app


def _route_from_coordinator(state: Dict[str, Any]) -> str:
    """
    协调器路由函数

    Args:
        state: 当前状态

    Returns:
        下一个节点名称
    """
    intent = state.get("intent", "")

    if intent == "calendar":
        return "planner"
    elif intent == "chat":
        return "__end__"
    else:
        return "__end__"


def _route_from_planner(state: Dict[str, Any]) -> str:
    """
    规划器路由函数

    Args:
        state: 当前状态

    Returns:
        下一个节点名称
    """
    pending_confirmation = state.get("pending_confirmation", False)

    if pending_confirmation:
        return "human_feedback"
    else:
        return "executor"


def _route_from_human_feedback(state: Dict[str, Any]) -> str:
    """
    人机协作路由函数

    Args:
        state: 当前状态

    Returns:
        下一个节点名称
    """
    # 这里需要根据用户反馈决定路由
    # 暂时简化处理
    user_input = state.get("user_input", "").lower()

    if any(word in user_input for word in ["确认", "是的", "好的", "可以"]):
        return "executor"
    elif any(word in user_input for word in ["取消", "不要", "算了"]):
        return "__end__"
    else:
        # 用户提供修改建议，重新规划
        return "planner"


def _route_from_intent_classifier(state: Dict[str, Any]) -> str:
    """
    意图分类器路由函数（备用）

    Args:
        state: 当前状态

    Returns:
        下一个节点名称
    """
    intent = state.get("intent", "chat")

    if intent == "calendar":
        return "calendar"
    elif intent in ["journal", "media"]:
        return "chat"  # 暂时都路由到聊天处理
    else:
        return "chat"


# 创建全局工作流实例
workflow_app = create_workflow_graph()
