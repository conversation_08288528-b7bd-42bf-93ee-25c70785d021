# 🤖 智能日历助手 (Intelligent Calendar Assistant)

[![Python 3.8+](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104+-green.svg)](https://fastapi.tiangolo.com/)

> 基于大语言模型的智能日历管理系统，通过自然语言交互实现高效的日程管理

**智能日历助手**是一个基于PDeerFlow架构设计的多代理协作系统，支持自然语言交互、人机协作确认机制和多轮对话上下文管理。用户可以通过简单的自然语言描述来创建、查询、修改和删除日历事件。

## ✨ 核心特性

### 🧠 智能意图识别
- **高精度语义理解**：意图识别准确率达95%+
- **多意图支持**：calendar、chat、journal、media
- **操作类型识别**：create、query、update、delete
- **置信度评估**：0.0-1.0的智能置信度评分

### 🤝 人机协作机制
- **确认流程**：重要操作前要求用户确认
- **修改建议**：用户可以实时修改参数
- **取消操作**：随时取消待执行的操作
- **补充信息**：智能引导用户提供缺失信息

### 💬 多轮对话支持
- **上下文保持**：避免重复意图识别
- **状态管理**：完整的会话状态持久化
- **会话记忆**：支持对话历史和信息累积

### ⏰ 强大的时间解析
- **相对时间**：明天、下周、后天等
- **具体时间**：下午3点、上午9点半等
- **重复模式**：每天、工作日、周末等
- **时间范围**：8点到10点等复杂表达

## 🎮 使用演示

### 命令行交互
```bash
$ python cli.py -i

🤖 智能日历助手已启动！
💡 您可以用自然语言描述您的日程需求

👤 用户: 明天下午3点安排一个产品评审会议
🤖 助手: 请确认创建以下日历事件：
        📝 标题：产品评审会议
        📅 时间：2025-07-14 15:00
        请回复 '确认' 继续，'取消' 放弃，或提供修改建议。

👤 用户: 改成下午4点
🤖 助手: 已更新时间，请确认：
        📝 标题：产品评审会议
        📅 时间：2025-07-14 16:00

👤 用户: 确认
🤖 助手: ✅ 已成功创建日历事件：产品评审会议
```

### API调用示例
```python
import requests

response = requests.post("http://localhost:8000/chat/", json={
    "message": "明天下午3点安排会议",
    "user_id": "user123"
})

print(response.json())
# {
#   "message": "请确认创建以下日历事件...",
#   "intent": "calendar",
#   "confidence": 0.95,
#   "success": true
# }
```

## 🚀 快速开始

### 环境要求
- Python 3.8+
- Git
- LLM API密钥 (OpenAI、Claude等)

### 安装步骤

1. **克隆仓库**
   ```bash
   git clone https://github.com/ifcheung2012/feishu-coze-plugin.git
   cd feishu-coze-plugin
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   # 创建 .env 文件
   OPENAI_API_KEY=your_openai_api_key_here
   OPENAI_MODEL=gpt-4o-mini
   
   # 飞书配置 (可选)
   FEISHU_APP_ID=your_feishu_app_id
   FEISHU_APP_SECRET=your_feishu_app_secret
   ```

4. **启动服务**
   ```bash
   # 命令行模式
   python cli.py -i
   
   # API服务模式
   python main.py
   # 访问 http://localhost:8000
   ```

### 使用Docker

```bash
docker run -p 8000:8000 -e OPENAI_API_KEY=your_openai_api_key_here intelligent-calendar-assistant:latest
```

## 🏗️ 系统架构

### 多代理协作系统
基于PDeerFlow设计思路的分层架构：

```
用户输入 → CoordinatorAgent → PlannerAgent → ExecutorAgent → 结果输出
    ↑                                                           │
    └─────────────── 人机协作反馈循环 ←─────────────────────────┘
```

- **CoordinatorAgent**: 协调器，负责意图识别和路由
- **PlannerAgent**: 规划器，负责任务规划和实体提取  
- **ExecutorAgent**: 执行器，负责具体操作执行

### 核心组件

#### 1. 会话管理系统
- **ConversationContext**: 会话上下文管理
- **ConversationManager**: 会话生命周期管理
- **状态机**: normal → waiting_confirmation → waiting_supplement

#### 2. 时间解析引擎
- **TimeParser**: 增强的时间解析器
- **支持类型**: 相对时间、具体时间、重复模式、时间范围
- **中文优化**: 针对中文时间表达优化

#### 3. 人机协作机制
- **ConfirmationService**: 确认服务
- **反馈分类**: 确认/拒绝/修改/补充
- **状态保持**: 避免重复意图识别

### 项目结构
```
feishu-coze-plugin/
├── core/                   # 核心模块
│   ├── agents/            # 多代理系统
│   ├── ai/                # AI客户端
│   └── workflow/          # 工作流引擎
├── models/                # 数据模型
│   ├── agent_models.py    # 代理模型
│   ├── chat.py           # 聊天模型
│   └── conversation.py   # 会话模型
├── services/              # 业务服务
│   ├── chat_service.py   # 聊天服务
│   └── confirmation_service.py # 确认服务
├── utils/                 # 工具模块
│   └── time_parser.py    # 时间解析器
├── api/                   # API路由
├── docs/                  # 文档
│   ├── PRD.md            # 产品需求文档
│   └── TECH_ARCHITECTURE.md # 技术架构文档
├── cli.py                 # 命令行入口
├── main.py               # API服务入口
└── requirements.txt      # 依赖列表
```

## 📊 性能指标

### 当前性能
- **意图识别准确率**: 95%+
- **时间解析准确率**: 90%+
- **响应时间**: < 2秒
- **并发支持**: 100+ 用户

### 测试结果
| 功能模块 | 测试用例 | 成功率 | 备注 |
|---------|---------|--------|------|
| 意图识别 | 7个不同类型 | 100% | 置信度0.95+，包含推理 |
| 时间解析 | 5个复杂场景 | 100% | 支持相对时间、具体时间、重复模式 |
| 实体提取 | 6个复杂句子 | 100% | 准确提取标题、时间、地点、参与者 |
| 人机协作 | 8个交互场景 | 100% | 确认、修改、取消、补充流程 |

## 🔮 发展路线图

### ✅ 第一阶段 (已完成)
- 多代理架构设计
- 意图识别系统
- 人机协作机制
- 时间解析引擎
- 多轮对话支持

### 🔄 第二阶段 (进行中)
- 飞书API集成
- 增强时间解析能力
- 智能冲突检测

### 📋 第三阶段 (规划中)
- Web界面开发
- 智能提醒系统
- 多用户权限管理

### 🔮 第四阶段 (未来)
- 数据分析与洞察
- 移动端适配
- 企业级功能

## 📚 文档

- [📋 产品需求文档 (PRD)](./docs/PRD.md)
- [🏗️ 技术架构文档](./docs/TECH_ARCHITECTURE.md)
- [🛠️ 开发指南](./docs/DEVELOPMENT.md)
- [🚀 部署指南](./docs/DEPLOYMENT.md)
- [📖 API文档](./docs/API.md)

## 🤝 贡献指南

我们欢迎所有形式的贡献！请查看 [贡献指南](./CONTRIBUTING.md) 了解如何参与项目开发。

### 开发流程
1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 📜 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- 感谢 [PDeerFlow](https://github.com/bytedance/deer-flow) 项目提供的架构设计灵感
- 感谢飞书开放平台提供的API支持
- 感谢所有贡献者的努力和支持

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
