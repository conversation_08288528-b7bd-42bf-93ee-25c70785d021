"""
协调器代理
类似PDeerFlow的Coordinator，负责初步意图识别和任务分发
"""

import logging
from typing import Any, Dict, List

from models.agent_models import AgentResponse, IntentType

from .base import AgentState, AgentType, BaseAgent, Command
from .tools import CalendarTools

logger = logging.getLogger(__name__)


class CoordinatorAgent(BaseAgent):
    """
    协调器代理

    职责：
    1. 接收用户输入
    2. 进行初步意图识别
    3. 决定是否需要移交给规划器
    4. 处理简单的问候和聊天
    """

    def __init__(self):
        super().__init__(AgentType.COORDINATOR)
        self.bind_tools(CalendarTools.get_coordinator_tools())

    async def process(self, state: AgentState) -> Command:
        """
        处理用户输入并进行意图识别

        Args:
            state: 当前状态

        Returns:
            Command: 下一步操作命令
        """
        user_input = state.user_input or ""
        logger.info(f"Coordinator processing input: {user_input}")

        # 构建系统提示
        system_prompt = self._get_coordinator_prompt()

        # 准备消息
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_input},
        ]

        try:
            # 获取绑定了工具的LLM
            llm_with_tools = self.get_llm_with_tools()

            # 调用LLM进行处理
            response = llm_with_tools.invoke(messages)

            # 检查是否有工具调用
            if hasattr(response, "tool_calls") and response.tool_calls:
                # 有工具调用，说明需要移交给规划器
                tool_call = response.tool_calls[0]

                if tool_call.get("name") == "handoff_to_planner":
                    args = tool_call.get("args", {})

                    return Command(
                        update={
                            "intent": args.get("intent_type", "calendar"),
                            "confidence": args.get("confidence", 0.8),
                            "extracted_entities": args.get("extracted_entities", {}),
                            "messages": state.messages
                            + [
                                {
                                    "role": "assistant",
                                    "content": response.content,
                                    "name": "coordinator",
                                }
                            ],
                        },
                        goto="planner",
                    )

            # 没有工具调用，直接响应用户
            return Command(
                update={
                    "intent": "chat",
                    "confidence": 1.0,
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": response.content,
                            "name": "coordinator",
                        }
                    ],
                },
                goto="__end__",
            )

        except Exception as e:
            logger.error(f"Coordinator processing error: {e}")
            return Command(
                update={
                    "intent": "chat",
                    "confidence": 0.5,
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "抱歉，我遇到了一些问题。请重新描述您的需求。",
                            "name": "coordinator",
                        }
                    ],
                },
                goto="__end__",
            )

    def _get_coordinator_prompt(self) -> str:
        """获取协调器提示词"""
        return """你是一个智能日历助手的协调器，负责理解用户意图并决定如何处理。

你的主要职责：
1. 识别用户输入的意图类型
2. 对于简单的问候和聊天，直接回应
3. 对于需要日历操作的请求，调用handoff_to_planner工具移交给规划器

# 意图分类规则

**直接处理的情况**：
- 简单问候："你好"、"早上好"、"谢谢"等
- 闲聊对话："你是谁"、"你能做什么"等
- 不涉及具体操作的一般性问题

**移交给规划器的情况**：
- 日历相关操作：创建、查询、修改、删除日程
- 包含时间、日期、会议、安排等关键词的请求
- 需要复杂处理的任务

# 执行规则

- 如果是简单问候或聊天，直接用友好的语气回应
- 如果涉及日历操作，调用handoff_to_planner工具，参数包括：
  - intent_type: "calendar"
  - user_request: 用户的原始请求
  - extracted_entities: 初步提取的实体信息（可选）
  - confidence: 置信度（0.0-1.0）

保持友好、专业的语气，始终使用中文回应。"""

    def classify_intent(self, text: str) -> Dict[str, Any]:
        """
        简单的意图分类（备用方法）

        Args:
            text: 用户输入文本

        Returns:
            分类结果字典
        """
        text_lower = text.lower()

        # 问候词
        greetings = [
            "你好",
            "hello",
            "hi",
            "早上好",
            "下午好",
            "晚上好",
            "谢谢",
            "再见",
        ]
        if any(greeting in text_lower for greeting in greetings):
            return {
                "intent": IntentType.CHAT,
                "confidence": 0.9,
                "should_handoff": False,
            }

        # 日历关键词
        calendar_keywords = [
            "安排",
            "会议",
            "日程",
            "提醒",
            "约会",
            "活动",
            "事件",
            "今天",
            "明天",
            "后天",
            "下周",
            "下月",
            "点",
            "时",
            "分钟",
            "小时",
            "创建",
            "添加",
            "查看",
            "删除",
            "修改",
            "取消",
        ]

        if any(keyword in text for keyword in calendar_keywords):
            return {
                "intent": IntentType.CALENDAR,
                "confidence": 0.8,
                "should_handoff": True,
            }

        # 默认为聊天
        return {"intent": IntentType.CHAT, "confidence": 0.6, "should_handoff": False}
