/**
 * Next.js API Route for Streamable MCP Tool Calls
 * POST /api/mcp/tools/stream - 流式调用 MCP 工具
 */

import { NextRequest } from 'next/server';
import { SimpleFeishuClient } from '@/lib/feishu-client-simple';
import { <PERSON>Handler } from '@/lib/stream-handler';
import { logger } from '@/lib/logger';
import type { MCPToolCall, MCPToolResult } from '@/types/mcp';

// 初始化客户端
const feishuClient = new SimpleFeishuClient(
  process.env.FEISHU_APP_ID!,
  process.env.FEISHU_APP_SECRET!,
  process.env.FEISHU_USER_ACCESS_TOKEN
);

const streamHandler = new StreamHandler({
  timeout: parseInt(process.env.STREAM_TIMEOUT_MS || '30000'),
  bufferSize: parseInt(process.env.STREAM_BUFFER_SIZE || '1024')
});

async function callTool(name: string, args: any): Promise<MCPToolResult> {
  const startTime = Date.now();

  try {
    logger.feishuRequest(name);

    // 使用简化的通用 API 调用方法
    const result = await feishuClient.callApi(name, args);

    const duration = Date.now() - startTime;
    logger.feishuResponse(name, result.code, duration);

    return {
      content: [{
        type: 'text',
        text: JSON.stringify(result, null, 2)
      }],
      isError: result.code !== 0
    };

  } catch (error) {
    const duration = Date.now() - startTime;
    logger.feishuError(name, error as Error);

    return {
      content: [{
        type: 'text',
        text: `Error: ${error instanceof Error ? error.message : String(error)}`
      }],
      isError: true
    };
  }
}

async function callToolStream(name: string, args: any, requestId: string) {
  // 对于支持分页的工具，创建分页流
  if (name === 'calendar_list' || name === 'calendar_event_list' || name === 'calendar_event_search') {
    return streamHandler.createPaginatedStream(
      async (pageToken?: string) => {
        const paginatedArgs = { ...args, page_token: pageToken };
        const result = await callTool(name, paginatedArgs);
        
        if (result.isError) {
          throw new Error(result.content[0]?.text || 'Unknown error');
        }
        
        const data = JSON.parse(result.content[0]?.text || '{}');
        return {
          data: data.data?.calendar_list || data.data?.items || [data.data],
          nextPageToken: data.data?.page_token
        };
      }
    );
  }
  
  // 对于其他工具，创建单次调用流
  return streamHandler.createStreamableResponse(
    (async function* () {
      const result = await callTool(name, args);
      yield result;
    })()
  );
}

export async function POST(request: NextRequest) {
  try {
    const requestId = `stream_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const body = await request.json() as MCPToolCall;
    const { name, arguments: args } = body;
    
    logger.mcpRequest(name, args, { requestId });
    logger.streamStart(requestId);
    
    const streamResponse = await callToolStream(name, args, requestId);
    
    // 创建 Response 对象
    return new Response(streamResponse.stream, {
      headers: streamResponse.headers
    });
    
  } catch (error) {
    logger.streamError('unknown', error as Error);
    
    const errorStream = streamHandler.createErrorStream(error as Error);
    return new Response(errorStream.stream, {
      headers: errorStream.headers
    });
  }
}
