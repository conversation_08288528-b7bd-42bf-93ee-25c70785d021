#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工作的MCP客户端
基于成功的stdio测试，创建可用的MCP客户端
"""

import asyncio
import json
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)


class WorkingMCPClient:
    """工作的MCP客户端 - 基于成功的stdio通信"""
    
    def __init__(self):
        self.is_connected = False
        self.tools_cache = []
        self.process = None
        self.request_id = 0
        
    async def connect_to_existing_service(self):
        """连接到已存在的MCP服务"""
        try:
            # 直接连接到现有服务，不启动新进程
            await self._initialize_connection()

        except Exception as e:
            logger.error(f"连接到MCP服务失败: {e}")
            raise

    async def start_mcp_service(self):
        """启动MCP服务"""
        try:
            logger.info("启动飞书MCP服务...")

            # 启动MCP进程
            cmd = [
                "cmd", "/c", "npx", "-y", "@larksuiteoapi/lark-mcp", "mcp",
                "-a", "cli_a76a68f612bf900c",
                "-s", "EVumG3wCHsDBeJRfpbmJkfRhzCns73jC",
                "--oauth",
                "--token-mode", "user_access_token",
                "-t", "calendar.v4.calendar.list,calendar.v4.calendar.create,calendar.v4.calendar.search,calendarEvent.instanceView,calendar.v4.calendarEvent.create"
            ]
            
            self.process = await asyncio.create_subprocess_exec(
                *cmd,
                stdin=asyncio.subprocess.PIPE,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            # 等待进程启动
            await asyncio.sleep(3)
            
            if self.process.returncode is not None:
                stderr = await self.process.stderr.read()
                raise Exception(f"MCP进程启动失败: {stderr.decode()}")
            
            # 初始化连接
            await self._initialize_connection()
            
            self.is_connected = True
            logger.info("✅ 成功连接到飞书MCP服务")
            
        except Exception as e:
            logger.error(f"启动MCP服务失败: {str(e)}")
            raise
    
    async def _initialize_connection(self):
        """初始化MCP连接"""
        try:
            # 发送初始化请求
            init_request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {"tools": {}},
                    "clientInfo": {"name": "feishu-calendar-assistant", "version": "1.0.0"}
                }
            }
            
            response = await self._send_request(init_request)
            logger.info("MCP连接初始化成功")
            
            # 获取工具列表
            await self._load_tools()
            
        except Exception as e:
            logger.error(f"MCP连接初始化失败: {str(e)}")
            raise
    
    async def _send_request(self, request: Dict[str, Any]) -> Dict[str, Any]:
        """发送MCP请求"""
        if not self.process or self.process.returncode is not None:
            raise Exception("MCP服务未运行")
        
        try:
            # 发送请求
            request_data = json.dumps(request) + "\n"
            self.process.stdin.write(request_data.encode())
            await self.process.stdin.drain()
            
            # 读取响应
            response_line = await asyncio.wait_for(self.process.stdout.readline(), timeout=15.0)
            if not response_line:
                raise Exception("未收到MCP响应")
            
            response = json.loads(response_line.decode().strip())
            
            if "error" in response:
                raise Exception(f"MCP错误: {response['error']}")
            
            return response
            
        except Exception as e:
            logger.error(f"MCP请求失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """加载可用工具"""
        try:
            request = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self._send_request(request)
            tools = response.get("result", {}).get("tools", [])
            
            for tool in tools:
                self.tools_cache.append({
                    "name": tool.get("name", ""),
                    "description": tool.get("description", ""),
                    "schema": tool.get("inputSchema", {})
                })
            
            logger.info(f"加载了 {len(self.tools_cache)} 个工具: {[t['name'] for t in self.tools_cache]}")
            
        except Exception as e:
            logger.error(f"加载工具失败: {str(e)}")
            raise
    
    async def list_calendars(self) -> Dict[str, Any]:
        """获取日历列表"""
        try:
            # 查找日历列表工具
            calendar_tools = [t for t in self.tools_cache if "calendar_list" in t["name"]]
            
            if not calendar_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到获取日历列表的工具。可用工具: {available_tools}"
                }
            
            tool_name = calendar_tools[0]["name"]
            
            # 调用工具
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": {
                        "params": {},  # 空的params对象
                        "useUAT": True  # 使用用户访问令牌
                    }
                }
            }
            
            response = await self._send_request(request)
            result = response.get("result", {})
            
            # 解析响应
            calendars = []
            if "content" in result and result["content"]:
                content_text = result["content"][0].get("text", "")
                if content_text:
                    calendar_data = json.loads(content_text)
                    calendars = calendar_data.get("calendar_list", [])
            
            return {
                "success": True,
                "calendars": calendars,
                "message": f"获取到 {len(calendars)} 个日历",
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"获取日历列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历列表失败: {str(e)}"
            }
    
    async def search_calendar_events(self, query: str, start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """搜索日历事件"""
        try:
            # 查找搜索事件工具
            search_tools = [t for t in self.tools_cache if "calendar_event_search" in t["name"] or "calendarEvent_search" in t["name"]]
            
            if not search_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到搜索日历事件的工具。可用工具: {available_tools}"
                }
            
            tool_name = search_tools[0]["name"]
            
            # 准备参数
            arguments = {
                "params": {
                    "query": query
                },
                "useUAT": True
            }
            
            if start_time:
                arguments["params"]["start_time"] = {"timestamp": start_time}
            if end_time:
                arguments["params"]["end_time"] = {"timestamp": end_time}
            
            # 调用工具
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_request(request)
            result = response.get("result", {})
            
            # 解析响应
            events = []
            if "content" in result and result["content"]:
                content_text = result["content"][0].get("text", "")
                if content_text:
                    event_data = json.loads(content_text)
                    events = event_data.get("items", [])
            
            return {
                "success": True,
                "events": events,
                "message": f"搜索到 {len(events)} 个相关事件",
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"搜索日历事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"搜索日历事件失败: {str(e)}"
            }
    
    async def create_calendar(self, calendar_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历"""
        try:
            # 查找创建日历工具
            create_tools = [t for t in self.tools_cache if "calendar_create" in t["name"] and "Event" not in t["name"]]

            if not create_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到创建日历的工具。可用工具: {available_tools}"
                }

            tool_name = create_tools[0]["name"]
            logger.info(f"调用工具 {tool_name} 创建日历，参数: {calendar_data}")
            # 准备参数 - 使用正确的飞书MCP格式
            arguments = {
                "data": {
                    "summary": calendar_data.get("summary", "新日历"),
                    "description": calendar_data.get("description", "")
                },
                "useUAT": True
            }

            # 调用工具
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            response = await self._send_request(request)
            result = response.get("result", {})

            return {
                "success": True,
                "message": f"日历 '{calendar_data.get('summary')}' 创建成功",
                "raw_result": result
            }

        except Exception as e:
            logger.error(f"创建日历失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"日历创建失败: {str(e)}"
            }

    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件"""
        try:
            # 查找创建事件工具
            create_tools = [t for t in self.tools_cache if "calendar_event_create" in t["name"] or "calendarEvent_create" in t["name"]]

            if not create_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到创建日历事件的工具。可用工具: {available_tools}"
                }

            tool_name = create_tools[0]["name"]

            # 如果指定了目标日历名称，尝试获取日历ID
            calendar_id = ""
            target_calendar = event_data.get("target_calendar")
            if target_calendar:
                calendar_id = await self._get_calendar_id_by_name(target_calendar)
                if not calendar_id:
                    logger.warning(f"未找到日历 '{target_calendar}'，将使用默认日历")

            logger.info(f"创建事件，目标日历: {target_calendar}, 日历ID: {calendar_id}")

            # 处理时间格式 - 优先使用已解析的时间戳
            start_timestamp = event_data.get("start_timestamp")
            end_timestamp = event_data.get("end_timestamp")

            # 如果没有预解析的时间戳，使用LLM解析
            if not start_timestamp or not end_timestamp:
                start_timestamp, end_timestamp = await self._parse_event_time_with_llm(event_data)

            # 构建飞书MCP工具参数 - 使用标准的飞书API格式
            # 根据飞书API文档，需要path、params、data三个部分
            event_data_obj = {
                "summary": event_data.get("title", event_data.get("summary", "")),
                "description": event_data.get("description", ""),
                "start_time": {
                    "timestamp": start_timestamp
                },
                "end_time": {
                    "timestamp": end_timestamp
                }
            }

            # 处理地点格式 - 只有在有地点时才添加
            location_str = event_data.get("location", "")
            if location_str:
                event_data_obj["location"] = {"name": location_str}

            arguments = {
                "path": {
                    "calendar_id": calendar_id or event_data.get("calendar_id", "")
                },
                "params": {},  # 查询参数，通常为空
                "data": event_data_obj
            }
            
            # 调用工具
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }
            
            response = await self._send_request(request)
            result = response.get("result", {})

            # 提取事件详细信息
            event_info = result.get("content", [{}])[0] if result.get("content") else {}
            event_id = event_info.get("event_id", "")

            # 构建飞书日历链接
            calendar_link = ""
            if event_id and calendar_id:
                calendar_link = f"https://calendar.feishu.cn/calendar/{calendar_id}/event/{event_id}"
            elif event_id:
                calendar_link = f"https://calendar.feishu.cn/event/{event_id}"

            # 格式化时间显示
            from datetime import datetime
            start_dt = datetime.fromtimestamp(int(start_timestamp)) if start_timestamp else None
            end_dt = datetime.fromtimestamp(int(end_timestamp)) if end_timestamp else None

            formatted_time = ""
            if start_dt and end_dt:
                formatted_time = f"{start_dt.strftime('%Y-%m-%d %H:%M')} - {end_dt.strftime('%H:%M')}"

            return {
                "success": True,
                "message": "事件创建成功",
                "event_id": event_id,
                "calendar_id": calendar_id,
                "calendar_link": calendar_link,
                "event_title": event_data.get("title", ""),
                "formatted_time": formatted_time,
                "start_timestamp": start_timestamp,
                "end_timestamp": end_timestamp,
                "raw_result": result
            }
            
        except Exception as e:
            logger.error(f"创建日历事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"事件创建失败: {str(e)}"
            }

    def _parse_event_time(self, event_data: Dict[str, Any]) -> tuple[str, str]:
        """解析事件时间，返回开始和结束时间戳"""
        from datetime import datetime, timedelta
        from utils.time_parser import TimeParser

        start_time_str = event_data.get("start_time", "")
        end_time_str = event_data.get("end_time", "")

        # 如果已经是时间戳格式，直接返回
        if start_time_str.isdigit() and end_time_str.isdigit():
            return start_time_str, end_time_str

        # 使用时间解析器解析时间
        time_parser = TimeParser()

        # 解析开始时间
        start_datetime = None
        if start_time_str:
            logger.info(f"解析开始时间字符串: {start_time_str}")

            # 尝试解析相对时间（如"今天"）
            relative_time = time_parser.parse_relative_time(start_time_str)
            if relative_time:
                start_datetime = relative_time
                logger.info(f"解析到相对时间: {start_datetime}")

            # 尝试解析具体时间（如"下午3点"）
            time_of_day = time_parser.parse_time_of_day(start_time_str)
            if time_of_day:
                logger.info(f"解析到时间: {time_of_day[0]}:{time_of_day[1]}")
                if start_datetime:
                    # 如果有日期，设置具体时间
                    start_datetime = start_datetime.replace(hour=time_of_day[0], minute=time_of_day[1], second=0, microsecond=0)
                else:
                    # 如果没有日期，使用今天
                    today = datetime.now().replace(hour=time_of_day[0], minute=time_of_day[1], second=0, microsecond=0)
                    start_datetime = today
                logger.info(f"设置后的开始时间: {start_datetime}")

        # 如果没有解析到开始时间，使用当前时间
        if not start_datetime:
            start_datetime = datetime.now()
            logger.info(f"使用当前时间作为开始时间: {start_datetime}")

        # 解析结束时间或计算默认结束时间
        end_datetime = None
        if end_time_str:
            # 尝试解析结束时间
            end_time_of_day = time_parser.parse_time_of_day(end_time_str)
            if end_time_of_day:
                end_datetime = start_datetime.replace(hour=end_time_of_day[0], minute=end_time_of_day[1])

        # 如果没有结束时间，默认1小时后
        if not end_datetime:
            end_datetime = start_datetime + timedelta(hours=1)

        # 转换为时间戳
        start_timestamp = str(int(start_datetime.timestamp()))
        end_timestamp = str(int(end_datetime.timestamp()))

        logger.info(f"时间解析结果: {start_time_str} -> {start_datetime} -> {start_timestamp}")
        logger.info(f"结束时间: {end_datetime} -> {end_timestamp}")

        return start_timestamp, end_timestamp

    async def _parse_event_time_with_llm(self, event_data: Dict[str, Any]) -> tuple[str, str]:
        """使用LLM解析事件时间"""
        try:
            from services.llm_time_parser import get_llm_time_parser

            # 构建时间描述文本
            time_text_parts = []
            if event_data.get("start_time"):
                time_text_parts.append(event_data["start_time"])
            if event_data.get("title"):
                time_text_parts.append(event_data["title"])
            if event_data.get("description"):
                time_text_parts.append(event_data["description"])

            time_text = " ".join(time_text_parts)

            # 使用LLM解析时间
            time_parser = get_llm_time_parser()
            result = await time_parser.parse_time_expression(time_text)

            if result.get("success"):
                logger.info(f"LLM时间解析成功: {time_text} -> {result['start_timestamp']}")
                return result["start_timestamp"], result["end_timestamp"]
            else:
                logger.warning(f"LLM时间解析失败，使用降级方案: {result.get('message', '未知错误')}")
                return self._fallback_time_parsing()

        except Exception as e:
            logger.error(f"LLM时间解析异常: {e}")
            raise ValueError(f"时间解析失败: {e}")

    async def update_calendar(self, calendar_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新日历"""
        try:
            # 注意：飞书MCP可能不支持更新日历，这里返回提示信息
            return {
                "success": False,
                "message": "飞书MCP暂不支持更新日历操作，请通过飞书客户端进行修改"
            }
        except Exception as e:
            logger.error(f"更新日历失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"更新日历失败: {str(e)}"
            }

    async def delete_calendar(self, calendar_name: str) -> Dict[str, Any]:
        """删除日历"""
        try:
            # 注意：飞书MCP可能不支持删除日历，这里返回提示信息
            return {
                "success": False,
                "message": "飞书MCP暂不支持删除日历操作，请通过飞书客户端进行删除"
            }
        except Exception as e:
            logger.error(f"删除日历失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"删除日历失败: {str(e)}"
            }

    async def list_calendar_events(self, calendar_name: str = None) -> Dict[str, Any]:
        """获取日历事件列表"""
        try:
            # 查找事件列表工具
            event_tools = [t for t in self.tools_cache if "calendarEvent" in t["name"] and "list" in t["name"]]

            if not event_tools:
                available_tools = [t["name"] for t in self.tools_cache]
                return {
                    "success": False,
                    "message": f"未找到获取事件列表的工具。可用工具: {available_tools}"
                }

            tool_name = event_tools[0]["name"]

            # 准备参数
            arguments = {
                "params": {},
                "useUAT": True
            }

            if calendar_name:
                arguments["params"]["calendar_id"] = calendar_name

            # 调用工具
            self.request_id += 1
            request = {
                "jsonrpc": "2.0",
                "id": self.request_id,
                "method": "tools/call",
                "params": {
                    "name": tool_name,
                    "arguments": arguments
                }
            }

            response = await self._send_request(request)
            result = response.get("result", {})

            # 解析响应
            events = []
            if "content" in result and result["content"]:
                content_text = result["content"][0].get("text", "")
                if content_text:
                    event_data = json.loads(content_text)
                    events = event_data.get("items", [])

            return {
                "success": True,
                "events": events,
                "message": f"获取到 {len(events)} 个事件",
                "raw_result": result
            }

        except Exception as e:
            logger.error(f"获取事件列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取事件列表失败: {str(e)}"
            }

    async def close(self):
        """关闭连接"""
        if self.process:
            try:
                self.process.terminate()
                await self.process.wait()
            except:
                pass
        self.is_connected = False
        logger.info("MCP客户端连接已关闭")
    
    async def _get_calendar_id_by_name(self, calendar_name: str) -> str:
        """根据日历名称获取日历ID"""
        try:
            # 获取日历列表
            calendars_result = await self.list_calendars()
            if calendars_result.get("success"):
                calendars = calendars_result.get("calendars", [])
                for calendar in calendars:
                    if calendar.get("summary") == calendar_name:
                        return calendar.get("calendar_id", "")
            return ""
        except Exception as e:
            logger.error(f"获取日历ID失败: {e}")
            return ""

    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool["name"] for tool in self.tools_cache]


# 全局客户端实例
_working_mcp_client: Optional[WorkingMCPClient] = None

def get_working_mcp_client() -> WorkingMCPClient:
    """获取工作的MCP客户端实例"""
    global _working_mcp_client
    if _working_mcp_client is None:
        _working_mcp_client = WorkingMCPClient()
    return _working_mcp_client


# 测试代码已移除，保持代码整洁
