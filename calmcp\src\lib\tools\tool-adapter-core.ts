/**
 * MCP 工具适配器 - 核心版本
 * 只保留项目必需的9个核心日历工具
 */

import { MCPTool } from '../../types/mcp';

/**
 * 核心日历工具集 - 7个精选工具
 */
export const CORE_CALENDAR_TOOLS: MCPTool[] = [
  // 📅 日历管理
  {
    name: 'calendar.v4.calendar.list',
    description: '获取日历列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '分页大小，最大值为 1000',
          minimum: 1,
          maximum: 1000
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        sync_token: {
          type: 'string',
          description: '同步标记，用于增量同步'
        }
      }
    }
  },
  {
    name: 'calendar.v4.calendar.get',
    description: '获取单个日历详情',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        }
      },
      required: ['calendar_id']
    }
  },

  // 📝 日程管理 (核心功能)
  {
    name: 'calendar.v4.calendarEvent.instanceView',
    description: '查看日程视图(含重复日程展开)',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        start_time: {
          type: 'string',
          description: '开始时间，Unix 时间戳，单位为秒'
        },
        end_time: {
          type: 'string',
          description: '结束时间，Unix 时间戳，单位为秒'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.get',
    description: '获取单个日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.create',
    description: '创建日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        summary: {
          type: 'string',
          description: '日程标题'
        },
        description: {
          type: 'string',
          description: '日程描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '开始时间'
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '结束时间'
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '地点地址'
            },
            latitude: {
              type: 'number',
              description: '纬度'
            },
            longitude: {
              type: 'number',
              description: '经度'
            }
          },
          description: '日程地点'
        },
        color: {
          type: 'number',
          description: '日程颜色'
        },
        recurrence: {
          type: 'string',
          description: '重复规则，RRULE 格式'
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '可见性'
        },
        attendee_ability: {
          type: 'string',
          enum: ['none', 'can_see_others', 'can_invite_others', 'can_modify_event'],
          description: '参与者权限'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '忙闲状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'summary', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.patch',
    description: '更新日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        summary: {
          type: 'string',
          description: '日程标题'
        },
        description: {
          type: 'string',
          description: '日程描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '开始时间'
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '结束时间'
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '地点地址'
            }
          },
          description: '日程地点'
        },
        color: {
          type: 'number',
          description: '日程颜色'
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '可见性'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '忙闲状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.delete',
    description: '删除日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        need_notification: {
          type: 'boolean',
          description: '是否给日程参与人发送bot通知'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  }
];

/**
 * 导出核心工具集 (兼容原有接口)
 */
export const ALL_FEISHU_CALENDAR_TOOLS: MCPTool[] = CORE_CALENDAR_TOOLS;

/**
 * 根据工具名称获取工具定义
 */
export function getToolByName(name: string): MCPTool | undefined {
  return CORE_CALENDAR_TOOLS.find(tool => tool.name === name);
}

/**
 * 验证工具参数
 */
export function validateToolArguments(toolName: string, args: any): { valid: boolean; errors?: string[] } {
  const tool = getToolByName(toolName);
  if (!tool) {
    return { valid: false, errors: [`Unknown tool: ${toolName}`] };
  }

  // 简单的必需参数检查
  const required = tool.inputSchema.required || [];
  const missing = required.filter(field => !(field in args));

  if (missing.length > 0) {
    return {
      valid: false,
      errors: [`Missing required fields: ${missing.join(', ')}`]
    };
  }

  return { valid: true };
}
