/**
 * MCP 工具适配器 - 核心版本
 * 包含日历和任务管理的核心工具
 */

import { MCPTool } from '../../types/mcp';

/**
 * 核心日历工具集 - 7个精选工具
 */
export const CORE_CALENDAR_TOOLS: MCPTool[] = [
  // 📅 日历管理
  {
    name: 'calendar.v4.calendar.list',
    description: '获取日历列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '分页大小，最大值为 1000',
          minimum: 1,
          maximum: 1000
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        sync_token: {
          type: 'string',
          description: '同步标记，用于增量同步'
        }
      }
    }
  },
  {
    name: 'calendar.v4.calendar.get',
    description: '获取单个日历详情。注意：必须先调用calendar.list获取日历ID，不能直接使用日历名称',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取'
        }
      },
      required: ['calendar_id']
    }
  },

  // 📝 日程管理 (核心功能)
  {
    name: 'calendar.v4.calendarEvent.instanceView',
    description: '查看指定日历中指定时间范围的日程视图(含重复日程展开)。注意：必须先调用calendar.list获取日历ID',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历的唯一ID（格式如：<EMAIL>），不是日历名称！必须从calendar.list的结果中获取'
        },
        start_time: {
          type: 'string',
          description: '开始时间，Unix 时间戳，单位为秒'
        },
        end_time: {
          type: 'string',
          description: '结束时间，Unix 时间戳，单位为秒'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.get',
    description: '获取单个日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.create',
    description: '创建日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        summary: {
          type: 'string',
          description: '日程标题'
        },
        description: {
          type: 'string',
          description: '日程描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '开始时间'
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '结束时间'
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '地点地址'
            },
            latitude: {
              type: 'number',
              description: '纬度'
            },
            longitude: {
              type: 'number',
              description: '经度'
            }
          },
          description: '日程地点'
        },
        color: {
          type: 'number',
          description: '日程颜色'
        },
        recurrence: {
          type: 'string',
          description: '重复规则，RRULE 格式'
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '可见性'
        },
        attendee_ability: {
          type: 'string',
          enum: ['none', 'can_see_others', 'can_invite_others', 'can_modify_event'],
          description: '参与者权限'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '忙闲状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'summary', 'start_time', 'end_time']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.patch',
    description: '更新日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        summary: {
          type: 'string',
          description: '日程标题'
        },
        description: {
          type: 'string',
          description: '日程描述'
        },
        start_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '开始时间'
        },
        end_time: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '结束时间，Unix 时间戳，单位为秒'
            },
            timezone: {
              type: 'string',
              description: '时区'
            }
          },
          required: ['timestamp'],
          description: '结束时间'
        },
        location: {
          type: 'object',
          properties: {
            name: {
              type: 'string',
              description: '地点名称'
            },
            address: {
              type: 'string',
              description: '地点地址'
            }
          },
          description: '日程地点'
        },
        color: {
          type: 'number',
          description: '日程颜色'
        },
        visibility: {
          type: 'string',
          enum: ['default', 'public', 'private'],
          description: '可见性'
        },
        free_busy_status: {
          type: 'string',
          enum: ['busy', 'free'],
          description: '忙闲状态'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  },
  {
    name: 'calendar.v4.calendarEvent.delete',
    description: '删除日程',
    inputSchema: {
      type: 'object',
      properties: {
        calendar_id: {
          type: 'string',
          description: '日历 ID'
        },
        event_id: {
          type: 'string',
          description: '日程 ID'
        },
        need_notification: {
          type: 'boolean',
          description: '是否给日程参与人发送bot通知'
        }
      },
      required: ['calendar_id', 'event_id']
    }
  }
];

/**
 * 核心任务管理工具集 - 精选任务工具（排除订阅、附件、评论、自定义字段）
 */
export const CORE_TASK_TOOLS: MCPTool[] = [
  // 📋 任务清单管理
  {
    name: 'task.v2.tasklist.list',
    description: '获取任务清单列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '分页大小，最大值为 100',
          minimum: 1,
          maximum: 100
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      }
    }
  },
  {
    name: 'task.v2.tasklist.get',
    description: '获取单个任务清单详情',
    inputSchema: {
      type: 'object',
      properties: {
        tasklist_guid: {
          type: 'string',
          description: '任务清单的全局唯一ID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['tasklist_guid']
    }
  },
  {
    name: 'task.v2.tasklist.create',
    description: '创建任务清单',
    inputSchema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '清单名称，不能为空，最大100个utf8字符'
        },
        description: {
          type: 'string',
          description: '清单描述，最大1000个utf8字符'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['name']
    }
  },
  {
    name: 'task.v2.tasklist.patch',
    description: '更新任务清单',
    inputSchema: {
      type: 'object',
      properties: {
        tasklist_guid: {
          type: 'string',
          description: '要更新的任务清单GUID'
        },
        name: {
          type: 'string',
          description: '清单名称'
        },
        description: {
          type: 'string',
          description: '清单描述'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['tasklist_guid']
    }
  },
  {
    name: 'task.v2.tasklist.delete',
    description: '删除任务清单',
    inputSchema: {
      type: 'object',
      properties: {
        tasklist_guid: {
          type: 'string',
          description: '要删除的任务清单GUID'
        }
      },
      required: ['tasklist_guid']
    }
  },

  // 📝 任务管理
  {
    name: 'task.v2.task.list',
    description: '获取任务列表（我负责的任务）',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '每页的任务数量，最大值为 100',
          minimum: 1,
          maximum: 100
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        completed: {
          type: 'boolean',
          description: '是否按任务完成进行过滤。true=已完成，false=未完成，不填=不过滤'
        },
        type: {
          type: 'string',
          description: '列取任务的类型，目前只支持"my_tasks"（我负责的）',
          enum: ['my_tasks']
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      }
    }
  },
  {
    name: 'task.v2.task.get',
    description: '获取任务详情',
    inputSchema: {
      type: 'object',
      properties: {
        task_guid: {
          type: 'string',
          description: '要获取的任务GUID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['task_guid']
    }
  },
  {
    name: 'task.v2.task.create',
    description: '创建任务',
    inputSchema: {
      type: 'object',
      properties: {
        summary: {
          type: 'string',
          description: '任务标题，不能为空，支持最大3000个utf8字符'
        },
        description: {
          type: 'string',
          description: '任务描述，支持最大3000个utf8字符'
        },
        due: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '截止时间的时间戳（毫秒）'
            },
            is_all_day: {
              type: 'boolean',
              description: '是否截止到一个日期'
            }
          },
          description: '任务截止时间'
        },
        start: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间的时间戳（毫秒）'
            },
            is_all_day: {
              type: 'boolean',
              description: '是否开始于一个日期'
            }
          },
          description: '任务开始时间'
        },
        completed_at: {
          type: 'string',
          description: '任务的完成时刻时间戳(ms)。不填写或者设为0表示创建未完成任务'
        },
        extra: {
          type: 'string',
          description: '调用者可以传入的任意附带到任务上的数据'
        },
        mode: {
          type: 'number',
          description: '任务完成模式, 1=会签任务, 2=或签任务'
        },
        is_milestone: {
          type: 'boolean',
          description: '是否是里程碑任务'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['summary']
    }
  },
  {
    name: 'task.v2.task.patch',
    description: '更新任务',
    inputSchema: {
      type: 'object',
      properties: {
        task_guid: {
          type: 'string',
          description: '要更新的任务GUID'
        },
        summary: {
          type: 'string',
          description: '任务标题'
        },
        description: {
          type: 'string',
          description: '任务描述'
        },
        due: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '截止时间的时间戳（毫秒）'
            },
            is_all_day: {
              type: 'boolean',
              description: '是否截止到一个日期'
            }
          },
          description: '任务截止时间'
        },
        start: {
          type: 'object',
          properties: {
            timestamp: {
              type: 'string',
              description: '开始时间的时间戳（毫秒）'
            },
            is_all_day: {
              type: 'boolean',
              description: '是否开始于一个日期'
            }
          },
          description: '任务开始时间'
        },
        completed_at: {
          type: 'string',
          description: '任务的完成时刻时间戳(ms)。设为0表示标记为未完成'
        },
        extra: {
          type: 'string',
          description: '调用者可以传入的任意附带到任务上的数据'
        },
        mode: {
          type: 'number',
          description: '任务完成模式, 1=会签任务, 2=或签任务'
        },
        is_milestone: {
          type: 'boolean',
          description: '是否是里程碑任务'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['task_guid']
    }
  },
  {
    name: 'task.v2.task.delete',
    description: '删除任务',
    inputSchema: {
      type: 'object',
      properties: {
        task_guid: {
          type: 'string',
          description: '要删除的任务GUID'
        }
      },
      required: ['task_guid']
    }
  },

  // 📂 任务分组管理
  {
    name: 'task.v2.section.list',
    description: '获取自定义分组列表',
    inputSchema: {
      type: 'object',
      properties: {
        page_size: {
          type: 'number',
          description: '分页大小，最大值为 100',
          minimum: 1,
          maximum: 100
        },
        page_token: {
          type: 'string',
          description: '分页标记，第一次请求不填'
        },
        resource_type: {
          type: 'string',
          description: '自定义分组所属的资源类型，支持my_tasks（我负责的）和tasklist（清单）',
          enum: ['my_tasks', 'tasklist']
        },
        resource_id: {
          type: 'string',
          description: '如resource_type为"tasklist"，这里需要填写要列取自定义分组的清单的GUID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['resource_type']
    }
  },
  {
    name: 'task.v2.section.get',
    description: '获取自定义分组详情',
    inputSchema: {
      type: 'object',
      properties: {
        section_guid: {
          type: 'string',
          description: '要获取的自定义分组GUID'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['section_guid']
    }
  },
  {
    name: 'task.v2.section.create',
    description: '创建自定义分组',
    inputSchema: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: '自定义分组名，不允许为空，最大100个utf8字符'
        },
        resource_type: {
          type: 'string',
          description: '自定义分组的资源类型，支持"tasklist"（清单）或者"my_tasks"（我负责的）',
          enum: ['tasklist', 'my_tasks']
        },
        resource_id: {
          type: 'string',
          description: '自定义分组要归属的资源id。当resource_type为"tasklist"时这里需要填写清单的GUID'
        },
        insert_before: {
          type: 'string',
          description: '要将新分组插入到自定义分组的前面的目标分组的guid'
        },
        insert_after: {
          type: 'string',
          description: '要将新分组插入到自定义分组的后面的目标分组的guid'
        },
        user_id_type: {
          type: 'string',
          enum: ['open_id', 'union_id', 'user_id'],
          description: '用户ID类型'
        }
      },
      required: ['name', 'resource_type']
    }
  }
];

/**
 * 合并所有工具集
 */
export const ALL_FEISHU_TOOLS: MCPTool[] = [
  ...CORE_CALENDAR_TOOLS,
  ...CORE_TASK_TOOLS
];

/**
 * 导出核心工具集 (兼容原有接口)
 */
export const ALL_FEISHU_CALENDAR_TOOLS: MCPTool[] = CORE_CALENDAR_TOOLS;

/**
 * 根据工具名称获取工具定义
 */
export function getToolByName(name: string): MCPTool | undefined {
  return ALL_FEISHU_TOOLS.find(tool => tool.name === name);
}

/**
 * 验证工具参数
 */
export function validateToolArguments(toolName: string, args: any): { valid: boolean; errors?: string[] } {
  const tool = getToolByName(toolName);
  if (!tool) {
    return { valid: false, errors: [`Unknown tool: ${toolName}`] };
  }

  // 简单的必需参数检查
  const required = tool.inputSchema.required || [];
  const missing = required.filter(field => !(field in args));

  if (missing.length > 0) {
    return {
      valid: false,
      errors: [`Missing required fields: ${missing.join(', ')}`]
    };
  }

  return { valid: true };
}

/**
 * 获取所有日历工具
 */
export function getCalendarTools(): MCPTool[] {
  return CORE_CALENDAR_TOOLS;
}

/**
 * 获取所有任务工具
 */
export function getTaskTools(): MCPTool[] {
  return CORE_TASK_TOOLS;
}

/**
 * 获取所有工具
 */
export function getAllTools(): MCPTool[] {
  return ALL_FEISHU_TOOLS;
}
