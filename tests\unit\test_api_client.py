"""
测试飞书API客户端
"""

import asyncio
from unittest.mock import AsyncMock, MagicMock, patch

import httpx
import pytest

from integrations.feishu.api_client import (
    FeishuAPIError,
    FeishuAuthError,
    FeishuNetworkError,
    exchange_code,
    get_calendars,
    get_user_info,
)


class TestFeishuAPIClient:
    """飞书API客户端测试"""

    @pytest.mark.asyncio
    async def test_exchange_code_success(self):
        """测试成功交换授权码"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "msg": "success",
            "data": {
                "access_token": "test_access_token",
                "refresh_token": "test_refresh_token",
                "expires_in": 7200,
            },
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.return_value = mock_response
            mock_client.return_value = mock_client_instance

            result = await exchange_code("test_code")

            assert result["code"] == 0
            assert result["data"]["access_token"] == "test_access_token"
            assert result["data"]["refresh_token"] == "test_refresh_token"

    @pytest.mark.asyncio
    async def test_exchange_code_network_error(self):
        """测试网络错误处理"""
        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.post.side_effect = httpx.RequestError("Network error")
            mock_client.return_value = mock_client_instance

            with pytest.raises(FeishuNetworkError):
                await exchange_code("test_code")

    @pytest.mark.asyncio
    async def test_get_user_info_success(self):
        """测试成功获取用户信息"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "msg": "success",
            "data": {
                "open_id": "test_user_id",
                "name": "Test User",
                "avatar_url": "https://example.com/avatar.jpg",
            },
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance

            result = await get_user_info("test_access_token")

            assert result["code"] == 0
            assert result["data"]["open_id"] == "test_user_id"
            assert result["data"]["name"] == "Test User"

    @pytest.mark.asyncio
    async def test_get_calendars_success(self):
        """测试成功获取日历列表"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "code": 0,
            "msg": "success",
            "data": {
                "calendar_list": [
                    {
                        "calendar_id": "test_calendar_id",
                        "summary": "Test Calendar",
                        "type": "primary",
                        "role": "owner",
                    }
                ]
            },
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance

            result = await get_calendars("test_access_token")

            assert result["code"] == 0
            assert len(result["data"]["calendar_list"]) == 1
            assert (
                result["data"]["calendar_list"][0]["calendar_id"] == "test_calendar_id"
            )

    @pytest.mark.asyncio
    async def test_api_error_handling(self):
        """测试API错误处理"""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "code": 99991663,
            "msg": "invalid refresh token",
        }

        with patch("httpx.AsyncClient") as mock_client:
            mock_client_instance = AsyncMock()
            mock_client_instance.__aenter__.return_value = mock_client_instance
            mock_client_instance.get.return_value = mock_response
            mock_client.return_value = mock_client_instance

            result = await get_user_info("invalid_token")

            # API客户端会包装错误，所以检查实际返回的结构
            assert result["code"] == 400  # HTTP状态码
            assert "invalid refresh token" in str(result)  # 错误信息在某个地方

    def test_custom_exceptions(self):
        """测试自定义异常"""
        # 测试FeishuAPIError
        error = FeishuAPIError(400, "API Error", {"detail": "test"})
        assert error.code == 400
        assert error.message == "API Error"
        assert error.details == {"detail": "test"}

        # 测试FeishuNetworkError
        network_error = FeishuNetworkError("Network Error")
        assert str(network_error) == "Network Error"

        # 测试FeishuAuthError
        auth_error = FeishuAuthError("Auth Error")
        assert str(auth_error) == "Auth Error"
