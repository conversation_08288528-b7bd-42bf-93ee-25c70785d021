# MCP服务使用指南

## 快速开始

### 1. 启动MCP服务器

首先启动真实飞书MCP服务器：

```bash
cd tests/streamable_mcp
python start_real_server.py
```

服务器将在 `http://localhost:5000` 启动。

### 2. 查看日历

使用测试脚本查看你的日历：

```bash
# 查看所有日历和第一个日历的事件
python test_calendar_view.py

# 查看特定日历的事件（需要提供日历ID）
python test_calendar_view.py <calendar_id>
```

### 3. 可用的MCP工具

服务器提供以下飞书日历工具：

- `mcp_lark-mcp_calendar_v4_calendar_list` - 获取日历列表
- `mcp_lark-mcp_calendar_v4_calendarEvent_search` - 搜索日历事件
- `mcp_lark-mcp_calendar_v4_calendarEvent_create` - 创建日历事件
- `mcp_lark-mcp_calendar_v4_calendar_create` - 创建日历

## 使用示例

### 查看所有日历

```python
from feishu_mcp_client_streamable import StreamableMCPClient

async def view_calendars():
    async with StreamableMCPClient("http://localhost:5000") as client:
        await client.initialize()
        
        result = await client.call_tool(
            "mcp_lark-mcp_calendar_v4_calendar_list",
            {"params": {"page_size": 20}}
        )
        
        calendars = result["result"]["data"]["items"]
        for calendar in calendars:
            print(f"日历: {calendar['summary']} (ID: {calendar['calendar_id']})")
```

### 查看特定日历的事件

```python
async def view_events(calendar_id):
    async with StreamableMCPClient("http://localhost:5000") as client:
        await client.initialize()
        
        result = await client.call_tool(
            "mcp_lark-mcp_calendar_v4_calendarEvent_search",
            {
                "data": {"query": ""},
                "params": {"page_size": 10},
                "path": {"calendar_id": calendar_id}
            }
        )
        
        events = result["result"]["data"]["items"]
        for event in events:
            print(f"事件: {event['summary']}")
```

## 故障排除

### 1. 服务器连接失败

确保：
- 服务器正在运行 (`python start_real_server.py`)
- 端口8000未被占用
- 防火墙允许本地连接

### 2. 认证失败

确保：
- 已完成OAuth认证
- 访问令牌有效
- 飞书应用权限配置正确

### 3. 无日历数据

检查：
- 飞书账号是否有日历
- 应用是否有日历读取权限
- 访问令牌是否有效

## 配置文件

- `mcp_streamable_config.json` - MCP服务器配置
- `mcp_server_config.json` - 服务器基础配置

## 日志

服务器和客户端都会输出详细日志，帮助诊断问题。 