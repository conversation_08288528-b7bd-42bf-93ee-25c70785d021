"""
执行器代理
负责执行具体的日历操作
"""

import logging
from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from config import FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET
from integrations.feishu import (
    FeishuCalendarLark,
    create_event,
    delete_event,
    get_calendar_events,
    get_calendars,
    update_event,
)
from integrations.storage import get_token
from core.mcp.working_mcp_client import get_working_mcp_client

from .base import AgentState, AgentType, BaseAgent, Command
from .tools import CalendarTools

logger = logging.getLogger(__name__)


class ExecutorAgent(BaseAgent):
    """
    执行器代理

    职责：
    1. 执行具体的日历操作
    2. 调用飞书API
    3. 返回执行结果
    """

    def __init__(self):
        super().__init__(AgentType.EXECUTOR)
        self.bind_tools(CalendarTools.get_executor_tools())
        # 初始化飞书日历客户端
        self.feishu_calendar = FeishuCalendarLark(
            app_id=FEISHU_CLIENT_ID, app_secret=FEISHU_CLIENT_SECRET
        )
        # 初始化MCP客户端
        self.mcp_client = get_working_mcp_client()

    async def process(self, state: AgentState) -> Command:
        """
        执行日历操作

        Args:
            state: 当前状态

        Returns:
            Command: 执行结果命令
        """
        calendar_plan = state.calendar_plan
        user_id = getattr(state, "user_id", "default_user")

        if not calendar_plan:
            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": "没有找到执行计划。",
                            "name": "executor",
                        }
                    ]
                },
                goto="__end__",
            )

        try:
            # 提取计划信息
            extracted_event = calendar_plan.get("extracted_event", {})
            action = extracted_event.get("action", "create")

            # 根据操作类型执行相应的飞书API调用
            if action == "create":
                result = await self._create_calendar_event(user_id, extracted_event)
            elif action == "query":
                result = await self._query_calendar_events(user_id, extracted_event)
            elif action == "update":
                result = await self._update_calendar_event(user_id, extracted_event)
            elif action == "delete":
                result = await self._delete_calendar_event(user_id, extracted_event)
            else:
                result = {"success": False, "message": f"不支持的操作类型: {action}"}

            # 构建响应消息
            if result.get("success", False):
                success_message = result.get("message", f"已成功{action}日历事件")
                logger.info(f"Executor completed: {success_message}")

                return Command(
                    update={
                        "messages": state.messages
                        + [
                            {
                                "role": "assistant",
                                "content": success_message,
                                "name": "executor",
                            }
                        ],
                        "last_operation": action,
                        "execution_result": result,
                    },
                    goto="__end__",
                )
            else:
                error_message = result.get("message", f"执行{action}操作失败")
                logger.error(f"Executor failed: {error_message}")

                return Command(
                    update={
                        "messages": state.messages
                        + [
                            {
                                "role": "assistant",
                                "content": error_message,
                                "name": "executor",
                            }
                        ]
                    },
                    goto="__end__",
                )

        except Exception as e:
            logger.error(f"Executor error: {e}")
            return Command(
                update={
                    "messages": state.messages
                    + [
                        {
                            "role": "assistant",
                            "content": f"执行操作时遇到问题：{str(e)}",
                            "name": "executor",
                        }
                    ]
                },
                goto="__end__",
            )

    async def _get_user_access_token(self, user_id: str) -> Optional[str]:
        """获取用户访问令牌"""
        try:
            token_data = get_token(user_id)
            if token_data and "access_token" in token_data:
                return token_data["access_token"]
            return None
        except Exception as e:
            logger.error(f"获取用户访问令牌失败: {e}")
            return None

    async def _get_primary_calendar_id(self, access_token: str) -> Optional[str]:
        """获取用户主日历ID"""
        try:
            calendars_result = await get_calendars(access_token)
            if calendars_result.get("code") == 0:
                calendars = calendars_result.get("data", {}).get("calendar_list", [])
                # 查找主日历
                for calendar in calendars:
                    if calendar.get("type") == "primary":
                        return calendar.get("calendar_id")
                # 如果没有找到主日历，返回第一个日历
                if calendars:
                    return calendars[0].get("calendar_id")
            return None
        except Exception as e:
            logger.error(f"获取主日历ID失败: {e}")
            return None

    async def _create_calendar_event(
        self, user_id: str, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """创建日历事件 - 使用MCP客户端"""
        try:
            logger.info(f"使用MCP客户端创建日历事件: {event_data}")

            # 确保MCP客户端已连接
            if not self.mcp_client.is_connected:
                await self.mcp_client.start_mcp_service()

            # 准备事件数据
            title = event_data.get("title", "新事件")
            start_time = event_data.get("start_time", "")
            end_time = event_data.get("end_time", "")
            description = event_data.get("description", "")
            location = event_data.get("location", "")

            # 如果时间是datetime对象，转换为ISO字符串
            if hasattr(start_time, 'isoformat'):
                start_time = start_time.isoformat()
            if hasattr(end_time, 'isoformat'):
                end_time = end_time.isoformat()

            # 使用MCP客户端创建事件
            mcp_event_data = {
                "title": title,
                "description": description,
                "start_time": start_time,
                "end_time": end_time,
                "location": location
            }

            result = await self.mcp_client.create_calendar_event(mcp_event_data)

            if result.get("success"):
                return {
                    "success": True,
                    "message": f"✅ 已成功创建日历事件：{title}",
                    "event_id": result.get("event_id"),
                    "mcp_result": result
                }
            else:
                return {
                    "success": False,
                    "message": f"创建事件失败：{result.get('message', '未知错误')}",
                    "mcp_result": result
                }

        except Exception as e:
            logger.error(f"创建日历事件失败: {e}")
            return {"success": False, "message": f"创建事件时发生错误：{str(e)}"}

    async def _query_calendar_events(
        self, user_id: str, query_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """查询日历事件 - 使用MCP客户端"""
        try:
            logger.info(f"使用MCP客户端查询日历: {query_data}")

            # 确保MCP客户端已连接
            if not self.mcp_client.is_connected:
                await self.mcp_client.start_mcp_service()

            # 检查是否是搜索操作
            query_text = query_data.get("title", query_data.get("query", ""))
            action = query_data.get("action", "query")

            if action == "search" or query_text:
                # 执行搜索操作
                result = await self.mcp_client.search_calendar_events(query_text)

                if result.get("success"):
                    events = result.get("events", [])
                    if events:
                        event_list = []
                        for event in events:
                            event_info = f"• {event.get('summary', '无标题')}"
                            if event.get("start_time"):
                                event_info += f" ({event['start_time']})"
                            if event.get("location"):
                                event_info += f" - {event['location']}"
                            event_list.append(event_info)

                        events_text = "\n".join(event_list)
                        return {
                            "success": True,
                            "message": f"🔍 搜索到 {len(events)} 个相关事件：\n{events_text}",
                            "events": events,
                            "mcp_result": result
                        }
                    else:
                        return {
                            "success": True,
                            "message": "🔍 没有找到匹配的事件",
                            "events": [],
                            "mcp_result": result
                        }
                else:
                    return {
                        "success": False,
                        "message": f"搜索失败：{result.get('message', '未知错误')}",
                        "mcp_result": result
                    }
            else:
                # 执行日历列表查询
                result = await self.mcp_client.list_calendars()

                if result.get("success"):
                    calendars = result.get("calendars", [])
                    if calendars:
                        calendar_list = []
                        for i, calendar in enumerate(calendars, 1):
                            calendar_info = f"{i}. {calendar.get('summary', '未命名日历')}"
                            if calendar.get('description'):
                                calendar_info += f" - {calendar['description']}"
                            calendar_list.append(calendar_info)

                        calendars_text = "\n".join(calendar_list)
                        return {
                            "success": True,
                            "message": f"📅 您的日历列表：\n{calendars_text}",
                            "calendars": calendars,
                            "mcp_result": result
                        }
                    else:
                        return {
                            "success": True,
                            "message": "📅 没有找到日历",
                            "calendars": [],
                            "mcp_result": result
                        }
                else:
                    return {
                        "success": False,
                        "message": f"查询日历失败：{result.get('message', '未知错误')}",
                        "mcp_result": result
                    }

        except Exception as e:
            logger.error(f"查询日历事件失败: {e}")
            return {"success": False, "message": f"查询事件时发生错误：{str(e)}"}

    async def _update_calendar_event(
        self, user_id: str, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """更新日历事件"""
        try:
            # TODO: 实现事件更新逻辑
            # 需要先查询到具体的事件ID，然后调用update_event API
            return {"success": False, "message": "事件更新功能正在开发中"}
        except Exception as e:
            logger.error(f"更新日历事件失败: {e}")
            return {"success": False, "message": f"更新事件时发生错误：{str(e)}"}

    async def _delete_calendar_event(
        self, user_id: str, event_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """删除日历事件"""
        try:
            # TODO: 实现事件删除逻辑
            # 需要先查询到具体的事件ID，然后调用delete_event API
            return {"success": False, "message": "事件删除功能正在开发中"}
        except Exception as e:
            logger.error(f"删除日历事件失败: {e}")
            return {"success": False, "message": f"删除事件时发生错误：{str(e)}"}
