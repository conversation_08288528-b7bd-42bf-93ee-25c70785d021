#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版Supabase Token MCP客户端 - 直接使用项目现有的token管理
"""

import os
import logging
import requests
from typing import Dict, List, Optional, Any

# 直接使用项目现有的token管理
from integrations.storage import get_token

logger = logging.getLogger(__name__)

class SimpleSupabaseMCPClient:
    """简化版Supabase Token MCP客户端"""
    
    def __init__(self, 
                 mcp_service_url: str = "http://localhost:3000",
                 user_id: str = None):
        self.mcp_service_url = mcp_service_url
        self.user_id = user_id or os.getenv("DEFAULT_USER_ID", "default_user")
        self.available_tools = []
        
    async def initialize(self):
        """初始化客户端"""
        try:
            # 检查token是否可用
            token_data = get_token(self.user_id)
            if not token_data:
                raise Exception(f"用户 {self.user_id} 没有可用的token")
            
            # 获取MCP工具列表
            await self._load_tools()
            
            logger.info(f"简化版MCP客户端初始化成功，用户: {self.user_id}, 工具数量: {len(self.available_tools)}")
            
        except Exception as e:
            logger.error(f"简化版MCP客户端初始化失败: {str(e)}")
            raise
    
    async def _load_tools(self):
        """获取MCP工具列表"""
        try:
            url = f"{self.mcp_service_url}/api/mcp/tools/list"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    self.available_tools = result.get("tools", [])
                    logger.info(f"成功获取 {len(self.available_tools)} 个MCP工具")
                else:
                    logger.error(f"获取工具列表失败: {result}")
            else:
                logger.error(f"获取工具列表失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"获取MCP工具列表失败: {str(e)}")
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """调用MCP工具"""
        try:
            if arguments is None:
                arguments = {}
            
            # 获取当前token（项目的定时刷新服务会自动处理过期token）
            token_data = get_token(self.user_id)
            if not token_data:
                return {"error": f"用户 {self.user_id} 没有可用的token"}
            
            # 调用MCP工具
            url = f"{self.mcp_service_url}/api/mcp/tools/call"
            payload = {
                "name": tool_name,
                "arguments": arguments
            }
            
            # 添加token到请求头（如果MCP服务支持）
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {token_data['access_token']}"
            }
            
            logger.info(f"调用MCP工具: {tool_name}, 用户: {self.user_id}")
            
            response = requests.post(url, json=payload, headers=headers, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"工具调用成功: {tool_name}")
                return result
            else:
                logger.error(f"工具调用失败: {response.status_code} - {response.text}")
                return {"error": f"API调用失败: {response.status_code}"}
                
        except Exception as e:
            logger.error(f"调用MCP工具失败: {str(e)}")
            return {"error": f"工具调用失败: {str(e)}"}
    
    def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用工具列表"""
        return self.available_tools
    
    def format_tools_for_llm(self) -> List[Dict[str, Any]]:
        """格式化工具列表供LLM使用"""
        tools = []
        for tool in self.available_tools:
            tools.append({
                "type": "function",
                "function": {
                    "name": tool["name"],
                    "description": tool.get("description", ""),
                    "parameters": tool.get("inputSchema", {})
                }
            })
        return tools

# 全局客户端实例
_simple_supabase_mcp_client: Optional[SimpleSupabaseMCPClient] = None

def get_simple_supabase_mcp_client(user_id: str = None) -> SimpleSupabaseMCPClient:
    """获取简化版Supabase Token MCP客户端实例"""
    global _simple_supabase_mcp_client
    if _simple_supabase_mcp_client is None:
        _simple_supabase_mcp_client = SimpleSupabaseMCPClient(user_id=user_id)
    return _simple_supabase_mcp_client

async def main():
    """测试函数"""
    logging.basicConfig(level=logging.INFO)
    
    client = SimpleSupabaseMCPClient()
    await client.initialize()
    
    # 测试调用工具
    result = await client.call_tool("calendar.v4.calendar.list", {"page_size": 10})
    print(f"测试结果: {result}")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
