/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/stack-trace";
exports.ids = ["vendor-chunks/stack-trace"];
exports.modules = {

/***/ "(rsc)/./node_modules/stack-trace/lib/stack-trace.js":
/*!*****************************************************!*\
  !*** ./node_modules/stack-trace/lib/stack-trace.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("exports.get = function(belowFn) {\n  var oldLimit = Error.stackTraceLimit;\n  Error.stackTraceLimit = Infinity;\n\n  var dummyObject = {};\n\n  var v8Handler = Error.prepareStackTrace;\n  Error.prepareStackTrace = function(dummyObject, v8StackTrace) {\n    return v8StackTrace;\n  };\n  Error.captureStackTrace(dummyObject, belowFn || exports.get);\n\n  var v8StackTrace = dummyObject.stack;\n  Error.prepareStackTrace = v8Handler;\n  Error.stackTraceLimit = oldLimit;\n\n  return v8StackTrace;\n};\n\nexports.parse = function(err) {\n  if (!err.stack) {\n    return [];\n  }\n\n  var self = this;\n  var lines = err.stack.split('\\n').slice(1);\n\n  return lines\n    .map(function(line) {\n      if (line.match(/^\\s*[-]{4,}$/)) {\n        return self._createParsedCallSite({\n          fileName: line,\n          lineNumber: null,\n          functionName: null,\n          typeName: null,\n          methodName: null,\n          columnNumber: null,\n          'native': null,\n        });\n      }\n\n      var lineMatch = line.match(/at (?:(.+)\\s+\\()?(?:(.+?):(\\d+)(?::(\\d+))?|([^)]+))\\)?/);\n      if (!lineMatch) {\n        return;\n      }\n\n      var object = null;\n      var method = null;\n      var functionName = null;\n      var typeName = null;\n      var methodName = null;\n      var isNative = (lineMatch[5] === 'native');\n\n      if (lineMatch[1]) {\n        functionName = lineMatch[1];\n        var methodStart = functionName.lastIndexOf('.');\n        if (functionName[methodStart-1] == '.')\n          methodStart--;\n        if (methodStart > 0) {\n          object = functionName.substr(0, methodStart);\n          method = functionName.substr(methodStart + 1);\n          var objectEnd = object.indexOf('.Module');\n          if (objectEnd > 0) {\n            functionName = functionName.substr(objectEnd + 1);\n            object = object.substr(0, objectEnd);\n          }\n        }\n        typeName = null;\n      }\n\n      if (method) {\n        typeName = object;\n        methodName = method;\n      }\n\n      if (method === '<anonymous>') {\n        methodName = null;\n        functionName = null;\n      }\n\n      var properties = {\n        fileName: lineMatch[2] || null,\n        lineNumber: parseInt(lineMatch[3], 10) || null,\n        functionName: functionName,\n        typeName: typeName,\n        methodName: methodName,\n        columnNumber: parseInt(lineMatch[4], 10) || null,\n        'native': isNative,\n      };\n\n      return self._createParsedCallSite(properties);\n    })\n    .filter(function(callSite) {\n      return !!callSite;\n    });\n};\n\nfunction CallSite(properties) {\n  for (var property in properties) {\n    this[property] = properties[property];\n  }\n}\n\nvar strProperties = [\n  'this',\n  'typeName',\n  'functionName',\n  'methodName',\n  'fileName',\n  'lineNumber',\n  'columnNumber',\n  'function',\n  'evalOrigin'\n];\nvar boolProperties = [\n  'topLevel',\n  'eval',\n  'native',\n  'constructor'\n];\nstrProperties.forEach(function (property) {\n  CallSite.prototype[property] = null;\n  CallSite.prototype['get' + property[0].toUpperCase() + property.substr(1)] = function () {\n    return this[property];\n  }\n});\nboolProperties.forEach(function (property) {\n  CallSite.prototype[property] = false;\n  CallSite.prototype['is' + property[0].toUpperCase() + property.substr(1)] = function () {\n    return this[property];\n  }\n});\n\nexports._createParsedCallSite = function(properties) {\n  return new CallSite(properties);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/stack-trace/lib/stack-trace.js\n");

/***/ })

};
;