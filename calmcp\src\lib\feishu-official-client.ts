/**
 * Official Feishu SDK Client
 * 基于官方 @larksuiteoapi/node-sdk 的完整实现
 * 支持所有 calendar.v4 API
 */

import * as lark from '@larksuiteoapi/node-sdk';
import { getToolByName, validateToolArguments } from './tools/tool-adapter';

export interface OfficialApiResponse {
  code: number;
  msg: string;
  data: any;
}

export class OfficialFeishuClient {
  private client: lark.Client;
  private userAccessToken?: string;

  constructor(appId: string, appSecret: string, userAccessToken?: string) {
    this.client = new lark.Client({
      appId,
      appSecret,
      appType: lark.AppType.SelfBuild,
      domain: lark.Domain.Feishu
    });

    // 从环境变量获取用户访问令牌
    this.userAccessToken = userAccessToken || process.env.FEISHU_USER_ACCESS_TOKEN;

    console.log('Official Feishu client initialized:', {
      appId,
      hasAppSecret: !!appSecret,
      hasUserToken: !!this.userAccessToken,
      userTokenPrefix: this.userAccessToken ? this.userAccessToken.substring(0, 10) + '...' : 'none'
    });
  }

  setUserAccessToken(token: string) {
    this.userAccessToken = token;
  }

  /**
   * 通用 API 调用方法
   * 支持所有 calendar.v4 API
   */
  async callApi(method: string, params: any = {}): Promise<OfficialApiResponse> {
    try {
      console.log(`🔧 调用官方 API 方法: ${method}`, params);

      // 验证工具和参数
      const tool = getToolByName(method);
      if (!tool) {
        console.warn(`⚠️  未知的 API 方法: ${method}`);
        return {
          code: -1,
          msg: `Unknown method: ${method}`,
          data: null
        };
      }

      // 验证参数
      const validation = validateToolArguments(method, params);
      if (!validation.valid) {
        console.error(`❌ 参数验证失败:`, validation.errors);
        return {
          code: -1,
          msg: `Invalid arguments: ${validation.errors?.join(', ')}`,
          data: null
        };
      }

      // 根据方法名调用相应的 API
      const result = await this._callSpecificApi(method, params);
      
      console.log(`✅ API 调用成功: ${method}`, { code: result.code, msg: result.msg });
      return result;

    } catch (error) {
      console.error(`❌ API 调用失败 (${method}):`, error);
      return {
        code: -1,
        msg: `API 调用失败: ${error}`,
        data: null
      };
    }
  }

  /**
   * 调用具体的 API 方法
   */
  private async _callSpecificApi(method: string, params: any): Promise<OfficialApiResponse> {
    const useUserToken = params.useUAT !== false; // 默认使用用户访问令牌

    try {
      let response: any;
      let requestOptions: any = {};

      // 根据方法名构建请求
      switch (method) {
        // 日历管理 API
        case 'calendar.v4.calendar.list':
          requestOptions = { params: this._buildParams(params, ['page_size', 'page_token', 'sync_token']) };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.list(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.list(requestOptions);
          break;

        case 'calendar.v4.calendar.get':
          requestOptions = { path: { calendar_id: params.calendar_id } };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.get(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.get(requestOptions);
          break;

        case 'calendar.v4.calendar.create':
          requestOptions = {
            data: this._buildParams(params, ['summary', 'description', 'permissions', 'color', 'summary_alias'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.create(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.create(requestOptions);
          break;

        case 'calendar.v4.calendar.delete':
          requestOptions = { path: { calendar_id: params.calendar_id } };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.delete(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.delete(requestOptions);
          break;

        case 'calendar.v4.calendar.patch':
          requestOptions = {
            path: { calendar_id: params.calendar_id },
            data: this._buildParams(params, ['summary', 'description', 'permissions', 'color', 'summary_alias'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.patch(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.patch(requestOptions);
          break;

        case 'calendar.v4.calendar.primary':
          requestOptions = { params: this._buildParams(params, ['user_id_type']) };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.primary(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.primary(requestOptions);
          break;

        case 'calendar.v4.calendar.search':
          requestOptions = { params: this._buildParams(params, ['query', 'user_id_type', 'page_token', 'page_size']) };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendar.search(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendar.search(requestOptions);
          break;

        // 日历事件 API
        case 'calendar.v4.calendarEvent.list':
          requestOptions = {
            path: { calendar_id: params.calendar_id },
            params: this._buildParams(params, ['page_size', 'page_token', 'sync_token', 'start_time', 'end_time', 'user_id_type'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.list(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.list(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.create':
          requestOptions = {
            path: { calendar_id: params.calendar_id },
            data: this._buildParams(params, ['summary', 'description', 'start_time', 'end_time', 'location', 'visibility', 'attendee_ability', 'free_busy_status']),
            params: this._buildParams(params, ['user_id_type'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.create(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.create(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.get':
          requestOptions = {
            path: { calendar_id: params.calendar_id, event_id: params.event_id },
            params: this._buildParams(params, ['user_id_type', 'need_meeting_settings', 'need_attendee', 'max_attendee_num'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.get(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.get(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.delete':
          requestOptions = {
            path: { calendar_id: params.calendar_id, event_id: params.event_id },
            params: this._buildParams(params, ['need_notification'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.delete(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.delete(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.patch':
          requestOptions = {
            path: { calendar_id: params.calendar_id, event_id: params.event_id },
            data: this._buildParams(params, ['summary', 'description', 'start_time', 'end_time', 'location', 'visibility', 'attendee_ability', 'free_busy_status']),
            params: this._buildParams(params, ['user_id_type'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.patch(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.patch(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.search':
          requestOptions = {
            path: { calendar_id: params.calendar_id },
            data: this._buildParams(params, ['query', 'filter'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.search(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.search(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.instances':
          requestOptions = {
            path: { calendar_id: params.calendar_id, event_id: params.event_id },
            params: this._buildParams(params, ['start_time', 'end_time', 'user_id_type', 'page_size', 'page_token'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.instances(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.instances(requestOptions);
          break;

        case 'calendar.v4.calendarEvent.instanceView':
          requestOptions = {
            path: { calendar_id: params.calendar_id },
            params: this._buildParams(params, ['start_time', 'end_time', 'user_id_type'])
          };
          response = useUserToken && this.userAccessToken
            ? await this.client.calendar.v4.calendarEvent.instanceView(requestOptions, lark.withUserAccessToken(this.userAccessToken))
            : await this.client.calendar.v4.calendarEvent.instanceView(requestOptions);
          break;

        default:
          console.warn(`⚠️  未实现的 API 方法: ${method}`);
          return {
            code: -1,
            msg: `Unimplemented method: ${method}`,
            data: null
          };
      }

      // 统一处理响应
      return {
        code: response.code || 0,
        msg: response.msg || 'success',
        data: response.data
      };

    } catch (error: any) {
      console.error(`❌ 具体 API 调用失败 (${method}):`, error);

      // 提取更详细的错误信息
      let errorMsg = `API 调用失败: ${error.message || error}`;
      if (error.response?.data) {
        errorMsg += ` | API Error: ${JSON.stringify(error.response.data)}`;
      }

      return {
        code: -1,
        msg: errorMsg,
        data: null
      };
    }
  }

  /**
   * 构建参数对象，只包含指定的字段
   */
  private _buildParams(params: any, allowedFields: string[]): any {
    const result: any = {};
    for (const field of allowedFields) {
      if (params[field] !== undefined) {
        result[field] = params[field];
      }
    }
    return result;
  }
}
