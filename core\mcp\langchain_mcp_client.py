#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
本地飞书MCP客户端 - LangChain集成版本
连接到自开发的本地MCP服务器，实现完全自主可控
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from core.mcp.local_mcp_client import get_local_mcp_client

logger = logging.getLogger(__name__)


class LangChainMCPClient:
    """本地MCP客户端 - LangChain集成版本"""

    def __init__(self):
        self.is_connected = False
        self.local_client = get_local_mcp_client()
        self.tools_cache = []

    async def start_mcp_service(self):
        """启动本地MCP服务"""
        try:
            logger.info("连接到本地飞书MCP服务...")

            # 连接到本地MCP服务器
            await self.local_client.connect()

            # 获取工具列表
            self.tools_cache = self.local_client.tools_cache

            self.is_connected = True
            logger.info(f"✅ 成功连接到本地飞书MCP服务，加载了 {len(self.tools_cache)} 个工具")

            # 打印工具名称以便调试
            tool_names = [tool.name for tool in self.tools_cache]
            logger.info(f"可用工具: {tool_names}")

        except Exception as e:
            import traceback
            logger.error(f"连接本地MCP服务失败: {str(e)}")
            logger.error(f"详细错误信息: {traceback.format_exc()}")
            raise
    
    async def list_calendars(self, user_id: str = "default_user") -> Dict[str, Any]:
        """获取日历列表"""
        if not self.is_connected:
            await self.start_mcp_service()

        try:
            result = await self.local_client.list_calendars(user_id)
            logger.info(f"获取日历列表结果: {result}")
            return result

        except Exception as e:
            logger.error(f"获取日历列表失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历列表失败: {str(e)}"
            }

    async def create_calendar_event(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建日历事件"""
        if not self.is_connected:
            await self.start_mcp_service()

        try:
            result = await self.local_client.create_calendar_event(
                user_id=event_data.get("user_id", "default_user"),
                title=event_data.get("title", event_data.get("summary", "")),
                start_time=event_data.get("start_time", ""),
                end_time=event_data.get("end_time", ""),
                calendar_id=event_data.get("calendar_id", "primary"),
                description=event_data.get("description", ""),
                location=event_data.get("location", ""),
                attendees=event_data.get("attendees", [])
            )

            logger.info(f"创建日历事件结果: {result}")
            return result

        except Exception as e:
            logger.error(f"创建日历事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"事件创建失败: {str(e)}"
            }

    async def get_today_events(self, user_id: str = "default_user") -> Dict[str, Any]:
        """获取今天的事件"""
        if not self.is_connected:
            await self.start_mcp_service()

        try:
            result = await self.local_client.get_today_events(user_id)
            logger.info(f"获取今天事件结果: {result}")
            return result

        except Exception as e:
            logger.error(f"获取今天事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取今天事件失败: {str(e)}"
            }

    async def get_calendar_events(self, user_id: str = "default_user",
                                 calendar_id: str = "primary",
                                 start_time: str = None,
                                 end_time: str = None) -> Dict[str, Any]:
        """获取日历事件"""
        if not self.is_connected:
            await self.start_mcp_service()

        try:
            result = await self.local_client.get_calendar_events(
                user_id=user_id,
                calendar_id=calendar_id,
                start_time=start_time,
                end_time=end_time
            )
            logger.info(f"获取日历事件结果: {result}")
            return result

        except Exception as e:
            logger.error(f"获取日历事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"获取日历事件失败: {str(e)}"
            }

    async def search_calendar_events(self, query: str, user_id: str = "default_user",
                                   start_time: str = None, end_time: str = None) -> Dict[str, Any]:
        """搜索日历事件"""
        if not self.is_connected:
            await self.start_mcp_service()

        try:
            # 使用get_calendar_events来模拟搜索功能
            result = await self.local_client.get_calendar_events(
                user_id=user_id,
                calendar_id="primary",
                start_time=start_time,
                end_time=end_time
            )

            # 简单的文本搜索过滤
            if result.get("success") and "events" in result:
                events = result["events"]
                filtered_events = []
                for event in events:
                    if (query.lower() in event.get("title", "").lower() or
                        query.lower() in event.get("description", "").lower()):
                        filtered_events.append(event)

                result["events"] = filtered_events
                result["message"] = f"搜索到 {len(filtered_events)} 个相关事件"

            logger.info(f"搜索日历事件结果: {result}")
            return result

        except Exception as e:
            logger.error(f"搜索日历事件失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": f"搜索日历事件失败: {str(e)}"
            }
    
    async def close(self):
        """关闭连接"""
        try:
            if self.local_client:
                await self.local_client.disconnect()
            self.is_connected = False
            logger.info("本地MCP客户端连接已关闭")
        except Exception as e:
            logger.error(f"关闭连接时出错: {str(e)}")
    
    def get_available_tools(self) -> List[str]:
        """获取可用工具列表"""
        return [tool.name for tool in self.tools_cache]


# 全局客户端实例
_langchain_mcp_client: Optional[LangChainMCPClient] = None

def get_langchain_mcp_client() -> LangChainMCPClient:
    """获取langchain MCP客户端实例"""
    global _langchain_mcp_client
    if _langchain_mcp_client is None:
        _langchain_mcp_client = LangChainMCPClient()
    return _langchain_mcp_client


# 测试代码已移除，保持代码整洁
