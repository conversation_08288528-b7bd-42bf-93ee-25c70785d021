"""
工作流引擎
统一的工作流管理和执行引擎
"""

import logging
from typing import Any, Callable, Dict

from models.workflow import Command

from .nodes import (
    calendar_confirm_processor_node,
    calendar_processor_node,
    chat_processor_node,
    intent_classifier_node,
    journal_processor_node,
    media_processor_node,
    result_presenter_node,
    text_input_node,
)

logger = logging.getLogger(__name__)


class WorkflowEngine:
    """工作流引擎"""

    def __init__(self):
        """初始化工作流引擎"""
        self.nodes = {}
        self._setup_nodes()

    def _setup_nodes(self):
        """设置所有节点"""
        self.nodes = {
            "text_input": text_input_node,
            "intent_classifier": intent_classifier_node,
            "calendar_processor": calendar_processor_node,
            "calendar_confirm_processor": calendar_confirm_processor_node,
            "chat_processor": chat_processor_node,
            "journal_processor": journal_processor_node,
            "media_processor": media_processor_node,
            "result_presenter": result_presenter_node,
        }

    def add_node(self, name: str, func: Callable):
        """添加节点"""
        self.nodes[name] = func

    def run(
        self, initial_state: Dict[str, Any], start_node: str = "text_input"
    ) -> Dict[str, Any]:
        """
        运行工作流

        Args:
            initial_state: 初始状态
            start_node: 起始节点

        Returns:
            最终状态
        """
        state = initial_state.copy()
        current_node = start_node
        max_iterations = 20  # 防止无限循环
        iteration = 0

        while current_node and iteration < max_iterations:
            iteration += 1

            if current_node not in self.nodes:
                logger.error(f"Node not found: {current_node}")
                break

            logger.info(f"Running node: {current_node}")

            try:
                node_func = self.nodes[current_node]
                result = node_func(state)

                # 处理不同类型的返回值
                if isinstance(result, Command):
                    # 更新状态
                    if result.update:
                        state.update(result.update)

                    # 获取下一个节点
                    current_node = result.goto

                elif isinstance(result, dict):
                    # 直接返回结果（终端节点）
                    state.update(result)
                    # 保持上下文状态，以便下次对话使用
                    if "context_state" in result:
                        state["context_state"] = result["context_state"]
                    break

                else:
                    logger.error(
                        f"Invalid result type from node {current_node}: {type(result)}"
                    )
                    break

            except Exception as e:
                logger.error(f"Error in node {current_node}: {str(e)}")
                state["error"] = str(e)
                break

        if iteration >= max_iterations:
            logger.warning("Workflow reached maximum iterations")
            state["error"] = "工作流执行超时"

        return state
