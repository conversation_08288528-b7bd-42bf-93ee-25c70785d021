#!/usr/bin/env node
/**
 * 查询"小宝暑假计划"日历中明天的日程安排
 */

const http = require('http');

async function makeRequest(url, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const options = {
      method,
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const req = http.request(url, options, (res) => {
      let responseData = '';
      res.on('data', (chunk) => {
        responseData += chunk;
      });
      res.on('end', () => {
        try {
          resolve({
            status: res.statusCode,
            data: JSON.parse(responseData)
          });
        } catch (e) {
          resolve({
            status: res.statusCode,
            data: responseData
          });
        }
      });
    });
    
    req.on('error', (err) => {
      reject(err);
    });
    
    req.setTimeout(15000, () => {
      req.destroy();
      reject(new Error('Request timeout'));
    });

    if (data) {
      req.write(JSON.stringify(data));
    }
    req.end();
  });
}

function formatDateTime(timestamp) {
  const date = new Date(parseInt(timestamp) * 1000);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    weekday: 'long'
  });
}

function formatTime(timestamp) {
  const date = new Date(parseInt(timestamp) * 1000);
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });
}

async function queryTomorrowEvents() {
  console.log('📅 查询"小宝暑假计划"日历中明天的日程安排...\n');
  
  try {
    // 计算明天的时间范围
    const now = new Date();
    const tomorrow = new Date(now);
    tomorrow.setDate(now.getDate() + 1);
    tomorrow.setHours(0, 0, 0, 0); // 明天 00:00:00
    
    const tomorrowEnd = new Date(tomorrow);
    tomorrowEnd.setHours(23, 59, 59, 999); // 明天 23:59:59
    
    const startTimestamp = Math.floor(tomorrow.getTime() / 1000).toString();
    const endTimestamp = Math.floor(tomorrowEnd.getTime() / 1000).toString();
    
    console.log(`🕐 查询时间范围: ${formatDateTime(startTimestamp)} 到 ${formatDateTime(endTimestamp)}`);
    console.log(`📊 时间戳范围: ${startTimestamp} - ${endTimestamp}\n`);
    
    // 小宝暑假计划的日历ID
    const calendarId = '<EMAIL>';
    
    // 调用日程列表查询工具
    console.log('📋 调用日程查询工具...');
    const response = await makeRequest(
      'http://localhost:3000/api/mcp/tools/call',
      'POST',
      {
        name: 'calendar.v4.calendarEvent.list',
        arguments: {
          calendar_id: calendarId,
          start_time: startTimestamp,
          end_time: endTimestamp,
          page_size: 50
        }
      }
    );
    
    if (response.status === 200 && response.data.success) {
      console.log('✅ 日程查询成功!\n');
      
      // 解析响应数据
      const result = response.data.result;
      if (result.isError) {
        console.log('❌ 查询出现错误:');
        console.log(result.content[0].text);
        return;
      }
      
      // 解析日程数据
      const eventData = JSON.parse(result.content[0].text);
      
      if (eventData.code === 0) {
        const events = eventData.data.items || [];
        
        console.log('=' .repeat(60));
        console.log(`📅 小宝暑假计划 - 明天的日程安排 (${events.length} 个事件)`);
        console.log('=' .repeat(60));
        
        if (events.length === 0) {
          console.log('🎉 明天没有安排任何日程，可以自由安排时间！');
        } else {
          events.forEach((event, index) => {
            console.log(`\n📌 事件 ${index + 1}:`);
            console.log(`   标题: ${event.summary || '无标题'}`);
            
            if (event.description) {
              console.log(`   描述: ${event.description}`);
            }
            
            // 处理时间显示
            if (event.start_time && event.end_time) {
              const startTime = formatTime(event.start_time.timestamp);
              const endTime = formatTime(event.end_time.timestamp);
              console.log(`   时间: ${startTime} - ${endTime}`);
            }
            
            // 处理地点
            if (event.location && event.location.name) {
              console.log(`   地点: ${event.location.name}`);
              if (event.location.address) {
                console.log(`   地址: ${event.location.address}`);
              }
            }
            
            // 处理参与者
            if (event.attendee_ability && event.attendee_ability !== 'none') {
              console.log(`   参与者权限: ${event.attendee_ability}`);
            }
            
            // 处理状态
            if (event.status) {
              const statusMap = {
                'confirmed': '已确认',
                'tentative': '待定',
                'cancelled': '已取消'
              };
              console.log(`   状态: ${statusMap[event.status] || event.status}`);
            }
            
            // 处理忙闲状态
            if (event.free_busy_status) {
              const busyMap = {
                'busy': '忙碌',
                'free': '空闲'
              };
              console.log(`   忙闲: ${busyMap[event.free_busy_status] || event.free_busy_status}`);
            }
            
            console.log(`   事件ID: ${event.event_id}`);
          });
        }
        
        // 显示分页信息
        if (eventData.data.has_more) {
          console.log(`\n📄 还有更多日程，使用 page_token: ${eventData.data.page_token}`);
        }
        
      } else {
        console.log('❌ 飞书API返回错误:');
        console.log(`   错误代码: ${eventData.code}`);
        console.log(`   错误信息: ${eventData.msg}`);
      }
      
    } else {
      console.log('❌ 日程查询失败:', response.status);
      console.log('响应:', response.data);
    }
    
    console.log('\n' + '='.repeat(60));
    console.log('🎯 查询完成！');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.log('❌ 查询过程中出现错误:', error.message);
    console.log('\n💡 请确保:');
    console.log('1. Next.js 服务器正在运行');
    console.log('2. 飞书应用配置正确');
    console.log('3. 用户访问令牌有效');
  }
}

// 运行查询
queryTomorrowEvents();
