/**
 * Feishu Calendar API Types
 */

export interface FeishuCalendar {
  calendar_id: string;
  summary: string;
  description?: string;
  permissions: string;
  color: number;
  type: 'primary' | 'shared' | 'google' | 'resource' | 'exchange';
  summary_alias?: string;
  is_deleted: boolean;
  is_third_party: boolean;
  role: 'owner' | 'writer' | 'reader' | 'free_busy_reader';
}

export interface FeishuCalendarEvent {
  event_id: string;
  organizer_calendar_id: string;
  summary: string;
  description?: string;
  start_time: {
    timestamp: string;
    timezone?: string;
  };
  end_time: {
    timestamp: string;
    timezone?: string;
  };
  visibility: 'default' | 'public' | 'private';
  attendee_ability: 'none' | 'can_see_others' | 'can_invite_others' | 'can_modify_event';
  free_busy_status: 'busy' | 'free';
  location?: {
    name?: string;
    address?: string;
    latitude?: number;
    longitude?: number;
  };
  color: number;
  reminders?: Array<{
    minutes: number;
  }>;
  recurrence?: string;
  status: 'tentative' | 'confirmed' | 'cancelled';
  is_organizer: boolean;
  is_attendee: boolean;
  attendees?: Array<{
    type: 'user' | 'chat' | 'resource' | 'third_party';
    attendee_id: string;
    rsvp_status?: 'needs_action' | 'accept' | 'tentative' | 'decline';
    is_optional?: boolean;
    display_name?: string;
  }>;
}

export interface FeishuApiResponse<T> {
  code: number;
  msg: string;
  data: T;
}

export interface CalendarListResponse {
  has_more: boolean;
  page_token?: string;
  sync_token?: string;
  calendar_list: FeishuCalendar[];
}

export interface EventListResponse {
  has_more: boolean;
  page_token?: string;
  sync_token?: string;
  items: FeishuCalendarEvent[];
}

export interface CreateEventRequest {
  calendar_id: string;
  summary: string;
  description?: string;
  start_time: {
    timestamp: string;
    timezone?: string;
  };
  end_time: {
    timestamp: string;
    timezone?: string;
  };
  location?: {
    name?: string;
    address?: string;
  };
  attendees?: Array<{
    type: 'user' | 'chat' | 'resource';
    attendee_id: string;
    is_optional?: boolean;
  }>;
  reminders?: Array<{
    minutes: number;
  }>;
  recurrence?: string;
  visibility?: 'default' | 'public' | 'private';
  free_busy_status?: 'busy' | 'free';
}

export interface UpdateEventRequest extends Partial<CreateEventRequest> {
  event_id: string;
}

export interface SearchEventsRequest {
  calendar_id: string;
  query?: string;
  start_time?: {
    timestamp: string;
    timezone?: string;
  };
  end_time?: {
    timestamp: string;
    timezone?: string;
  };
  page_size?: number;
  page_token?: string;
}

export interface DeleteEventRequest {
  calendar_id: string;
  event_id: string;
}
