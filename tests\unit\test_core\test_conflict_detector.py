"""
冲突检测单元测试
"""

from datetime import datetime, timedelta
from unittest.mock import AsyncMock, Mock

import pytest

# 临时注释掉不存在的导入，等待实际模块实现
# from core.calendar.conflict_detector import (
#     ConflictDetector,
#     ConflictResult,
#     ConflictType,
# )
# from models.calendar import CalendarEvent, EventStatus


# 创建临时的mock类用于测试
class ConflictType:
    FULL_OVERLAP = "full_overlap"
    START_OVERLAP = "start_overlap"
    END_OVERLAP = "end_overlap"
    CONTAINS = "contains"
    BUFFER_CONFLICT = "buffer_conflict"
    LOCATION_CONFLICT = "location_conflict"
    ATTENDEE_CONFLICT = "attendee_conflict"


class EventStatus:
    CONFIRMED = "confirmed"
    TENTATIVE = "tentative"
    CANCELLED = "cancelled"


class CalendarEvent:
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)


class ConflictResult:
    def __init__(self):
        self.has_conflict = False
        self.conflicts = []
        self.suggestions = []


class ConflictDetector:
    def __init__(self, buffer_minutes=0):
        self.buffer_minutes = buffer_minutes

    def check_conflict(self, new_event, existing_events, consider_priority=False):
        # 简单的mock实现
        result = ConflictResult()
        # 这里应该是实际的冲突检测逻辑
        return result

    async def check_conflict_async(self, new_event, get_events_func):
        # 简单的mock实现
        result = ConflictResult()
        return result


class TestConflictDetector:
    """冲突检测器测试"""

    def setup_method(self):
        """测试前准备"""
        self.detector = ConflictDetector()

    def test_time_overlap_detection(self):
        """测试时间重叠检测"""
        # 现有事件：2025-07-15 14:00-16:00
        existing_event = CalendarEvent(
            event_id="existing_1",
            summary="现有会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 16, 0),
            status=EventStatus.CONFIRMED,
        )

        test_cases = [
            # 完全重叠
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 14, 30),
                    end_time=datetime(2025, 7, 15, 15, 30),
                ),
                "expected_conflict": True,
                "conflict_type": ConflictType.FULL_OVERLAP,
            },
            # 开始时间冲突
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 13, 30),
                    end_time=datetime(2025, 7, 15, 14, 30),
                ),
                "expected_conflict": True,
                "conflict_type": ConflictType.START_OVERLAP,
            },
            # 结束时间冲突
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 15, 30),
                    end_time=datetime(2025, 7, 15, 17, 0),
                ),
                "expected_conflict": True,
                "conflict_type": ConflictType.END_OVERLAP,
            },
            # 包含现有事件
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 13, 0),
                    end_time=datetime(2025, 7, 15, 17, 0),
                ),
                "expected_conflict": True,
                "conflict_type": ConflictType.CONTAINS,
            },
            # 无冲突 - 之前
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 12, 0),
                    end_time=datetime(2025, 7, 15, 13, 0),
                ),
                "expected_conflict": False,
            },
            # 无冲突 - 之后
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 17, 0),
                    end_time=datetime(2025, 7, 15, 18, 0),
                ),
                "expected_conflict": False,
            },
            # 边界情况 - 紧接着开始
            {
                "new_event": CalendarEvent(
                    summary="新会议",
                    start_time=datetime(2025, 7, 15, 16, 0),
                    end_time=datetime(2025, 7, 15, 17, 0),
                ),
                "expected_conflict": False,  # 紧接着不算冲突
            },
        ]

        for case in test_cases:
            result = self.detector.check_conflict(case["new_event"], [existing_event])

            if case["expected_conflict"]:
                assert result.has_conflict is True
                assert len(result.conflicts) == 1
                assert result.conflicts[0].conflict_type == case["conflict_type"]
                assert result.conflicts[0].conflicting_event == existing_event
            else:
                assert result.has_conflict is False
                assert len(result.conflicts) == 0

    def test_multiple_conflicts(self):
        """测试多个冲突检测"""
        # 新事件：2025-07-15 14:00-17:00
        new_event = CalendarEvent(
            summary="长会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 17, 0),
        )

        # 多个现有事件
        existing_events = [
            CalendarEvent(
                event_id="event_1",
                summary="会议1",
                start_time=datetime(2025, 7, 15, 13, 30),
                end_time=datetime(2025, 7, 15, 14, 30),
                status=EventStatus.CONFIRMED,
            ),
            CalendarEvent(
                event_id="event_2",
                summary="会议2",
                start_time=datetime(2025, 7, 15, 15, 0),
                end_time=datetime(2025, 7, 15, 16, 0),
                status=EventStatus.CONFIRMED,
            ),
            CalendarEvent(
                event_id="event_3",
                summary="会议3",
                start_time=datetime(2025, 7, 15, 16, 30),
                end_time=datetime(2025, 7, 15, 17, 30),
                status=EventStatus.CONFIRMED,
            ),
        ]

        result = self.detector.check_conflict(new_event, existing_events)

        assert result.has_conflict is True
        assert len(result.conflicts) == 3  # 与所有三个事件都有冲突

    def test_event_status_consideration(self):
        """测试事件状态考虑"""
        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
        )

        test_cases = [
            {
                "existing_status": EventStatus.CONFIRMED,
                "expected_conflict": True,
                "description": "已确认事件应该产生冲突",
            },
            {
                "existing_status": EventStatus.TENTATIVE,
                "expected_conflict": True,
                "description": "暂定事件应该产生冲突警告",
            },
            {
                "existing_status": EventStatus.CANCELLED,
                "expected_conflict": False,
                "description": "已取消事件不应该产生冲突",
            },
        ]

        for case in test_cases:
            existing_event = CalendarEvent(
                event_id="test_event",
                summary="测试事件",
                start_time=datetime(2025, 7, 15, 14, 0),
                end_time=datetime(2025, 7, 15, 15, 0),
                status=case["existing_status"],
            )

            result = self.detector.check_conflict(new_event, [existing_event])

            assert result.has_conflict == case["expected_conflict"], case["description"]

    def test_buffer_time_consideration(self):
        """测试缓冲时间考虑"""
        # 设置15分钟缓冲时间
        detector_with_buffer = ConflictDetector(buffer_minutes=15)

        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 15, 0),
            end_time=datetime(2025, 7, 15, 16, 0),
        )

        existing_event = CalendarEvent(
            event_id="existing",
            summary="现有会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 14, 50),  # 10分钟间隔，小于缓冲时间
            status=EventStatus.CONFIRMED,
        )

        result = detector_with_buffer.check_conflict(new_event, [existing_event])

        # 应该检测到缓冲时间冲突
        assert result.has_conflict is True
        assert result.conflicts[0].conflict_type == ConflictType.BUFFER_CONFLICT

    def test_location_conflict_detection(self):
        """测试地点冲突检测"""
        # 同一地点的冲突
        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
            location="会议室A",
        )

        existing_event = CalendarEvent(
            event_id="existing",
            summary="现有会议",
            start_time=datetime(2025, 7, 15, 14, 30),
            end_time=datetime(2025, 7, 15, 15, 30),
            location="会议室A",
            status=EventStatus.CONFIRMED,
        )

        result = self.detector.check_conflict(new_event, [existing_event])

        assert result.has_conflict is True
        assert any(
            c.conflict_type == ConflictType.LOCATION_CONFLICT for c in result.conflicts
        )

    def test_attendee_conflict_detection(self):
        """测试参会者冲突检测"""
        common_attendees = ["<EMAIL>", "<EMAIL>"]

        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
            attendees=common_attendees + ["<EMAIL>"],
        )

        existing_event = CalendarEvent(
            event_id="existing",
            summary="现有会议",
            start_time=datetime(2025, 7, 15, 14, 30),
            end_time=datetime(2025, 7, 15, 15, 30),
            attendees=common_attendees + ["<EMAIL>"],
            status=EventStatus.CONFIRMED,
        )

        result = self.detector.check_conflict(new_event, [existing_event])

        assert result.has_conflict is True
        assert any(
            c.conflict_type == ConflictType.ATTENDEE_CONFLICT for c in result.conflicts
        )

    @pytest.mark.asyncio
    async def test_async_conflict_detection(self):
        """测试异步冲突检测"""

        # 模拟异步获取日历事件
        async def mock_get_events(start_time, end_time):
            return [
                CalendarEvent(
                    event_id="async_event",
                    summary="异步事件",
                    start_time=datetime(2025, 7, 15, 14, 30),
                    end_time=datetime(2025, 7, 15, 15, 30),
                    status=EventStatus.CONFIRMED,
                )
            ]

        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
        )

        result = await self.detector.check_conflict_async(new_event, mock_get_events)

        assert result.has_conflict is True

    def test_conflict_resolution_suggestions(self):
        """测试冲突解决建议"""
        new_event = CalendarEvent(
            summary="新会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
        )

        existing_event = CalendarEvent(
            event_id="existing",
            summary="现有会议",
            start_time=datetime(2025, 7, 15, 14, 30),
            end_time=datetime(2025, 7, 15, 15, 30),
            status=EventStatus.CONFIRMED,
        )

        result = self.detector.check_conflict(new_event, [existing_event])

        assert result.has_conflict is True
        assert len(result.suggestions) > 0

        # 应该包含时间调整建议
        time_suggestions = [
            s for s in result.suggestions if s.suggestion_type == "time_adjustment"
        ]
        assert len(time_suggestions) > 0

    def test_priority_based_conflict_resolution(self):
        """测试基于优先级的冲突解决"""
        # 高优先级新事件
        high_priority_event = CalendarEvent(
            summary="重要会议",
            start_time=datetime(2025, 7, 15, 14, 0),
            end_time=datetime(2025, 7, 15, 15, 0),
            priority="high",
        )

        # 低优先级现有事件
        low_priority_event = CalendarEvent(
            event_id="existing",
            summary="普通会议",
            start_time=datetime(2025, 7, 15, 14, 30),
            end_time=datetime(2025, 7, 15, 15, 30),
            priority="low",
            status=EventStatus.CONFIRMED,
        )

        result = self.detector.check_conflict(
            high_priority_event, [low_priority_event], consider_priority=True
        )

        # 应该建议移动低优先级事件
        assert any("移动现有事件" in s.description for s in result.suggestions)
