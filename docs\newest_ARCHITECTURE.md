# 飞书日历 MCP 服务技术架构文档

## 📋 项目概述

本项目为效率人事移动 APP 提供飞书日历管理服务，通过自然语言交互实现日历和日程的增删改查功能。

### 核心特性
- **多用户支持**：移动端 APP 服务多个用户
- **自然语言交互**：基于 LLM 的智能对话
- **飞书集成**：对接飞书官方 API
- **云端部署**：支持 Vercel、Railway 等平台

## 🏗️ 整体技术架构
主要内容
项目概述 - 核心特性和目标
整体技术架构 - 详细的架构图和组件说明
调用流程详解 - 从用户输入到返回结果的完整流程
技术栈选择 - 前端、后端、部署的技术选型
项目结构 - 完整的目录结构设计
核心代码实现 - Python LLM 服务、Node.js MCP 服务、API 网关的完整代码
部署方案 - Vercel + Railway、全栈 Railway、Docker Compose 三种方案
安全考虑 - 认证授权、数据安全、限流防护
监控与日志 - 监控指标和日志系统设计
扩展性设计 - 水平扩展和功能扩展方案
开发指南 - 本地开发、测试、部署的完整指南
🎯 架构特点
服务分离：Python (LangGraph) + Node.js (MCP) 的混合架构
正确流程：LLM 服务在 MCP 服务之前，符合实际调用顺序
多部署方案：支持 Vercel、Railway、Docker 等多种部署方式
完整代码：提供了可直接使用的代码示例
安全考虑：包含认证、授权、限流等安全措施

### 架构图

```mermaid
graph TB
    subgraph "移动端 APP"
        A1[用户1 - APP客户端]
        A2[用户2 - APP客户端]
        A3[用户N - APP客户端]
    end
    
    subgraph "API 网关层"
        B1[负载均衡器]
        B2[API 网关]
        B3[认证服务]
    end
    
    subgraph "LLM 服务层 (Python + LangGraph)"
        D1[LLM 推理服务]
        D2[自然语言处理]
        D3[意图识别 & 实体提取]
        D4[LangGraph 工作流引擎]
        D5[工具调用决策]
    end
    
    subgraph "MCP 服务层 (Node.js)"
        C1[MCP Server 1]
        C2[MCP Server 2]
        C3[MCP Server N]
        C4[连接池管理]
        C5[飞书 API 客户端]
    end
    
    subgraph "飞书集成层"
        E1[令牌管理]
        E2[限流控制]
        E3[API 调用]
    end
    
    subgraph "数据层"
        F1[用户会话存储]
        F2[缓存服务]
        F3[日志系统]
    end
    
    A1 --> B1
    A2 --> B1
    A3 --> B1
    
    B1 --> B2
    B2 --> B3
    
    B3 --> D1
    D1 --> D2
    D2 --> D3
    D3 --> D4
    D4 --> D5
    
    D5 --> C1
    D5 --> C2
    D5 --> C3
    
    C1 --> C5
    C2 --> C5
    C3 --> C5
    
    C5 --> E1
    C5 --> E2
    C5 --> E3
    
    E3 --> F1
    E3 --> F2
    E3 --> F3
```

## 🔄 调用流程详解

### 1. 用户请求流程
```
用户输入自然语言 → APP 客户端 → API 网关 → 认证服务 → LLM 服务 → MCP 服务 → 飞书 API → 返回结果
```

### 2. 详细步骤
1. **用户输入**：用户在 APP 中输入"明天下午3点开会"
2. **API 调用**：APP 发送 HTTP 请求到后端
3. **认证验证**：验证用户身份和权限
4. **意图识别**：LLM 分析用户意图（创建日历事件）
5. **实体提取**：提取时间、地点、参与者等信息
6. **工具选择**：选择对应的 MCP 工具（calendar.v4.calendarEvent.create）
7. **MCP 调用**：调用飞书 API 创建事件
8. **结果返回**：返回创建结果给用户

## 🛠️ 技术栈选择

### 前端技术栈
- **移动端**：React Native / Flutter
- **状态管理**：Redux / MobX
- **网络请求**：Axios / Fetch API

### 后端技术栈
- **LLM 服务**：Python + LangGraph + LangChain
- **MCP 服务**：Node.js + Express + MCP SDK
- **数据库**：PostgreSQL / MongoDB
- **缓存**：Redis
- **消息队列**：RabbitMQ / Redis

### 部署技术栈
- **容器化**：Docker + Docker Compose
- **编排**：Kubernetes (可选)
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack

## 📁 项目结构

```
project/
├── frontend/                 # 移动端 APP
│   ├── src/
│   │   ├── components/
│   │   ├── screens/
│   │   ├── services/
│   │   └── utils/
│   └── package.json
├── backend/
│   ├── llm-service/         # Python LLM 服务
│   │   ├── app/
│   │   │   ├── main.py
│   │   │   ├── workflows/
│   │   │   ├── models/
│   │   │   └── utils/
│   │   ├── requirements.txt
│   │   └── Dockerfile
│   ├── mcp-service/         # Node.js MCP 服务
│   │   ├── src/
│   │   │   ├── mcp-server/
│   │   │   ├── tools/
│   │   │   └── utils/
│   │   ├── package.json
│   │   └── Dockerfile
│   └── api-gateway/         # API 网关
│       ├── src/
│       ├── package.json
│       └── Dockerfile
├── infrastructure/          # 基础设施
│   ├── docker-compose.yml
│   ├── nginx.conf
│   └── k8s/
└── docs/                    # 文档
    ├── ARCHITECTURE.md
    ├── API.md
    └── DEPLOYMENT.md
```

## 🔧 核心代码实现

### 1. LLM 服务 (Python + LangGraph)

```python
# backend/llm-service/app/main.py
from fastapi import FastAPI, HTTPException
from langgraph import StateGraph, END
from langchain_openai import ChatOpenAI
from langchain_core.messages import HumanMessage
import httpx

app = FastAPI()

class CalendarAssistant:
    def __init__(self):
        self.llm = ChatOpenAI(model="gpt-4")
        self.workflow = self.build_workflow()
        self.http_client = httpx.AsyncClient()
    
    def build_workflow(self):
        workflow = StateGraph()
        
        # 添加节点
        workflow.add_node("intent_recognition", self.recognize_intent)
        workflow.add_node("entity_extraction", self.extract_entities)
        workflow.add_node("tool_selection", self.select_tool)
        workflow.add_node("mcp_call", self.call_mcp)
        
        # 设置流程
        workflow.set_entry_point("intent_recognition")
        workflow.add_edge("intent_recognition", "entity_extraction")
        workflow.add_edge("entity_extraction", "tool_selection")
        workflow.add_edge("tool_selection", "mcp_call")
        workflow.add_edge("mcp_call", END)
        
        return workflow.compile()
    
    async def recognize_intent(self, state):
        """识别用户意图"""
        user_input = state["user_input"]
        
        prompt = f"""
        分析以下用户输入，识别其意图：
        用户输入：{user_input}
        
        可能的意图：
        - 创建日历事件
        - 查询日历事件
        - 更新日历事件
        - 删除日历事件
        - 查询日历列表
        
        请返回意图类型。
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        intent = response.content.strip()
        
        return {"intent": intent}
    
    async def extract_entities(self, state):
        """提取实体信息"""
        user_input = state["user_input"]
        intent = state["intent"]
        
        prompt = f"""
        从以下用户输入中提取相关实体信息：
        用户输入：{user_input}
        意图：{intent}
        
        请提取以下信息：
        - 时间（开始时间、结束时间）
        - 地点
        - 参与者
        - 事件标题
        - 描述
        
        返回 JSON 格式。
        """
        
        response = await self.llm.ainvoke([HumanMessage(content=prompt)])
        entities = eval(response.content)
        
        return {"entities": entities}
    
    async def select_tool(self, state):
        """选择 MCP 工具"""
        intent = state["intent"]
        
        tool_mapping = {
            "创建日历事件": "calendar.v4.calendarEvent.create",
            "查询日历事件": "calendar.v4.calendarEvent.search",
            "更新日历事件": "calendar.v4.calendarEvent.update",
            "删除日历事件": "calendar.v4.calendarEvent.delete",
            "查询日历列表": "calendar.v4.calendar.list"
        }
        
        selected_tool = tool_mapping.get(intent)
        
        return {"selected_tool": selected_tool}
    
    async def call_mcp(self, state):
        """调用 MCP 服务"""
        tool = state["selected_tool"]
        entities = state["entities"]
        
        mcp_request = {
            "tool": tool,
            "arguments": entities
        }
        
        # 调用 MCP 服务
        response = await self.http_client.post(
            "http://mcp-service:3000/tools/call",
            json=mcp_request
        )
        
        if response.status_code != 200:
            raise HTTPException(status_code=500, detail="MCP 调用失败")
        
        result = response.json()
        return {"result": result}

# 初始化助手
assistant = CalendarAssistant()

@app.post("/chat")
async def chat_endpoint(request: dict):
    """聊天接口"""
    try:
        # 执行工作流
        result = await assistant.workflow.ainvoke({
            "user_input": request["message"]
        })
        
        return {
            "success": True,
            "data": result["result"]
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
```

### 2. MCP 服务 (Node.js)

```javascript
// backend/mcp-service/src/mcp-server/index.ts
import express from 'express';
import { McpServer } from '@modelcontextprotocol/sdk/server/mcp';
import { LarkClient } from '@larksuiteoapi/lark';

const app = express();
app.use(express.json());

// 初始化飞书客户端
const larkClient = new LarkClient({
  appId: process.env.LARK_APP_ID,
  appSecret: process.env.LARK_APP_SECRET,
});

// MCP 工具调用接口
app.post('/tools/call', async (req, res) => {
  const { tool, arguments: args } = req.body;
  
  try {
    // 调用对应的飞书 API
    const result = await callLarkAPI(tool, args);
    res.json({ success: true, data: result });
  } catch (error) {
    console.error('MCP 调用错误:', error);
    res.status(500).json({ success: false, error: error.message });
  }
});

// 飞书 API 调用函数
async function callLarkAPI(tool, args) {
  switch (tool) {
    case 'calendar.v4.calendar.list':
      return await larkClient.calendar.v4.calendar.list({
        page_size: args.page_size || 10,
        page_token: args.page_token
      });
      
    case 'calendar.v4.calendarEvent.create':
      return await larkClient.calendar.v4.calendarEvent.create({
        calendar_id: args.calendar_id,
        summary: args.summary,
        description: args.description,
        start_time: args.start_time,
        end_time: args.end_time,
        location: args.location,
        attendees: args.attendees
      });
      
    case 'calendar.v4.calendarEvent.search':
      return await larkClient.calendar.v4.calendarEvent.search({
        calendar_id: args.calendar_id,
        query: args.query,
        start_time: args.start_time,
        end_time: args.end_time
      });
      
    case 'calendar.v4.calendarEvent.update':
      return await larkClient.calendar.v4.calendarEvent.update({
        calendar_id: args.calendar_id,
        event_id: args.event_id,
        summary: args.summary,
        description: args.description,
        start_time: args.start_time,
        end_time: args.end_time,
        location: args.location,
        attendees: args.attendees
      });
      
    case 'calendar.v4.calendarEvent.delete':
      return await larkClient.calendar.v4.calendarEvent.delete({
        calendar_id: args.calendar_id,
        event_id: args.event_id
      });
      
    default:
      throw new Error(`不支持的工具: ${tool}`);
  }
}

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.PORT || 3000;
app.listen(PORT, () => {
  console.log(`MCP 服务运行在端口 ${PORT}`);
});
```

### 3. API 网关

```javascript
// backend/api-gateway/src/index.js
const express = require('express');
const rateLimit = require('express-rate-limit');
const cors = require('cors');
const { createProxyMiddleware } = require('http-proxy-middleware');

const app = express();

// 中间件
app.use(cors());
app.use(express.json());

// 限流配置
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15分钟
  max: 100, // 限制每个IP 15分钟内最多100个请求
  message: '请求过于频繁，请稍后再试'
});
app.use(limiter);

// 认证中间件
const authMiddleware = (req, res, next) => {
  const token = req.headers.authorization?.replace('Bearer ', '');
  
  if (!token) {
    return res.status(401).json({ error: '缺少认证令牌' });
  }
  
  // 验证令牌逻辑
  // TODO: 实现实际的令牌验证
  req.user = { id: 'user123', name: '测试用户' };
  next();
};

// 路由配置
app.use('/api/chat', authMiddleware, createProxyMiddleware({
  target: 'http://llm-service:8000',
  changeOrigin: true,
  pathRewrite: {
    '^/api/chat': '/chat'
  }
}));

app.use('/api/mcp', authMiddleware, createProxyMiddleware({
  target: 'http://mcp-service:3000',
  changeOrigin: true,
  pathRewrite: {
    '^/api/mcp': ''
  }
}));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', services: ['api-gateway'] });
});

const PORT = process.env.PORT || 8080;
app.listen(PORT, () => {
  console.log(`API 网关运行在端口 ${PORT}`);
});
```

## 🚀 部署方案

### 方案 A：Vercel + Railway (推荐)

#### Vercel 配置 (LLM 服务)
```json
// vercel.json
{
  "functions": {
    "api/chat/*.py": {
      "runtime": "python3.9",
      "maxDuration": 60
    }
  },
  "rewrites": [
    { "source": "/api/chat/(.*)", "destination": "/api/chat/$1" }
  ],
  "env": {
    "OPENAI_API_KEY": "@openai-api-key",
    "MCP_SERVICE_URL": "@mcp-service-url"
  }
}
```

#### Railway 配置 (MCP 服务)
```yaml
# railway.toml
[build]
builder = "nixpacks"

[deploy]
startCommand = "npm start"
healthcheckPath = "/health"
healthcheckTimeout = 300

[[services]]
name = "mcp-service"
```

### 方案 B：Railway (全栈)

```dockerfile
# Dockerfile
FROM python:3.9-slim

# 安装 Node.js
RUN apt-get update && apt-get install -y curl
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
RUN apt-get install -y nodejs

# Python 服务
WORKDIR /app/python
COPY llm-service/requirements.txt .
RUN pip install -r requirements.txt
COPY llm-service/ .

# Node.js 服务
WORKDIR /app/node
COPY mcp-service/package*.json ./
RUN npm install
COPY mcp-service/ .

# 启动脚本
WORKDIR /app
COPY start.sh .
RUN chmod +x start.sh

CMD ["./start.sh"]
```

```bash
#!/bin/bash
# start.sh
cd /app/python && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 &
cd /app/node && npm start &
wait
```

### 方案 C：Docker Compose (本地开发)

```yaml
# docker-compose.yml
version: '3.8'

services:
  api-gateway:
    build: ./backend/api-gateway
    ports:
      - "8080:8080"
    environment:
      - NODE_ENV=development
    depends_on:
      - llm-service
      - mcp-service

  llm-service:
    build: ./backend/llm-service
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - MCP_SERVICE_URL=http://mcp-service:3000
    volumes:
      - ./backend/llm-service:/app

  mcp-service:
    build: ./backend/mcp-service
    ports:
      - "3000:3000"
    environment:
      - LARK_APP_ID=${LARK_APP_ID}
      - LARK_APP_SECRET=${LARK_APP_SECRET}
    volumes:
      - ./backend/mcp-service:/app

  redis:
    image: redis:alpine
    ports:
      - "6379:6379"

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=calendar_app
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## 🔒 安全考虑

### 1. 认证与授权
- **JWT 令牌**：用户身份验证
- **API 密钥**：服务间通信
- **OAuth 2.0**：飞书 API 授权

### 2. 数据安全
- **HTTPS**：所有通信加密
- **数据脱敏**：敏感信息处理
- **访问控制**：基于角色的权限管理

### 3. 限流与防护
- **请求限流**：防止 API 滥用
- **DDoS 防护**：云服务商防护
- **输入验证**：防止注入攻击

## 📊 监控与日志

### 1. 监控指标
- **响应时间**：API 调用延迟
- **错误率**：失败请求比例
- **吞吐量**：每秒请求数
- **资源使用**：CPU、内存、磁盘

### 2. 日志系统
```javascript
// 日志配置
const winston = require('winston');

const logger = winston.createLogger({
  level: 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.json()
  ),
  transports: [
    new winston.transports.File({ filename: 'error.log', level: 'error' }),
    new winston.transports.File({ filename: 'combined.log' })
  ]
});
```

## 🔄 扩展性设计

### 1. 水平扩展
- **无状态服务**：支持多实例部署
- **负载均衡**：自动分发请求
- **数据库分片**：支持大规模数据

### 2. 功能扩展
- **插件系统**：支持第三方集成
- **工作流引擎**：复杂业务逻辑
- **多租户**：企业级支持

## 📝 开发指南

### 1. 本地开发环境搭建
```bash
# 克隆项目
git clone <repository-url>
cd project

# 安装依赖
cd backend/llm-service && pip install -r requirements.txt
cd ../mcp-service && npm install
cd ../api-gateway && npm install

# 启动服务
docker-compose up -d
```

### 2. 测试
```bash
# 运行测试
cd backend/llm-service && python -m pytest
cd ../mcp-service && npm test
cd ../api-gateway && npm test
```

### 3. 部署
```bash
# 部署到 Vercel
vercel --prod

# 部署到 Railway
railway up
```

## 📚 相关文档

- [API 文档](./API.md)
- [部署指南](./DEPLOYMENT.md)
- [开发指南](./DEVELOPMENT.md)
- [故障排除](./TROUBLESHOOTING.md)

---

*最后更新时间：2024年12月* 