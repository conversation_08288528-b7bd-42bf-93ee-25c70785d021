"""
测试配置文件，包含共享的fixture和测试设置
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timedelta
from pathlib import Path

import pytest

# 加载环境变量
try:
    from dotenv import load_dotenv

    # 获取项目根目录
    if 'conftest.py' in __file__:
        project_root = Path(__file__).parent.parent
    else:
        project_root = Path.cwd()

    # 尝试加载不同位置的.env文件
    env_files = [
        project_root / ".env",
        project_root / ".env.local",
        project_root / ".env.test"
    ]

    for env_file in env_files:
        if env_file.exists():
            load_dotenv(env_file)
            print(f"📁 测试环境已加载: {env_file}")
            break
    else:
        print("⚠️  未找到.env文件，使用系统环境变量")

except ImportError:
    print("⚠️  python-dotenv未安装，使用系统环境变量")

# 确保可以导入项目模块
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

# 尝试加载测试环境变量
test_env_path = os.path.join(os.path.dirname(__file__), "test.env")
if os.path.exists(test_env_path):
    load_dotenv(test_env_path)
else:
    print("警告: test.env文件不存在，将使用默认测试配置")

# 设置测试环境标志
os.environ["TESTING"] = "1"

# 加载环境变量
load_dotenv()

# 导入项目模块
# import feishu_api  # 注释掉不存在的导入
# from storage import TokenStorage  # 注释掉不存在的导入

# import config  # 注释掉不存在的导入


# 解决pytest中的asyncio事件循环问题
@pytest.fixture(scope="session")
def event_loop():
    """创建一个会话范围的事件循环"""
    policy = asyncio.get_event_loop_policy()
    loop = policy.new_event_loop()
    yield loop
    loop.close()


# 导入测试辅助类
try:
    from tests.test_utils import TEST_CALENDAR_ID, TEST_USER_ID, TestCalendarHelper
except ImportError:
    try:
        from test_utils import TEST_CALENDAR_ID, TEST_USER_ID, TestCalendarHelper
    except ImportError:
        # 如果导入失败，使用默认值
        TEST_CALENDAR_ID = "test_calendar_id"
        TEST_USER_ID = "test_user_id"

        class TestCalendarHelper:
            @staticmethod
            async def setup():
                return "mock_token"

            @staticmethod
            async def create_test_event(token):
                return "mock_event_id"

            @staticmethod
            async def cleanup(token, event_id):
                pass


@pytest.fixture
def real_feishu_client():
    """提供真实的飞书客户端"""
    from integrations.feishu import get_feishu_client
    return get_feishu_client()


@pytest.fixture
def test_user_id():
    """提供测试用户ID"""
    return f"test_user_{int(datetime.now().timestamp())}"


@pytest.fixture
def sample_token_data():
    """提供示例token数据"""
    now = datetime.now()
    return {
        "access_token": f"test_token_{int(now.timestamp())}",
        "refresh_token": f"refresh_token_{int(now.timestamp())}",
        "access_token_expire": int(now.timestamp()) + 7200,
        "refresh_token_expire": int(now.timestamp()) + 86400
    }


@pytest.fixture
def test_environment_check():
    """检查测试环境"""
    required_env_vars = ["FEISHU_CLIENT_ID", "FEISHU_CLIENT_SECRET"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]

    if missing_vars:
        pytest.skip(f"缺少必要的环境变量: {missing_vars}")

    return True


@pytest.fixture
def test_calendar_id():
    """提供测试用的日历ID"""
    return f"test_calendar_{int(datetime.now().timestamp())}"


@pytest.fixture
def sample_event_data():
    """提供示例事件数据"""
    now = datetime.now()
    start_time = int(now.timestamp())
    end_time = int((now + timedelta(hours=1)).timestamp())

    return {
        "summary": f"测试会议_{int(now.timestamp())}",
        "description": "这是一个测试会议",
        "start_time": {"timestamp": str(start_time)},
        "end_time": {"timestamp": str(end_time)},
        "location": {"name": "会议室A"},
        "reminders": [{"minutes": 10}],
        "is_all_day": False,
    }


@pytest.fixture
def test_access_token():
    """获取测试访问令牌"""
    token = os.getenv("TEST_ACCESS_TOKEN")
    if not token:
        pytest.skip("需要TEST_ACCESS_TOKEN环境变量进行真实API测试")
    return token


@pytest.fixture
def cleanup_test_data():
    """清理测试数据"""
    created_items = []

    def add_item(item_type, item_id):
        """添加需要清理的项目"""
        created_items.append((item_type, item_id))

    yield add_item

    # 测试完成后清理
    for item_type, item_id in created_items:
        try:
            # 这里可以添加具体的清理逻辑
            print(f"清理 {item_type}: {item_id}")
        except Exception as e:
            print(f"清理失败: {e}")


# 测试会话设置
@pytest.fixture(scope="session", autouse=True)
def test_session_setup():
    """测试会话设置"""
    print("\n" + "="*50)
    print("开始测试会话 - 使用真实数据测试")
    print("="*50)

    yield

    print("\n" + "="*50)
    print("测试会话结束")
    print("="*50)
