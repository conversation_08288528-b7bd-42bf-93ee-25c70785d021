# API Client 功能分析

## 概述

`integrations/feishu/api_client.py` 是项目中的核心飞书API客户端，提供了完整的飞书API调用功能。该文件包含了认证、日历操作、事件管理等多个功能模块。

## 文件结构分析

### 1. 导入和配置
- **依赖库**: httpx, tenacity, datetime, typing等
- **配置项**: FEISHU_CLIENT_ID, FEISHU_CLIENT_SECRET, MAX_RETRY_ATTEMPTS等
- **全局变量**: calendar_name_cache（日历名称缓存）

### 2. 异常类定义
- `FeishuAPIError`: 飞书API异常
- `FeishuNetworkError`: 飞书网络异常  
- `FeishuAuthError`: 飞书认证异常

### 3. 装饰器和工具函数
- `feishu_retry`: 重试装饰器，处理网络错误重试
- `api_logger`: API调用日志装饰器，记录请求和响应
- `generate_request_id`: 生成唯一请求ID

## 功能模块详细分析

### 认证相关API

#### 1. exchange_code(code: str)
- **功能**: 通过授权码获取访问令牌
- **参数**: code - 授权码
- **返回**: 包含access_token、refresh_token、expires_in的字典
- **API端点**: `/open-apis/authen/v1/access_token`

#### 2. refresh_token(app_id: str, app_secret: str, refresh_token: str)
- **功能**: 刷新访问令牌
- **参数**: app_id, app_secret, refresh_token
- **返回**: 新的访问令牌信息
- **API端点**: `/open-apis/authen/v1/refresh_access_token`

#### 3. get_user_info(access_token: str)
- **功能**: 获取用户信息
- **参数**: access_token - 用户访问令牌
- **返回**: 用户基本信息（open_id, name, email等）
- **API端点**: `/open-apis/authen/v1/user_info`

### 日历相关API

#### 1. get_calendars(access_token: str, page_token: str = None, page_size: int = 100)
- **功能**: 获取用户日历列表
- **参数**: access_token, page_token（分页标记）, page_size（页面大小）
- **返回**: 日历列表数据
- **API端点**: `/open-apis/calendar/v4/calendars`

#### 2. get_calendar_detail(access_token: str, calendar_id: str)
- **功能**: 获取特定日历的详细信息
- **参数**: access_token, calendar_id
- **返回**: 日历详细信息
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}`

#### 3. create_calendar(access_token: str, summary: str, description: str = None, permissions: str = "private", color: str = "#1976d2")
- **功能**: 创建新日历
- **参数**: access_token, summary（日历名称）, description, permissions, color
- **返回**: 创建的日历信息
- **API端点**: `/open-apis/calendar/v4/calendars`

#### 4. update_calendar(access_token: str, calendar_id: str, summary: str = None, description: str = None, permissions: str = None, color: str = None)
- **功能**: 更新日历信息
- **参数**: access_token, calendar_id, 可选的更新字段
- **返回**: 更新结果
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}`

#### 5. delete_calendar(access_token: str, calendar_id: str)
- **功能**: 删除日历
- **参数**: access_token, calendar_id
- **返回**: 删除结果
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}`

#### 6. subscribe_calendar(access_token: str, calendar_id: str)
- **功能**: 订阅日历
- **参数**: access_token, calendar_id
- **返回**: 订阅结果

#### 7. unsubscribe_calendar(access_token: str, calendar_id: str)
- **功能**: 取消订阅日历
- **参数**: access_token, calendar_id
- **返回**: 取消订阅结果

### 事件相关API

#### 1. get_calendar_events(access_token: str, calendar_id: str, start_time: str = None, end_time: str = None, page_token: str = None, page_size: int = 100, query: str = None)
- **功能**: 获取日历事件列表
- **参数**: access_token, calendar_id, 时间范围, 分页参数, 查询关键词
- **返回**: 事件列表
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}/events`

#### 2. get_event_detail(access_token: str, calendar_id: str, event_id: str)
- **功能**: 获取事件详情
- **参数**: access_token, calendar_id, event_id
- **返回**: 事件详细信息
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}`

#### 3. create_event(access_token: str, calendar_id: str, summary: str, start_time: Dict[str, Any], end_time: Dict[str, Any], description: str = None, location: Dict[str, str] = None, reminders: List[Dict[str, int]] = None, attendees: List[Dict[str, str]] = None)
- **功能**: 创建日历事件
- **参数**: access_token, calendar_id, 事件基本信息, 时间, 地点, 提醒, 参与者
- **返回**: 创建的事件信息
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}/events`

#### 4. update_event(access_token: str, calendar_id: str, event_id: str, summary: str = None, start_time: Dict[str, Any] = None, end_time: Dict[str, Any] = None, description: str = None, location: Dict[str, str] = None, reminders: List[Dict[str, int]] = None, attendees: List[Dict[str, str]] = None)
- **功能**: 更新事件信息
- **参数**: access_token, calendar_id, event_id, 可选的更新字段
- **返回**: 更新结果
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}`

#### 5. delete_event(access_token: str, calendar_id: str, event_id: str)
- **功能**: 删除事件
- **参数**: access_token, calendar_id, event_id
- **返回**: 删除结果
- **API端点**: `/open-apis/calendar/v4/calendars/{calendar_id}/events/{event_id}`

### 特殊功能API

#### 1. get_today_events(access_token: str, calendar_id: str = None, date: str = None, query: str = None)
- **功能**: 获取今天的事件
- **参数**: access_token, calendar_id（可选）, date（可选）, query（可选）
- **返回**: 今天的事件列表

#### 2. get_week_events(access_token: str, calendar_id: str = None, start_date: str = None)
- **功能**: 获取一周的事件
- **参数**: access_token, calendar_id（可选）, start_date（可选）
- **返回**: 一周的事件列表

#### 3. search_calendar_events(access_token: str, query: str, calendar_id: str = None, start_time: str = None, end_time: str = None, page_token: str = None, page_size: int = 100)
- **功能**: 搜索日历事件
- **参数**: access_token, query（搜索关键词）, 其他可选参数
- **返回**: 搜索结果

### 批量操作API

#### 1. batch_create_events(access_token: str, events: List[Dict[str, Any]])
- **功能**: 批量创建事件
- **参数**: access_token, events（事件列表）
- **返回**: 批量创建结果

#### 2. batch_update_events(access_token: str, events: List[Dict[str, Any]])
- **功能**: 批量更新事件
- **参数**: access_token, events（事件列表）
- **返回**: 批量更新结果

#### 3. batch_delete_events(access_token: str, events: List[Dict[str, str]])
- **功能**: 批量删除事件
- **参数**: access_token, events（包含calendar_id和event_id的列表）
- **返回**: 批量删除结果

### 任务创建API

#### 1. create_daily_task(access_token: str, title: str, description: str = None, calendar_id: str = None, time_slot: str = "09:00")
- **功能**: 创建每日任务
- **参数**: access_token, title, description, calendar_id, time_slot
- **返回**: 创建的任务信息

#### 2. create_task_for_date(access_token: str, title: str, date: str, description: str = None, calendar_id: str = None, time_slot: str = "09:00")
- **功能**: 为特定日期创建任务
- **参数**: access_token, title, date, description, calendar_id, time_slot
- **返回**: 创建的任务信息

### 工具函数

#### 1. convert_to_timestamp(time_value: Union[str, int, datetime])
- **功能**: 将各种时间格式转换为时间戳字符串
- **参数**: time_value（支持多种时间格式）
- **返回**: 时间戳字符串

#### 2. timestamp_to_iso(timestamp, timezone="Asia/Shanghai")
- **功能**: 将时间戳转换为ISO 8601格式
- **参数**: timestamp, timezone
- **返回**: ISO格式时间字符串

#### 3. handle_api_response(response, operation_name)
- **功能**: 统一处理API响应
- **参数**: response（httpx响应对象）, operation_name（操作名称）
- **返回**: 标准化的响应数据

## 总结

api_client.py 是一个功能完整的飞书API客户端，包含：
- **认证模块**: 3个函数
- **日历管理**: 7个函数  
- **事件管理**: 5个基础函数 + 3个特殊功能函数
- **批量操作**: 3个函数
- **任务创建**: 2个函数
- **工具函数**: 3个函数

总计约30个主要函数，覆盖了飞书日历API的所有核心功能。
