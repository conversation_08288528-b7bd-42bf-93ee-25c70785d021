"""
日历相关的API模型
"""

from typing import Optional

from pydantic import BaseModel


class CalendarCreate(BaseModel):
    """创建日历的请求模型"""

    summary: str
    description: Optional[str] = ""
    permissions: Optional[str] = "private"
    color: Optional[int] = 1


class CalendarUpdate(BaseModel):
    """更新日历的请求模型"""

    summary: Optional[str] = None
    description: Optional[str] = None
    permissions: Optional[str] = None
    color: Optional[int] = None
