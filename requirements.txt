# Web framework and server
fastapi==0.115.13
uvicorn==0.33.0

# HTTP clients
httpx==0.28.1
requests>=2.28.0

# Configuration and environment
python-dotenv==1.1.0

# Data validation and models
pydantic==2.11.7

# Feishu API
lark-oapi==1.4.14

# Async HTTP
aiohttp==3.9.5

# Database
supabase==2.16.0

# AI and intelligent processing
langdetect>=1.0.9
pyspellchecker>=0.7.0

# Retry mechanism
tenacity==8.2.3

# MCP (Model Context Protocol) support
mcp>=1.6.0
httpx-sse>=0.4.0

# 新增依赖
langchain==0.3.26
langchain-openai==0.3.28
lark-oapi==1.4.14
sseclient==0.0.27
